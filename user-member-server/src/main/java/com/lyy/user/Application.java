package com.lyy.user;

import cn.lyy.cache.config.EnableMultiLevelCache;
import com.lyy.idempotent.starter.LyyIdempotentAutoConfiguration;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableTransactionManagement(proxyTargetClass = true)
@EnableFeignClients(basePackages = {"cn.lyy","com.lyy"})
@MapperScan(basePackages = {"com.lyy.user.domain.benefit.repository","com.lyy.user.domain.member.repository",
        "com.lyy.user.domain.user.repository","com.lyy.user.domain.account.repository","com.lyy.user.domain.report.repository",
        "com.lyy.user.domain.statistics.repository", "com.lyy.user.domain.correction.repository","com.lyy.user.domain.doris.mapper"})
@ComponentScan(basePackages = {"com.lyy","cn.lyy"},
    excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = LyyIdempotentAutoConfiguration.class))
@EnableAsync
@EnableMultiLevelCache
@EnableAspectJAutoProxy(exposeProxy = true)
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

}
