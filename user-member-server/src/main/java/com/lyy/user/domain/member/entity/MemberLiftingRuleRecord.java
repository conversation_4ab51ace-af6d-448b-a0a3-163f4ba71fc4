package com.lyy.user.domain.member.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 会员升级策略规则记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("um_member_lifting_rule_record")
public class MemberLiftingRuleRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 商户ID
     */
    @TableField("merchant_id")
    private Long merchantId;

    /**
     * 平台用户
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 商户用户
     */
    @TableField("merchant_user_id")
    private Long merchantUserId;

    /**
     * 会员id
     */
    @TableField("member_id")
    private Long memberId;

    /**
     * 会员升级记录规则
     */
    @TableField("member_lifting_rule_id")
    private Long memberLiftingRuleId;

    /**
     * 状态，1：已经触发，未处理；2：已经处理完成；3：已经失效(过期)
     * @see com.lyy.user.account.infrastructure.constant.MemberLiftingRuleRecordStatusEnum
     */
    @TableField("status")
    private Short status;

    /**
     * 范围值,若为消费金额时，单位为元
     */
    @TableField("range_value")
    private BigDecimal rangeValue;

    /**
     * 其他记录信息
     */
    @TableField("other_info")
    private String otherInfo;

    /**
     * 有效期截止时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建者
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新者
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 描述
     */
    @TableField("description")
    private String description;
}
