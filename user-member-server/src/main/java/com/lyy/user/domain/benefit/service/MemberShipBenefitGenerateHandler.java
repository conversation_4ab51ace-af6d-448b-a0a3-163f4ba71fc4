package com.lyy.user.domain.benefit.service;

import com.lyy.user.account.infrastructure.benefit.dto.GenerateAccountDTO;
import com.lyy.user.domain.benefit.repository.BenefitMapper;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 会员权益生成处理类
 *
 * <AUTHOR>
 * @create 2021/4/9 18:50
 */
@Slf4j
@Component(value = "MEMBERSHIP_LEVEL_CHANGE")
public class MemberShipBenefitGenerateHandler implements BenefitGenerateHandler{

    @Resource
    private BenefitMapper benefitMapper;



    @Override
    public void doGenerate(GenerateAccountDTO generateAccountDTO) {

    }
}
