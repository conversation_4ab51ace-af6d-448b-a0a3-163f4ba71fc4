package com.lyy.user.domain.correction.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 会员升级策略
 * @TableName um_member_lifting_bak
 */
@Data
public class UmMemberLiftingBak implements Serializable {

    private static final long serialVersionUID = -6004798469003398721L;
    /**
     * 
     */
    private Long id;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 会员组ID
     */
    private Long memberGroupId;

    /**
     * 升降策略(1 升级策略,2 降级策略(降成长值),3 降等级策略(降1级))
     */
    private Integer lifting;

    /**
     * 成长值
     */
    private Long growValue;

    /**
     * 条件(0 满足其中一条,1 满足所有条件)
     */
    private Integer condition;

    /**
     * 有效次数，若为-1则生效无限次，其他正数为生效的次数
     */
    private Integer effectiveNumber;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 升/降级策略名称
     */
    private String name;

    /**
     * 是否有效 true 有效,false 无效
     */
    private Boolean active;

}