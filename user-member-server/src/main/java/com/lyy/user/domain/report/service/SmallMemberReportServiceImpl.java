package com.lyy.user.domain.report.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.common.DataList;
import com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberCardReportDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberCardReportQueryDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberCardStoreValueReportDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberReportDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberReportQueryDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberStoreValueRecordDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberStoreValueRecordQueryDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberStoreValueReportDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueStoreValueSimpleDto;
import com.lyy.user.account.infrastructure.user.dto.tag.TagSimpleInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagSimpleInfoParam;
import com.lyy.user.application.report.SmallMemberReportService;
import com.lyy.user.application.user.ITagUserService;
import com.lyy.user.domain.report.repository.SmallVenueMemberReportMapper;
import com.lyy.user.domain.user.entity.TagUser;
import com.lyy.user.infrastructure.execption.BusinessException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date : 2022-12-21 11:35
 **/
@Service
public class SmallMemberReportServiceImpl implements SmallMemberReportService {

    @Resource
    SmallVenueMemberReportMapper smallVenueMemberReportMapper;
    @Resource
    ITagUserService iTagUserService;

    @Override
    public DataList<SmallVenueMemberReportDto> queryUserMemberReport(SmallVenueMemberReportQueryDto memberReportQueryDto) {
        //查询会员门店标签
        List<Long> userTagList = getUserTagList(memberReportQueryDto);

        //查询当前条件下会员总数
        Long total = smallVenueMemberReportMapper.queryUserMemberReportTotalMemberNum(memberReportQueryDto,userTagList.toArray(new Long[]{}));

        if (total == null || total == 0) {
            return new DataList<SmallVenueMemberReportDto>(0L, null, 0L);
        }

        long totalPage = total % memberReportQueryDto.getPageSize() == 0 ? total/memberReportQueryDto.getPageSize() : total/memberReportQueryDto.getPageSize()+1;

        //查询当页会员基本数据
        List<SmallVenueMemberReportDto> smallVenueMemberReportDtoList = smallVenueMemberReportMapper.queryUserMemberReport(memberReportQueryDto ,userTagList.toArray(new Long[]{}),(memberReportQueryDto.getPageIndex() -1) * memberReportQueryDto.getPageSize());

        //当页数据为空 返回空结果
        if(CollUtil.isEmpty(smallVenueMemberReportDtoList)) {
            return new DataList<SmallVenueMemberReportDto>(total, null, totalPage);
        }
        //查询当页会员储值数据
        List<Long> userIdList = smallVenueMemberReportDtoList.stream().map(SmallVenueMemberReportDto::getUserId).collect(Collectors.toList());
        List<SmallVenueStoreValueSimpleDto> smallVenueMemberStoreValueDtoList = smallVenueMemberReportMapper.queryUserMemberStoreValueReport(userIdList,memberReportQueryDto.getMerchantId(),memberReportQueryDto.getStoreValueCurrencyStoreIds());

        //聚合会员储值数据 key-merchantUserId  value -List<SmallVenueStoreValueSimpleDto>
        Map<Long, List<SmallVenueStoreValueSimpleDto>> merchantUserIdStoreValuesMap = Optional.ofNullable(smallVenueMemberStoreValueDtoList)
                .map(item -> {
                    return item.stream().collect(Collectors.toMap(
                            SmallVenueStoreValueSimpleDto::getMerchantUserId
                            , value -> {
                                List<SmallVenueStoreValueSimpleDto> list = new ArrayList<>();
                                list.add(value);
                                return list;
                            }
                            , (a, b) -> {
                                a.addAll(b);
                                return a;
                            })
                    );
                }).orElse(new HashMap<>(16));

        //反查标签数据
        List<Long> storeTagIdList = smallVenueMemberReportDtoList.stream().flatMap(item -> Arrays.stream(Optional.ofNullable(item.getUserTagIds()).orElse(new Long[]{}))).distinct().collect(Collectors.toList());
        Map<Long, String> storeTagMap = Optional.ofNullable(iTagUserService.list(new QueryWrapper<TagUser>()
                .in("id", storeTagIdList)
                .eq("merchant_id",memberReportQueryDto.getMerchantId())
                .notIn("business_type", TagBusinessTypeEnum.GROUP_NAME.getStatus())
                )).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(TagUser::getId, TagUser::getName, (a, b) -> a));

        //数据聚合
        smallVenueMemberReportDtoList.forEach(smallVenueMemberReportDto ->{
            //聚合标签
            List<Long> userTagIds = CollUtil.toList(smallVenueMemberReportDto.getUserTagIds());
            if(CollUtil.isNotEmpty(userTagIds)){
                ArrayList<String> tagNameList = new ArrayList<>();
                for (Long userTagId : userTagIds) {
                    Optional.ofNullable(storeTagMap.get(userTagId)).ifPresent(tagNameList::add);
                }
                smallVenueMemberReportDto.setUserTagNames(tagNameList);
            }

            //聚合储值数据
            smallVenueMemberReportDto.setStoreValueListDto(merchantUserIdStoreValuesMap.get(smallVenueMemberReportDto.getMerchantUserId()));
        });

        return new DataList<SmallVenueMemberReportDto>(total, smallVenueMemberReportDtoList, totalPage);
    }

    @Override
    public SmallVenueMemberStoreValueReportDto totalQueryMemberStoreValueReport(SmallVenueMemberReportQueryDto smallVenueMemberReportQueryDto) {
        //会员总数
        //查询会员门店标签
        List<Long> userTagList = getUserTagList(smallVenueMemberReportQueryDto);

        SmallVenueMemberStoreValueReportDto smallVenueMemberStoreValueReportDto = new SmallVenueMemberStoreValueReportDto();

        Long total = smallVenueMemberReportMapper.queryUserMemberReportTotalMemberNum(smallVenueMemberReportQueryDto,userTagList.toArray(new Long[]{}));
        smallVenueMemberStoreValueReportDto.setMemberNum(total);;

        List<SmallVenueStoreValueSimpleDto> list = smallVenueMemberReportMapper.totalQueryMemberStoreValueReport(smallVenueMemberReportQueryDto,userTagList.toArray(new Long[]{}));
        smallVenueMemberStoreValueReportDto.setStoreValueListDto(list);
        return smallVenueMemberStoreValueReportDto;
    }

    @Override
    public DataList<SmallVenueMemberCardReportDto> queryUserMemberCardReport(SmallVenueMemberCardReportQueryDto smallVenueMemberCardReportQueryDto) {
        //查询会员门店标签
        List<Long> storeTagIds = getStoreTagId(smallVenueMemberCardReportQueryDto.getMerchantId(), smallVenueMemberCardReportQueryDto.getStoreName());
        Long storeTagId = storeTagIds.get(0);
        List<Long> userTagList = Optional.ofNullable(smallVenueMemberCardReportQueryDto.getUserTagIdList()).orElse(new ArrayList<>());
        userTagList.add(storeTagId);
        smallVenueMemberCardReportQueryDto.setUserTagIdList(userTagList);

        //查询会员卡总数
        Long cardNum = smallVenueMemberReportMapper.queryUserMemberReportTotalCardNum(smallVenueMemberCardReportQueryDto,storeTagIds.toArray(new Long[]{}));
        if (cardNum == null || cardNum == 0) {
            return new DataList<SmallVenueMemberCardReportDto>(0L, null, 0L);
        }
        long totalPage = cardNum % smallVenueMemberCardReportQueryDto.getPageSize() == 0 ? cardNum/smallVenueMemberCardReportQueryDto.getPageSize() : cardNum/smallVenueMemberCardReportQueryDto.getPageSize()+1;

        //查询会员卡基本信息
        List<SmallVenueMemberCardReportDto> smallVenueMemberCardReportDtoList = smallVenueMemberReportMapper.queryUserMemberCardReport(smallVenueMemberCardReportQueryDto,storeTagIds.toArray(new Long[]{}),(smallVenueMemberCardReportQueryDto.getPageIndex() -1) * smallVenueMemberCardReportQueryDto.getPageSize());
        if(CollUtil.isEmpty(smallVenueMemberCardReportDtoList)){
            return new DataList<SmallVenueMemberCardReportDto>(cardNum, null, totalPage);
        }

        //聚合账户id进行储值余额查询
        List<Long> accountIdList = smallVenueMemberCardReportDtoList.stream().map(SmallVenueMemberCardReportDto::getAccountId).collect(Collectors.toList());
        List<SmallVenueStoreValueSimpleDto> smallVenueMemberStoreValueDtoList = smallVenueMemberReportMapper.queryUserMemberCardStoreValueReport(accountIdList,smallVenueMemberCardReportQueryDto.getMerchantId(),smallVenueMemberCardReportQueryDto.getStoreValueCurrencyStoreIds());

        //聚合会员储值数据 key-accountId  value -List<SmallVenueStoreValueSimpleDto>
        Map<Long, List<SmallVenueStoreValueSimpleDto>> accountStoreValuesMap = Optional.ofNullable(smallVenueMemberStoreValueDtoList)
                .map(item -> {
                    return item.stream().collect(Collectors.toMap(
                            SmallVenueStoreValueSimpleDto::getAccountId
                            , value -> {
                                List<SmallVenueStoreValueSimpleDto> list = new ArrayList<>();
                                list.add(value);
                                return list;
                            }
                            , (a, b) -> {
                                a.addAll(b);
                                return a;
                            })
                    );
                }).orElse(new HashMap<>(16));

        smallVenueMemberCardReportDtoList.forEach(item -> item.setStoreValueListDto(accountStoreValuesMap.get(item.getAccountId())));

        return new DataList<SmallVenueMemberCardReportDto>(cardNum, smallVenueMemberCardReportDtoList, totalPage);
    }

    @Override
    public SmallVenueMemberCardStoreValueReportDto totalQueryUserMemberCardReport(SmallVenueMemberCardReportQueryDto smallVenueMemberCardReportQueryDto) {
        //查询会员门店标签
        SmallVenueMemberCardStoreValueReportDto smallVenueMemberCardStoreValueReportDto = new SmallVenueMemberCardStoreValueReportDto();
        List<Long> storeTagIds = getStoreTagId(smallVenueMemberCardReportQueryDto.getMerchantId(), smallVenueMemberCardReportQueryDto.getStoreName());
        Long storeTagId = storeTagIds.get(0);
        List<Long> userTagList = Optional.ofNullable(smallVenueMemberCardReportQueryDto.getUserTagIdList()).orElse(new ArrayList<>());
        userTagList.add(storeTagId);
        smallVenueMemberCardReportQueryDto.setUserTagIdList(userTagList);

        //查询会员卡总数
        Long cardNum = smallVenueMemberReportMapper.queryUserMemberReportTotalCardNum(smallVenueMemberCardReportQueryDto,storeTagIds.toArray(new Long[]{}));
        smallVenueMemberCardStoreValueReportDto.setCardNum(cardNum);

        List<SmallVenueStoreValueSimpleDto> list = smallVenueMemberReportMapper.totalQueryMemberCardStoreValueReport(smallVenueMemberCardReportQueryDto ,userTagList.toArray(new Long[]{}));
        smallVenueMemberCardStoreValueReportDto.setStoreValueListDto(list);
        return smallVenueMemberCardStoreValueReportDto;
    }

    @Override
    public DataList<SmallVenueMemberStoreValueRecordDto> pageQueryMemberStoreValueReport(SmallVenueMemberStoreValueRecordQueryDto smallVenueMemberStoreValueRecordQueryDto) {
        //获取门店标签
        List<Long> storeTagIds = getStoreTagId(smallVenueMemberStoreValueRecordQueryDto.getMerchantId(), smallVenueMemberStoreValueRecordQueryDto.getStoreName());
        //连表查询
        Long total =smallVenueMemberReportMapper.totalQueryMemberStoreValueRecordTotalReport(smallVenueMemberStoreValueRecordQueryDto,storeTagIds.toArray(new Long[]{}));
        if(total == null || total == 0){
            return new DataList<SmallVenueMemberStoreValueRecordDto>(0L,null,0L);
        }
        List<SmallVenueMemberStoreValueRecordDto> list =smallVenueMemberReportMapper.totalQueryMemberStoreValueRecordReport(smallVenueMemberStoreValueRecordQueryDto,storeTagIds.toArray(new Long[]{}),(smallVenueMemberStoreValueRecordQueryDto.getPageIndex() -1) * smallVenueMemberStoreValueRecordQueryDto.getPageSize());
        long totalPage = total % smallVenueMemberStoreValueRecordQueryDto.getPageSize() == 0 ? total/smallVenueMemberStoreValueRecordQueryDto.getPageSize() : total/smallVenueMemberStoreValueRecordQueryDto.getPageSize()+1;
        return new DataList<SmallVenueMemberStoreValueRecordDto>(total,list,totalPage);

    }

    @Override
    public BigDecimal totalQueryMemberStoreValueChangeReport(SmallVenueMemberStoreValueRecordQueryDto smallVenueMemberStoreValueRecordQueryDto) {
        //获取门店标签
        List<Long> storeTagIds = getStoreTagId(smallVenueMemberStoreValueRecordQueryDto.getMerchantId(), smallVenueMemberStoreValueRecordQueryDto.getStoreName());
        //这样才能触发分表
        return Optional.ofNullable(smallVenueMemberReportMapper.totalQueryMemberStoreValueRecordChangeReport(smallVenueMemberStoreValueRecordQueryDto,storeTagIds.toArray(new Long[]{}),1)).orElse(BigDecimal.ZERO)
        .subtract(Optional.ofNullable(smallVenueMemberReportMapper.totalQueryMemberStoreValueRecordChangeReport(smallVenueMemberStoreValueRecordQueryDto,storeTagIds.toArray(new Long[]{}),2)).orElse(BigDecimal.ZERO));
    }

    @Override
    public DataList<SmallVenueMemberReportDto> queryUserMemberReportV2(SmallVenueMemberReportQueryDto memberReportQueryDto) {
        //查询会员门店标签
        List<Long> userTagList = getUserTagList(memberReportQueryDto);

        //查询当前条件下会员总数
        Long total = smallVenueMemberReportMapper.queryUserMemberReportTotalMemberNum(memberReportQueryDto,userTagList.toArray(new Long[]{}));

        if (total == null || total == 0) {
            return new DataList<SmallVenueMemberReportDto>(0L, null, 0L);
        }

        long totalPage = total % memberReportQueryDto.getPageSize() == 0 ? total/memberReportQueryDto.getPageSize() : total/memberReportQueryDto.getPageSize()+1;

        //查询当页会员基本数据
        List<SmallVenueMemberReportDto> smallVenueMemberReportDtoList = smallVenueMemberReportMapper.queryUserMemberReportV2(memberReportQueryDto ,userTagList.toArray(new Long[]{}),(memberReportQueryDto.getPageIndex() -1) * memberReportQueryDto.getPageSize());

        //当页数据为空 返回空结果
        if(CollUtil.isEmpty(smallVenueMemberReportDtoList)) {
            return new DataList<SmallVenueMemberReportDto>(total, null, totalPage);
        }
        //查询当页会员储值数据
        List<Long> userIdList = smallVenueMemberReportDtoList.stream().map(SmallVenueMemberReportDto::getUserId).collect(Collectors.toList());
        List<SmallVenueStoreValueSimpleDto> smallVenueMemberStoreValueDtoList = smallVenueMemberReportMapper.queryUserMemberStoreValueReport(userIdList,memberReportQueryDto.getMerchantId(),memberReportQueryDto.getStoreValueCurrencyStoreIds());

        //聚合会员储值数据 key-merchantUserId  value -List<SmallVenueStoreValueSimpleDto>
        Map<Long, List<SmallVenueStoreValueSimpleDto>> merchantUserIdStoreValuesMap = Optional.ofNullable(smallVenueMemberStoreValueDtoList)
                .map(item -> {
                    return item.stream().collect(Collectors.toMap(
                            SmallVenueStoreValueSimpleDto::getMerchantUserId
                            , value -> {
                                List<SmallVenueStoreValueSimpleDto> list = new ArrayList<>();
                                list.add(value);
                                return list;
                            }
                            , (a, b) -> {
                                a.addAll(b);
                                return a;
                            })
                    );
                }).orElse(new HashMap<>(16));

        //反查标签数据
        List<Long> storeTagIdList = smallVenueMemberReportDtoList.stream().flatMap(item -> Arrays.stream(Optional.ofNullable(item.getUserTagIds()).orElse(new Long[]{}))).distinct().collect(Collectors.toList());
        Map<Long, String> storeTagMap = Optional.ofNullable(iTagUserService.list(new QueryWrapper<TagUser>()
                .in("id", storeTagIdList)
                .eq("merchant_id",memberReportQueryDto.getMerchantId())
                .notIn("business_type", TagBusinessTypeEnum.GROUP_NAME.getStatus())
        )).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(TagUser::getId, TagUser::getName, (a, b) -> a));

        //数据聚合
        smallVenueMemberReportDtoList.forEach(smallVenueMemberReportDto ->{
            //聚合标签
            List<Long> userTagIds = CollUtil.toList(smallVenueMemberReportDto.getUserTagIds());
            if(CollUtil.isNotEmpty(userTagIds)){
                ArrayList<String> tagNameList = new ArrayList<>();
                for (Long userTagId : userTagIds) {
                    Optional.ofNullable(storeTagMap.get(userTagId)).ifPresent(tagNameList::add);
                }
                smallVenueMemberReportDto.setUserTagNames(tagNameList);
            }

            //聚合储值数据
            smallVenueMemberReportDto.setStoreValueListDto(merchantUserIdStoreValuesMap.get(smallVenueMemberReportDto.getMerchantUserId()));
        });

        return new DataList<SmallVenueMemberReportDto>(total, smallVenueMemberReportDtoList, totalPage);
    }


    private List<Long> getUserTagList(SmallVenueMemberReportQueryDto smallVenueMemberReportQueryDto) {
        List<Long> storeTagIds = getStoreTagId(smallVenueMemberReportQueryDto.getMerchantId(), smallVenueMemberReportQueryDto.getStoreName());
        List<Long> userTagList = Optional.ofNullable(smallVenueMemberReportQueryDto.getUserTagIdList()).orElse(new ArrayList<>());
        userTagList.addAll(storeTagIds);
        userTagList = userTagList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        smallVenueMemberReportQueryDto.setUserTagIdList(userTagList);
        return userTagList;
    }

    private List<Long> getStoreTagId(Long merchantId, List<String> storeName) {
        TagSimpleInfoParam param = new TagSimpleInfoParam();
        param.setMerchantId(merchantId);
        param.setNames(storeName);
        List<TagSimpleInfoDTO> tagSimpleInfoDtoList = iTagUserService.tagSimpleInfoList(param);
        List<Long> storeTagIds = Optional.ofNullable(tagSimpleInfoDtoList).map(item -> item.stream().map(TagSimpleInfoDTO::getTagId).collect(Collectors.toList())).orElse(null);
        //门店标签不存在打回
        if (CollUtil.isEmpty(storeTagIds)) {
            throw new BusinessException(GlobalErrorCode.BAD_REQUEST.getCode(), "门店信息错误");
        }
        return storeTagIds;
    }
}
