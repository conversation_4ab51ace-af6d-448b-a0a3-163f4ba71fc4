package com.lyy.user.domain.correction.repository;

import com.baomidou.mybatisplus.annotation.TableField;
import com.google.common.base.CaseFormat;
import com.google.common.collect.Lists;
import com.lyy.user.domain.correction.dto.CorrectionDTO;
import com.lyy.user.infrastructure.constants.ShardingGCEnum;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.sql.Date;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.Data;
import lombok.Setter;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.expression.DateValue;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.JdbcNamedParameter;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.GreaterThan;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.expression.operators.relational.ItemsList;
import net.sf.jsqlparser.expression.operators.relational.MinorThan;
import net.sf.jsqlparser.expression.operators.relational.MultiExpressionList;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.Limit;
import net.sf.jsqlparser.statement.select.OrderByElement;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.SelectExpressionItem;
import net.sf.jsqlparser.statement.select.SelectItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/**
 * <AUTHOR>
 * @date 2023/9/26 清完数据应该删除
 */
@Data
@Builder
public class DataGCParser {

    private final Logger logger = LoggerFactory.getLogger(DataGCParser.class);

    public static final String COLUMN_MERCHANT = "merchant_id";

    public static final String COLUMN_ID = "id";

    public static final String CREATE_TIME = "create_time";

    private static final String TIME = "2021-11-06 00:00:00";

    private static Date LAST_TIME_DATE;

    private List<Tuple2<String, String>> selectFieldStr;

    private List<Column> columns;

    private List<String> nameIngColumns;

    String tableName;

    Long pageSize;

    Long merchantId;

    Integer dsNo;

    @Setter
    private Long id;

    static {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        try {
            java.util.Date parse = sdf.parse(TIME);
            LAST_TIME_DATE = new Date(parse.getTime());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    class SqlParser {

        public String deleteParser(List<Long> ids) {
            Delete delete = new Delete();
            Table table = new Table(tableName);
            table.setAlias(new Alias(aliasFormat(tableName)));
            delete.setTable(table);

            InExpression inExpression = new InExpression();
            inExpression.setLeftExpression(new Column(table, COLUMN_ID));
            ExpressionList itemsList = new ExpressionList();

            List<LongValue> expressionList = ids.stream().map(LongValue::new).collect(Collectors.toList());
            itemsList.addExpressions(expressionList);
            inExpression.setRightItemsList(itemsList);

            delete.setWhere(inExpression);
            if (logger.isDebugEnabled()) {
                logger.debug("DataGCParser 组装delete SQL =====> [{}]", delete);
            }

            return delete.toString();
        }

        public String pageParser(Class clazz) {
            PlainSelect plainSelect = new PlainSelect();
            Table table = new Table(tableName);
            table.setAlias(new Alias(aliasFormat(tableName)));
            plainSelect.setFromItem(table);
            selectFieldAndParseUnderLine(clazz);

            // 查询列
            List<String> selectColumnsStr = Arrays.asList(COLUMN_ID, COLUMN_MERCHANT);
            List<SelectItem> expressionItemList = selectColumnsStr.stream().map(item -> {
                SelectExpressionItem selectExpressionItem = new SelectExpressionItem();
                selectExpressionItem.setExpression(new Column(table, item));
                return (SelectItem) selectExpressionItem;
            }).collect(Collectors.toList());
            plainSelect.setSelectItems(expressionItemList);

            MinorThan minorThan = new MinorThan();
            minorThan.setLeftExpression(new Column(table, CREATE_TIME));
            minorThan.setRightExpression(new DateValue(LAST_TIME_DATE));

            // 设置 um表的 ID
            if (null != id && id > 0) {
                GreaterThan greaterThan = new GreaterThan();
                greaterThan.setLeftExpression(new Column(table, COLUMN_ID));
                greaterThan.setRightExpression(new LongValue(id));
                plainSelect.setWhere(ShardingGCEnum.UM_MEMBER_RANGE.getTableName().equals(tableName) ? greaterThan : new AndExpression(minorThan, greaterThan));
            } else if (!ShardingGCEnum.UM_MEMBER_RANGE.getTableName().equals(tableName)) {
                plainSelect.setWhere(minorThan);
            }

            //排序
            OrderByElement orderByElement = new OrderByElement();
            orderByElement.setAsc(true);
            orderByElement.setExpression(new Column(table, COLUMN_ID));
            plainSelect.setOrderByElements(Lists.newArrayList(orderByElement));

            //分页
            Limit limit = new Limit();
            limit.withRowCount(new LongValue(pageSize));
            plainSelect.setLimit(limit);

            if (logger.isDebugEnabled()) {
                logger.debug("DataGCParser 组装SQL =====> [{}]", plainSelect);
            }

            return plainSelect.toString();
        }

        public String pageParserWithoutTime(Class clazz) {
            PlainSelect plainSelect = new PlainSelect();
            Table table = new Table(tableName);
            table.setAlias(new Alias(aliasFormat(tableName)));
            plainSelect.setFromItem(table);
            selectFieldAndParseUnderLine(clazz);

            // 查询列
            List<SelectItem> expressionItemList = selectFieldStr.stream().map(item -> {
                SelectExpressionItem selectExpressionItem = new SelectExpressionItem();
                selectExpressionItem.setExpression(new Column(table, item.getT1()));
                selectExpressionItem.setAlias(new Alias(item.getT2()));
                return (SelectItem) selectExpressionItem;
            }).collect(Collectors.toList());

            plainSelect.setSelectItems(expressionItemList);


            // 设置 um表的 ID
            if (null != id && id > 0) {
                GreaterThan greaterThan = new GreaterThan();
                greaterThan.setLeftExpression(new Column(table, COLUMN_ID));
                greaterThan.setRightExpression(new LongValue(id));
                plainSelect.setWhere(greaterThan);
            }

            //排序
            OrderByElement orderByElement = new OrderByElement();
            orderByElement.setAsc(true);
            orderByElement.setExpression(new Column(table, COLUMN_ID));
            plainSelect.setOrderByElements(Lists.newArrayList(orderByElement));

            //分页
            Limit limit = new Limit();
            limit.withRowCount(new LongValue(pageSize));
            plainSelect.setLimit(limit);

            if (logger.isDebugEnabled()) {
                logger.debug("DataGCParser 组装SQL =====> [{}]", plainSelect);
            }

            return plainSelect.toString();
        }

        public String pageParser(Class clazz, List<Long> indexId) {
            PlainSelect plainSelect = new PlainSelect();
            Table table = new Table(tableName);
            table.setAlias(new Alias(aliasFormat(tableName)));
            plainSelect.setFromItem(table);
            selectFieldAndParseUnderLine(clazz);

            // 查询列
            List<SelectItem> expressionItemList = selectFieldStr.stream().map(item -> {
                SelectExpressionItem selectExpressionItem = new SelectExpressionItem();
                selectExpressionItem.setExpression(new Column(table, item.getT1()));
                selectExpressionItem.setAlias(new Alias(item.getT2()));
                return (SelectItem) selectExpressionItem;
            }).collect(Collectors.toList());

            plainSelect.setSelectItems(expressionItemList);

            MinorThan minorThan = new MinorThan();
            minorThan.setLeftExpression(new Column(table, CREATE_TIME));
            minorThan.setRightExpression(new DateValue(LAST_TIME_DATE));

            // 设置 um表的 ID
            GreaterThan greaterThan = new GreaterThan();
            greaterThan.setLeftExpression(new Column(table, COLUMN_ID));
            greaterThan.setRightExpression(new LongValue(id));

            List<Expression> inValue = indexId.stream().map(LongValue::new).collect(Collectors.toList());
            ItemsList inItem = new ExpressionList(inValue);
            InExpression inExpression = new InExpression(new Column(COLUMN_ID), inItem);

            plainSelect.setWhere(ShardingGCEnum.UM_MEMBER_RANGE.getTableName().equals(tableName) ? inExpression : new AndExpression(minorThan, inExpression));

            if (logger.isDebugEnabled()) {
                logger.debug("DataGCParser 组装SQL =====> [{}]", plainSelect);
            }

            return plainSelect.toString();
        }

        // 插入备份和恢复备份
        public String insertParser(Class clazz) {
            Insert insert = new Insert();
            Table table = new Table(tableName);
            insert.setTable(table);

            // field
            insertFieldAndParseUnderLine(clazz);
            insert.setColumns(columns);

            // value
            MultiExpressionList multiExpressionList = new MultiExpressionList();
            int size = nameIngColumns.size();
            List<Expression> jdbcParameters = new ArrayList<>(size);
            for (int i = 0; i < size; i++) {
                String param = nameIngColumns.get(i);
                JdbcNamedParameter jdbcNamedParameter = new JdbcNamedParameter(param);
                jdbcParameters.add(jdbcNamedParameter);

//                JdbcParameter jdbcParameter = new JdbcParameter();
//                jdbcParameters.add(jdbcParameter);
            }
            multiExpressionList.addExpressionList(jdbcParameters);
            insert.setItemsList(multiExpressionList);
            return insert.toString();
        }

    }

    public String pageParser(Function<String, Class> func) {
        SqlParser sqlParser = new SqlParser();
        Class className = func.apply(tableName);
        return sqlParser.pageParser(className);
    }

    public String pageParserWithoutTime(Function<String, Class> func) {
        SqlParser sqlParser = new SqlParser();
        Class className = func.apply(tableName);
        return sqlParser.pageParserWithoutTime(className);
    }

    public String pageParser(List<Long> id) {
        SqlParser sqlParser = new SqlParser();
        Class className = ShardingGCEnum.getSourceClass(tableName);
        return sqlParser.pageParser(className, id);
    }

    public String deleteParser(List<Long> ids) {
        SqlParser sqlParser = new SqlParser();
        return sqlParser.deleteParser(ids);
    }

    public String insertParser(Class clazz) {
        SqlParser sqlParser = new SqlParser();
        return sqlParser.insertParser(clazz);
    }

    public void nextIndex(List<CorrectionDTO> corrections) {
        if (corrections.isEmpty()) {
            return;
        }

        CorrectionDTO dto = corrections.get(corrections.size() - 1);
        id = dto.getId();
    }

    private static String aliasFormat(String tableName) {
        String[] split = tableName.split("_");
        return Arrays.stream(split).map(alias -> alias.substring(0, 1)).reduce((a1, a2) -> a1 + a2).get();
    }

    private void selectFieldAndParseUnderLine(Class clazz) {
        Field[] declaredFields = clazz.getDeclaredFields();
        selectFieldStr = new ArrayList<>(declaredFields.length);
        FIELD_CYCLE:
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            String fieldName = declaredField.getName();
            if ("serialVersionUID".equals(fieldName)) {
                continue;
            }

            Annotation[] annotations = declaredField.getDeclaredAnnotations();
            for (Annotation annotation : annotations) {
                if (annotation instanceof TableField) {
                    TableField tableField = (TableField) annotation;
                    String realFieldName = tableField.value();
                    selectFieldStr.add(Tuples.of(realFieldName, fieldName));
                    continue FIELD_CYCLE;
                }
            }

            // upper2underLine
            String underLineName = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, fieldName);
            selectFieldStr.add(Tuples.of(underLineName, fieldName));
        }

    }

    private void insertFieldAndParseUnderLine(Class clazz) {
        Field[] declaredFields = clazz.getDeclaredFields();
        columns = Lists.newArrayList();
        nameIngColumns = Lists.newArrayList();
        FIELD_CYCLE:
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            String fieldName = declaredField.getName();
            if ("serialVersionUID".equals(fieldName)) {
                continue;
            }

            nameIngColumns.add(fieldName);
            Annotation[] annotations = declaredField.getDeclaredAnnotations();
            for (Annotation annotation : annotations) {
                if (annotation instanceof TableField) {
                    TableField tableField = (TableField) annotation;
                    String realFieldName = tableField.value();
                    Column column = new Column(realFieldName);
                    columns.add(column);
                    continue FIELD_CYCLE;
                }
            }

            // upper2underLine
            String underLineName = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, fieldName);
            Column column = new Column(underLineName);
            columns.add(column);
        }
    }

//    public static void main(String[] args) {
//        DataGCParser build = DataGCParser.builder().build();
//        build.getFieldAndParseUnderLine(TagUser.class);
//    }

}
