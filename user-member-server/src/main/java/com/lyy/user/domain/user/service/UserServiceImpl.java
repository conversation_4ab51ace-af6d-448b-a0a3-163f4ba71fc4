package com.lyy.user.domain.user.service;

import static java.util.Optional.ofNullable;

import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.UserAppDTO;
import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneDTO;
import com.lyy.user.app.interfaces.facade.dto.UserDTO;
import com.lyy.user.app.interfaces.facade.dto.UserVO;
import com.lyy.user.application.statistics.StatisticsService;
import com.lyy.user.application.user.IMerchantUserService;
import com.lyy.user.application.user.IPlatformUserPhoneService;
import com.lyy.user.application.user.IUserService;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.repository.user.UserRepository;
import com.lyy.user.infrastructure.util.CommonConverterTools;
import com.lyy.user.interfaces.assembler.UserAssembler;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName: UserServiceImpl
 * @description: 用户
 * @author: pengkun
 * @date: 2021/04/01
 **/
@Slf4j
@Service
public class UserServiceImpl implements IUserService {

    @Autowired
    private IMerchantUserService merchantUserService;

    @Autowired
    private IPlatformUserPhoneService platformUserPhoneService;

    @Autowired
    private StatisticsService statisticsService;

    @Resource
    private UserRepository userRepository;

    @Resource
    private Executor userPoolExecutor;

    @Autowired
    private Executor userInitStatisticsExecutor;

    /**
     * 根据平台用户id和商户id获取用户信息
     *
     * @param userId     平台用户Id
     * @param merchantId 商户Id
     * @return com.lyy.user.account.infrastructure.user.dto.UserInfoDTO
     */
    @Override
    public UserInfoDTO getUserInfoByUserIdAndMerchantId(Long userId, Long merchantId,Boolean initStatisticsAysn) {
        UserDTO userDTO = userRepository.get(userId);
        if (Objects.isNull(userDTO)) {
            return null;
        }
        UserInfoDTO userInfoDTO = UserAssembler.INSTANCE.fromUserDTO(userDTO);
        if (merchantId != null) {
            MerchantUser merchantUser = merchantUserService.findByUserIdAndMerchantId(userId, merchantId);
            userInfoDTO.setMerchantId(merchantId);
            if (merchantUser != null) {
                UserAssembler.INSTANCE.assignUserInfoDTO(merchantUser, userInfoDTO);
            } else {
                userInfoDTO.setMerchantUserId(saveMerchantUser(merchantId, userDTO,true));
            }
            if(Objects.isNull(initStatisticsAysn) || initStatisticsAysn.booleanValue() == false){
                statisticsService.initStatistics(merchantId, userId, userInfoDTO.getMerchantUserId());
            }else{
                userInitStatisticsExecutor.execute(()->{
                    log.debug("[初始化指定商户下的 商户用户统计数据] 异步处理");
                    statisticsService.initStatistics(merchantId, userId, userInfoDTO.getMerchantUserId());
                });
            }
        }
        return userInfoDTO;
    }

    @Override
    public UserInfoDTO getUserInfoNoTagByUserIdAndMerchantId(Long userId, Long merchantId) {
        UserDTO userDTO = userRepository.get(userId);
        if (Objects.isNull(userDTO)) {
            return null;
        }
        UserInfoDTO userInfoDTO = UserAssembler.INSTANCE.fromUserDTO(userDTO);
        if (merchantId != null) {
            MerchantUser merchantUser = merchantUserService.findByUserIdAndMerchantId(userId, merchantId);
            userInfoDTO.setMerchantId(merchantId);
            if (merchantUser != null) {
                UserAssembler.INSTANCE.assignUserInfoDTO(merchantUser, userInfoDTO);
            } else {
                userInfoDTO.setMerchantUserId(saveMerchantUser(merchantId, userDTO,false));
            }
            statisticsService.initStatistics(merchantId, userId, userInfoDTO.getMerchantUserId());
        }
        return userInfoDTO;
    }

    /**
     * 根据平台用户id,商户用户id,商户id获取用户信息
     *
     * @param userId         平台用户id
     * @param merchantUserId 商户用户id
     * @param merchantId     商户id
     * @return com.lyy.user.account.infrastructure.user.dto.UserInfoDTO
     */
    @Override
    public UserInfoDTO getUserInfoByUserIdAndMerchantUserId(Long userId, Long merchantUserId, Long merchantId) {
        UserDTO userDTO = userRepository.get(userId);
        if (Objects.isNull(userDTO)) {
            return null;
        }
        UserInfoDTO userInfoDTO = UserAssembler.INSTANCE.fromUserDTO(userDTO);
        if (merchantUserId != null && merchantId != null && merchantId > 0) {
            MerchantUser merchantUser = merchantUserService.findById(merchantUserId, merchantId);
            userInfoDTO.setMerchantId(merchantId);
            if (merchantUser != null) {
                UserAssembler.INSTANCE.assignUserInfoDTO(merchantUser, userInfoDTO);
            }else {
                throw new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR.getCode(),UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR.getMessage());
            }
        }
        if (Objects.isNull(userInfoDTO.getMerchantId())) {
            userInfoDTO.setMerchantId(merchantId);
        }
        return userInfoDTO;
    }

    /**
     * 根据openId或unionId获取用户信息
     *
     * @param openId     openId
     * @param unionId    unionId
     * @param merchantId 商户Id
     * @return com.lyy.user.account.infrastructure.user.dto.UserInfoDTO
     */
    @Override
    public UserInfoDTO getUserInfoByOpenIdOrUnionId(String openId, String unionId, Long merchantId) {
        UserVO userVO = userRepository.getByUnionIdOrOpenIdV2(unionId, openId);
        if (Objects.isNull(userVO)) {
            return null;
        }
        UserInfoDTO userInfoDTO = UserAssembler.INSTANCE.toUserInfoDTO(userVO);
        if (merchantId != null) {
            MerchantUser merchantUser = merchantUserService.findByUserIdAndMerchantId(userVO.getId(), merchantId);
            if (merchantUser != null) {
                UserAssembler.INSTANCE.assignUserInfoDTO(merchantUser, userInfoDTO);
            } else {
                UserDTO userDTO = UserAssembler.INSTANCE.toUserDTO(userVO);
                userInfoDTO.setMerchantUserId(saveMerchantUser(merchantId, userDTO,true));
                userInfoDTO.setMerchantId(merchantId);
            }
            userInfoDTO.setUpdated(userVO.getUpdated());
            CompletableFuture.runAsync(()->statisticsService.initStatistics(merchantId, userVO.getId(), userInfoDTO.getMerchantUserId())
                ,userPoolExecutor);
        }
        return userInfoDTO;
    }



    /**
     * 新增商户用户信息
     *
     * @param merchantId 商户id
     * @param userDTO    平台用户信息
     * @return java.lang.Long
     */
    private Long saveMerchantUser(Long merchantId, UserDTO userDTO,Boolean isCreateTag) {
        //新增商户用户信息
        MerchantUserDTO dto = new MerchantUserDTO();
        dto.setUserId(userDTO.getId());
        dto.setMerchantId(merchantId);
        dto.setTelephone(userDTO.getTelephone());
        dto.setBirthday(userDTO.getBirthday());
        dto.setName(userDTO.getName());
        if(StringUtils.isNotBlank(userDTO.getGender())){
            dto.setGender(userDTO.getGender().trim());
        }else {
            dto.setGender("");
        }
        dto.setProvinceCity(userDTO.getProvinceCity());
        dto.setUserType(userDTO.getUserType());
        dto.setCityId(userDTO.getCityId());
        dto.setHeadImg(userDTO.getHeadImg());
        dto.setIsCreateTag(isCreateTag);
        return merchantUserService.saveOrUpdateMerchantUser(dto);
    }

    /**
     * 根据openId或unionId获取用户信息
     *
     * @param telephone  手机号码
     * @param merchantId 商户Id
     * @return java.util.List<com.lyy.user.account.infrastructure.user.dto.UserInfoDTO>
     */
    @Override
    public List<UserInfoDTO> listByTelephoneAndMerchantId(String telephone, Long merchantId) {
        //获取商户用户信息
        List<MerchantUser> merchantUserList = merchantUserService.findByTelephoneAndMerchantId(telephone, merchantId);
        if (merchantUserList == null || merchantUserList.size() == 0) {
            throw new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR.getCode(), UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR.getMessage());
        }

        List<UserInfoDTO> userInfoDTOList = new ArrayList<>(merchantUserList.size());
        for (MerchantUser merchantUser : merchantUserList) {
            UserInfoDTO userInfoDTO = CommonConverterTools.convert(UserInfoDTO.class, merchantUser);
            userInfoDTO.setMerchantUserId(merchantUser.getId());
            userInfoDTO.setLyyUserId(merchantUser.getUserId());
            userInfoDTOList.add(userInfoDTO);
        }
        return userInfoDTOList;
    }

    /**
     * 更新绑定的手机号码
     *
     * @param dto
     * @return java.lang.Boolean
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTelephone(MerchantUserDTO dto) {
        //查询平台用户信息，校验是否需要进行更新手机号码
        Long userId = dto.getUserId();
        UserDTO user = userRepository.get(userId);
        if (!user.getTelephone().equals(dto.getTelephone())) {
            UserDTO userDTO = new UserDTO();
            userDTO.setId(userId);
            userDTO.setTelephone(dto.getTelephone());
            userDTO.setUpdated(new Date());
            userRepository.insertOrUpdateUser(userDTO);
            //保存平台用户手机号码记录
            savePlatformPhoneRecord(dto);
        }
        // TODO: 2021/4/6 是否同步更新该用户的全部商户用户的手机号码
        if (dto.getMerchantId() != null) {
            //更新商户用户手机号信息
            merchantUserService.updateTelephone(dto.getMerchantId(), dto.getId(), userId, dto.getTelephone(), dto.getUpdatedby());
        }
        return true;
    }

    /**
     * 更新用户信息
     *
     * @param userCreateDTO
     * @return
     */
    @Override
    public Boolean updatePlatformUserInfo(UserCreateDTO userCreateDTO) {
        UserDTO userDTO = UserAssembler.INSTANCE.fromUserCreateDTO(userCreateDTO);
        userRepository.insertOrUpdateUser(userDTO);
        return true;
    }

    /**
     * 注销用户
     *
     * @param userId          平台用户id
     * @param merchantId      商户id
     * @param operationUserId 操作人id
     * @return
     */
    @Override
    public Boolean deleteUserInfo(Long userId,Long merchantUserId, Long merchantId, Long operationUserId) {
        //商户用户处理
        MerchantUser updateMerchantUser = new MerchantUser();
        updateMerchantUser.setId(merchantUserId);
        updateMerchantUser.setUserId(userId);
        updateMerchantUser.setMerchantId(merchantId);
        updateMerchantUser.setActive(UserMemberSysConstants.DISABLE);
        updateMerchantUser.setUpdatedby(ofNullable(operationUserId).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        return merchantUserService.updateByIdAndMerchantId(updateMerchantUser);
    }

    /**
     * 创建用户APP信息
     *
     * @param userAppDTO
     * @return
     */
    @Override
    public Boolean createUserApp(UserAppDTO userAppDTO) {
        userRepository.insertOrUpdateUserApp(userAppDTO.getLyyUserId(), userAppDTO.getOpenid(), userAppDTO.getAppId());
        return true;
    }

    /**
     * 更新用户信息
     *
     * @param dto
     * @return
     */
    @Override
    public Boolean updateUserInfo(MerchantUserDTO dto) {
        boolean flag = false;
        //商户用户ID为空
        if (dto.getId() == null) {
            //更新平台用户信息
            UserDTO userDTO = UserAssembler.INSTANCE.fromMerchantUserDTO(dto);
            userRepository.insertOrUpdateUser(userDTO);
            flag = true;
            if(StringUtils.isNotBlank(dto.getTelephone())){
                savePlatformPhoneRecord(dto);
            }
        } else {
            if (dto.getMerchantId() == null) {
                throw new BusinessException(UserErrorCode.MERCHANT_USER_UPDATE_PARAM_ERROR.getCode(), UserErrorCode.MERCHANT_USER_UPDATE_PARAM_ERROR.getMessage());
            }
            if (dto.getUserId() != null) {
                UserDTO userDTO = UserAssembler.INSTANCE.fromMerchantUserDTO(dto);
                userRepository.insertOrUpdateUser(userDTO);
                flag = true;
                if (StringUtils.isNotBlank(dto.getTelephone())) {
                    savePlatformPhoneRecord(dto);
                }
            }
            //更新商户用户信息
            merchantUserService.saveOrUpdateMerchantUser(dto);
        }
        return flag;
    }

    /**
     * 保存平台用户手机记录
     *
     * @param dto
     */
    private void savePlatformPhoneRecord(MerchantUserDTO dto) {
        PlatformUserPhoneDTO userPhoneDTO = new PlatformUserPhoneDTO();
        userPhoneDTO.setUserId(dto.getUserId());
        userPhoneDTO.setMerchantId(ofNullable(dto.getMerchantId()).orElse(UserMemberSysConstants.PLATFORM_MERCHANT_ID));
        userPhoneDTO.setTelephone(dto.getTelephone());
        userPhoneDTO.setOperatorId(ofNullable(dto.getUpdatedby()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        userPhoneDTO.setMerchantUserId(dto.getId());
        platformUserPhoneService.saveOrUpdatePlatformUserPhone(userPhoneDTO);
    }

    /**
     * 根据openId和appId获取平台用户信息
     *
     * @param openId openId
     * @param appId  appId
     * @return
     */
    @Override
    public UserInfoDTO getPlatformUserInfoByOpenIdAndAppId(String openId, String appId) {
        UserDTO userDTO = userRepository.getByAppIdAndOpenId(appId, openId);
        return UserAssembler.INSTANCE.fromUserDTO(userDTO);
    }

    /**
     * 根据平台用户id获取平台用户信息
     *
     * @param userId 用户id
     * @return
     */
    @Override
    public UserInfoDTO getPlatformUserInfoByUserId(Long userId) {
        UserDTO userDTO = userRepository.get(userId);
        return UserAssembler.INSTANCE.fromUserDTO(userDTO);
    }

    /**
     * 根据openId和appId获取用户信息
     *
     * @param openId     openId
     * @param appId      appId
     * @param merchantId 商户id
     * @return
     */
    @Override
    public UserInfoDTO getUserInfoByOpenIdAndAppId(String openId, String appId, Long merchantId) {
        UserVO userVO = userRepository.getByAppIdAndOpenIdV2(appId, openId);
        if (Objects.isNull(userVO)) {
            return null;
        }
        UserInfoDTO userInfoDTO = UserAssembler.INSTANCE.toUserInfoDTO(userVO);
        if (merchantId != null) {
            MerchantUser merchantUser = merchantUserService.findByUserIdAndMerchantId(userVO.getId(), merchantId);
            if (merchantUser != null) {
                UserAssembler.INSTANCE.assignUserInfoDTO(merchantUser, userInfoDTO);
            } else {
                UserDTO userDTO = UserAssembler.INSTANCE.toUserDTO(userVO);
                userInfoDTO.setMerchantUserId(saveMerchantUser(merchantId, userDTO,true));
                userInfoDTO.setMerchantId(merchantId);
            }
            userInfoDTO.setUpdated(userVO.getUpdated());
            CompletableFuture.runAsync(()->statisticsService.initStatistics(merchantId, userVO.getId(), userInfoDTO.getMerchantUserId())
                    ,userPoolExecutor);
        }
        return userInfoDTO;
    }
}
