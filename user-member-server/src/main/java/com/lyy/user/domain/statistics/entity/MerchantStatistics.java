package com.lyy.user.domain.statistics.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 商户统计总表
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName(value = "um_merchant_statistics")
public class MerchantStatistics {
    /**
     * 商家用户id
     */
    @TableId
    private Long Id;

    /**
     * 商家id
     */
    @TableField(value = "merchant_id")
    private Long merchantId;

    /**
     * 商户用户总数
     */
    @TableField(value = "user_amount")
    private Long userAmount;

    /**
     * 支付总金额（包括充值支付和业务支付）
     */
    @TableField(value = "total_pay_consume")
    private BigDecimal totalPayConsume;

    /**
     * 余额总数
     */
    @TableField(value = "balance_amount")
    private BigDecimal balanceAmount;

    /**
     * 剩余币数
     */
    @TableField(value = "balance_coins")
    private BigDecimal balanceCoins;


    /**
     * 记录创建时间
     */
    @TableField(value = "created")
    private Date created;

    /**
     * 记录更新时间
     */
    @TableField(value = "updated")
    private Date updated;



}