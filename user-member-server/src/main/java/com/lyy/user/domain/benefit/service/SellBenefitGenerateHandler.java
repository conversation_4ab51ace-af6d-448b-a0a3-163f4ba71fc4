package com.lyy.user.domain.benefit.service;

import com.lyy.user.account.infrastructure.benefit.dto.GenerateAccountDTO;
import com.lyy.user.domain.benefit.repository.BenefitMapper;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 售卖方式生成权益
 *     售卖只是针对商家而言，对于用户就是购买商品行为
 *
 * <AUTHOR>
 * @create 2021/4/6 14:52
 */
@Slf4j
@Component(value = "sell")
public class SellBenefitGenerateHandler implements BenefitGenerateHandler {

    @Resource
    private BenefitMapper benefitMapper;


    @Override
    public void doGenerate(GenerateAccountDTO generateAccountDTO) {
    }
}
