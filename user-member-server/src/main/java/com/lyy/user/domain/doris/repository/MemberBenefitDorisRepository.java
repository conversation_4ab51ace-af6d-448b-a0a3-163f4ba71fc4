package com.lyy.user.domain.doris.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.domain.doris.entity.VUmAccountBenefitExpired6m;
import com.lyy.user.domain.doris.entity.VUmBenefitExpired;
import com.lyy.user.domain.doris.mapper.AccountBenefitExpired6mMapper;
import com.lyy.user.domain.doris.mapper.NotScopeBenefitExpiredMapper;
import com.lyy.user.account.infrastructure.constant.AccountBenefitStatusEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.domain.doris.entity.MemberCouponNumber;
import com.lyy.user.domain.doris.entity.VUmAccountBenefitExpired;
import com.lyy.user.domain.doris.mapper.BenefitExpiredMapper;
import com.lyy.user.domain.doris.mapper.MemberCouponNumberMapper;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since 2024/12/3 - 16:00
 */
@DS("doris")
@Repository
@RequiredArgsConstructor
public class MemberBenefitDorisRepository {

    private final BenefitExpiredMapper benefitExpiredMapper;
    private final MemberCouponNumberMapper memberCouponNumberMapper;
    private final AccountBenefitExpired6mMapper accountBenefitExpired6mMapper;

    private final NotScopeBenefitExpiredMapper notScopeBenefitExpiredMapper;

    public IPage<VUmAccountBenefitExpired> listExpiredBenefit(Long merchantId, Integer limit, Integer pageSize) {
        Page<VUmAccountBenefitExpired> page = new Page<>(limit, pageSize);
        page.setSearchCount(false);
        return benefitExpiredMapper.selectPage(page,
                Wrappers.<VUmAccountBenefitExpired>lambdaQuery()
                        .eq(Objects.nonNull(merchantId), VUmAccountBenefitExpired::getMerchantId, merchantId)
                        .ne(VUmAccountBenefitExpired::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                        .ne(VUmAccountBenefitExpired::getStatus, AccountBenefitStatusEnum.EXPIRED.getStatus())
                        .orderByAsc(VUmAccountBenefitExpired::getMerchantId, VUmAccountBenefitExpired::getId)

        );
    }

    public IPage<VUmAccountBenefitExpired> listVenueExpiredBenefit(Long merchantId, Integer limit, Integer pageSize) {
        Page<VUmAccountBenefitExpired> page = new Page<>(limit, pageSize);
        page.setSearchCount(false);
        return benefitExpiredMapper.selectPage(page,
                Wrappers.<VUmAccountBenefitExpired>lambdaQuery()
                        .eq(Objects.nonNull(merchantId), VUmAccountBenefitExpired::getMerchantId, merchantId)
                        .eq(VUmAccountBenefitExpired::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                        .eq(VUmAccountBenefitExpired::getStatus, AccountBenefitStatusEnum.NORMAL.getStatus())
                        .orderByAsc(VUmAccountBenefitExpired::getMerchantId, VUmAccountBenefitExpired::getId)
        );
    }

    public IPage<MemberCouponNumber> listMemberCouponNumber(Long merchantId, Integer limit, Integer pageSize) {
        Page<MemberCouponNumber> page = new Page<>(limit, pageSize);
        page.setSearchCount(false);
        return memberCouponNumberMapper.selectPage(page,
                Wrappers.<MemberCouponNumber>lambdaQuery()
                        .eq(Objects.nonNull(merchantId), MemberCouponNumber::getMerchantId, merchantId)
                        .orderByAsc(MemberCouponNumber::getMerchantId, MemberCouponNumber::getUserId)
        );
    }

    public IPage<VUmAccountBenefitExpired6m> listExpiredBenefit6m(Long merchantId, Integer limit, Integer pageSize) {
        Page<VUmAccountBenefitExpired6m> page = new Page<>(limit, pageSize);
        page.setSearchCount(false);
        return accountBenefitExpired6mMapper.selectPage(page,
                Wrappers.<VUmAccountBenefitExpired6m>lambdaQuery()
                        .eq(Objects.nonNull(merchantId), VUmAccountBenefitExpired6m::getMerchantId, merchantId)
                        .orderByAsc(VUmAccountBenefitExpired6m::getMerchantId, VUmAccountBenefitExpired6m::getId)
        );
    }

    public IPage<VUmBenefitExpired> listNotScopeExpiredBenefit(Long merchantId, Integer limit, Integer pageSize) {
        Page<VUmBenefitExpired> page = new Page<>(limit, pageSize);
        page.setSearchCount(false);
        return notScopeBenefitExpiredMapper.selectPage(page,
                Wrappers.<VUmBenefitExpired>lambdaQuery()
                        .eq(Objects.nonNull(merchantId), VUmBenefitExpired::getMerchantId, merchantId)
                        .orderByAsc(VUmBenefitExpired::getMerchantId, VUmBenefitExpired::getBenefitId)
        );
    }
}
