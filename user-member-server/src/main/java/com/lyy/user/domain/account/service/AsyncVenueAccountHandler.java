package com.lyy.user.domain.account.service;

import com.google.common.collect.Lists;
import com.lyy.lock.redis.RedisLock;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MerchantBenefitIncrementDTO;
import com.lyy.user.infrastructure.constants.RedisKey;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * @ClassName: AsyncVenueAccountHandler
 * @description: 异步多金宝账户处理
 * @author: pengkun
 * @date: 2022/06/15
 **/
@Slf4j
@Component
public class AsyncVenueAccountHandler {

    private static final int MAX_NUM = 500;

    @Resource
    private RedisLock redisLock;

    @Autowired
    private AsyncVenuePayoutBenefitHandler asyncVenuePayoutBenefitHandler;

    @Async("merchantPayoutBenefitTaskExecutor")
    public void asyncMerchantPayoutBusiness(MerchantBenefitIncrementDTO dto) {
        int limit = (dto.getUserIds().size() + MAX_NUM - 1) / MAX_NUM;
        List<List<Long>> userIds = Lists.partition(dto.getUserIds(), MAX_NUM);
        CountDownLatch latch = new CountDownLatch(limit);
        userIds.forEach(ids -> asyncVenuePayoutBenefitHandler.handler(ids, dto, latch));
        try {
            latch.await();
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            log.error(e.getMessage(), e);
        } finally {
            redisLock.unlock(RedisKey.MERCHANT_PAYOUT_BENEFIT_LOCK + dto.getMerchantId(), "Y");
        }
    }

}
