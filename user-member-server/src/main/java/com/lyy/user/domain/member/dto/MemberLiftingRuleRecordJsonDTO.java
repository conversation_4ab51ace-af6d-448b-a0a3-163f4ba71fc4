package com.lyy.user.domain.member.dto;

import com.google.gson.Gson;
import com.lyy.user.domain.member.entity.MemberLiftingRule;
import com.lyy.user.domain.member.entity.MemberLiftingRuleRecord;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 升级策略规则记录的json格式数据
 * <AUTHOR>
 * @className: MemberLiftingRuleRecordJsonDTO
 * @date 2021/4/12
 */
@Data
@Slf4j
public class MemberLiftingRuleRecordJsonDTO {
    private Long ruleId;
    private List<Long> recordIds;


    /**
     * 原始的记录map转换为list数据
     * @param memberLiftingRuleMap
     * @return
     */
    public static List<MemberLiftingRuleRecordJsonDTO> exchangeToList(Map<MemberLiftingRule, Collection<MemberLiftingRuleRecord>> memberLiftingRuleMap) {
        if (memberLiftingRuleMap == null || memberLiftingRuleMap.isEmpty()) {
            return Collections.emptyList();
        }
        return memberLiftingRuleMap.entrySet().stream()
                .map(entry -> {
                    MemberLiftingRuleRecordJsonDTO memberLiftingRuleRecordJsonDTO = new MemberLiftingRuleRecordJsonDTO();
                    memberLiftingRuleRecordJsonDTO.setRuleId(entry.getKey().getId());
                    List<Long> memberLiftingRuleRecordIdList = entry.getValue().stream()
                            .map(MemberLiftingRuleRecord::getId)
                            .collect(Collectors.toList());
                    memberLiftingRuleRecordJsonDTO.setRecordIds(memberLiftingRuleRecordIdList);
                    return memberLiftingRuleRecordJsonDTO;
                })
                .collect(Collectors.toList());
    }
    public static String toMapString(List<MemberLiftingRuleRecordJsonDTO> list){
        return toMapString(list,255);
    }

    /**
     * 转换为map字符串格式
     * @param list
     * @param limitLength
     * @return
     */
    public static String toMapString(List<MemberLiftingRuleRecordJsonDTO> list,int limitLength){
        Map<Long,List<Long>> map = new TreeMap<>();
        list.forEach(json->map.put(json.getRuleId(),json.getRecordIds()));
        String str = new Gson().toJson(map);
        if(limitLength > 0 && str.length() > limitLength){
            if(log.isDebugEnabled()){
                log.debug("toMapString 内容需要截取长度为 {},原始内容为 {}",limitLength,str);
            }
            return str.substring(0,limitLength - 1);
        }
        return str;
    }
}
