package com.lyy.user.domain.account.service;

import static java.util.Optional.ofNullable;

import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitIncrementItemDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MerchantBenefitIncrementDTO;
import com.lyy.user.account.infrastructure.constant.AccountBenefitNumTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountBenefitStatusEnum;
import com.lyy.user.account.infrastructure.constant.AccountRecordOperationTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountStatusEnum;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.domain.account.dto.AccountInitDTO;
import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.domain.account.entity.AccountBenefit;
import com.lyy.user.domain.account.entity.AccountRecord;
import com.lyy.user.domain.statistics.entity.SmallVenueStoredStatistics;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.repository.account.SmallVenueAccountRepository;
import com.lyy.user.infrastructure.repository.user.MerchantUserRepository;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @ClassName: AsyncVenuePayoutBenefitHandler
 * @description: TODO
 * @author: pengkun
 * @date: 2022/06/15
 **/
@Slf4j
@Component
public class AsyncVenuePayoutBenefitHandler {

    @Resource(name = "smallVenueAccountRepository")
    private SmallVenueAccountRepository accountRepository;

    @Resource
    private MerchantUserRepository merchantUserRepository;

    @Async("merchantPayoutBenefitTaskExecutor")
    public void handler(List<Long> userIds, MerchantBenefitIncrementDTO dto, CountDownLatch latch) {
        try {
            userIds.forEach(id -> doHandler(id, dto));
        } finally {
            latch.countDown();
        }
    }

    private void doHandler(Long userId, MerchantBenefitIncrementDTO dto) {
        try {
            //1.获取商户用户信息
            MerchantUser merchantUser = ofNullable(merchantUserRepository.getByUserIdAndMerchantId(userId, dto.getMerchantId()))
                    .orElseThrow(() -> new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR));
            //2.获取用户的默认账户
            Account account = getUserAccount(userId, merchantUser.getId(), dto);
            if (account == null) {
                log.warn("获取用户默认账户失败,userId:{},merchantUserId:{},merchantId:{}", userId, merchantUser.getId(), dto.getMerchantId());
                return;
            } else {
                if (!AccountStatusEnum.NORMAL.getStatus().equals(account.getStatus())) {
                    log.warn("商户权益赠送,默认的账号状态异常,{},{},账号状态:{}", dto.getMerchantId(), userId, account.getStatus());
                    return;
                }
            }
            Date now = new Date();
            //3.获取默认账户权益明细
            List<Long> classifyIds = dto.getIncrementItemDTO().getBenefitDetails().stream().map(BenefitIncrementItemDTO.BenefitDetailsDTO::getMerchantBenefitClassifyId).collect(Collectors.toList());
            List<AccountBenefit> accountBenefits = accountRepository.selectBenefit(dto.getMerchantId(), userId, account.getId(), classifyIds);
            for (BenefitIncrementItemDTO.BenefitDetailsDTO detail : dto.getIncrementItemDTO().getBenefitDetails()) {
                AccountBenefit accountBenefit = null;
                //如果是无限期
                if (couldAggregateBenefit(detail.getExpiryDateCategory(), detail.getNumType(), detail.getUpTime())) {
                    //找出对应的需要处理的明细id
                    List<AccountBenefit> accountBenefitList;
                    if (detail.getUseRuleId() == null) {
                        accountBenefitList = accountBenefits.stream()
                                .filter(d -> couldAggregateBenefit(d.getExpiryDateCategory(), d.getValueType(), d.getUpTime()))
                                .filter(d -> d.getUseRuleId() == null)
                                .collect(Collectors.toList());
                    } else {
                        accountBenefitList = accountBenefits.stream()
                                .filter(d -> couldAggregateBenefit(d.getExpiryDateCategory(), d.getValueType(), d.getUpTime()))
                                .filter(d -> d.getUseRuleId().equals(detail.getUseRuleId()))
                                .collect(Collectors.toList());
                    }
                    if (!CollectionUtils.isEmpty(accountBenefitList)) {
                        accountBenefit = accountBenefitList.get(0);
                    }
                }
                AccountRecord accountRecord = new AccountRecord();
                if (accountBenefit == null) {
                    accountBenefit = getAccountBenefit(userId, dto, detail, accountRecord, account, now, merchantUser);
                    accountRepository.saveAccountBenefit(accountBenefit);
                } else {
                    accountRecord.setInitialBenefit(accountBenefit.getBalance());
                    //更新权益明细
                    accountRepository.updateAccountBenefit(accountBenefit, detail.getNum(), detail.getNum(),
                            ofNullable(dto.getOperatorId()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
                }
                //4.获取对应类型的权益统计
                List<SmallVenueStoredStatistics> statistics = accountRepository.selectStatistics(dto.getMerchantId(), userId, Collections.singletonList(detail.getMerchantBenefitClassifyId()));

                boolean isFrequency = Objects.equals(detail.getNumType(), AccountBenefitNumTypeEnum.FREQUENCY.getCode());
                SmallVenueStoredStatistics smallVenueStoredStatistics;
                //5.统计数据处理
                if (CollectionUtils.isEmpty(statistics)) {
                    smallVenueStoredStatistics = getSmallVenueStoredStatistics(userId, dto, detail, merchantUser, now, isFrequency, accountRecord);
                    accountRepository.saveAccountStatistics(smallVenueStoredStatistics);

                    accountRecord.setAccountInitialBalance(BigDecimal.ZERO);
                } else {
                    smallVenueStoredStatistics = statistics.get(0);
                    accountRecord.setAccountInitialBalance(smallVenueStoredStatistics.getBalance());
                    if (isFrequency) {
                        accountRecord.setAccountInitialNum(BigDecimal.valueOf(smallVenueStoredStatistics.getRemainNum()));
                    }
                    accountRepository.updateAccountStatistics(dto.getMerchantId(), userId, detail.getMerchantBenefitClassifyId(), detail.getNum(),
                            isFrequency, now);
                }
                //6.账户流水保存
                setAccountRecord(userId, dto, detail, accountRecord, account, accountBenefit, merchantUser, now);
                accountRepository.saveAccountRecord(accountRecord);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private static void setAccountRecord(Long userId, MerchantBenefitIncrementDTO dto, BenefitIncrementItemDTO.BenefitDetailsDTO detail, AccountRecord accountRecord, Account account, AccountBenefit accountBenefit, MerchantUser merchantUser, Date now) {
        accountRecord.setCardNo(account.getCardNo());
        accountRecord.setStoreId(dto.getStoreId());
        accountRecord.setAccountId(account.getId());
        accountRecord.setAccountBenefitId(accountBenefit.getId());
        accountRecord.setMerchantId(dto.getMerchantId());
        accountRecord.setUserId(userId);
        accountRecord.setMerchantUserId(merchantUser.getId());
        accountRecord.setMerchantBenefitClassifyId(detail.getMerchantBenefitClassifyId());
        accountRecord.setMerchantBenefitClassifyName(detail.getMerchantBenefitClassifyName());
        accountRecord.setBenefitClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        accountRecord.setRecordType(ofNullable(dto.getRecord().getRecordType()).orElse(AccountRecordTypeEnum.SV_PRESENTATION.getCode()));
        // operationType  相当于一个大分类，recordType 属于 operationType 的子分类
        accountRecord.setOperationType(AccountRecordOperationTypeEnum.BENEFIT_MODIFY.getCode());
        accountRecord.setOperationChannel(dto.getRecord().getOperationChannel());
        accountRecord.setMode(AdjustTypeEnum.INCREMENT.getType());
        accountRecord.setBenefitId(0L);
        accountRecord.setOriginalBenefit(detail.getNum());
        accountRecord.setActualBenefit(detail.getNum());
        accountRecord.setSubOrderNo(detail.getSubOrderNo());
        accountRecord.setActualValue(detail.getActualValue());
        accountRecord.setDescription(detail.getDescription());
        accountRecord.setCreateName(dto.getRecord().getOperatorName());
        accountRecord.setStoreName(dto.getRecord().getStoreName());
        accountRecord.setCreateTime(now);
        accountRecord.setCreatedby(ofNullable(dto.getOperatorId()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        accountRecord.setCreateName(dto.getOperatorName());
    }

    private static SmallVenueStoredStatistics getSmallVenueStoredStatistics(Long userId, MerchantBenefitIncrementDTO dto, BenefitIncrementItemDTO.BenefitDetailsDTO detail, MerchantUser merchantUser, Date now, boolean isFrequency, AccountRecord accountRecord) {
        SmallVenueStoredStatistics smallVenueStoredStatistics;
        smallVenueStoredStatistics = new SmallVenueStoredStatistics();
        smallVenueStoredStatistics.setUserId(userId);
        smallVenueStoredStatistics.setMerchantId(dto.getMerchantId());
        smallVenueStoredStatistics.setMerchantUserId(merchantUser.getId());
        smallVenueStoredStatistics.setMerchantBenefitClassifyId(detail.getMerchantBenefitClassifyId());
        smallVenueStoredStatistics.setCreated(now);
        smallVenueStoredStatistics.setUpdated(now);
        smallVenueStoredStatistics.setCreatedby(ofNullable(dto.getOperatorId()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        smallVenueStoredStatistics.setUpdatedby(ofNullable(dto.getOperatorId()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        smallVenueStoredStatistics.setTotal(detail.getNum());
        smallVenueStoredStatistics.setBalance(detail.getNum());
        if (isFrequency) {
            smallVenueStoredStatistics.setRemainNum(1);
            smallVenueStoredStatistics.setTotalNum(1);
            accountRecord.setAccountInitialNum(BigDecimal.ZERO);
        }
        return smallVenueStoredStatistics;
    }

    private static AccountBenefit getAccountBenefit(Long userId, MerchantBenefitIncrementDTO dto, BenefitIncrementItemDTO.BenefitDetailsDTO detail, AccountRecord accountRecord, Account account, Date now, MerchantUser merchantUser) {
        AccountBenefit accountBenefit;
        accountRecord.setInitialBenefit(BigDecimal.ZERO);
        //初始化权益明细
        accountBenefit = new AccountBenefit();
        accountBenefit.setAccountId(account.getId());
        accountBenefit.setUserId(userId);
        accountBenefit.setBenefitId(0L);
        accountBenefit.setTotal(detail.getNum());
        accountBenefit.setBalance(detail.getNum());
        accountBenefit.setClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        accountBenefit.setStatus(AccountBenefitStatusEnum.NORMAL.getStatus());
        accountBenefit.setExpiryDateCategory(detail.getExpiryDateCategory());
        accountBenefit.setUpTime(detail.getUpTime());
        accountBenefit.setDownTime(detail.getDownTime());
        accountBenefit.setCreateTime(now);
        accountBenefit.setUpdateTime(now);
        accountBenefit.setCreateBy(ofNullable(dto.getOperatorId()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        accountBenefit.setUpdateBy(ofNullable(dto.getOperatorId()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        accountBenefit.setMerchantId(merchantUser.getMerchantId());
        accountBenefit.setMerchantUserId(merchantUser.getId());
        accountBenefit.setMerchantBenefitClassifyId(detail.getMerchantBenefitClassifyId());
        accountBenefit.setUseRuleId(detail.getUseRuleId());
        accountBenefit.setStoreId(dto.getStoreId());
        accountBenefit.setValueType(detail.getNumType());
        return accountBenefit;
    }

    /**
     * 获取用户账户
     *
     * @param userId        用户id
     * @param merchantUseId 商户用户id
     * @param dto
     * @return
     */
    private Account getUserAccount(Long userId, Long merchantUseId, MerchantBenefitIncrementDTO dto) {
        Account account = accountRepository.getDefaultAccount(dto.getMerchantId(), userId);
        if (account == null) {
            //初始化账户
            AccountInitDTO accountInitDTO = assembleAccount(userId, dto.getMerchantId(), dto.getIncrementItemDTO().getBenefitDetails().get(0).getDescription(),
                    ofNullable(dto.getOperatorId()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID),
                    dto.getStoreId(), merchantUseId, dto.getIncrementItemDTO());
            account = accountRepository.initAccount(accountInitDTO).getAccount();
        }
        return account;
    }


    private AccountInitDTO assembleAccount(Long userId, Long merchantId, String description, Long operatorId,
                                           Long storeId, Long merchantUserId, BenefitIncrementItemDTO dto) {
        AccountInitDTO initDTO = new AccountInitDTO();
        initDTO.setUserId(userId);
        initDTO.setMerchantId(merchantId);
        initDTO.setClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        initDTO.setDescription(description);
        initDTO.setOperatorId(operatorId);
        initDTO.setCardNo(dto.getCardNo());
        initDTO.setStoreId(storeId);
        initDTO.setMerchantUserId(merchantUserId);
        initDTO.setDeposit(dto.getDeposit());
        initDTO.setDefaultFlag(true);
        initDTO.setTotal(BigDecimal.ZERO);
        initDTO.setBalance(BigDecimal.ZERO);
        return initDTO;
    }

    private boolean couldAggregateBenefit(Integer category, Integer valueType, String uptime) {
        return Objects.equals(category, ExpiryDateCategoryEnum.NO_LIMIT.getValue())
                && Objects.equals(valueType, AccountBenefitNumTypeEnum.AMOUNT.getCode())
                && StringUtils.isBlank(uptime);
    }

}
