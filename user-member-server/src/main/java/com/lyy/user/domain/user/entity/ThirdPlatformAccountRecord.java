package com.lyy.user.domain.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 第三方资金变动表
 *
 * <AUTHOR>
 * @since 2023/4/23
 */
@ToString
@Getter
@Setter
@TableName(value = "um_third_platform_account_record")
public class ThirdPlatformAccountRecord {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 设备id
     */
    private Long equipmentId;

    /**
     * 设备value
     */
    private String equipmentValue;

    /**
     * 订单号
     */
    private String outTradeNo;

    /**
     * IC卡卡号
     */
    private String cardNo;

    /**
     * 外部用户id
     */
    private String externalUserId;

    /**
     * 外部系统标识： 1.东晙
     * @see com.lyy.user.app.infrastructure.constant.ExternalSystemEnum
     */
    private Integer externalSystem;

    /**
     * 事件类型 1：刷卡，2.扫码
     */
    private Short eventType;

    /**
     * 操作类型 1:增加；2：减少
     */
    private Short mode;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private Long updateBy;

}