package com.lyy.user.domain.user.service.handler;

import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.app.interfaces.facade.dto.UserVO;
import com.lyy.user.domain.user.service.AbstractUserInitService;
import com.lyy.user.domain.user.service.UserInitHandler;
import com.lyy.user.infrastructure.util.DateTimeUtils;
import com.lyy.user.interfaces.assembler.UserAssembler;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName: AliWebUserInitHandler
 * @description: 支付宝网页用户初始化
 * @author: pengkun
 * @date: 2021/04/06
 **/
@Slf4j
@Service("aliWebUserInit")
public class AliWebUserInitHandler extends AbstractUserInitService implements UserInitHandler {

    /**
     * 用户初始化处理方法
     * @param dto
     * @return
     */
    @Override
    public UserInfoDTO handle(UserCreateDTO dto) {
        log.debug("支付宝网页用户初始化");
        String telephone = "";
        String isActive = null;
        UserVO openUser = super.getByOpenId(dto.getOpenid());
        if(Objects.isNull(openUser)){
            //初始化平台用户信息
            Long userId = super.initPlatformUser(dto);
            openUser = new UserVO();
            openUser.setId(userId);
            openUser.setGender(dto.getGender());
        }else {
            telephone = openUser.getTelephone();
            isActive = openUser.getIsActive();
        }
        Long userId = openUser.getId();

        LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
        LocalDateTime updateTime = openUser.getUpdated() == null ? now : LocalDateTime.ofInstant(openUser.getUpdated().toInstant(), ZoneId.systemDefault());
        long days = DateTimeUtils.betweenTwoTime(updateTime, now, ChronoUnit.DAYS);
        if(Objects.isNull(openUser.getGender()) || days > BETWEEN_DAY){
            //更新平台用户信息
            UserVO updateDTO = UserAssembler.INSTANCE.toUserVO(dto);
            updateDTO.setId(userId);
            super.updatePlatformUser(updateDTO);
        }

        return getUserInfoDTO(dto, userId, telephone, isActive);
    }

}
