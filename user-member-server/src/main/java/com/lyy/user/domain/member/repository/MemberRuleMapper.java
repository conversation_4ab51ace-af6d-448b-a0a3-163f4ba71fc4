package com.lyy.user.domain.member.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lyy.user.domain.member.entity.MemberRule;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 会员规则 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
public interface MemberRuleMapper extends BaseMapper<MemberRule> {

    /**
     * 根据当前等级，获取所有已经获得的会员规则权益
     * @param merchantId
     * @param memberLevelId
     * @return
     */
    List<MemberRule> findAllByMemberLevel(@Param("merchantId") Long merchantId,@Param("memberLevelId") Long memberLevelId);
}
