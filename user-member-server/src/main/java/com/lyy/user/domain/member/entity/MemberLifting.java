package com.lyy.user.domain.member.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 会员升级策略
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("um_member_lifting")
public class MemberLifting implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 商户ID
     */
    @TableField("merchant_id")
    private Long merchantId;

    /**
     * 会员组ID
     */
    @TableField("member_group_id")
    private Long memberGroupId;

    /**
     * 升降策略(1 升级策略,2 降级策略)
     * @see com.lyy.user.infrastructure.constants.MemberLiftingStrategyEnum
     */
    @TableField("lifting")
    private Short lifting;

    /**
     * 成长值
     */
    @TableField("grow_value")
    private Long growValue;

    /**
     * 条件(0 满足其中一条,1 满足所有条件)
     * @see com.lyy.user.account.infrastructure.constant.MemberLiftingConditionEnum
     */
    @TableField("condition")
    private Short condition;

    /**
     * 有效次数，若为-1则生效无限次，其他正数为生效的次数
     */
    @TableField("effective_number")
    private Short effectiveNumber;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建者
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新者
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 升/降级策略名称
     */
    @TableField("name")
    private String name;

    /**
     * 是否有效 true 有效,false 无效
     */
    @TableField("active")
    private Boolean active;

}
