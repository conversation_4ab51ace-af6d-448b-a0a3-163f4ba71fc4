package com.lyy.user.domain.user.service.handler;

import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneQueryParam;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneRecordDTO;
import com.lyy.user.app.interfaces.facade.dto.UserVO;
import com.lyy.user.application.user.IPlatformUserPhoneService;
import com.lyy.user.domain.user.service.AbstractUserInitService;
import com.lyy.user.domain.user.service.UserInitHandler;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.util.PasswordUtil;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName: OtherUserInitHandler
 * @description: 其他平台用户初始化
 * @author: pengkun
 * @date: 2021/04/06
 **/
@Slf4j
@Service("otherUserInit")
public class OtherUserInitHandler extends AbstractUserInitService implements UserInitHandler {

    @Autowired
    private IPlatformUserPhoneService platformUserPhoneService;

    /**
     * 用户初始化处理方法
     * @param dto
     * @return
     */
    @Override
    public UserInfoDTO handle(UserCreateDTO dto) {
        log.debug("其他方式用户初始化");

        //根据手机号获取平台用户信息
        String telephone = "";
        String isActive = null;
        PlatformUserPhoneQueryParam queryParam = new PlatformUserPhoneQueryParam();
        queryParam.setTelephone(dto.getTelephone());
        queryParam.setMerchantId(UserMemberSysConstants.PLATFORM_MERCHANT_ID);
        List<PlatformUserPhoneRecordDTO> list = platformUserPhoneService.list(queryParam);
        Long userId;
        if(list != null && list.size() > 0){
            if(list.size() == 1){
                PlatformUserPhoneRecordDTO platformUserPhoneRecordDTO = list.get(0);
                telephone = platformUserPhoneRecordDTO.getTelephone();
                userId = platformUserPhoneRecordDTO.getUserId();
                UserVO userDTO = getPlatformUserByUserId(userId);
                isActive = userDTO.getIsActive();
            }else {
                log.error("手机号:{},绑定了多个用户",dto.getTelephone());
                throw new BusinessException(UserErrorCode.TELEPHONE_BIND_MULTI_PLATFORM_USER_ERROR.getCode(),UserErrorCode.TELEPHONE_BIND_MULTI_PLATFORM_USER_ERROR.getMessage());
            }
        }else {
            if(StringUtils.isBlank(dto.getNickname())){
                dto.setNickname("匿名用户");
            }
            if(StringUtils.isBlank(dto.getPassword())){
                dto.setPassword(PasswordUtil.generateEncryptPassword());
            }
            userId = initPlatformUser(dto);
        }

        return getUserInfoDTO(dto, userId, telephone, isActive);
    }
}
