package com.lyy.user.domain.account.service;

import static com.lyy.user.account.infrastructure.constant.AdjustTypeEnum.EMPTY;
import static com.lyy.user.account.infrastructure.constant.AdjustTypeEnum.INCREMENT;
import static java.math.BigDecimal.ZERO;
import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.lyy.base.communal.constant.TradeTypeEnum;
import cn.lyy.base.utils.RedisClient;
import cn.lyy.base.utils.SnowflakeIdWorker;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.lyy.error.member.infrastructure.AccountErrorCode;
import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.klock.annotation.Klock;
import com.lyy.lock.redis.RedisLock;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitCountDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitCreateDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDataDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDecreaseReqDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitModifyDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitResultDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitScopeDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitScopeQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountClassifyBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountClassifyBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountConditionDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountConsumption;
import com.lyy.user.account.infrastructure.account.dto.AccountCreateDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordCountDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordSaveDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountStatusBatchDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountStatusDTO;
import com.lyy.user.account.infrastructure.account.dto.AddSupplementaryCardDTO;
import com.lyy.user.account.infrastructure.account.dto.BenefitRollbackDTO;
import com.lyy.user.account.infrastructure.account.dto.BenefitWithScopeDTO;
import com.lyy.user.account.infrastructure.account.dto.ConsumeDTO;
import com.lyy.user.account.infrastructure.account.dto.ConsumeDetailDTO;
import com.lyy.user.account.infrastructure.account.dto.PayRefundBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.PayRefundBenefitDetailDTO;
import com.lyy.user.account.infrastructure.account.dto.RecordBenefitScopeDTO;
import com.lyy.user.account.infrastructure.account.dto.RecordBenefitScopeQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.SmallVenueAccountInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.SmallVenueAllCardDTO;
import com.lyy.user.account.infrastructure.account.dto.SupplementaryCardCheckDTO;
import com.lyy.user.account.infrastructure.account.dto.SupplyAnonymousCardDataDTO;
import com.lyy.user.account.infrastructure.account.dto.request.OrderBenefitRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.request.StoreBenefitClearDTO;
import com.lyy.user.account.infrastructure.account.dto.response.OrderAccountBenefit;
import com.lyy.user.account.infrastructure.account.dto.response.OrderBenefitConsume;
import com.lyy.user.account.infrastructure.account.dto.response.OrderBenefitCountDTO;
import com.lyy.user.account.infrastructure.account.dto.response.OrderBenefitInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.response.OrderBenefitRecord;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.AccountSupplementaryCardQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.AvailableBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.HasSupplementaryCardDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountBenefitInfo;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountDetailsDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountListDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalCardCountInfo;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalRecordSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.ReissueCardDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueAccountRecordSaveDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueRecordListDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueRecordSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.UpdateCardStatusDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.request.SmallVenueAccountRecordSaveBatchDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitConsumeDetailInfoDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitConsumeInfoDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitInfoDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitPreDeductionDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitScopeSaveDTO;
import com.lyy.user.account.infrastructure.common.DataList;
import com.lyy.user.account.infrastructure.constant.AccountBenefitNumTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountBenefitStatusEnum;
import com.lyy.user.account.infrastructure.constant.AccountRecordOperationTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeGroupEnum;
import com.lyy.user.account.infrastructure.constant.AccountStatusEnum;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.ApplicableEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyGroupEnum;
import com.lyy.user.account.infrastructure.constant.CardValidTypeEnum;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import com.lyy.user.account.infrastructure.constant.MemberGrowRecordModeEnum;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsUpdateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.app.interfaces.facade.dto.request.UserMerchantRelationDTO;
import com.lyy.user.app.interfaces.facade.rpc.IUserMerchantRelationRpc;
import com.lyy.user.application.account.AccountBenefitService;
import com.lyy.user.application.account.AccountService;
import com.lyy.user.application.benefit.BenefitConsumeRuleService;
import com.lyy.user.application.benefit.BenefitService;
import com.lyy.user.application.statistics.StatisticsService;
import com.lyy.user.application.user.IUserService;
import com.lyy.user.domain.account.dto.AccountBenefitConsumeDTO;
import com.lyy.user.domain.account.dto.AccountBenefitScopeDO;
import com.lyy.user.domain.account.dto.AccountBenefitWithScopeDTO;
import com.lyy.user.domain.account.dto.AccountInitDTO;
import com.lyy.user.domain.account.dto.AccountInitResultDTO;
import com.lyy.user.domain.account.dto.BenefitRollbackDelayedMessage;
import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.domain.account.entity.AccountBenefit;
import com.lyy.user.domain.account.entity.AccountRecord;
import com.lyy.user.domain.account.repository.AccountBenefitMapper;
import com.lyy.user.domain.account.repository.AccountMapper;
import com.lyy.user.domain.account.repository.AccountRecordMapper;
import com.lyy.user.domain.benefit.entity.BenefitConsumeRule;
import com.lyy.user.domain.benefit.entity.BenefitScope;
import com.lyy.user.domain.benefit.repository.BenefitScopeMapper;
import com.lyy.user.domain.statistics.entity.SmallVenueStoredStatistics;
import com.lyy.user.domain.statistics.repository.SmallVenueStoredStatisticsMapper;
import com.lyy.user.domain.user.dto.TagUserParamDTO;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.domain.user.repository.MerchantUserMapper;
import com.lyy.user.domain.user.service.MerchantAutoTagAsyncHandler;
import com.lyy.user.infrastructure.constants.RedisKey;
import com.lyy.user.infrastructure.constants.TradeTypeGroupEnum;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.listener.BenefitRollbackDelayedProducer;
import com.lyy.user.infrastructure.repository.account.AccountBenefitRepository;
import com.lyy.user.infrastructure.repository.account.AccountBenefitRepository.Query;
import com.lyy.user.infrastructure.repository.account.AccountRecordRepository;
import com.lyy.user.infrastructure.repository.account.AccountRepository;
import com.lyy.user.infrastructure.repository.account.SmallVenueAccountRepository;
import com.lyy.user.infrastructure.repository.benefit.BenefitRepository;
import com.lyy.user.infrastructure.repository.user.MerchantUserRepository;
import com.lyy.user.infrastructure.util.CommonConverterTools;
import com.lyy.user.infrastructure.util.LyyStringUtil;
import com.lyy.user.infrastructure.util.WxAuthUpdateUtils;
import com.lyy.user.interfaces.assembler.AccountBenefitAssembler;
import com.lyy.user.interfaces.assembler.AccountRecordAssembler;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

/**
 * 类描述：账户领域对象
 * <p>
 *
 * <AUTHOR>
 * @since 2021/3/31 09:20
 */
@Slf4j
@Service
public class AccountServiceImpl implements AccountService {

    // 所有余额余币类型
    private static final List<Integer> ALL_COINS_AND_MONEY_CLASSIFY = Collections.unmodifiableList(
            Stream.of(BenefitClassifyGroupEnum.COINS, BenefitClassifyGroupEnum.MONEY)
                    .map(BenefitClassifyGroupEnum::getClassify)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList()));

    @Resource
    private MerchantUserMapper merchantUserMapper;
    @Resource
    private AccountMapper accountMapper;

    @Resource
    private AccountBenefitMapper accountBenefitMapper;

    @Resource
    private AccountRecordMapper accountRecordMapper;

    @Resource
    private BenefitScopeMapper benefitScopeMapper;

    @Autowired
    private MerchantAutoTagAsyncHandler merchantAutoTagAsyncHandler;

    @Resource
    private BenefitService benefitService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private RedisLock redisLock;

    @Autowired
    private BenefitRollbackDelayedProducer rollbackDelayedProducer;

    @Resource
    private SnowflakeIdWorker snowflakeIdWorker;

    @Resource
    private IUserService userService;

    @Autowired
    private StatisticsService statisticsService;

    @Resource(name = "smallVenueAccountRepository")
    private SmallVenueAccountRepository smallVenueAccountRepository;

    @Autowired
    private BenefitConsumeRuleService benefitConsumeRuleService;

    @Resource
    private AccountRepository accountRepository;
    @Resource
    private AccountRecordRepository accountRecordRepository;
    @Resource
    private AccountBenefitRepository accountBenefitRepository;
    @Resource
    private MerchantUserRepository merchantUserRepository;

    @Resource
    private AccountBenefitAssembler assembler;

    @Autowired
    private SmallVenueStoredStatisticsMapper smallVenueStoredStatisticsMapper;

    @Autowired
    private AccountBenefitService accountBenefitService;
    @Autowired
    private IUserMerchantRelationRpc userMerchantRelationRpc;

    @Autowired
    private BenefitRepository benefitRepository;

    @Autowired
    private AccountRecordAssembler accountRecordAssembler;


    @Value("${benefit.delay.time:1}")
    private Integer delay;

    final String timePattern = "yyyy-MM-dd HH:mm:ss";


    /**
     * 允许扣成负数的品类
     */
    private static List<Long> allowNegativeEquipmentTypeIds;

    @Value("#{'${benefit.consume.allow.list:1001096}'.split(',')}")
    public void setLimitPower(List<Long> equipmentTypeIds) {
        allowNegativeEquipmentTypeIds = equipmentTypeIds;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void increaseAccountBenefit(List<AccountCreateDTO> accountList) {
        if (CollectionUtils.isEmpty(accountList)) {
            log.warn("[创建账户] 入参为空集合");
            return;
        }
        // 根据用户-商户加锁，重试时间3000ms，锁超时5000ms
        String lockKey = RedisKey.ACCOUNT_BENEFIT_UPDATE_LOCK
                + accountList.get(0).getMerchantId()
                + ":" + accountList.get(0).getUserId()
                + ":" + accountList.get(0).getClassify();
        if (redisLock.lock(lockKey, UserMemberSysConstants.YES, 5000, 3000) == null) {
            log.error("获取锁失败 -> {}", lockKey);
            throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_IS_UPDATING);
        }
        try {
            accountList.forEach(account -> {
                BigDecimal total = account.getAccountBenefit().stream()
                        .map(AccountBenefitCreateDTO::getTotal).reduce(BigDecimal::add)
                        .orElse(BigDecimal.ZERO);
                BigDecimal initialBenefit = BigDecimal.ZERO;
                if(account.getMerchantUserId() == null && account.getUserId() == null){
                    throw new BusinessException(AccountErrorCode.ACCOUNT_QUERY_PARAM_ERROR);
                }
                AccountInitDTO initDTO = new AccountInitDTO();
                initDTO.setMerchantId(account.getMerchantId());
                initDTO.setOperatorId(ofNullable(account.getOperator()).orElse(account.getUserId()));
                if (account.getMerchantUserId() != null) {
                    initDTO.setMerchantUserId(account.getMerchantUserId());
                    if (account.getUserId() == null) {
                        // 查询用户id
                        MerchantUser merchantUser = merchantUserRepository.getByIdAndMerchantId(account.getMerchantUserId(), account.getMerchantId());
                        Assert.notNull(merchantUser, "商户用户ID异常");
                        initDTO.setUserId(merchantUser.getUserId());
                    } else {
                        initDTO.setUserId(account.getUserId());
                    }
                } else {
                    initDTO.setUserId(account.getUserId());
                    // 查询商户用户用户id
                    MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(account.getUserId(), account.getMerchantId());
                    Assert.notNull(merchantUser, "平台用户ID异常");
                    initDTO.setMerchantUserId(merchantUser.getId());
                }
                initDTO.setTotal(total);
                initDTO.setBalance(total);
                AccountInitResultDTO accountInitResultDTO = smallVenueAccountRepository.initAccount(initDTO);
                Account accountUpdate = accountInitResultDTO.getAccount();
                if (!accountInitResultDTO.getIsCreate()) {
                    //账户不是新建的
                    initialBenefit = accountUpdate.getBalance();
                    accountMapper.increaseBalanceAndTotal(accountUpdate.getId(), accountUpdate.getMerchantId(), account.getOperator(), total, total);
                }
                //统计数据处理
                updateUserStatistics(account.getUserId(), account.getMerchantUserId(), account.getMerchantId(),
                        account.getClassify(), total, total);

                Date now = new Date();
                for (AccountBenefitCreateDTO benefit : account.getAccountBenefit()) {
                    AccountBenefit accountBenefit = accountBenefitMapper.selectOne(new QueryWrapper<AccountBenefit>()
                            .eq("account_id", accountUpdate.getId())
                            .eq("merchant_id", account.getMerchantId())
                            .eq("benefit_id", benefit.getBenefitId()));
                    if (accountBenefit == null) {
                        accountBenefit = CommonConverterTools.convert(AccountBenefit.class, benefit);
                        if (accountBenefit.getClassify() == null) {
                            accountBenefit.setClassify(account.getClassify());
                        }
                        accountBenefit.setAccountId(accountUpdate.getId());
                        accountBenefit.setBenefitId(benefit.getBenefitId());
                        accountBenefit.setBalance(benefit.getTotal());
                        accountBenefit.setTotal(benefit.getTotal());
                        accountBenefit.setUserId(account.getUserId());
                        accountBenefit.setCreateBy(account.getOperator());
                        accountBenefit.setUpdateBy(account.getOperator());
                        accountBenefit.setCreateTime(now);
                        accountBenefit.setUpdateTime(now);
                        accountBenefit.setStatus(AccountBenefitStatusEnum.NORMAL.getStatus());
                        accountBenefit.setResource(account.getBenefitResourceType());
                        accountBenefit.setResourceId(account.getBenefitResourceId());
                        accountBenefit.setMerchantId(account.getMerchantId());
                        accountBenefit.setMerchantUserId(account.getMerchantUserId());
                        accountBenefitMapper.insert(accountBenefit);
                    } else {
                        accountBenefitMapper.increaseBalanceAndTotal(accountBenefit.getId(), accountBenefit.getMerchantId(), account.getOperator(), benefit.getTotal(), benefit.getTotal());
                    }
                    // 保存变更流水
                    AccountRecord accountRecord = getAccountRecord(account, benefit, accountUpdate, initialBenefit, now, accountBenefit);
                    //自动打支付类型标签
                    TagUserParamDTO tagUserParamDTO = new TagUserParamDTO();
                    tagUserParamDTO.setTradeType(accountRecord.getTradeType());
                    tagUserParamDTO.setUserType(null);
                    tagUserParamDTO.setUserId(account.getUserId());
                    tagUserParamDTO.setStoreName(accountRecord.getStoreName());
                    tagUserParamDTO.setMerchantUserId(account.getMerchantUserId());
                    tagUserParamDTO.setMerchantId(account.getMerchantId());
                    merchantAutoTagAsyncHandler.autoCreateTag(tagUserParamDTO);
                    accountRecordMapper.insert(accountRecord);
                }
            });
        } finally {
            redisLock.unlock(lockKey, "Y");
        }
    }

    private static AccountRecord getAccountRecord(AccountCreateDTO account, AccountBenefitCreateDTO benefit, Account accountUpdate, BigDecimal initialBenefit, Date now, AccountBenefit accountBenefit) {
        AccountRecord accountRecord = new AccountRecord();
        accountRecord.setAccountId(accountUpdate.getId());
        accountRecord.setUserId(account.getUserId());
        accountRecord.setMerchantUserId(account.getMerchantUserId());
        accountRecord.setMerchantId(account.getMerchantId());
        accountRecord.setStoreId(account.getStoreId());
        accountRecord.setEquipmentId(account.getEquipmentId());
        accountRecord.setBenefitClassify(account.getClassify());
        accountRecord.setBenefitId(benefit.getBenefitId());
        accountRecord.setInitialBenefit(initialBenefit);
        accountRecord.setOriginalBenefit(benefit.getTotal());
        accountRecord.setActualBenefit(benefit.getTotal());
        accountRecord.setOutTradeNo(account.getOutTradeNo());
        accountRecord.setOrderNo(account.getOrderNo());
        accountRecord.setMode(INCREMENT.getType());
        accountRecord.setCreateTime(now);
        accountRecord.setResource(account.getResource());
        ofNullable(account.getAccountRecordTypeEnum()).ifPresent(r -> {
            accountRecord.setRecordType(r.getCode());
        });
        accountRecord.setDescription(account.getDescription());
        accountRecord.setRecordType(ofNullable(accountRecord.getRecordType()).orElse(AccountRecordTypeEnum.OTHER_All.getCode()));
        accountRecord.setAccountBenefitId(accountBenefit.getId());
        if (!StringUtils.isEmpty(accountRecord.getStoreName())) {
            // 只取32位，避免过长保存报错
            accountRecord.setStoreName(LyyStringUtil.substring(accountRecord.getStoreName(), 32));
        }
        return accountRecord;
    }

    @Override
    public List<AccountDTO> getByUserAndMerchant(AccountConditionDTO condition) {
        Long merchantId = condition.getMerchantId();
        Long userId = condition.getUserId();
        if (condition.getMerchantUserId() == null) {
            MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(userId, merchantId);
            if (merchantUser == null) {
                log.warn("账号信息获取商户用户失败,userId:{},merchantId:{}", userId, merchantId);
                throw new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR);
            }
            condition.setMerchantUserId(merchantUser.getId());
        }
        List<Account> accounts = accountMapper.selectList(getAccountQueryWrapper(condition));

        if (accounts.isEmpty()) {
            return new ArrayList<>();
        }
        if (condition.isExcludeExpired()) {
            Map<Integer, BigDecimal> expiredBenefitMap = getExpiredBenefitMap(condition);
            log.debug("过期权益信息,expiredBenefitMap:{}", expiredBenefitMap);
            accounts.forEach(a -> {
                //扣减过期权益
                BigDecimal expired = ofNullable(a.getClassify()).map(expiredBenefitMap::get).orElse(ZERO);
                a.setBalance(a.getBalance().subtract(expired));
            });
        }
        return CommonConverterTools.convert(AccountDTO.class, accounts);
    }

    private Map<Integer, BigDecimal> getExpiredBenefitMap(AccountConditionDTO condition) {
        AccountBenefitQueryDTO benefitQueryDTO = new AccountBenefitQueryDTO();
        benefitQueryDTO.setMerchantId(condition.getMerchantId());
        benefitQueryDTO.setUserId(condition.getUserId());
        benefitQueryDTO.setMerchantUserId(condition.getMerchantUserId());
        benefitQueryDTO.setClassify(condition.getClassifies());
        benefitQueryDTO.setExcludeClassify(condition.getExcludeClassify());
        //得到过期的权益，外面扣减掉
        return accountBenefitMapper.allBenefitRecord(benefitQueryDTO)
                .stream()
                .filter(accountBenefitDTO -> !isNotExpireBenefit(accountBenefitDTO))
                .collect(Collectors.groupingBy(AccountBenefitDTO::getClassify,
                        Collectors.reducing(ZERO, AccountBenefitDTO::getBalance, BigDecimal::add)));
    }

    /**
     * 是否不是过期的权益
     * @param accountBenefitDTO
     * @return  true:正常  false:过期
     */
    private boolean isNotExpireBenefit(AccountBenefitDTO accountBenefitDTO) {
        if (Objects.equals(accountBenefitDTO.getExpiryDateCategory(), ExpiryDateCategoryEnum.TIME_INTERVAL.getValue())) {
            LocalDateTime begin = LocalDateTime.parse(ofNullable(accountBenefitDTO.getUpTime()).orElse("1970-01-01 00:00:00"),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime end = LocalDateTime.parse(ofNullable(accountBenefitDTO.getDownTime()).orElse("9999-01-01 00:00:00"),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return begin.isBefore(LocalDateTime.now()) && end.isAfter(LocalDateTime.now());
        }
        if (Objects.equals(accountBenefitDTO.getExpiryDateCategory(), ExpiryDateCategoryEnum.ONE_DAY_TIME_INTERVAL.getValue())) {
            LocalTime begin = LocalTime.parse(ofNullable(accountBenefitDTO.getUpTime()).orElse("00:00:00"),
                    DateTimeFormatter.ofPattern("HH:mm:ss"));
            LocalTime end = LocalTime.parse(ofNullable(accountBenefitDTO.getDownTime()).orElse("23:59:59"),
                    DateTimeFormatter.ofPattern("HH:mm:ss"));
            return begin.isBefore(LocalTime.now()) && end.isAfter(LocalTime.now());
        }
        return true;
    }

    private static QueryWrapper<Account> getAccountQueryWrapper(AccountConditionDTO condition) {
        QueryWrapper<Account> wrapper = new QueryWrapper<>();
        wrapper.select("id", "total", "balance", "description", "classify")
                .eq("user_id", condition.getUserId())
                .eq("merchant_id", condition.getMerchantId())
                .eq("status", AccountStatusEnum.NORMAL.getStatus());
        ofNullable(condition.getMerchantUserId()).ifPresent(merchantUserId ->{
            wrapper.eq("merchant_user_id",merchantUserId);
        });

        // 是否根据类型查询
        ofNullable(condition.getClassify()).ifPresent(classify -> {
            wrapper.eq("classify", classify);
        });
        // 批量查询
        if (!CollectionUtils.isEmpty(condition.getClassifies())) {
            wrapper.in("classify", condition.getClassifies());
        }
        // 批量查询
        if (!CollectionUtils.isEmpty(condition.getExcludeClassify())) {
            wrapper.notIn("classify", condition.getExcludeClassify());
        }
        return wrapper;
    }

    @Override
    public void decreaseAccountBenefit(List<AccountBenefitAdjustDTO> adjust) {
        AccountServiceImpl proxy = (AccountServiceImpl) AopContext.currentProxy();
        proxy.decreaseAccountBenefit(adjust, true,null);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void decreaseAccountBenefit(List<AccountBenefitAdjustDTO> adjust, boolean checkBalance,Boolean allowNegative) {
        if (CollectionUtils.isEmpty(adjust)) {
            return;
        }
        // 根据用户-商户加锁，重试时间3000ms，锁超时5000ms
        String lockKey = RedisKey.ACCOUNT_BENEFIT_UPDATE_LOCK
                + adjust.get(0).getMerchantId()
                + ":" + adjust.get(0).getUserId()
                + ":" + adjust.get(0).getClassify();
        if (redisLock.lock(lockKey, UserMemberSysConstants.YES, 5000, 3000) == null) {
            log.error("获取锁失败 -> {}", lockKey);
            throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_IS_UPDATING);
        }
        try {
            Date now = new Date();
            adjust.forEach(adjustItem -> {
                if (BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON.getCode().equals(adjustItem.getClassify())) {
                    rollBackMerchantCoupon(adjustItem);
                    return;
                }
                LambdaQueryWrapper<AccountBenefit> lambdaQueryWrapper=  new QueryWrapper<AccountBenefit>().lambda()
                        .eq(AccountBenefit::getId,adjustItem.getId())
                        .eq(AccountBenefit::getMerchantUserId,adjustItem.getMerchantUserId())
                        .eq(AccountBenefit::getMerchantId,adjustItem.getMerchantId());
                AccountBenefit accountBenefit = accountBenefitMapper.selectOne(lambdaQueryWrapper);
                if (accountBenefit == null) {
                    throw new BusinessException(AccountErrorCode.BENEFIT_NOT_FOUND);
                }
                Account account = accountMapper.selectOne(new QueryWrapper<>(new Account())
                        .eq("merchant_id", accountBenefit.getMerchantId())
                        .eq("id", accountBenefit.getAccountId()));
                if (account == null) {
                    throw new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND);
                }
                // 判断余额是否够扣减
                if (AdjustTypeEnum.DECREMENT.equals(adjustItem.getAdjustType())
                        && accountBenefit.getBalance().compareTo(adjustItem.getAmount()) < 0) {
                    if (checkBalance) {
                        throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_NOT_ENOUGH);
                    } else {
                        //2021-05-26 售水机先出水后计费允许负数
                        if (allowNegative != null && allowNegative) {
                            // adjustItem.setAmount(adjustItem.getAmount());
                        }else {
                            adjustItem.setAmount(accountBenefit.getBalance());
                        }
                    }
                }
                BigDecimal increment = adjustItem.getAmount();
                if (AdjustTypeEnum.DECREMENT.equals(adjustItem.getAdjustType())) {
                    increment = increment.negate();
                }
                // 更新账户和权益的余额
                accountBenefitMapper.increaseBalanceAndTotal(accountBenefit.getId(), accountBenefit.getMerchantId(), adjustItem.getOperator(), null, increment);
                accountMapper.increaseBalanceAndTotal(account.getId(), account.getMerchantId(), adjustItem.getOperator(), null, increment);

                //更新用户统计
                updateUserStatistics(account.getUserId(), account.getMerchantUserId(), account.getMerchantId(),
                        account.getClassify(), null, increment);

                // 保存变更流水
                AccountRecord accountRecord = getAccountRecord(adjustItem, account, accountBenefit, increment, now);
                //自动打支付类型标签
                TagUserParamDTO tagUserParamDTO = new TagUserParamDTO();
                tagUserParamDTO.setTradeType(accountRecord.getTradeType());
                tagUserParamDTO.setUserType(null);
                tagUserParamDTO.setUserId(account.getUserId());
                tagUserParamDTO.setStoreName(accountRecord.getStoreName());
                tagUserParamDTO.setMerchantUserId(account.getMerchantUserId());
                tagUserParamDTO.setMerchantId(account.getMerchantId());
                //退款来源不打标签
                if (!Objects.equals(adjustItem.getExcludeUserTag(),true)){
                    merchantAutoTagAsyncHandler.autoCreateTag(tagUserParamDTO);
                    log.debug("退款来源不打标签");
                }
                accountRecordMapper.insert(accountRecord);
                saveUserMerchantRelation(account.getUserId(),account.getMerchantId(),accountRecord.getStoreId());
            });
        } finally {
            redisLock.unlock(lockKey, "Y");
        }
    }

    private static AccountRecord getAccountRecord(AccountBenefitAdjustDTO adjustItem, Account account, AccountBenefit accountBenefit, BigDecimal increment, Date now) {
        AccountRecord accountRecord = new AccountRecord();
        accountRecord.setAccountId(account.getId());
        accountRecord.setUserId(account.getUserId());
        accountRecord.setMerchantUserId(adjustItem.getMerchantUserId());
        accountRecord.setMerchantId(account.getMerchantId());
        accountRecord.setStoreId(adjustItem.getStoreId());
        accountRecord.setEquipmentId(adjustItem.getEquipmentId());
        accountRecord.setBenefitClassify(account.getClassify());
        accountRecord.setBenefitId(accountBenefit.getBenefitId());
        accountRecord.setInitialBenefit(account.getBalance());
        accountRecord.setOriginalBenefit(increment.abs());
        accountRecord.setActualBenefit(increment.abs());
        accountRecord.setOutTradeNo(adjustItem.getOutTradeNo());
        accountRecord.setOrderNo(adjustItem.getOrderNo());
        accountRecord.setMode(adjustItem.getAdjustType().getType());
        accountRecord.setCreatedby(ofNullable(adjustItem.getOperator()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        accountRecord.setCreateTime(now);
        accountRecord.setResource(adjustItem.getResource());
        accountRecord.setDescription(adjustItem.getDescription());

        accountRecord.setTradeType(adjustItem.getTradeType());
        accountRecord.setTradeAmount(adjustItem.getTradeAmount());
        accountRecord.setCommodityName(adjustItem.getCommodityName());
        if (!StringUtils.isEmpty(adjustItem.getStoreName())) {
            // 只取32位，避免过长保存报错
            String groupName = LyyStringUtil.substring(adjustItem.getStoreName(), 32);
            accountRecord.setStoreName(groupName);
        }

        accountRecord.setEquipmentTypeId(adjustItem.getEquipmentTypeId());
        accountRecord.setEquipmentTypeName(adjustItem.getEquipmentTypeName());
        accountRecord.setEquipmentValue(adjustItem.getEquipmentValue());
        accountRecord.setGroupNumber(adjustItem.getGroupNumber());
        accountRecord.setRecordType(adjustItem.getRecordType());
        accountRecord.setSort(adjustItem.getSort());
        accountRecord.setAccountBenefitId(accountBenefit.getId());
        return accountRecord;
    }

    /**
     * 处理商家券
     * @param adjustItem
     */
    private void rollBackMerchantCoupon(AccountBenefitAdjustDTO adjustItem) {
        BigDecimal increment = adjustItem.getAmount();
        if (AdjustTypeEnum.DECREMENT.equals(adjustItem.getAdjustType())) {
            increment = increment.negate();
        }
        log.debug("处理商家券:{}",adjustItem);
        Long accountId = adjustItem.getAccountId();
        if (accountId == null) {
            Account account = getCouponAccount(adjustItem.getMerchantId(), adjustItem.getMerchantUserId(), adjustItem.getUserId());
            if (account == null) {
                throw new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND);
            }
            accountId = account.getId();
        }
        accountMapper.increaseBalanceAndTotal(accountId, adjustItem.getMerchantId(), adjustItem.getOperator(), null, increment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void benefitConsume(ConsumeDTO consume) {
        getDeductionResultList(consume);
    }

    private List<AccountBenefitAdjustDTO> getDeductionResultList(ConsumeDTO consume) {
        List<AccountBenefitAdjustDTO> adjustList = new ArrayList<>();
        //商家券
        List<AccountBenefitAdjustDTO>  merchantCouponList = new ArrayList<>();
        MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(consume.getUserId(), consume.getMerchantId());
        Assert.notNull(merchantUser,AccountErrorCode.ACCOUNT_NOT_FOUND.getMessage());
        Boolean checkBalance = consume.getCheckBalance();
        //真实扣减
        boolean deduction = consume.hasDeduction();
        log.debug("权益计算-是否真实扣除,true为真实扣除,deduction：{}", deduction);
        if (!deduction) {
        BigDecimal totalAmount = consume.getConsume().stream().map(e->ofNullable(e.getAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            if (BigDecimal.ZERO.compareTo(totalAmount) == 0) {
                log.info("0元预扣-返回数据,用户id:{}",consume.getUserId());
                return preZeroConsumeResult(consume,merchantUser);
            }
        }
        // 计算筛选出具体怎么扣权益
        consume.getConsume().forEach(consumeDetailDTO -> {
            //商家券处理
            AccountBenefitAdjustDTO  coupon  =  handleMerchantCoupon(consume,merchantUser,consumeDetailDTO,deduction) ;
            if (coupon != null) {
                merchantCouponList.add(coupon);
                return;
            }
            //0元处理
            if (deduction && BigDecimal.ZERO.compareTo(consumeDetailDTO.getAmount()) == 0) {
                 handleZeroConsume(consume, merchantUser);
                 return;
            }

            List<AccountBenefitWithScopeDTO> benefitList = queryBenefitForConsume(merchantUser.getId(), consume.getMerchantId(), consumeDetailDTO.getClassify(),
                    consumeDetailDTO.getBenefitId(), consume.getExcludeClassify(),false, consume.getServiceType());
            // 判断余额是否足以抵扣
            if (CollectionUtils.isEmpty(benefitList)) {
                if (checkBalance) {
                    throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_NOT_ENOUGH);
                } else {
                    log.warn("不判断余额是否足以抵扣,设备id:{} ,用户id:{},场地id:{},consume:{} ",consume.getEquipmentId(),consume.getUserId(),consume.getStoreId(),consumeDetailDTO);
                    return;
                }
            }
            List<AccountBenefitConsumeDTO> benefitGroupByClassify = getAccountBenefitConsumeList(consume.getMerchantId(), benefitList);
            log.debug("[账户] 权益扣减(查出来的所有权益) -> {}", benefitGroupByClassify);
            adjustList.addAll(calculate(consume, consumeDetailDTO, benefitGroupByClassify, merchantUser.getId()));

        });
        if (deduction) {
            // 实际扣减权益
            AccountServiceImpl proxy = (AccountServiceImpl) AopContext.currentProxy();
            proxy.decreaseAccountBenefit(adjustList, checkBalance, consume.getAllowNegative());
            taggingGroupNameOrEquipmentTypeName(merchantUser,consume);
        }

        if (consume.getOrderNo() != null) {
            adjustList.addAll(merchantCouponList);
            if (!deduction) {
                return adjustList;
            }
            if (CollectionUtils.isEmpty(adjustList)) {
                log.debug("[账户] 权益扣减-adjustList为空:");
                return adjustList;
            }
            try {
                RedisClient.setex(RedisKey.MEMBER_CONSUME_CACHE.concat(consume.getOrderNo()), 300, objectMapper.writeValueAsString(adjustList));
                log.debug("[账户] 权益扣减-缓存后:");
            } catch (JsonProcessingException e) {
                log.error("序列化失败 {}", adjustList);
            }
        }
        return adjustList;
    }

    private List<AccountBenefitAdjustDTO> preZeroConsumeResult(ConsumeDTO consume, MerchantUser merchantUser) {
        log.debug("[0元预扣],consumeDetailDTOs:{}", consume.getConsume());
        List<AccountBenefitAdjustDTO> result = new ArrayList<>();
        for (ConsumeDetailDTO detailDTO : consume.getConsume()) {
            if (!CollectionUtils.isEmpty(detailDTO.getClassify())) {
                detailDTO.getClassify().forEach(e -> {
                    AccountBenefitAdjustDTO adjustDTO = new AccountBenefitAdjustDTO();
                    adjustDTO.setClassify(e);
                    adjustDTO.setUserId(merchantUser.getMerchantId());
                    adjustDTO.setMerchantId(merchantUser.getMerchantId());
                    adjustDTO.setMerchantUserId(merchantUser.getId());
                    adjustDTO.setAmount(BigDecimal.ZERO);
                    result.add(adjustDTO);
                });
            }else {
                log.error("[0元预扣],consumeDetailDTOs:{}", consume.getConsume());
            }
        }
        return result;
    }

    /**
     *
     * @param merchantId 商户Id
     * @param benefitList
     * @return
     */
    private List<AccountBenefitConsumeDTO> getAccountBenefitConsumeList(Long merchantId, List<AccountBenefitWithScopeDTO> benefitList) {
        List<Integer> classifyList = benefitList.stream().map(AccountBenefitWithScopeDTO::getClassify)
                .distinct().collect(Collectors.toList());
        Map<Integer, List<AccountBenefitWithScopeDTO>> benefitMap
                = benefitList.stream().collect(Collectors.groupingBy(AccountBenefitWithScopeDTO::getClassify));
        // 最终用来计算的集合
        List<BenefitConsumeRule> benefitConsumeRules = benefitRepository.listBenefitConsumeRule(merchantId, classifyList, Boolean.TRUE);
        List<AccountBenefitConsumeDTO> benefitGroupByClassify = CommonConverterTools.convert(AccountBenefitConsumeDTO.class, benefitConsumeRules);
        if (benefitGroupByClassify.isEmpty()) {
            //权益消耗规则兼容
            List<BenefitConsumeRule> consumeRules = benefitConsumeRuleService.getDefaultBenefitConsumeRule(classifyList);
            benefitGroupByClassify = CommonConverterTools.convert(AccountBenefitConsumeDTO.class, consumeRules);
        }
        if (benefitGroupByClassify.isEmpty()) {
            throw new BusinessException(AccountErrorCode.BENEFIT_CONSUME_RULE_MISS);
        }
        if (benefitGroupByClassify.size() != classifyList.size()) {
            classifyList.removeAll(benefitGroupByClassify.stream().map(AccountBenefitConsumeDTO::getBenefitClassify).collect(Collectors.toList()));
            if (classifyList.size() > 0) {
                List<BenefitConsumeRule> consumeRules = benefitConsumeRuleService.getDefaultBenefitConsumeRule(classifyList);
                benefitGroupByClassify.addAll(CommonConverterTools.convert(AccountBenefitConsumeDTO.class, consumeRules));
            }
        }
        benefitGroupByClassify.forEach(benefitGroup -> {
            benefitGroup.setBenefitWithScope(benefitMap.get(benefitGroup.getBenefitClassify()));
            //有有效期类型
            ArrayList<Integer> expireClassifyList = Lists.newArrayList(BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE.getCode(),
                    BenefitClassifyEnum.MERCHANT_PAYOUT_COIN.getCode(),
                    BenefitClassifyEnum.USER_RECHARGE_BALANCE.getCode(), BenefitClassifyEnum.USER_RECHARGE_COIN.getCode(),
                    BenefitClassifyEnum.USER_RECHARGE_GIVE_BALANCE.getCode(), BenefitClassifyEnum.USER_RECHARGE_GIVE_COIN.getCode(),
                    BenefitClassifyEnum.MERCHANT_PAYOUT_GROUP_BALANCE.getCode(), BenefitClassifyEnum.MERCHANT_PAYOUT_GROUP_COIN.getCode());
            if (expireClassifyList.contains(benefitGroup.getBenefitClassify())) {
                //派币和派红包也可能会有有效期(百川充值类型增加有效期)
                benefitGroup.setExpirePriority(2);
            }
            // 根据优先级排序
            benefitGroup.sort();
        });
        // 根据权重排序，数值越大权重越高
        benefitGroupByClassify.sort(Comparator.comparing(AccountBenefitConsumeDTO::getWeight).reversed());
        return benefitGroupByClassify;
    }

    @Override
    public List<BenefitPreDeductionDTO> benefitPreDeductionConsume(ConsumeDTO consume) {
        List<BenefitPreDeductionDTO> result = new ArrayList<>();
        List<AccountBenefitAdjustDTO> adjustDTOList = getDeductionResultList(consume);
        adjustDTOList.forEach(e->{
            BenefitPreDeductionDTO dto = new BenefitPreDeductionDTO();
            dto.setBenefitClassify(BenefitClassifyEnum.of(e.getClassify()));
            dto.setBenefitId(e.getBenfitId());
            dto.setConsume(e.getAmount());
            dto.setSort(e.getSort());
            dto.setBenefitScopeList(e.getBenefitScopeList());
            dto.setAccountBenefitId(e.getId());
            result.add(dto);
        });
        log.info("[新会员中心先使用]-{} 预计算扣除结果,result：{}", consume.getEquipmentValue(),result);
        return result;
    }

    /**
     * 根据订单号和退款金额退回抵扣的权益
     *
     * @param rollbackDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<BenefitPreDeductionDTO> refundBenefitByOrderNo(BenefitRollbackDTO rollbackDTO) {
        MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(rollbackDTO.getUserId(), rollbackDTO.getMerchantId());
        if (merchantUser == null || merchantUser.getId() == null) {
            throw new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR);
        }

        // 如果调整类型参数不为空
        if (rollbackDTO.getAdjustType() != null) {
            // 检查一下调整类型参数的正误
            if (!AdjustTypeEnum.checkAdjustType(rollbackDTO.getAdjustType())) {
                log.error("未知的调整类型 {}", rollbackDTO.getAdjustType());
                return null;
            }
        } else {
            // 没传默认就是查询扣减权益的流水
            rollbackDTO.setAdjustType(AdjustTypeEnum.DECREMENT.getType());
        }

        //默认只查7天消费记录回退权益
        List<AccountRecord> recordList = accountRecordMapper.findAllByOrderNo(rollbackDTO.getOrderNo(), rollbackDTO.getMerchantId(),
                rollbackDTO.getUserId(), merchantUser.getId(), rollbackDTO.getClassify(),  rollbackDTO.getAdjustType(),
                WxAuthUpdateUtils.getLocalDateTimeModifyDaysDate(LocalDateTime.now(),-7),null);
        if (CollectionUtils.isEmpty(recordList)) {
            return null;
        }

        boolean hasRefundAmount = false;
        if (rollbackDTO.getRefundAmount() != null && rollbackDTO.getRefundAmount().compareTo(BigDecimal.ZERO) > 0) {
            hasRefundAmount = true;
        }
        //遍历扣除的权益
        BigDecimal consumeAmount = BigDecimal.ZERO;
        //过滤掉不需要进行退权益扣费记录
        for (AccountRecord record : recordList) {
            if (hasRefundAmount) {
                consumeAmount = consumeAmount.add(record.getActualBenefit());
            }
            if (record.getSort() == null) {
                record.setSort(1000);
            }
        }
        if (hasRefundAmount && rollbackDTO.getRefundAmount().compareTo(consumeAmount) > 0) {
            //原启动扣除的权益不够抵扣
            log.error("原订单:{},不够进行抵扣退回,扣除权益总额:{},退回总额:{}", rollbackDTO.getOrderNo(), consumeAmount, rollbackDTO.getRefundAmount());
            throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_ROLLBACK_FAIL);
        }
        List<BenefitPreDeductionDTO> preDeductionDTOS = new ArrayList<>();
        //进行权益退回
        if (!CollectionUtils.isEmpty(recordList)) {
            List<AccountRecord> list = recordList.stream().sorted(Comparator.comparing(AccountRecord::getSort)).collect(Collectors.toList());
            List<BenefitPreDeductionDTO> benefitPreDeductionDTOList = refundByRecord(list, rollbackDTO);
            if (!CollectionUtils.isEmpty(benefitPreDeductionDTOList)) {
                preDeductionDTOS.addAll(benefitPreDeductionDTOList);
            }
        }
        return preDeductionDTOS;
    }

    @Override
    public AccountStatusDTO findCardStatus(Long merchantId, String cardNo) {
        Account account = accountMapper.selectAccountByCardNo(merchantId, cardNo);
        return ofNullable(account)
                .map(account1 -> {
                    Date downTime = account1.getDownTime();
                    if (downTime != null && downTime.before(new Date())) {
                        return new AccountStatusDTO().setStatus(AccountStatusEnum.OVERDUE.getStatus());
                    }
                    return new AccountStatusDTO().setStatus(account1.getStatus());
                }).orElse(null);
    }

    @Override
    public List<AccountStatusBatchDTO> findBatchCardStatus(Long merchantId, List<String> cardNos) {
        if (CollectionUtils.isEmpty(cardNos)) {
            return new ArrayList<>();
        }

        List<Account> accounts = accountMapper.selectAccountByCardNos(merchantId, cardNos);
        if (CollectionUtils.isEmpty(accounts)) {
            return new ArrayList<>();
        }
        List<AccountStatusBatchDTO> accountStatusDTOS = new ArrayList<>();
        accounts.forEach(account -> {
            AccountStatusBatchDTO dto = new AccountStatusBatchDTO();
            dto.setCardNo(account.getCardNo());
            dto.setStatus(account.getStatus());
            Date downTime = account.getDownTime();
            if (downTime != null && downTime.before(new Date())) {
                dto.setStatus(AccountStatusEnum.OVERDUE.getStatus());
            }
            accountStatusDTOS.add(dto);
        });

        return accountStatusDTOS;
    }

    @Override
    public SmallVenueAccountInfoDTO getAccountInfo(Long merchantId, String keyword) {
        //根据keyword匹配查询
        String cardNoReg = "^[a-zA-Z][a-zA-Z0-9]*$";
        String telephoneReg = "^1(3[0-9]|4[********]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\\d{8}$";
        String merchantUserIdReg = "^[0-9]*$";
        //根据cardNo查询
        if (ReUtil.isMatch(cardNoReg, keyword)) {
            return accountMapper.getAccountInfoByCardNo(merchantId, keyword);
        }
        //根据手机号查询
        if (ReUtil.isMatch(telephoneReg, keyword)) {
            return accountMapper.getAccountInfoByTelephone(merchantId, keyword);
        }
        //根据会员id查询,纯数字先查卡号再查会员id
        if (ReUtil.isMatch(merchantUserIdReg, keyword) && !ReUtil.isMatch(telephoneReg, keyword)) {
            SmallVenueAccountInfoDTO accountInfoByCardNo = accountMapper.getAccountInfoByCardNo(merchantId, keyword);
            if (accountInfoByCardNo == null) {
                MerchantUser merchantUser = merchantUserRepository.getByIdAndMerchantId(Long.valueOf(keyword), merchantId);
                if (merchantUser != null) {
                    accountInfoByCardNo = new SmallVenueAccountInfoDTO();
                    accountInfoByCardNo.setMerchantId(merchantUser.getMerchantId());
                    accountInfoByCardNo.setMerchantUserId(merchantUser.getId());
                    accountInfoByCardNo.setUserId(merchantUser.getUserId());
                    QueryWrapper wrapper = new QueryWrapper<Account>().select("id", "card_no")
                            .eq("merchant_id", merchantUser.getMerchantId())
                            .eq("merchant_user_id", merchantUser.getId())
                            .eq("user_id", merchantUser.getUserId())
                            .last("limit 1");
                    Account account = accountMapper.selectOne(wrapper);
                    if (account != null) {
                        accountInfoByCardNo.setAccountId(account.getId());
                        accountInfoByCardNo.setCardNo(account.getCardNo());
                    }
                }
            }
            return accountInfoByCardNo;
        }
        return null;
    }

    @Override
    public List<SmallVenueAllCardDTO> selectUserAllCard(Long merchantId, Long userId, Boolean hasSupplementaryCard) {
        return accountMapper.selectUserAllCard(merchantId, userId, hasSupplementaryCard);
    }

    @Override
    public Boolean supplementaryCardCheck(SupplementaryCardCheckDTO supplementaryCardCheckDTO) {
        Long merchantId = supplementaryCardCheckDTO.getMerchantId();
        Long accountId = supplementaryCardCheckDTO.getAccountId();
        Long userId = supplementaryCardCheckDTO.getUserId();
        //校验主卡信息
        Integer status = accountMapper.selectParentAccountStatus(merchantId, userId, accountId);
        if (Objects.isNull(status) || status != 1) {
            return false;
        }
        //校验附属卡数量
        Integer count = accountMapper.selectSupplementaryCardCount(merchantId, userId, accountId);
        if (count >= 8) {
            return false;
        }
        //其他校验
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addSupplementaryCard(AddSupplementaryCardDTO addSupplementaryCardDTO) {
        //查询主卡信息
        Account mainCardAccount = null;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(addSupplementaryCardDTO.getCardNo())) {
            mainCardAccount = accountMapper.selectAccountByCardNo(addSupplementaryCardDTO.getMerchantId(), addSupplementaryCardDTO.getCardNo());
        } else {
            LambdaQueryWrapper<Account> lambdaQueryWrapper = new QueryWrapper<Account>().lambda()
                                    .eq(Account::getId, addSupplementaryCardDTO.getAccountId())
                                    .eq(Account::getMerchantId, addSupplementaryCardDTO.getMerchantId())
                                    .eq(Account::getClassify, 99);
            mainCardAccount = accountMapper.selectOne(lambdaQueryWrapper);
        }
        if (mainCardAccount == null) {
            throw new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND);
        }
        //附属卡添加校验
        SupplementaryCardCheckDTO supplementaryCardCheckDTO = new SupplementaryCardCheckDTO()
                .setAccountId(mainCardAccount.getId())
                .setMerchantId(addSupplementaryCardDTO.getMerchantId())
                .setUserId(addSupplementaryCardDTO.getUserId());
        if (!supplementaryCardCheck(supplementaryCardCheckDTO)) {
            throw new BusinessException(AccountErrorCode.SUPPLEMENTARY_CHECK_FAIL_ERROR);
        }
        //创建附属卡
        Account account = new Account()
                .setUserId(addSupplementaryCardDTO.getUserId())
                .setParentAccountId(mainCardAccount.getId())
                .setMerchantId(addSupplementaryCardDTO.getMerchantId())
                .setMerchantUserId(addSupplementaryCardDTO.getMerchantUserId())
                .setClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                .setStatus(AccountStatusEnum.NORMAL.getStatus())
                .setStoreId(mainCardAccount.getStoreId())
                .setTotal(BigDecimal.ZERO)
                .setBalance(BigDecimal.ZERO)
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setCreateBy(ofNullable(addSupplementaryCardDTO.getOperatorId()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID))
                .setCardNo(addSupplementaryCardDTO.getSupplementaryCardNo())
                .setDeposit(addSupplementaryCardDTO.getDeposit())
                .setDefaultFlag(false);
        //判断过期时间的设置
        if (CardValidTypeEnum.SAME_MAIN_CARD.getType().equals(addSupplementaryCardDTO.getCardValidType())) {
            //同主卡有效期
            account.setDownTime(mainCardAccount.getDownTime());
        }
        if (CardValidTypeEnum.APPOINT_DAYS.getType().equals(addSupplementaryCardDTO.getCardValidType())) {
            if (Objects.isNull(addSupplementaryCardDTO.getDownTime())) {
                Integer validDays = ofNullable(addSupplementaryCardDTO.getValidDays()).filter(day -> day >= 0).orElse(0);
                //指定有效期天数
                Date downTime = DateUtils.addDays(new Date(), validDays);
                account.setDownTime(downTime);
            } else {
                account.setDownTime(addSupplementaryCardDTO.getDownTime());
            }
        }

        return accountMapper.insert(account);
    }

    /**
     * 会员卡挂失/恢复
     * @param updateCardStatusDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer cardReportLossOrRecover(UpdateCardStatusDTO updateCardStatusDTO) {
        String cardNo = updateCardStatusDTO.getCardNo();
        Long merchantId = updateCardStatusDTO.getMerchantId();
        Integer status = updateCardStatusDTO.getType();
        Long operatorId = updateCardStatusDTO.getOperatorId();
        //状态校验
        if (!AccountStatusEnum.NORMAL.getStatus().equals(status) && !AccountStatusEnum.LOSS.getStatus().equals(status)) {
            throw new BusinessException(AccountErrorCode.ACCOUNT_STATUS_INPUT_ERROR);
        }
        //恢复的时候校验会员卡是否过期
        if (AccountStatusEnum.NORMAL.getStatus().equals(status)) {
            Account account = accountMapper.selectAccountByCardNo(merchantId, cardNo);
            if (Objects.isNull(account)) {
                //不存在
                throw new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND);
            }
            //过期校验
            if (account.getDownTime() != null && new Date().after(account.getDownTime())) {
                throw new BusinessException(AccountErrorCode.CARD_RECOVER_EXPIRE_ERROR);
            }
        }
        //更新卡的状态
        Integer updateStatus = accountMapper.updateCardStatus(status, merchantId, cardNo, operatorId);
        return updateStatus;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer cardDisabledOrRecover(UpdateCardStatusDTO updateCardStatusDTO) {
        String cardNo = updateCardStatusDTO.getCardNo();
        Long merchantId = updateCardStatusDTO.getMerchantId();
        Integer status = updateCardStatusDTO.getType();
        Long operatorId = updateCardStatusDTO.getOperatorId();
        //状态校验
        if (!AccountStatusEnum.NORMAL.getStatus().equals(status) && !AccountStatusEnum.DISABLED.getStatus().equals(status)) {
            throw new BusinessException(AccountErrorCode.ACCOUNT_STATUS_INPUT_ERROR);
        }
        //恢复的时候校验会员卡是否过期
        if (AccountStatusEnum.NORMAL.getStatus().equals(status)) {
            Account account = accountMapper.selectAccountByCardNo(merchantId, cardNo);
            if (Objects.isNull(account)) {
                //不存在
                throw new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND);
            }
            //过期校验
            if (account.getDownTime() != null && new Date().after(account.getDownTime())) {
                throw new BusinessException(AccountErrorCode.CARD_RECOVER_EXPIRE_ERROR);
            }
        }
        //更新卡的状态
        Integer updateStatus = accountMapper.updateCardStatus(status, merchantId, cardNo, operatorId);
        return updateStatus;
    }

    /**
     * 会员卡补卡/换卡
     *
     * @param reissueCardDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer reissueCard(ReissueCardDTO reissueCardDTO) {
        Long merchantId = reissueCardDTO.getMerchantId();
        String oldCardNo = reissueCardDTO.getOldCardNo();
        String newCardNo = reissueCardDTO.getNewCardNo();
        Long operatorId = reissueCardDTO.getOperatorId();
        //校验
        Account account = accountMapper.selectAccountByCardNo(merchantId, newCardNo);
        if (account != null) {
            throw new BusinessException(AccountErrorCode.MULTIPLE_ACCOUNTS_EXIST);
        }
        //更新卡号
        Integer updateStatus = accountMapper.updateAccountCardNo(merchantId, oldCardNo, newCardNo, operatorId);
        //挂失的卡需要更新状态
        accountMapper.updateStatusAfterReissue(merchantId, newCardNo, operatorId);
        return updateStatus;
    }

    /**
     * 商品销售记录、兑换记录、商品回收记录保存
     *
     * @param smallVenueAccountRecordSaveDTO
     * @return
     */
    @Override
    public Boolean smallVenueSaveRecord(SmallVenueAccountRecordSaveDTO smallVenueAccountRecordSaveDTO) {
        AccountRecord accountRecord = new AccountRecord();
        BeanUtils.copyProperties(smallVenueAccountRecordSaveDTO, accountRecord);
        accountRecord.setCreatedby(smallVenueAccountRecordSaveDTO.getOperatorId());
        accountRecord.setCreateName(smallVenueAccountRecordSaveDTO.getOperatorName());
        accountRecord.setCreateTime(new Date());
        return saveAccountRecord(accountRecord);
    }

    @Override
    public Boolean smallVenueSaveRecordBatch(SmallVenueAccountRecordSaveBatchDTO dto) {
        if (Objects.isNull(dto.getMerchantUserId())) {
            //根据商户ID和用户id获取商户用户信息
            MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(dto.getUserId(), dto.getMerchantId());
            if (merchantUser == null) {
                log.warn("保存消费记录获取商户用户失败,userId:{},merchantId:{}", dto.getUserId(), dto.getMerchantId());
                throw new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR);
            }
            dto.setMerchantUserId(merchantUser.getId());
        }
        List<AccountRecord> records = dto.getRecords().stream().map(accountRecordAssembler::toAccountRecord).collect(Collectors.toList());
        records.forEach(record -> {
            record.setMerchantUserId(dto.getMerchantUserId());
            record.setMerchantId(dto.getMerchantId());
            record.setUserId(dto.getUserId());
            record.setCreatedby(dto.getOperatorId());
            record.setCreateName(dto.getOperatorName());
            record.setCreateTime(new Date());
            if (!StringUtils.isEmpty(record.getStoreName())) {
                // 只取32位，避免过长保存报错
                record.setStoreName(LyyStringUtil.substring(record.getStoreName(), 32));
            }
        });
        //自动打标签
        records.stream().map(AccountRecord::getStoreName).distinct().map(storeName -> {
            TagUserParamDTO tag = new TagUserParamDTO();
            tag.setUserId(dto.getUserId());
            tag.setStoreName(storeName);
            tag.setMerchantUserId(dto.getMerchantUserId());
            tag.setMerchantId(dto.getMerchantId());
            log.info("自动打标签：{}", tag);
            return tag;
        }).forEach(merchantAutoTagAsyncHandler::autoCreateTag);
        return accountRecordMapper.insertBatch(records) > 0;
    }

    /**
     * 商品购买记录、商品销售记录、兑换记录、商品回收记录、设备消费记录、储值变更记录查询
     *
     * @param smallVenueRecordSelectDTO
     * @return
     */
    @Override
    public DataList<SmallVenueRecordListDTO> smallVenueRecord(SmallVenueRecordSelectDTO smallVenueRecordSelectDTO) {
        Integer pageIndex = smallVenueRecordSelectDTO.getPageIndex();
        Integer pageSize = smallVenueRecordSelectDTO.getPageSize();
        Page<SmallVenueRecordListDTO> page = new Page<>(pageIndex, pageSize);
        if (smallVenueRecordSelectDTO.getCountSql() != null) {
            page.setSearchCount(smallVenueRecordSelectDTO.getCountSql());
        }
        IPage<SmallVenueRecordListDTO> smallVenueRecordListDTOIPage = accountRecordMapper.smallVenueRecord(page, smallVenueRecordSelectDTO);
        //设置业务类型名称
        List<SmallVenueRecordListDTO> records = smallVenueRecordListDTOIPage.getRecords();
        records = records.stream()
                .map(smallVenueRecordListDTO -> {
                    if (smallVenueRecordListDTO.getRecordType() != null) {
                        AccountRecordTypeEnum accountRecordTypeEnum = AccountRecordTypeEnum.findByCode(smallVenueRecordListDTO.getRecordType());
                        smallVenueRecordListDTO.setRecordTypeName(ofNullable(accountRecordTypeEnum).map(AccountRecordTypeEnum::getShowText).orElse(null));
                    }
                    return smallVenueRecordListDTO;
                }).collect(Collectors.toList());
        return new DataList<>(smallVenueRecordListDTOIPage.getTotal(), records, smallVenueRecordListDTOIPage.getPages());
    }

    /**
     * 查询主卡是否存在附属卡
     * @param hasSupplementaryCardDTO
     * @return
     */
    @Override
    public Boolean hasSupplementaryCard(HasSupplementaryCardDTO hasSupplementaryCardDTO) {
        return accountMapper.selectSupplementaryCardCount(hasSupplementaryCardDTO.getMerchantId(), hasSupplementaryCardDTO.getUserId(),
                hasSupplementaryCardDTO.getAccountId()) > 0;
    }

    @Override
    public Boolean renewalCard(Long merchantId, String cardNo, Integer extendedDays, Long updateBy) {
        //sql日期函数会导致主键路由问题查询问题？先查出过期时间
        Account account = accountMapper.selectAccountByCardNo(merchantId, cardNo);
        //账号不存在
        if (account == null) {
            throw new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND);
        }
        if (account.getDownTime() == null) {
            log.error("会员卡续期,过期时间为空");
            return false;
        }
        if (extendedDays < 0) {
            extendedDays = 0;
        }
        Date downTime = account.getDownTime();
        Date renewalTime = DateUtils.addDays(downTime, extendedDays);
        boolean renewStatus = accountMapper.renewalCard(merchantId, cardNo, renewalTime, updateBy) > 0;
        //卡过期，续期后更新状态
        if (AccountStatusEnum.OVERDUE.getStatus().equals(account.getStatus()) && renewalTime.after(new Date())) {
            accountMapper.updateCardStatus(AccountStatusEnum.NORMAL.getStatus(), merchantId, cardNo, updateBy);
        }
        return renewStatus;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean supplyAnonymousCardData(SupplyAnonymousCardDataDTO supplyAnonymousCardDataDTO) {
        Boolean defaultFlag = supplyAnonymousCardDataDTO.getDefaultFlag();
        String cardNo = supplyAnonymousCardDataDTO.getCardNo();
        Long merchantId = supplyAnonymousCardDataDTO.getMerchantId();
        Long merchantUserId = supplyAnonymousCardDataDTO.getMerchantUserId();
        Long userId = supplyAnonymousCardDataDTO.getUserId();
        Long operatorId = supplyAnonymousCardDataDTO.getOperatorId();
        //校验会员是否存在
        MerchantUser merchantUser = merchantUserRepository.getByIdAndMerchantId(merchantUserId, merchantId);
        if (merchantUser == null) {
            throw new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR);
        }
        Account anonymousAccount = accountMapper.selectAccountByCardNo(merchantId, cardNo);
        if (anonymousAccount == null) {
            throw new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND);
        }
        Long oldAccountUserId = anonymousAccount.getUserId();
        Long oldAccountMerchantUserId = anonymousAccount.getMerchantUserId();
        if (!AccountStatusEnum.NORMAL.getStatus().equals(anonymousAccount.getStatus())) {
            throw new BusinessException(AccountErrorCode.ACCOUNT_STATUS_ERROR);
        }
        //判断会员是否已经有卡
        List<SmallVenueAllCardDTO> smallVenueAllCardDTOS = accountMapper.selectUserAllCard(merchantId, userId, null);
        Boolean hasCard = ofNullable(smallVenueAllCardDTOS).map(cardDTOS -> {
            List<SmallVenueAllCardDTO> cardList = cardDTOS.stream()
                    .filter(card -> StrUtil.isNotBlank(card.getCardNo()) && BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode().equals(card.getClassify())).collect(Collectors.toList());
            return cardList.size() > 0;
        }).orElse(false);
        if (hasCard) {
            //绑定不记名卡
            bindingAnonymousCardNoTransfer(defaultFlag, cardNo, merchantId, merchantUserId, userId, anonymousAccount.getId(), oldAccountUserId, oldAccountMerchantUserId);
        } else {
            SmallVenueAllCardDTO smallVenueAllCardDTO = ofNullable(smallVenueAllCardDTOS).map(cardDTOS -> {
                List<SmallVenueAllCardDTO> cardList = cardDTOS.stream()
                        .filter(card -> StrUtil.isBlank(card.getCardNo()) && BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode().equals(card.getClassify()))
                        .collect(Collectors.toList());
                return CollectionUtils.isEmpty(cardList) ? null : cardList.get(0);
            }).orElse(null);

            if (smallVenueAllCardDTO == null) {
                //绑定不记名卡
                bindingAnonymousCardNoTransfer(defaultFlag, cardNo, merchantId, merchantUserId, userId, anonymousAccount.getId(), oldAccountUserId, oldAccountMerchantUserId);
            } else {
                //转移权益
                mergeMerchantUserBenefitToCardBenefit(cardNo, merchantId, merchantUserId, userId,
                        smallVenueAllCardDTO.getAccountId(), defaultFlag, anonymousAccount.getId(),operatorId);
                //绑定不记名卡
                bindingAnonymousCardNoTransfer(defaultFlag, cardNo, merchantId, merchantUserId, userId, anonymousAccount.getId(), oldAccountUserId, oldAccountMerchantUserId);
            }

        }
        return true;
    }

    @Override
    public List<AccountBenefitScopeDTO> listBenefitWithScope(AccountBenefitScopeQueryDTO query) {
        List<Long> merchantIds = query.getMerchantIds();
        if (CollectionUtils.isEmpty(merchantIds) && Objects.isNull(query.getMerchantId())) {
            throw new BusinessException(AccountErrorCode.BENEFIT_PARAM_ERROR);
        }
        if (Objects.nonNull(query.getMerchantId())) {
            List<AccountBenefitScopeDO> list = accountRepository.listBenefitWithScope(query);
            return convertScopeBenefit(list);
        } else if (CollectionUtils.isNotEmpty(merchantIds)) {
            return merchantIds.stream()
                    .flatMap(merchantId -> {
                        query.setMerchantId(merchantId);
                        List<AccountBenefitScopeDO> list = accountRepository.listBenefitWithScope(query);
                        return convertScopeBenefit(list).stream();
                    })
                    .collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    /**
     * 绑定不记名卡不转移权益
     * @param defaultFlag
     * @param cardNo
     * @param merchantId
     * @param merchantUserId
     * @param userId
     * @param accountId
     */
    private void bindingAnonymousCardNoTransfer(Boolean defaultFlag, String cardNo,
                                                Long merchantId, Long merchantUserId, Long userId, Long accountId, Long oldUserId, Long oldMerchantUserId) {

        log.info("绑定不记名卡不转移权益:bindingAnonymousCardNoTransfer参数,defaultFlag:{},cardNo:{},merchantId:{},merchantUserId:{},userId:{},accountId:{}",
                defaultFlag, cardNo, merchantId, merchantUserId, userId, accountId);
        if (defaultFlag != null && defaultFlag) {
            //处理默认卡
            accountMapper.updateAccountDefaultFlag(merchantId, merchantUserId, UserMemberSysConstants.DEFAULT_OPERATION_USER_ID);
            accountMapper.updateAccountIdInfo(merchantId, userId, cardNo, merchantUserId, true);
            accountBenefitMapper.updateAccountBenefitIdInfo(merchantId, userId, accountId, merchantUserId);
            mergeAnonymousCardNoStatistics(merchantId, merchantUserId, userId, oldUserId, oldMerchantUserId);
        } else {
            accountMapper.updateAccountIdInfo(merchantId, userId, cardNo, merchantUserId, defaultFlag);
            accountBenefitMapper.updateAccountBenefitIdInfo(merchantId, userId, accountId, merchantUserId);
            mergeAnonymousCardNoStatistics(merchantId, merchantUserId, userId, oldUserId, oldMerchantUserId);
        }
    }

    /**
     * 合并不记名卡统计的用户信息
     * @param merchantId
     * @param merchantUserId
     * @param userId
     * @param oldUserId
     * @param oldMerchantUserId
     */
    private void mergeAnonymousCardNoStatistics(Long merchantId, Long merchantUserId, Long userId, Long oldUserId, Long oldMerchantUserId) {
        log.info("合并不记名卡统计的用户信息:mergeAnonymousCardNoStatistics参数,merchantId:{},merchantUserId:{},userId:{},oldUserId:{},oldMerchantUserId:{}",
                merchantId, merchantUserId, userId, oldUserId, oldMerchantUserId);
        // 不记名卡已有的储值记录
        List<SmallVenueStoredStatistics> oldSmallVenueStoredStatistics = smallVenueStoredStatisticsMapper.selectStoredStatisticsByUserId(oldUserId, oldMerchantUserId, merchantId);
        if (CollUtil.isEmpty(oldSmallVenueStoredStatistics)) {
            // 不记名卡没有储值，直接返回
            return;
        }
        List<SmallVenueStoredStatistics> smallVenueStoredStatistics = smallVenueStoredStatisticsMapper.selectStoredStatisticsByUserId(userId, merchantUserId, merchantId);
        if (CollUtil.isEmpty(smallVenueStoredStatistics)) {
            // 会员没有储值记录，直接把不记名卡储值记录更新到会员账户下
            smallVenueStoredStatisticsMapper.updateStatisticsIdInfo(userId, merchantUserId, merchantId, oldUserId, oldMerchantUserId);
        } else {
            // 会员已有的储值记录
            List<Long> classifyIdList = smallVenueStoredStatistics.stream().map(SmallVenueStoredStatistics::getMerchantBenefitClassifyId).collect(Collectors.toList());
            // 取交集, 不记名卡和会员都有的储值记录
            List<SmallVenueStoredStatistics> existedClassifyList = oldSmallVenueStoredStatistics.stream().filter(o -> classifyIdList.contains(o.getMerchantBenefitClassifyId())).collect(Collectors.toList());
            // 取差集，不记名卡独有的储值记录
            Set<Long> newClassifyIdList = oldSmallVenueStoredStatistics.stream().filter(o -> !classifyIdList.contains(o.getMerchantBenefitClassifyId()))
                                                    .map(SmallVenueStoredStatistics::getMerchantBenefitClassifyId).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(newClassifyIdList)) {
                log.info("更新不记名卡新增的储值记录:newClassifyIdList={}", newClassifyIdList);
                smallVenueStoredStatisticsMapper.updateStatisticsIdInfoByMerchantBenefitClassifyIds(userId, merchantUserId, merchantId, oldUserId, oldMerchantUserId, newClassifyIdList);
            }
            if (CollUtil.isNotEmpty(existedClassifyList)) {
                // 合并不记名卡的储值记录到会员的储值记录中
                log.info("更新会员已存在的储值记录:existedClassifyList={}", existedClassifyList);
                existedClassifyList.forEach(o -> smallVenueStoredStatisticsMapper.updateStatisticsIdInfoByMerchantBenefitClassifyId(userId, merchantUserId, merchantId, o.getMerchantBenefitClassifyId(), o));
            }
        }
    }


    /**
     * 转移会员下的权益到不记名卡上
     * @param cardNo  不记名卡卡号
     * @param merchantId  商户id
     * @param merchantUserId 商户用户id
     * @param userId  用户id
     * @param accountId 会员下的权益的账户id
     * @param defaultFlag 是否设置不记名卡为默认卡
     * @param anonymousCardAccountId 不记名卡的账户id
     */
    private void mergeMerchantUserBenefitToCardBenefit(String cardNo, Long merchantId,
                                                       Long merchantUserId, Long userId,
                                                       Long accountId, Boolean defaultFlag,
                                                       Long anonymousCardAccountId, Long operatorId) {

        log.info("转移会员下的权益到不记名卡上:mergeMerchantUserBenefitToCardBenefit参数,cardNo:{},merchantId:{},merchantUserId:{},userId:{},accountId:{},defaultFlag:{},anonymousCardAccountId:{},operatorId:{}",
                cardNo, merchantId, merchantUserId, userId, accountId, defaultFlag, anonymousCardAccountId, operatorId);
        AvailableBenefitQueryDTO availableBenefitQueryDTO = new AvailableBenefitQueryDTO()
                .setUserId(userId)
                .setMerchantId(merchantId)
                .setClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                .setAccountId(accountId);
        List<AccountBenefit> userAllBenefit = accountBenefitMapper.findUserAllBenefit(availableBenefitQueryDTO);
        //有权益
        if (!CollectionUtils.isEmpty(userAllBenefit)) {
            //根据有效期类型过滤
            List<AccountBenefit> expireNoLimitBenefit = userAllBenefit.stream().filter(accountBenefit -> benefitExpireJudge(accountBenefit.getExpiryDateCategory(),
                    accountBenefit.getValueType(), accountBenefit.getDownTime())).collect(Collectors.toList());

            List<AccountBenefit> expireLimitBenefit = userAllBenefit.stream().filter(accountBenefit -> !benefitExpireJudge(accountBenefit.getExpiryDateCategory(),
                    accountBenefit.getValueType(), accountBenefit.getDownTime())).collect(Collectors.toList());

            //查询不记名卡所有权益
            AvailableBenefitQueryDTO anonymousCardBenefitDTO = new AvailableBenefitQueryDTO()
                    .setUserId(userId)
                    .setMerchantId(merchantId)
                    .setCardNo(cardNo);
            List<AccountBenefit> anonymousCardAllBenefit = accountBenefitMapper.findUserAllBenefit(anonymousCardBenefitDTO);
            Map<Long, AccountBenefit> anonymousCardBenefitMap = ofNullable(anonymousCardAllBenefit)
                    .map(anonymousCardAllBenefits -> anonymousCardAllBenefits.stream()
                            .collect(Collectors.toMap(AccountBenefit::getId, Function.identity())))
                    .orElse(new HashMap<>(16));

            //权益转移操作
            List<AccountRecord> resultRecord = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(expireNoLimitBenefit)) {
                List<AccountRecord> accountRecordsNoLimit = expireNoLimitBenefitHandle(expireNoLimitBenefit, anonymousCardBenefitMap, merchantId, operatorId, anonymousCardAccountId);
                resultRecord.addAll(accountRecordsNoLimit);
            }
            if (CollectionUtils.isNotEmpty(expireLimitBenefit)) {
                List<AccountRecord> accountRecordsLimit = expireLimitBenefitHandle(expireLimitBenefit, merchantId, operatorId, anonymousCardAccountId);
                resultRecord.addAll(accountRecordsLimit);
            }
            accountRecordRepository.insertBatch(resultRecord);
        }

    }

    /**
     * 有效期类型判断
     * @param category
     * @param valueType
     * @param downTime
     * @return
     */
    private boolean benefitExpireJudge(Integer category, Integer valueType, String downTime) {
        return Objects.equals(category, ExpiryDateCategoryEnum.NO_LIMIT.getValue())
                && Objects.equals(valueType, AccountBenefitNumTypeEnum.AMOUNT.getCode())
                && org.apache.commons.lang3.StringUtils.isBlank(downTime);
    }


    private AccountRecord assembleRecord(AccountBenefit accountBenefit,
                                         BigDecimal transferNum,
                                         BigDecimal initialBenefit,
                                         AdjustTypeEnum adjustTypeEnum,
                                         AccountRecordTypeEnum type) {
        AccountRecord record = new AccountRecord();
        record.setAccountId(accountBenefit.getAccountId());
        record.setUserId(accountBenefit.getUserId());
        record.setMerchantUserId(accountBenefit.getMerchantUserId());
        record.setAccountBenefitId(accountBenefit.getId());
        record.setMerchantId(accountBenefit.getMerchantId());
        record.setCreateTime(new Date());
        record.setMerchantBenefitClassifyId(accountBenefit.getMerchantBenefitClassifyId());
        record.setBenefitClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        record.setOperationType(AccountRecordOperationTypeEnum.BENEFIT_MODIFY.getCode());
        record.setMode(adjustTypeEnum.getType());
        record.setBenefitId(0L);

        if (Objects.equals(adjustTypeEnum, AdjustTypeEnum.DECREMENT)) {
            record.setInitialBenefit(accountBenefit.getBalance());
        } else {
            record.setInitialBenefit(initialBenefit);
            record.setActualValue(ZERO);
        }
        record.setOriginalBenefit(transferNum);
        record.setActualBenefit(transferNum);
        record.setRecordType(type.getCode());
        return record;
    }

    /**
     * 无限期权益的处理
     * @param expireNoLimitBenefit
     * @param anonymousCardBenefitMap
     * @param merchantId
     * @param operatorId
     * @return
     */
    private List<AccountRecord> expireNoLimitBenefitHandle(List<AccountBenefit> expireNoLimitBenefit,
                                                           Map<Long, AccountBenefit> anonymousCardBenefitMap,
                                                           Long merchantId, Long operatorId,Long anonymousCardAccountId) {

        log.info("无限期权益的处理,expireNoLimitBenefitHandle参数,expireNoLimitBenefit:{},anonymousCardBenefitMap:{},merchantId:{}", expireNoLimitBenefit, anonymousCardBenefitMap, merchantId);
        if (CollectionUtils.isEmpty(expireNoLimitBenefit)) {
            return null;
        }
        List<AccountRecord> accountRecords = new ArrayList<AccountRecord>();
        Map<Long, BigDecimal> balanceInfoMap = expireNoLimitBenefit.stream().collect(Collectors.toMap(AccountBenefit::getId, AccountBenefit::getBalance, (k1, k2) -> k1));
        Map<Long, AccountBenefit> expireNoLimitBenefitMap = expireNoLimitBenefit.stream().collect(Collectors.toMap(AccountBenefit::getId, Function.identity(), (k1, k2) -> k1));
        //更新会员下的权益
        List<Long> accountBenefitIds = expireNoLimitBenefit.stream().map(AccountBenefit::getId).collect(Collectors.toList());
        accountBenefitMapper.batchUpdateAccountBenefitBalance(accountBenefitIds, ZERO, operatorId,merchantId);
        accountRecords.addAll(expireNoLimitBenefit.stream()
                .map(accountBenefit -> assembleRecord(accountBenefit, accountBenefit.getBalance(), null, AdjustTypeEnum.DECREMENT, AccountRecordTypeEnum.SV_TRANSFER_OUT))
                .collect(Collectors.toList()));

        //不记名卡已经存在的权益
        List<Long> existBenefitAnonymousCard = accountBenefitIds.stream().filter(id -> anonymousCardBenefitMap.get(id) != null).collect(Collectors.toList());
        for (Long benefitId : existBenefitAnonymousCard) {
            AccountBenefit accountBenefit = anonymousCardBenefitMap.get(benefitId);
            if (accountBenefit != null) {
                BigDecimal balance = accountBenefit.getBalance().add(ofNullable(balanceInfoMap.get(benefitId)).orElse(ZERO));
                BigDecimal total = accountBenefit.getTotal().add(ofNullable(balanceInfoMap.get(benefitId)).orElse(ZERO));
                AccountBenefit accountBenefitUpdate = assembleUpdatedAccountBenefit(accountBenefit.getId(), balance, total, operatorId, new Date());
                updateAccountBenefit(merchantId, accountBenefitUpdate);
                accountRecords.add(assembleRecord(accountBenefit, balanceInfoMap.get(benefitId), accountBenefit.getBalance(), AdjustTypeEnum.INCREMENT, AccountRecordTypeEnum.SV_TRANSFER_IN));
            }
        }

        //不记名卡不存在的权益
        List<Long> noExistBenefitAnonymousCard = accountBenefitIds.stream().filter(id -> anonymousCardBenefitMap.get(id) == null).collect(Collectors.toList());
        List<AccountBenefit> accountBenefitsBatchSave = new ArrayList<>();
        for (Long benefitId : noExistBenefitAnonymousCard) {
            AccountBenefit accountBenefit = expireNoLimitBenefitMap.get(benefitId);
            AccountBenefit accountBenefitSave = assembleNewAccountBenefit(accountBenefit, balanceInfoMap.get(benefitId), operatorId, new Date());
            accountBenefitSave.setAccountId(anonymousCardAccountId);
            accountBenefitsBatchSave.add(accountBenefitSave);
            accountRecords.add(assembleRecord(accountBenefitSave, balanceInfoMap.get(benefitId), ZERO, AdjustTypeEnum.INCREMENT, AccountRecordTypeEnum.SV_TRANSFER_IN));
        }
        if (CollectionUtils.isNotEmpty(accountBenefitsBatchSave)) {
            batchInsertBenefit(accountBenefitsBatchSave);
        }
        return accountRecords;
    }

    /**
     * 有限期权益的处理
     * @param expireLimitBenefit
     * @param merchantId
     * @param operatorId
     * @return
     */
    private List<AccountRecord> expireLimitBenefitHandle(List<AccountBenefit> expireLimitBenefit,
                                                         Long merchantId, Long operatorId,Long anonymousCardAccountId) {

        log.info("有限期权益的处理,expireLimitBenefitHandle参数,expireLimitBenefit:{},merchantId:{}", expireLimitBenefit, merchantId);
        if (CollectionUtils.isEmpty(expireLimitBenefit)) {
            return null;
        }
        List<AccountRecord> accountRecords = new ArrayList<AccountRecord>();
        Map<Long, BigDecimal> balanceInfoMap = expireLimitBenefit.stream().collect(Collectors.toMap(AccountBenefit::getId, AccountBenefit::getBalance, (k1, k2) -> k1));
        Map<Long, AccountBenefit> expireNoLimitBenefitMap = expireLimitBenefit.stream().collect(Collectors.toMap(AccountBenefit::getId, Function.identity(), (k1, k2) -> k1));
        //更新会员下的权益
        List<Long> accountBenefitIds = expireLimitBenefit.stream().map(AccountBenefit::getId).collect(Collectors.toList());
        accountBenefitMapper.batchUpdateAccountBenefitBalance(accountBenefitIds, ZERO, operatorId,merchantId);
        accountRecords.addAll(expireLimitBenefit.stream()
                .map(accountBenefit -> assembleRecord(accountBenefit, accountBenefit.getBalance(), null, AdjustTypeEnum.DECREMENT, AccountRecordTypeEnum.SV_TRANSFER_OUT))
                .collect(Collectors.toList()));

        List<AccountBenefit> accountBenefitsBatchSave = new ArrayList<>();
        for (Long benefitId : accountBenefitIds) {
            AccountBenefit accountBenefit = expireNoLimitBenefitMap.get(benefitId);
            AccountBenefit accountBenefitSave = assembleNewAccountBenefit(accountBenefit, balanceInfoMap.get(benefitId), operatorId, new Date());
            accountBenefitSave.setAccountId(anonymousCardAccountId);
            accountBenefitsBatchSave.add(accountBenefitSave);
            accountRecords.add(assembleRecord(accountBenefitSave, balanceInfoMap.get(benefitId), ZERO, AdjustTypeEnum.INCREMENT, AccountRecordTypeEnum.SV_TRANSFER_IN));
        }
        if (CollectionUtils.isNotEmpty(accountBenefitsBatchSave)) {
            batchInsertBenefit(accountBenefitsBatchSave);
        }
        return accountRecords;
    }

    private void batchInsertBenefit(List<AccountBenefit> accountBenefitsBatchSave) {
        accountBenefitRepository.insertBatch(accountBenefitsBatchSave);
    }

    /**
     * 构造更新AccountBenefit
     * @param id
     * @param balance
     * @param total
     * @param operatorId
     * @param now
     * @return
     */
    private AccountBenefit assembleUpdatedAccountBenefit(Long id, BigDecimal balance, BigDecimal total, Long operatorId, Date now) {
        AccountBenefit entity = new AccountBenefit();
        entity.setId(id);
        entity.setBalance(balance);
        if (Objects.nonNull(total)) {
            entity.setTotal(total);
        }
        entity.setUpdateTime(now);
        entity.setUpdateBy(operatorId);
        return entity;
    }

    /**
     * 构造新的AccountBenefit
     * @param accountBenefit
     * @param transferNum
     * @param operatorId
     * @param now
     * @return
     */
    private AccountBenefit assembleNewAccountBenefit(AccountBenefit accountBenefit, BigDecimal transferNum, Long operatorId, Date now) {
        AccountBenefit newBenefit = new AccountBenefit();
        BeanUtils.copyProperties(accountBenefit, newBenefit);
        newBenefit.setId(null);
        newBenefit.setUpdateTime(now);
        newBenefit.setUpdateBy(operatorId);
        newBenefit.setTotal(transferNum);
        newBenefit.setBalance(transferNum);
        newBenefit.setCreateBy(operatorId);
        newBenefit.setCreateTime(now);
        return newBenefit;
    }

    /**
     * 更新AccountBenefit
     * @param merchantId
     * @param entity
     */
    public void updateAccountBenefit(Long merchantId, AccountBenefit entity) {
        Wrapper<AccountBenefit> wrapper = Wrappers.<AccountBenefit>lambdaUpdate()
                .eq(AccountBenefit::getId, entity.getId())
                .eq(AccountBenefit::getMerchantId, merchantId)
                .eq(AccountBenefit::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        accountBenefitMapper.update(entity, wrapper);
    }

    @Override
    public Long countRecord(AccountRecordCountDTO accountRecordCountDTO) {
        return accountRecordMapper.countRecord(accountRecordCountDTO);
    }

    /**
     * 根据订单和权益类型查询消耗权益的使用范围
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<RecordBenefitScopeDTO> findRecordBenefitScopeByOrderNo(RecordBenefitScopeQueryDTO queryDTO) {
        MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(queryDTO.getUserId(), queryDTO.getMerchantId());
        if (merchantUser == null) {
            throw new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR);
        }
        //根据订单号和权益使用类型查询数据
        List<AccountRecord> list = accountRecordMapper.findAllByOrderNo(queryDTO.getOutTradeNo(), queryDTO.getMerchantId(), queryDTO.getUserId(),
                merchantUser.getId(), queryDTO.getClassifies(), queryDTO.getMode(),
                WxAuthUpdateUtils.getLocalDateTimeModifyMonthsDate(LocalDateTime.now(),-1),null);
        if (!CollectionUtils.isEmpty(list)) {
            Map<Integer, List<AccountRecord>> map = list.stream().collect(Collectors.groupingBy(AccountRecord::getBenefitClassify));
            List<RecordBenefitScopeDTO> benefitScopeDTOS = new ArrayList<>(map.size());
            for (Map.Entry<Integer, List<AccountRecord>> entry : map.entrySet()) {
                RecordBenefitScopeDTO recordBenefitScopeDTO = new RecordBenefitScopeDTO();
                List<Long> benefitIds = entry.getValue().stream().map(AccountRecord::getBenefitId).collect(Collectors.toList());
                List<Long> scopeList = benefitScopeMapper.findAllScopeId(queryDTO.getMerchantId(), benefitIds, queryDTO.getApplicable());
                recordBenefitScopeDTO.setClassify(entry.getKey());
                recordBenefitScopeDTO.setAssociatedIds(scopeList);
                benefitScopeDTOS.add(recordBenefitScopeDTO);
            }
            return benefitScopeDTOS;
        }
        return null;
    }

    /**
     * 根据订单获取扣款明细记录
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<AccountRecordDTO> findRecordByOrderNoAndCondition(AccountRecordQueryDTO queryDTO) {
        if (queryDTO.getMerchantUserId() == null) {
            MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(queryDTO.getUserId(), queryDTO.getMerchantId());
            if (merchantUser == null) {
                throw new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR);
            }
            queryDTO.setMerchantUserId(merchantUser.getId());
        }
        QueryWrapper<AccountRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("merchant_id", queryDTO.getMerchantId());
        queryWrapper.eq("merchant_user_id", queryDTO.getMerchantUserId());
        if (queryDTO.getStartTime() != null) {
            queryWrapper.gt("create_time", queryDTO.getStartTime());
        } else {
            //没有传开始时间，则查前面7天
            queryWrapper.gt("create_time", DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -7));
        }
        if (queryDTO.getEndTime() != null) {
            queryWrapper.lt("create_time", queryDTO.getEndTime());
        } else {
            queryWrapper.lt("create_time", new Date());
        }
        if (!StringUtils.isEmpty(queryDTO.getOutTradeNo())) {
            queryWrapper.eq("order_no", queryDTO.getOutTradeNo().trim());
        }
        if (queryDTO.getMode() != null) {
            queryWrapper.eq("mode", queryDTO.getMode());
        }
        List<AccountRecordDTO> recordDTOS = new ArrayList<>();
        List<AccountRecord> list = accountRecordMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(accountRecord -> {
                AccountRecordDTO accountRecordDTO = new AccountRecordDTO();
                BeanUtils.copyProperties(accountRecord, accountRecordDTO);
                recordDTOS.add(accountRecordDTO);
            });
        }
        return recordDTOS;
    }

    private List<AccountBenefitScopeDTO> convertScopeBenefit(List<AccountBenefitScopeDO> list) {
        return list
                .stream()
                .map(s -> {
                    AccountBenefitScopeDTO t = assembler.fromAccountBenefitScopeDO(s);
                    t.setClassifyName(BenefitClassifyEnum.getDesc(t.getClassify()));
                    return t;
                })
                .collect(Collectors.toList());
    }

    /**
     * 用户充值抵扣金退款校验
     *
     * @param merchantId
     * @param userId
     * @param outTradeNO
     * @return
     */
    @Override
    public Boolean deductionRechargeRefundCheck(Long merchantId, Long userId, String outTradeNO) {
        MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(userId, merchantId);
        if (merchantUser == null) {
            log.error("用户充值抵扣金退款校验获取商户用户信息失败,{},{},{}", merchantId, userId, outTradeNO);
            throw new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR);
        }
        //根据订单号查询充值抵扣金
        List<AccountRecord> records = accountRecordMapper.findAllByOrderNo(outTradeNO, merchantId, userId,
                merchantUser.getId(), Collections.singletonList(BenefitClassifyEnum.DEDUCTION_AMOUNT.getCode()),
                INCREMENT.getType(), DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -7), new Date());
        if (CollectionUtils.isEmpty(records)) {
            return false;
        }
        //充值增加的抵扣金
        BigDecimal recharge = records.stream().map(AccountRecord::getActualBenefit)
                .filter(s -> s.compareTo(BigDecimal.ZERO) > 0).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //查询充值账户剩余抵扣金
        Set<Long> accountBenefitIds = records.stream().map(AccountRecord::getAccountBenefitId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(accountBenefitIds)) {
            log.error("用户流水没有充值抵扣金账户明细,{}", records);
            return false;
        }
        List<AccountBenefit> accountBenefitList = accountBenefitMapper.selectList(new QueryWrapper<AccountBenefit>()
                .eq("merchant_id", merchantId).eq("user_id", userId)
                .eq("merchant_user_id", merchantUser.getId())
                .eq("classify", BenefitClassifyEnum.DEDUCTION_AMOUNT.getCode())
                .in("id", accountBenefitIds));
        if (CollectionUtils.isEmpty(accountBenefitList)) {
            log.error("充值抵扣金查询账户明细失败,{}", records);
            return false;
        }
        BigDecimal balance = accountBenefitList.stream().map(AccountBenefit::getBalance)
                .filter(s -> s.compareTo(BigDecimal.ZERO) > 0).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        log.info("用户充值抵扣金退款校验,{},{},{},{},{}", merchantId, userId, outTradeNO, recharge, balance);
        return balance.compareTo(recharge) >= 0;
    }

    /**
     * 移动端账户流水列表
     * @param mobileTerminalRecordSelectDTO
     * @return
     */
    @Override
    public DataList<SmallVenueRecordListDTO> mobileTerminalRecord(MobileTerminalRecordSelectDTO mobileTerminalRecordSelectDTO) {
        Integer pageIndex = mobileTerminalRecordSelectDTO.getPageIndex();
        Integer pageSize = mobileTerminalRecordSelectDTO.getPageSize();
        Page<SmallVenueRecordListDTO> page = new Page<>(pageIndex, pageSize);
        if (mobileTerminalRecordSelectDTO.getCountSql() != null) {
            page.setSearchCount(mobileTerminalRecordSelectDTO.getCountSql());
        }
        IPage<SmallVenueRecordListDTO> smallVenueRecordListDTOIPage = accountRecordMapper.mobileTerminalRecord(page, mobileTerminalRecordSelectDTO);
        List<SmallVenueRecordListDTO> records = smallVenueRecordListDTOIPage.getRecords();
        //设置业务类型名称
        records = records.stream()
                .map(smallVenueRecordListDTO -> {
                    if (smallVenueRecordListDTO.getRecordType() != null) {
                        AccountRecordTypeEnum accountRecordTypeEnum = AccountRecordTypeEnum.findByCode(smallVenueRecordListDTO.getRecordType());
                        smallVenueRecordListDTO.setRecordTypeName(ofNullable(accountRecordTypeEnum).map(AccountRecordTypeEnum::getShowText).orElse(null));
                    }
                    return smallVenueRecordListDTO;
                }).collect(Collectors.toList());
        return new DataList<>(smallVenueRecordListDTOIPage.getTotal(), records, smallVenueRecordListDTOIPage.getPages());
    }

    /**
     * 移动端会员卡列表查询
     * @param mobileTerminalAccountSelectDTO
     * @return
     */
    @Override
    public DataList<MobileTerminalAccountListDTO> mobileTerminalAccountList(MobileTerminalAccountSelectDTO mobileTerminalAccountSelectDTO) {
        //查询会员卡信息
        Long merchantId = mobileTerminalAccountSelectDTO.getMerchantId();
        Integer pageIndex = mobileTerminalAccountSelectDTO.getPageIndex();
        Integer pageSize = mobileTerminalAccountSelectDTO.getPageSize();
        Page<MobileTerminalAccountListDTO> page = new Page<>(pageIndex, pageSize);
        if (mobileTerminalAccountSelectDTO.getCountSql() != null) {
            page.setSearchCount(mobileTerminalAccountSelectDTO.getCountSql());
        }
        IPage<MobileTerminalAccountListDTO> mobileTerminalAccountListPage = accountMapper.mobileTerminalAccountList(page, mobileTerminalAccountSelectDTO);
        List<MobileTerminalAccountListDTO> records = mobileTerminalAccountListPage.getRecords();
        //获取accountId集合查询储值
        List<Long> accountIds = records.stream().map(MobileTerminalAccountListDTO::getAccountId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(accountIds)) {

            List<MobileTerminalAccountBenefitInfo> benefitByAccountIds = accountBenefitMapper.findBenefitByAccountIds(merchantId, accountIds);
            log.info("移动端会员列表会员储值信息:{}", benefitByAccountIds);

            //根据accountId转Map
            Map<Long, List<MobileTerminalAccountBenefitInfo>> benefitInfoMap = benefitByAccountIds.stream()
                    .filter(benefitInfo -> benefitInfo.getAccountId() != null)
                    .collect(Collectors.groupingBy(MobileTerminalAccountBenefitInfo::getAccountId));

            //查询附属卡张数
            List<MobileTerminalCardCountInfo> mobileTerminalCardCountInfos = accountMapper.batchSelectSupplementaryCardCount(merchantId, accountIds);
            log.info("移动端会员列表会员卡附属卡张数信息:{}", mobileTerminalCardCountInfos);

            //根据accountId转Map
            Map<Long, Integer> cardCountMap = mobileTerminalCardCountInfos.stream()
                    .filter(countInfo -> countInfo.getAccountId() != null)
                    .collect(Collectors.toMap(MobileTerminalCardCountInfo::getAccountId, MobileTerminalCardCountInfo::getCount, (k1, k2) -> k1));

            //设置储值信息和附属卡张数
            records = records.stream().map(mobileTerminalAccountListDTO -> {
                mobileTerminalAccountListDTO.setBenefitInfos(benefitInfoMap.get(mobileTerminalAccountListDTO.getAccountId()));
                mobileTerminalAccountListDTO.setSupplementaryCardNum(ofNullable(cardCountMap.get(mobileTerminalAccountListDTO.getAccountId())).orElse(0));
                return mobileTerminalAccountListDTO;
            }).collect(Collectors.toList());
        }
        return new DataList<>(mobileTerminalAccountListPage.getTotal(), records, mobileTerminalAccountListPage.getPages());
    }

    /**
     * 移动端会员卡详情
     * @param merchantId
     * @param userId
     * @param accountId
     * @return
     */
    @Override
    public MobileTerminalAccountDetailsDTO mobileTerminalAccountDetails(Long merchantId, Long userId, Long accountId) {
        //查询会员信息
        MobileTerminalAccountDetailsDTO mobileTerminalAccountDetailsDTO = accountMapper.mobileTerminalAccountDetails(merchantId, accountId);
        if (mobileTerminalAccountDetailsDTO != null) {
            //设置附属卡信息
            List<MobileTerminalCardCountInfo> cardCountInfos = accountMapper.batchSelectSupplementaryCardCount(merchantId, Arrays.asList(accountId));
            if (CollectionUtils.isNotEmpty(cardCountInfos)) {
                mobileTerminalAccountDetailsDTO.setSupplementaryCardNum(ofNullable(cardCountInfos.get(0)).map(MobileTerminalCardCountInfo::getCount).orElse(0));
            } else {
                mobileTerminalAccountDetailsDTO.setSupplementaryCardNum(0);
            }
            //获取附属卡列表
            mobileTerminalAccountDetailsDTO.setSupplementaryCardList(accountMapper.findAccountSupplementaryCardList(merchantId, accountId));
            //获取储值统计，按门店分组
            mobileTerminalAccountDetailsDTO.setBenefitStatisticsDetail(accountBenefitService.benefitStatisticsGroupByStoreId(merchantId, userId, accountId));
            //设置储值信息
            List<MobileTerminalAccountBenefitInfo> benefitByAccountId = accountBenefitMapper.findBenefitByAccountIds(merchantId, Arrays.asList(accountId));
            mobileTerminalAccountDetailsDTO.setBenefitInfos(benefitByAccountId);
        }
        return mobileTerminalAccountDetailsDTO;
    }

    /**
     * 获取账户指定类型权益信息
     */
    @Override
    public List<AccountClassifyBenefitDTO> findAccountClassifyBenefit(AccountClassifyBenefitQueryDTO request) {
        MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(request.getUserId(), request.getMerchantId());
        Assert.notNull(merchantUser,AccountErrorCode.ACCOUNT_NOT_FOUND.getMessage());
        List<AccountBenefitWithScopeDTO> benefitList = queryBenefitForConsume(merchantUser.getId(), request.getMerchantId(), request.getClassify(), null, null, false, null);
        List<BenefitWithScopeDTO> benefits = CommonConverterTools.convert(BenefitWithScopeDTO.class, benefitList);
        if (benefits.isEmpty()) {
            return Collections.emptyList();
        }
        //排除商家优惠券
        List<Integer> classifyList = benefits.stream().map(BenefitWithScopeDTO::getClassify)
                .filter(classify -> !BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON.getCode().equals(classify))
                .distinct().collect(Collectors.toList());
        Map<Integer, List<BenefitWithScopeDTO>> benefitMap
                = benefits.stream().collect(Collectors.groupingBy(BenefitWithScopeDTO::getClassify));
        //类型权益消耗规则
        List<AccountClassifyBenefitDTO> benefitGroupByClassify = getClassifyBenefitRule(request, classifyList);
        benefitGroupByClassify.forEach(benefit -> benefit.setBenefitWithScope(benefitMap.get(benefit.getBenefitClassify())));
        //补充商家优惠券
        if (request.getClassify().contains(BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON.getCode())) {
            AccountClassifyBenefitDTO benefit = new AccountClassifyBenefitDTO();
            AccountQueryDTO query = new AccountQueryDTO();
            query.setMerchantId(merchantUser.getMerchantId());
            query.setMerchantUserId(merchantUser.getId());
            BigDecimal couponNum = countMerchantUserAccount(query);
            BenefitWithScopeDTO couponBenefit = new BenefitWithScopeDTO();
            couponBenefit.setClassify(BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON.getCode());
            couponBenefit.setAmount(couponNum);
            benefit.setBenefitWithScope(Collections.singletonList(couponBenefit));
            benefit.setBenefitClassify(BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON.getCode());
            benefit.setExpirePriority(null);
            benefit.setWeight(null);
            benefitGroupByClassify.add(benefit);
        }
        return benefitGroupByClassify;
    }

    @Override
    public Boolean decreaseAccountBenefitAndTag(AccountBenefitDecreaseReqDTO request) {
        Boolean checkBalance = ofNullable(request.getCheckBalance()).orElse(true);
        if (CollectionUtils.isEmpty(request.getAdjustList())) {
            return false;
        }
        AccountBenefitAdjustDTO item = request.getAdjustList().get(0);
        if (Objects.isNull(item.getMerchantUserId())) {
            MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(item.getUserId(), item.getMerchantId());
            Assert.notNull(merchantUser,AccountErrorCode.ACCOUNT_NOT_FOUND.getMessage());
            request.getAdjustList().forEach(adjust -> adjust.setMerchantUserId(merchantUser.getId()));
        }
        AccountServiceImpl proxy = (AccountServiceImpl) AopContext.currentProxy();

        //扣减账户权益
        proxy.decreaseAccountBenefit(request.getAdjustList(), checkBalance, request.getAllowNegative());
        //是否需要打标签
        Boolean tag = ofNullable(request.getTag()).orElse(true);
        if (!tag) {
            return true;
        }
        Optional<AccountBenefitAdjustDTO> optional = request.getAdjustList().stream().findFirst();
        //打标签
        ConsumeDTO consume = optional.map(adjust -> {
            ConsumeDTO consumeDTO = new ConsumeDTO();
            consumeDTO.setStoreName(adjust.getStoreName());
            consumeDTO.setEquipmentTypeName(adjust.getEquipmentTypeName());
            return consumeDTO;
        }).orElse(null);
        optional.map(adjust -> {
            MerchantUser user = new MerchantUser();
            user.setMerchantId(adjust.getMerchantId());
            user.setId(adjust.getMerchantUserId());
            return user;
        }).ifPresent(merchantUser -> taggingGroupNameOrEquipmentTypeName(merchantUser, consume));
        return true;
    }

    @Override
    public OrderBenefitInfoDTO listOrderBenefitRecord(OrderBenefitRecordQueryDTO query) {
        List<AccountRecord> list = accountRecordRepository.listRecordByOrder(assembler.toRecordQuery(query));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        OrderBenefitInfoDTO dto = new OrderBenefitInfoDTO();
        Set<Long> accountBenefitIds = Sets.newHashSet();
        List<OrderBenefitRecord> accountRecords = list.stream()
                .map(accountRecord -> {
                    OrderBenefitRecord userAccountRecord = assembler.toOrderBenefitRecord(accountRecord);
                    accountBenefitIds.add(accountRecord.getAccountBenefitId());
                    return userAccountRecord;
                })
                .collect(Collectors.toList());
        dto.setAccountRecords(accountRecords);
        if (CollectionUtils.isNotEmpty(accountBenefitIds)) {
            //获取对应消耗权益的权益明细
            Query bq = new Query();
            bq.setUserId(query.getUserId());
            bq.setMerchantId(query.getMerchantId());
            bq.setAccountBenefitIds(new ArrayList<>(accountBenefitIds));
            List<AccountBenefit> accountBenefits = accountBenefitRepository.listBenefit(bq);
            if (CollectionUtils.isNotEmpty(accountBenefits)) {
                //把已过期的进行过滤
                List<OrderAccountBenefit> userAccountBenefits = accountBenefits.stream()
                        .filter(accountBenefit -> accountBenefit.getBalance().compareTo(BigDecimal.ZERO) > 0)
                        .filter(accountBenefit -> {
                            if (Objects.equals(accountBenefit.getExpiryDateCategory(), ExpiryDateCategoryEnum.TIME_INTERVAL.getValue())) {
                                LocalDateTime begin = LocalDateTime.parse(ofNullable(accountBenefit.getUpTime()).orElse("1970-01-01 00:00:00"),
                                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                                LocalDateTime end = LocalDateTime.parse(ofNullable(accountBenefit.getDownTime()).orElse("9999-01-01 00:00:00"),
                                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                                return begin.isBefore(LocalDateTime.now()) && end.isAfter(LocalDateTime.now());
                            }
                            if (Objects.equals(accountBenefit.getExpiryDateCategory(), ExpiryDateCategoryEnum.ONE_DAY_TIME_INTERVAL.getValue())) {
                                LocalTime begin = LocalTime.parse(ofNullable(accountBenefit.getUpTime()).orElse("00:00:00"),
                                        DateTimeFormatter.ofPattern("HH:mm:ss"));
                                LocalTime end = LocalTime.parse(ofNullable(accountBenefit.getDownTime()).orElse("23:59:59"),
                                        DateTimeFormatter.ofPattern("HH:mm:ss"));
                                return begin.isBefore(LocalTime.now()) && end.isAfter(LocalTime.now());
                            }
                            return true;
                        })
                        .map(accountBenefit -> assembler.toOrderAccountBenefit(accountBenefit))
                        .collect(Collectors.toList());
                dto.setAccountBenefits(userAccountBenefits);

                List<Integer> classifies = accountRecords.stream().map(OrderBenefitRecord::getClassify)
                        .distinct().collect(Collectors.toList());
                Map<Integer, Integer> classifyWeight = accountBenefitRepository.listBenefitConsumeRule(query.getMerchantId(), classifies)
                        .stream()
                        .collect(Collectors.toMap(BenefitConsumeRule::getBenefitClassify, BenefitConsumeRule::getWeight, (a, b) -> b));
                accountRecords.forEach(r -> r.setClassifyWeight(classifyWeight.get(r.getClassify())));
                List<Long> benefitIds = accountBenefits.stream().map(AccountBenefit::getBenefitId).collect(Collectors.toList());
                List<Long> scopeBenefits = accountBenefitRepository.listBenefitScope(query.getMerchantId(), benefitIds)
                        .stream()
                        .map(BenefitScope::getBenefitId)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(scopeBenefits)) {
                    log.debug("场地限制权益列表: {}", scopeBenefits);
                    userAccountBenefits.forEach(e -> e.setGroupScope(scopeBenefits.contains(e.getBenefitId())));
                }
            }
        }
        return dto;
    }

    @Override
    public OrderBenefitCountDTO listOrderConsume(OrderBenefitRecordQueryDTO query){
        List<AccountRecord> list = accountRecordRepository.listRecordByOrder(assembler.toRecordQuery(query));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<OrderBenefitConsume> cosumeList = list.stream().map(record -> {
            OrderBenefitConsume consume = new OrderBenefitConsume();
            consume.setBenefitClassify(record.getBenefitClassify());
            BigDecimal actualBenefit = ofNullable(record.getActualBenefit()).orElse(ZERO);
            consume.setBenefitConsume(record.getMode().intValue() == 2?actualBenefit.negate():actualBenefit);
            return consume;
        }).collect(Collectors.toList());

        Map<Integer,List<OrderBenefitConsume>> consumeMap = cosumeList.stream().collect(Collectors.groupingBy(OrderBenefitConsume::getBenefitClassify));
        List<OrderBenefitConsume> consuleList = consumeMap.entrySet().stream().map(t->{
            OrderBenefitConsume benefitConsume = new OrderBenefitConsume();
            benefitConsume.setBenefitClassify(t.getKey());
            benefitConsume.setBenefitConsume(t.getValue().stream().map(e->ofNullable(e.getBenefitConsume()).orElse(BigDecimal.ZERO)).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            return benefitConsume;
        }).collect(Collectors.toList());
        OrderBenefitCountDTO dto = new OrderBenefitCountDTO();
        dto.setConsumeList(consuleList);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void benefitModify(AccountBenefitModifyDTO modify) {
        List<AccountBenefitAdjustDTO> adjustList = modify.getAdjustList();
        List<ConsumeDTO> consumeList = modify.getConsumeList();
        modifyHandleIncrement(adjustList);
        modifyHandleDecrement(consumeList);
    }

    @Override
    public void storeBenefitClear(StoreBenefitClearDTO param) {
        Assert.notNull(param.getUserId(),"用户ID不能为空");
        Assert.notNull(param.getMerchantId(),"商户ID不能为空");
        Assert.notNull(param.getClassifyType(),"权益分账类型不能为空");
        Assert.notNull(param.getStoreId(),"场地ID不能为空");
        Assert.notNull(param.getOperator(),"操作人不能为空");
        MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(param.getUserId(), param.getMerchantId());
        Assert.notNull(merchantUser,AccountErrorCode.ACCOUNT_NOT_FOUND.getMessage());
        List<Integer> classify = BenefitClassifyGroupEnum.getClassifyByType(param.getClassifyType());
        // 根据用户-商户加锁，重试时间3000ms，锁超时5000ms
        String lockKey = RedisKey.ACCOUNT_BENEFIT_UPDATE_LOCK
                + param.getMerchantId()
                + ":" + param.getUserId()
                + ":" + classify.get(0);
        if (redisLock.lock(lockKey, UserMemberSysConstants.YES, 5000, 3000) == null) {
            log.error("获取锁失败 -> {}", lockKey);
            throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_IS_UPDATING);
        }
        try {
            clearStoreScopeBenefit(param, merchantUser, classify);
        } finally {
            redisLock.unlock(lockKey, "Y");
        }
    }

    @Override
    public List<SmallVenueAccountInfoDTO> getAccountInfoByCardNos(Long merchantId, List<String> cardNos) {

        if (CollectionUtils.isEmpty(cardNos)) {
            return new ArrayList<>();
        }

        return accountMapper.getAccountInfoByCardNos(merchantId,cardNos);
    }

    @Override
    public List<MobileTerminalCardCountInfo> getSupplementaryCard(AccountSupplementaryCardQueryDTO dto) {

        if (CollectionUtils.isEmpty(dto.getAccountIds())) {
            return new ArrayList<>();
        }

        List<MobileTerminalCardCountInfo> mobileTerminalCardCountInfos = accountMapper.batchSelectSupplementaryCardCount(dto.getMerchantId(), dto.getAccountIds());

        return mobileTerminalCardCountInfos;

    }

    public void clearStoreScopeBenefit(StoreBenefitClearDTO param, MerchantUser merchantUser, List<Integer> classify) {
        LambdaQueryWrapper<AccountBenefit> queryWrapper = Wrappers.<AccountBenefit>lambdaQuery()
                .eq(AccountBenefit::getMerchantId, param.getMerchantId()).eq(AccountBenefit::getMerchantUserId, merchantUser.getId())
                .in(AccountBenefit::getClassify, classify);
        //查询商户用户指定场地下权益(无场地限制权益不查询)
        List<AccountBenefit> accountBenefitList = accountBenefitMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(accountBenefitList)) {
            return;
        }
        List<Long> benefitIds = accountBenefitList.stream()
                .filter(benefit -> benefit.getBalance().compareTo(ZERO) > 0 )
                .map(AccountBenefit::getBenefitId).distinct().collect(Collectors.toList());
        List<BenefitScope> benefitScopes = accountBenefitRepository.listBenefitScope(param.getMerchantId(), benefitIds);
        if (CollectionUtils.isEmpty(benefitScopes)) {
            return;
        }
        List<Long> currentStoreScopeBenefitIds = benefitScopes.stream()
                .filter(benefitScope -> Objects.equals(benefitScope.getApplicable(), ApplicableEnum.GROUP.getValue())
                        && Objects.equals(benefitScope.getAssociatedId(), param.getStoreId())).map(BenefitScope::getBenefitId).distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(currentStoreScopeBenefitIds)) {
            return;
        }
        List<AccountBenefit> clearBenefits = accountBenefitList.stream()
                .filter(benefit -> currentStoreScopeBenefitIds.contains(benefit.getBenefitId())).collect(Collectors.toList());
        List<Integer> clearClassifyList = clearBenefits.stream().map(AccountBenefit::getClassify).distinct().collect(Collectors.toList());
        List<Account> accounts = accountMapper.selectList(
                Wrappers.<Account>lambdaQuery().eq(Account::getMerchantId, param.getMerchantId()).eq(Account::getUserId, param.getUserId())
                        .in(Account::getClassify, clearClassifyList));
        if (CollectionUtils.isEmpty(accounts)) {
            throw new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND);
        }
        Map<Integer, Account> classifyAccountMap = accounts.stream()
                .collect(Collectors.toMap(Account::getClassify, Function.identity(), (v1, v2) -> v1));
        // 生成唯一订单号
        String orderNo = String.valueOf(snowflakeIdWorker.nextId());
        List<AccountRecord> accountRecords = new ArrayList<>();
        for (AccountBenefit clearBenefit : clearBenefits) {
            BigDecimal initBenefit = clearBenefit.getBalance();
            if (initBenefit.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            Account account = classifyAccountMap.get(clearBenefit.getClassify());
            if (Objects.isNull(account)) {
                throw new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND);
            }
            BigDecimal increment = initBenefit.negate();
            // 更新账户和权益的余额
            accountBenefitMapper.increaseBalanceAndTotal(clearBenefit.getId(), param.getMerchantId(), param.getOperator(), null, increment);
            accountMapper.increaseBalanceAndTotal(account.getId(), param.getMerchantId(), param.getOperator(), null, increment);
            //更新用户统计
            updateUserStatistics(account.getUserId(), account.getMerchantUserId(), account.getMerchantId(), clearBenefit.getClassify(), null, increment);
            //保存流水
            AccountRecord accountRecord = new AccountRecord();
            accountRecord.setAccountId(account.getId());
            accountRecord.setUserId(account.getUserId());
            accountRecord.setMerchantUserId(account.getMerchantUserId());
            accountRecord.setMerchantId(account.getMerchantId());
            accountRecord.setBenefitClassify(clearBenefit.getClassify());
            accountRecord.setBenefitId(clearBenefit.getBenefitId());
            accountRecord.setInitialBenefit(initBenefit);
            accountRecord.setOriginalBenefit(initBenefit);
            accountRecord.setActualBenefit(initBenefit);
            accountRecord.setOutTradeNo(orderNo);
            accountRecord.setOrderNo(orderNo);
            accountRecord.setMode(AdjustTypeEnum.DECREMENT.getType());
            accountRecord.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_All.getCode());
            StringBuilder sb = new StringBuilder();
            sb.append(AccountRecordTypeEnum.MERCHANT_ADJUST_All.getDesc()).append("扣减权益");
            String resourceDesc = sb.toString();
            accountRecord.setResource(resourceDesc);
            accountRecord.setCreatedby(ofNullable(param.getOperator()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
            accountRecord.setCreateTime(new Date());
            accountRecord.setDescription(accountRecord.getResource());
            accountRecord.setAccountBenefitId(clearBenefit.getId());
            accountRecords.add(accountRecord);
        }
        accountRecordMapper.insertBatch(accountRecords);
    }

    private void saveUserMerchantRelation(Long userId,Long merchantId,Long groupId){
        UserMerchantRelationDTO relationDTO = new UserMerchantRelationDTO();
        relationDTO.setMerchantId(merchantId);
        relationDTO.setUserId(userId);
        relationDTO.setGroupId(groupId);
        userMerchantRelationRpc.saveRelation(relationDTO);
    }
    private void modifyHandleDecrement(List<ConsumeDTO> consumeList) {
        if (CollectionUtils.isEmpty(consumeList)) {
            return;
        }
        for (ConsumeDTO consume : consumeList) {
            if (consume.getCheckBalance() == null) {
                consume.setCheckBalance(Boolean.TRUE);
            }
            if (consume.getAllowNegative() == null) {
                consume.setAllowNegative(Boolean.FALSE);
            }
            AccountServiceImpl proxy = (AccountServiceImpl) AopContext.currentProxy();
            proxy.benefitConsume(consume);
        }
    }

    private void modifyHandleIncrement(List<AccountBenefitAdjustDTO> adjustList) {
        if (CollectionUtils.isEmpty(adjustList)) {
            return;
        }
        for (AccountBenefitAdjustDTO adjust : adjustList) {
            boolean isAddTotal = adjust.getAddTotal() == null || adjust.getAddTotal();
            AccountServiceImpl proxy = (AccountServiceImpl) AopContext.currentProxy();
            proxy.benefitAdd(adjust, isAddTotal);
        }
    }

    private List<AccountClassifyBenefitDTO> getClassifyBenefitRule(AccountClassifyBenefitQueryDTO request, List<Integer> classifyList) {
        List<BenefitConsumeRule> benefitConsumeRules = benefitRepository.listBenefitConsumeRule(request.getMerchantId(), classifyList, Boolean.TRUE);
        List<AccountClassifyBenefitDTO> benefitGroupByClassify
                = CommonConverterTools.convert(AccountClassifyBenefitDTO.class, benefitConsumeRules);
        if (benefitGroupByClassify.isEmpty()) {
            //权益消耗规则兼容
            List<BenefitConsumeRule> consumeRules = benefitConsumeRuleService.getDefaultBenefitConsumeRule(classifyList);
            benefitGroupByClassify = CommonConverterTools.convert(AccountClassifyBenefitDTO.class, consumeRules);
        }
        if (benefitGroupByClassify.isEmpty()) {
            throw new BusinessException(AccountErrorCode.BENEFIT_CONSUME_RULE_MISS);
        }
        if (benefitGroupByClassify.size() != classifyList.size()) {
            classifyList.removeAll(benefitGroupByClassify.stream().map(AccountClassifyBenefitDTO::getBenefitClassify).collect(Collectors.toList()));
            if (classifyList.size() > 0) {
                List<BenefitConsumeRule> consumeRules = benefitConsumeRuleService.getDefaultBenefitConsumeRule(classifyList);
                benefitGroupByClassify.addAll(CommonConverterTools.convert(AccountClassifyBenefitDTO.class, consumeRules));
            }
        }
        return benefitGroupByClassify;
    }

    /**
     * 权益回退（根据消费记录）
     *
     * @param records     消费记录
     * @param rollbackDTO 退款参数
     * @return
     */
    private List<BenefitPreDeductionDTO> refundByRecord(List<AccountRecord> records,BenefitRollbackDTO rollbackDTO) {
        if (CollectionUtils.isEmpty(records)) {
            log.debug("消费记录为空,不进行回退");
            return new ArrayList<>();
        }
        List<BenefitPreDeductionDTO> list = new ArrayList<>();
        // 根据用户-商户加锁，重试时间3000ms，锁超时5000ms
        String lockKey = RedisKey.ACCOUNT_BENEFIT_UPDATE_LOCK
                + records.get(0).getMerchantId()
                + ":" + records.get(0).getUserId()
                + ":" + records.get(0).getBenefitClassify();
        if (redisLock.lock(lockKey, UserMemberSysConstants.YES, 5000, 3000) == null) {
            log.error("获取锁失败 -> {}", lockKey);
            throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_IS_UPDATING);
        }
        try {
            Date now = new Date();
            BigDecimal refundAmount = rollbackDTO.getRefundAmount();
            for (AccountRecord record : records) {
                //根据订单消费记录进行处理
                BigDecimal increment = record.getActualBenefit();
                if (refundAmount != null) {
                    if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                        if (refundAmount.compareTo(record.getActualBenefit()) > 0) {
                            refundAmount = refundAmount.subtract(record.getActualBenefit());
                        } else {
                            increment = refundAmount;
                            refundAmount = BigDecimal.ZERO;
                        }
                    } else {
                        //已退完
                        break;
                    }
                }
                AccountBenefit accountBenefit = null;
                if (record.getAccountBenefitId() == null) {
                    //查询最新一条accountBenefit数据，将扣费数据退款到那个明细中
                    accountBenefit = accountBenefitMapper.getAccountBenefit(record.getMerchantId(), record.getUserId(),
                            record.getMerchantUserId(), record.getBenefitId(), record.getAccountId());
                } else {
                    LambdaQueryWrapper<AccountBenefit> lambdaQueryWrapper = new QueryWrapper<AccountBenefit>().lambda()
                            .eq(AccountBenefit::getId, record.getAccountBenefitId())
                            .eq(AccountBenefit::getMerchantUserId, record.getMerchantUserId())
                            .eq(AccountBenefit::getUserId, record.getUserId())
                            .eq(AccountBenefit::getMerchantId, record.getMerchantId());
                    accountBenefit = accountBenefitMapper.selectOne(lambdaQueryWrapper);
                }
                if (accountBenefit == null) {
                    throw new BusinessException(AccountErrorCode.BENEFIT_NOT_FOUND);
                }
                Account account = accountMapper.selectOne(new QueryWrapper<>(new Account())
                        .eq("merchant_id", record.getMerchantId())
                        .eq("merchant_user_id", record.getMerchantUserId())
                        .eq("classify", record.getBenefitClassify())
                        .eq("id", record.getAccountId()));
                if (account == null) {
                    throw new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND);
                }
                if (AdjustTypeEnum.INCREMENT.getType().equals(record.getMode())) {
                    if (accountBenefit.getBalance().compareTo(BigDecimal.ZERO) > 0) {
                        if (accountBenefit.getBalance().compareTo(increment) < 0) {
                            increment = accountBenefit.getBalance();
                        }
                        // 更新账户和权益的余额
                        accountBenefitMapper.increaseBalanceAndTotal(accountBenefit.getId(), accountBenefit.getMerchantId(),
                                ofNullable(rollbackDTO.getOperator()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID),
                                increment.negate(), increment.negate());
                        accountMapper.increaseBalanceAndTotal(account.getId(), account.getMerchantId(),
                                ofNullable(rollbackDTO.getOperator()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID),
                                increment.negate(), increment.negate());
                        updateUserStatistics(account.getUserId(), account.getMerchantUserId(),account.getMerchantId(),
                                record.getBenefitClassify(), increment.negate(), increment.negate());
                    } else {
                        //权益不够,不退成负数,输出日志
                        log.warn("商户id:{},用户id:{},权益类型:{},账户权益:{},回退权益:{},权益回退不足,流水记录id:{}", accountBenefit.getMerchantId(),
                                record.getUserId(), record.getBenefitClassify(), accountBenefit.getBalance(), increment, record.getId());
                        continue;
                    }
                } else {
                    // 更新账户和权益的余额
                    accountBenefitMapper.increaseBalanceAndTotal(accountBenefit.getId(), accountBenefit.getMerchantId(),
                            ofNullable(rollbackDTO.getOperator()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID), null, increment);
                    accountMapper.increaseBalanceAndTotal(account.getId(), account.getMerchantId(),
                            ofNullable(rollbackDTO.getOperator()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID), null, increment);
                    updateUserStatistics(account.getUserId(), account.getMerchantUserId(),account.getMerchantId(),
                            record.getBenefitClassify(), null, increment);
                }
                BenefitPreDeductionDTO benefitPreDeductionDTO = new BenefitPreDeductionDTO();
                benefitPreDeductionDTO.setBenefitClassify(BenefitClassifyEnum.of(record.getBenefitClassify()));
                benefitPreDeductionDTO.setConsume(increment);
                benefitPreDeductionDTO.setAccountBenefitId(record.getAccountBenefitId());
                benefitPreDeductionDTO.setSort(record.getSort());
                list.add(benefitPreDeductionDTO);

                //增加消费记录
                AccountRecord accountRecord = getAccountRecord(rollbackDTO, record, account, accountBenefit, increment, now);
                accountRecordMapper.insert(accountRecord);
            }
        } finally {
            redisLock.unlock(lockKey, "Y");
        }
        log.debug("根据订单进行回退,回退明细:{}", list);
        return list;
    }

    private static AccountRecord getAccountRecord(BenefitRollbackDTO rollbackDTO, AccountRecord record, Account account, AccountBenefit accountBenefit, BigDecimal increment, Date now) {
        AccountRecord accountRecord = new AccountRecord();
        accountRecord.setAccountId(account.getId());
        accountRecord.setUserId(account.getUserId());
        accountRecord.setMerchantUserId(record.getMerchantUserId());
        accountRecord.setMerchantId(record.getMerchantId());
        accountRecord.setStoreId(record.getStoreId());
        accountRecord.setEquipmentId(record.getEquipmentId());
        accountRecord.setBenefitClassify(account.getClassify());
        accountRecord.setBenefitId(accountBenefit.getBenefitId());
        accountRecord.setInitialBenefit(account.getBalance());
        accountRecord.setOriginalBenefit(increment.abs());
        accountRecord.setActualBenefit(increment.abs());
        accountRecord.setOutTradeNo(record.getOutTradeNo());
        accountRecord.setOrderNo(record.getOrderNo());
        if (AdjustTypeEnum.INCREMENT.getType().equals(record.getMode())) {
            accountRecord.setMode(AdjustTypeEnum.DECREMENT.getType());
        } else {
            accountRecord.setMode(AdjustTypeEnum.INCREMENT.getType());
        }
        accountRecord.setCreateTime(now);
        accountRecord.setResource(rollbackDTO.getResource());
        accountRecord.setDescription("权益退回");
        accountRecord.setTradeType(record.getTradeType());
        accountRecord.setTradeAmount(record.getTradeAmount());
        accountRecord.setCommodityName(record.getCommodityName());
        if (!StringUtils.isEmpty(record.getStoreName())) {
            // 只取32位，避免过长保存报错
            accountRecord.setStoreName(LyyStringUtil.substring(record.getStoreName(), 32));
        }
        accountRecord.setEquipmentTypeId(record.getEquipmentTypeId());
        accountRecord.setEquipmentTypeName(record.getEquipmentTypeName());
        accountRecord.setEquipmentValue(record.getEquipmentValue());
        accountRecord.setRecordType(rollbackDTO.getRecordType());
        accountRecord.setAccountBenefitId(accountBenefit.getId());
        return accountRecord;
    }

    @Override
    public Boolean payRefundRollbackBenefit(PayRefundBenefitDTO refundBenefitDTO) {
        MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(refundBenefitDTO.getUserId(), refundBenefitDTO.getMerchantId());
        Assert.notNull(merchantUser, AccountErrorCode.ACCOUNT_NOT_FOUND.getMessage());

        String orderNo = refundBenefitDTO.getOrderNo();
        //默认只查一个月内的
        List<AccountRecord> recordList = accountRecordMapper.findAllByOrderNo(orderNo, refundBenefitDTO.getMerchantId(),
                refundBenefitDTO.getUserId(), merchantUser.getId(), null, INCREMENT.getType(),
                WxAuthUpdateUtils.getLocalDateTimeModifyMonthsDate(LocalDateTime.now(),-1),null);
        if (CollectionUtils.isEmpty(recordList)) {
            log.warn("支付退款退权益-orderNo:{}没有消费记录,{},{}", orderNo, refundBenefitDTO.getMerchantId(), refundBenefitDTO.getUserId());
            return false;
        }
        if (recordList.get(0).getAccountBenefitId() == null) {
            AccountServiceImpl proxy = (AccountServiceImpl) AopContext.currentProxy();

            proxy.benefitConsume(convertOldConsume(refundBenefitDTO));
            return true;
        }
        //找回充值消费记录
        Map<Integer, List<AccountRecord>> classifyRecordMap = recordList.stream().collect(Collectors.groupingBy(AccountRecord::getBenefitClassify));
        List<Integer> classify;
        if (refundBenefitDTO.getAllRollBack() != null && refundBenefitDTO.getAllRollBack()) {
            classify = recordList.stream().map(AccountRecord::getBenefitClassify).collect(Collectors.toList());
        } else {
            classify = refundBenefitDTO.getRefundBenefitDetails().stream().map(PayRefundBenefitDetailDTO::getClassify).collect(Collectors.toList());
        }
        //目标权益类型的权益集合
        List<AccountBenefitWithScopeDTO> benefitList = queryBenefitForConsume(merchantUser.getId(), merchantUser.getMerchantId(),classify,
                null, null,false, null);
        if (CollectionUtils.isEmpty(benefitList)) {
            log.warn("查询权益集合数据为空,merchantUserId:{} ,classify:{}",merchantUser.getId(),classify);
            return true;
        }
        List<AccountBenefitConsumeDTO> benefitGroupByClassify = getAccountBenefitConsumeList(merchantUser.getMerchantId(), benefitList);
        Map<Integer, List<AccountBenefitConsumeDTO>> classifyBenefitGroup = benefitGroupByClassify.stream().collect(Collectors.groupingBy(AccountBenefitConsumeDTO::getBenefitClassify));
        refundBenefitDTO.getRefundBenefitDetails().forEach(benefitDetailDTO -> {
            rollbackByRecord(merchantUser, refundBenefitDTO, benefitDetailDTO, classifyRecordMap, classifyBenefitGroup);
        });
        return true;
    }

    /**
     * 兼容旧支付充值退款退权益处理
     * @param refundBenefitDTO
     * @return
     */
    private ConsumeDTO convertOldConsume(PayRefundBenefitDTO refundBenefitDTO) {
        log.debug("兼容旧支付充值退款退权益处理,{}", refundBenefitDTO.getOrderNo());
        ConsumeDTO consume = CommonConverterTools.convert(ConsumeDTO.class, refundBenefitDTO);
        //扣权益，若不够扣，应该全部扣完
        consume.setCheckBalance(false);
        consume.setAllowNegative(false);
        //组装消耗信息
        List<ConsumeDetailDTO> consumeDetailDTOList = new ArrayList<>();
        for (PayRefundBenefitDetailDTO benefitDetail : refundBenefitDTO.getRefundBenefitDetails()) {
            ConsumeDetailDTO consumeDetailDTO = new ConsumeDetailDTO();
            consumeDetailDTO.setAmount(benefitDetail.getAmount());
            if(benefitDetail.getBenefitId() != null){
                consumeDetailDTO.setBenefitId(Collections.singletonList(benefitDetail.getBenefitId()));
            }else if(benefitDetail.getClassify() != null) {
                consumeDetailDTO.setClassify(Collections.singletonList(benefitDetail.getClassify()));
            }
            consumeDetailDTOList.add(consumeDetailDTO);
        }
        consume.setConsume(consumeDetailDTOList);
        return consume;
    }

    /**
     * 支付退款退权益
     * @param merchantUser 商户用户信息
     * @param refundBenefitDTO 退款权益参数
     * @param benefitDetailDTO 权益详细信息
     * @param classifyRecordMap
     * @param classifyBenefitGroup
     */
    private void rollbackByRecord(MerchantUser merchantUser, PayRefundBenefitDTO refundBenefitDTO, PayRefundBenefitDetailDTO benefitDetailDTO,
                                  Map<Integer, List<AccountRecord>> classifyRecordMap, Map<Integer, List<AccountBenefitConsumeDTO>> classifyBenefitGroup) {
        Integer classify = benefitDetailDTO.getClassify();
        Account account = accountMapper.selectOne(new QueryWrapper<>(new Account())
                .eq("merchant_id", merchantUser.getMerchantId())
                .eq("user_id", merchantUser.getUserId())
                .eq("classify", classify));
        if (account == null) {
            throw new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND);
        }
        List<AccountRecord> accountRecords = classifyRecordMap.get(classify);
        log.debug("支付退款退权益-order_no:{} 对应消费记录size:{}", refundBenefitDTO.getOrderNo(), refundBenefitDTO.getOrderNo());
        if (CollectionUtils.isEmpty(accountRecords)) {
            log.error("支付退款退权益-classify:{} 找不到对应消费记录:{}", classify, refundBenefitDTO.getOrderNo());
            return;
        }
        List<Long> accountBenefitIds = accountRecords.stream().map(AccountRecord::getAccountBenefitId).collect(Collectors.toList());
        // 根据用户-商户加锁，重试时间3000ms，锁超时5000ms
        String lockKey = RedisKey.ACCOUNT_BENEFIT_PAY_REFUND_ROLLBACK_LOCK
                + merchantUser.getMerchantId()
                + ":" + merchantUser.getUserId()
                + ":" + benefitDetailDTO.getClassify();
        if (redisLock.lock(lockKey, UserMemberSysConstants.YES, 5000, 3000) == null) {
            log.error("支付退款退权益-获取锁失败 -> {}", lockKey);
            throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_ROLLBACK_FAIL);
        }
        try {
            Date now = new Date();
            QueryWrapper<AccountBenefit> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("id", accountBenefitIds)
                    .eq("merchant_id", merchantUser.getMerchantId());
            // TODO: 2021/11/25 旧充值退款的怎么处理
            List<AccountBenefit> result = accountBenefitMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(result)) {
                log.warn("支付退款退权益-result为空,order_no:{}", refundBenefitDTO.getOrderNo());
                return;
            }
            //单一权益账户明细
            List<AccountBenefitConsumeDTO> benefitConsumeDTOS = classifyBenefitGroup.get(classify);
            log.info("单一权益账户明细:{} size:{}", refundBenefitDTO.getOrderNo(), benefitConsumeDTOS == null ? null : benefitConsumeDTOS.size());
            if (CollectionUtils.isEmpty(benefitConsumeDTOS)) {
                return;
            }
            //待扣除的绝对值
            BigDecimal remain = benefitDetailDTO.getAmount().abs();
            Boolean modifyTotal = ofNullable(refundBenefitDTO.getModifyTotal()).orElse(Boolean.FALSE);
            AccountBenefitConsumeDTO benefitConsumeDTO = benefitConsumeDTOS.get(0);
            if (remain.compareTo(BigDecimal.ZERO) == 0) {
                return;
            }
            //可以使用权益集合(包括本权益)
            List<AccountBenefitWithScopeDTO> benefitWithScope = getEnableUseBenefitWithScopeDTOList(refundBenefitDTO, benefitConsumeDTO);

            for (AccountRecord record : accountRecords) {
                //根据订单消费记录进行处理
                if (record.getBenefitId() == 0) {
                    log.warn("支付退款退权益-benefitId为0,消费记录ID:{}", record.getId());
                    continue;
                }
                //订单权益ID相同集合
                List<AccountBenefitWithScopeDTO> sameBenefitList = benefitWithScope.stream().filter(e -> record.getBenefitId().equals(e.getBenefitId())).collect(Collectors.toList());
                // 可以返回一个list,多次更新account_benefit 一次更新 account
                if (!CollectionUtils.isEmpty(sameBenefitList)) {
                    for (AccountBenefitWithScopeDTO e : sameBenefitList) {
                        // 账户权益为负数，依旧允许退款
                        if (e.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }
                        if (e.getAmount().compareTo(remain) < 0) {
                            decreaseBenefitFromPayRefund(merchantUser, account, e, refundBenefitDTO, now, e.getAmount(), modifyTotal, "本权益回退权益:" + e.getAmount());
                            remain = remain.subtract(e.getAmount());
                            continue;
                        }
                        decreaseBenefitFromPayRefund(merchantUser, account, e, refundBenefitDTO, now, remain, modifyTotal, "本权益回退权益:" + remain);
                        return;
                    }
                }
                benefitWithScope.removeAll(sameBenefitList);

                if (!CollectionUtils.isEmpty(benefitWithScope)) {
                    //排序，时间最早优先处理
                    benefitWithScope.sort(Comparator.comparing(AccountBenefitWithScopeDTO::getCreateTime));
                    for (AccountBenefitWithScopeDTO e : benefitWithScope) {
                        if (e.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }
                        if (e.getAmount().compareTo(remain) < 0) {
                            decreaseBenefitFromPayRefund(merchantUser, account, e, refundBenefitDTO, now, e.getAmount(), modifyTotal, "场地通用权益回退权益:" + e.getAmount());
                            remain = remain.subtract(e.getAmount());
                            continue;
                        }
                        decreaseBenefitFromPayRefund(merchantUser, account, e, refundBenefitDTO, now, remain, modifyTotal, "场地通用权益回退权益:" + remain);
                        return;
                    }
                }
                if (remain.compareTo(BigDecimal.ZERO) > 0) {
                    log.info("支付充值退款退权益仍有未退,order_no:{} classify:{} remain:{}", refundBenefitDTO.getOrderNo(), benefitDetailDTO.getClassify(), remain);
                }
            }
        } finally {
            redisLock.unlock(lockKey, "Y");
        }
    }

    private List<AccountBenefitWithScopeDTO> getEnableUseBenefitWithScopeDTOList(PayRefundBenefitDTO refundBenefitDTO, AccountBenefitConsumeDTO benefitConsumeDTO) {
        ConsumeDTO consume = new ConsumeDTO();
        consume.setStoreId(refundBenefitDTO.getStoreId());
        consume.setEquipmentId(refundBenefitDTO.getEquipmentId());
        consume.setEquipmentTypeId(refundBenefitDTO.getEquipmentTypeId());
        consume.setCommodityId(refundBenefitDTO.getCommodityId());
        //通用场地权益集合  可以在前面一次性处理
        return benefitConsumeDTO.getBenefitWithScope().stream().filter(e -> {
            if (!checkScope(e.getScopeMap(), consume)) {
                log.debug("判断账户权益范围不可用,benefit:{}", e.getBenefitId());
                return false;
            }
            return true;
        }).collect(Collectors.toList());
    }

    /**
     * 支付退款扣减权益
     *  @param merchantUser
     * @param account
     * @param benefitWithScopeDTO
     * @param refundBenefitDTO
     * @param now
     * @param decreaseBalance 扣减金额
     * @param modifyTotal  是否修改total值
     */
    private void decreaseBenefitFromPayRefund(MerchantUser merchantUser, Account account, AccountBenefitWithScopeDTO benefitWithScopeDTO,
                                              PayRefundBenefitDTO refundBenefitDTO, Date now, BigDecimal decreaseBalance, Boolean modifyTotal, String description) {

        log.debug("支付退款退权益-decreaseBenefitFromPayRefund,order_no:{} ,accountBenefitId:{} decrease:{} ",
                refundBenefitDTO.getOrderNo(),benefitWithScopeDTO.getAccountBenefitId(),decreaseBalance);
        //确保处理为负数
        decreaseBalance = decreaseBalance.abs().negate();
        BigDecimal total = ofNullable(modifyTotal).orElse(false) ? decreaseBalance: BigDecimal.ZERO ;
        // 更新账户和权益的余额
        accountBenefitMapper.increaseBalanceAndTotal(benefitWithScopeDTO.getAccountBenefitId(), merchantUser.getMerchantId(), refundBenefitDTO.getOperator(), total, decreaseBalance);
        accountMapper.increaseBalanceAndTotal(account.getId(), account.getMerchantId(), refundBenefitDTO.getOperator(), total, decreaseBalance);

        updateUserStatistics(merchantUser.getUserId(), merchantUser.getId(), merchantUser.getMerchantId(),
                account.getClassify(), total, decreaseBalance);

        //增加消费记录
        AccountRecord accountRecord = new AccountRecord();
        accountRecord.setAccountId(account.getId());
        accountRecord.setUserId(account.getUserId());
        accountRecord.setMerchantUserId(merchantUser.getId());
        accountRecord.setMerchantId(merchantUser.getMerchantId());
        accountRecord.setStoreId(refundBenefitDTO.getStoreId());
        accountRecord.setEquipmentId(refundBenefitDTO.getEquipmentId());
        accountRecord.setBenefitClassify(account.getClassify());
        accountRecord.setBenefitId(benefitWithScopeDTO.getBenefitId());
        accountRecord.setInitialBenefit(account.getBalance());
        accountRecord.setOriginalBenefit(decreaseBalance.abs());
        accountRecord.setActualBenefit(decreaseBalance.abs());
        accountRecord.setOutTradeNo(refundBenefitDTO.getOutTradeNo());
        accountRecord.setOrderNo(refundBenefitDTO.getOrderNo());
        accountRecord.setMode(AdjustTypeEnum.DECREMENT.getType());
        accountRecord.setCreateTime(now);
        accountRecord.setResource(refundBenefitDTO.getResource());
        accountRecord.setDescription(description);
        accountRecord.setTradeType(refundBenefitDTO.getTradeType());
        accountRecord.setTradeAmount(refundBenefitDTO.getTradeAmount());
        accountRecord.setCommodityName(refundBenefitDTO.getCommodityName());
        if (!StringUtils.isEmpty(refundBenefitDTO.getStoreName())) {
            // 只取32位，避免过长保存报错
            accountRecord.setStoreName(LyyStringUtil.substring(refundBenefitDTO.getStoreName(), 32));
        }
        accountRecord.setEquipmentTypeId(refundBenefitDTO.getEquipmentTypeId());
        accountRecord.setEquipmentTypeName(refundBenefitDTO.getEquipmentTypeName());
        accountRecord.setEquipmentValue(refundBenefitDTO.getEquipmentValue());
        accountRecord.setRecordType(refundBenefitDTO.getRecordType());
        accountRecord.setAccountBenefitId(benefitWithScopeDTO.getAccountBenefitId());
        //账号
        account.setBalance(account.getBalance().add(decreaseBalance.abs().negate()));

        accountRecordMapper.insert(accountRecord);
    }


    /**
     * 保存消费记录
     *
     * @param recordDTO
     * @return
     */
    @Override
    public Boolean saveRecord(AccountRecordSaveDTO recordDTO) {
        AccountRecord accountRecord = new AccountRecord();
        BeanUtils.copyProperties(recordDTO, accountRecord);
        return saveAccountRecord(accountRecord);
    }

    /**
     * 保存消费记录
     *
     * @param accountRecord
     * @return
     */
    private Boolean saveAccountRecord(AccountRecord accountRecord) {
        if (accountRecord.getMerchantUserId() == null) {
            //根据商户ID和用户id获取商户用户信息
            MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(accountRecord.getUserId(), accountRecord.getMerchantId());
            if (merchantUser == null) {
                log.warn("保存消费记录获取商户用户失败,userId:{},merchantId:{}", accountRecord.getUserId(), accountRecord.getMerchantId());
                throw new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR);
            }
            accountRecord.setMerchantUserId(merchantUser.getId());
        }
        if (accountRecord.getAccountId() == null) {
            //获取用户对应的账号权益
            Account account = accountMapper.selectOne(new QueryWrapper<>(new Account())
                    .eq("merchant_id", accountRecord.getMerchantId())
                    .eq("user_id", accountRecord.getUserId())
                    .eq("merchant_user_id", accountRecord.getMerchantUserId())
                    .eq("classify", accountRecord.getBenefitClassify()));
            accountRecord.setAccountId(ofNullable(account).map(Account::getId).orElse(0L));
        }

        if (accountRecord.getBenefitId() == null) {
            //获取权益明细
            AccountBenefit accountBenefit = accountBenefitMapper.selectOne(new QueryWrapper<>(new AccountBenefit())
                    .eq("account_id", accountRecord.getAccountId())
                    .eq("merchant_id", accountRecord.getMerchantId())
                    .eq("user_id", accountRecord.getUserId())
                    .eq("merchant_user_id", accountRecord.getMerchantUserId())
                    .eq("classify", accountRecord.getBenefitClassify())
                    .eq("status", 1));
            accountRecord.setBenefitId(ofNullable(accountBenefit).map(AccountBenefit::getBenefitId).orElse(0L));
        }

        if(accountRecord.getCreateTime() == null) {
            accountRecord.setCreateTime(new Date());
        }
        if (!StringUtils.isEmpty(accountRecord.getStoreName())) {
            // 只取32位，避免过长保存报错
            accountRecord.setStoreName(LyyStringUtil.substring(accountRecord.getStoreName(), 32));
        }
        //自动打标签
        TagUserParamDTO tagUserParamDTO = new TagUserParamDTO();
        tagUserParamDTO.setTradeType(accountRecord.getTradeType());
        tagUserParamDTO.setUserType(null);
        tagUserParamDTO.setUserId(accountRecord.getUserId());
        tagUserParamDTO.setStoreName(accountRecord.getStoreName());
        tagUserParamDTO.setMerchantUserId(accountRecord.getMerchantUserId());
        tagUserParamDTO.setMerchantId(accountRecord.getMerchantId());
        log.info("自动打标签：{}", tagUserParamDTO);
        merchantAutoTagAsyncHandler.autoCreateTag(tagUserParamDTO);

        return accountRecordMapper.insert(accountRecord) > 0;
    }

    private void handleZeroConsume(ConsumeDTO consume, MerchantUser merchantUser) {
        log.info("0元启动:{}", consume.getEquipmentValue());
        // 保存变更流水
        AccountRecord accountRecord = new AccountRecord();
        //0元启动置0
        accountRecord.setAccountId(0L);
        accountRecord.setUserId(merchantUser.getUserId());
        accountRecord.setMerchantUserId(merchantUser.getId());
        accountRecord.setMerchantId(merchantUser.getMerchantId());
        accountRecord.setStoreId(consume.getStoreId());
        accountRecord.setEquipmentId(consume.getEquipmentId());
        accountRecord.setBenefitClassify(BenefitClassifyEnum.FREE_TO_USE.getCode());
        //0元启动置0
        accountRecord.setBenefitId(0L);
        accountRecord.setInitialBenefit(BigDecimal.ZERO);
        accountRecord.setOriginalBenefit(BigDecimal.ZERO);
        accountRecord.setActualBenefit(BigDecimal.ZERO);
        accountRecord.setOutTradeNo(consume.getOutTradeNo());
        accountRecord.setOrderNo(consume.getOrderNo());
        accountRecord.setMode(AdjustTypeEnum.DECREMENT.getType());
        accountRecord.setCreateTime(new Date());
        accountRecord.setResource(consume.getResource());
        accountRecord.setDescription(consume.getDescription());

        accountRecord.setTradeType(consume.getTradeType());
        accountRecord.setTradeAmount(consume.getTradeAmount());
        accountRecord.setCommodityName(consume.getCommodityName());
        if (!StringUtils.isEmpty(consume.getStoreName())) {
            // 只取32位，避免过长保存报错
            accountRecord.setStoreName(LyyStringUtil.substring(consume.getStoreName(), 32));
        }
        accountRecord.setEquipmentTypeId(consume.getEquipmentTypeId());
        accountRecord.setEquipmentTypeName(consume.getEquipmentTypeName());
        accountRecord.setEquipmentValue(consume.getEquipmentValue());
        accountRecord.setGroupNumber(consume.getGroupNumber());
        boolean fwjZeroStart = checkFWJfreeToStart(consume.getRecordType());
        accountRecord.setRecordType(fwjZeroStart?consume.getRecordType():AccountRecordTypeEnum.FREE_TO_USE.getCode());
        //自动打支付类型标签
        TagUserParamDTO tagUserParamDTO = new TagUserParamDTO();
        tagUserParamDTO.setTradeType(accountRecord.getTradeType());
        tagUserParamDTO.setUserType(null);
        tagUserParamDTO.setStoreName(accountRecord.getStoreName());
        tagUserParamDTO.setUserId(merchantUser.getUserId());
        tagUserParamDTO.setMerchantUserId(merchantUser.getId());
        tagUserParamDTO.setMerchantId(merchantUser.getMerchantId());
        merchantAutoTagAsyncHandler.autoCreateTag(tagUserParamDTO);
        accountRecordMapper.insert(accountRecord);
    }

    private boolean checkFWJfreeToStart(Integer recordType) {
        if (AccountRecordTypeEnum.EQUIP_START_BARRAGE_MESSAGE.getCode().equals(recordType)
        ||AccountRecordTypeEnum.EQUIP_START_ABSOLUTE_HIT.getCode().equals(recordType)
        ||AccountRecordTypeEnum.EQUIP_START_LIKE.getCode().equals(recordType)
        ||AccountRecordTypeEnum.EQUIP_START_HEART.getCode().equals(recordType)
        ||AccountRecordTypeEnum.EQUIP_START_FIGHTING.getCode().equals(recordType)
        ||AccountRecordTypeEnum.EQUIP_START_SIXTY_SIX.getCode().equals(recordType)

        ||AccountRecordTypeEnum.RECHARGE_FWJ_BARRAGE_MESSAGE.getCode().equals(recordType)
        ||AccountRecordTypeEnum.RECHARGE_ABSOLUTE_HIT.getCode().equals(recordType)
        ||AccountRecordTypeEnum.RECHARGE_LIKE.getCode().equals(recordType)
        ||AccountRecordTypeEnum.RECHARGE_HEART.getCode().equals(recordType)
        ||AccountRecordTypeEnum.RECHARGE_FIGHTING.getCode().equals(recordType)
        ||AccountRecordTypeEnum.RECHARGE_SIXTY_SIX.getCode().equals(recordType)
        ) {
            return true;
        }
        return false;

    }


    /**
     * 处理商家券
     * @param consume
     * @param merchantUser
     * @param consumeDetailDTO
     * @param deduction  true 真实扣减
     * @return
     */
    private AccountBenefitAdjustDTO handleMerchantCoupon(ConsumeDTO consume,MerchantUser merchantUser ,ConsumeDetailDTO consumeDetailDTO,boolean deduction) {
        //商家券单独一个
        Integer classify = consumeDetailDTO.getClassify().get(0);
        if (!BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON.getCode().equals(classify)) {
            return null;
        }
        AccountServiceImpl accountServiceImpl = (AccountServiceImpl)AopContext.currentProxy();
        Account account = accountServiceImpl.consumeCoupon(consume, merchantUser, consumeDetailDTO, deduction, classify);
        if (account == null) {
            return null;
        }
        AccountBenefitAdjustDTO coupon = new AccountBenefitAdjustDTO();
        coupon.setAccountId(account.getId());
        coupon.setAdjustType(AdjustTypeEnum.DECREMENT);
        coupon.setAmount(consumeDetailDTO.getAmount());
        coupon.setClassify(classify);
        coupon.setUserId(consume.getUserId());
        coupon.setMerchantId(consume.getMerchantId());
        coupon.setMerchantUserId(merchantUser.getId());
        coupon.setStoreId(consume.getStoreId());
        coupon.setEquipmentId(consume.getEquipmentId());
        coupon.setOutTradeNo(consume.getOutTradeNo());
        coupon.setOrderNo(consume.getOrderNo());
        coupon.setResource(consume.getResource());
        coupon.setOperator(consume.getOperator());
        return coupon;
    }

    @Klock(keys = "'user-member:klock:couponAdjust-'+#consume.merchantId+'_'+#merchantUser.id+'_'+#classify", waitTime = 5)
    public Account consumeCoupon(ConsumeDTO consume, MerchantUser merchantUser, ConsumeDetailDTO consumeDetailDTO, boolean deduction,
            Integer classify) {
        Account account = getCouponAccount(consume.getMerchantId(), merchantUser.getId(), consume.getUserId());
        if (account == null) {
            return null;
        }
        if (account.getBalance().compareTo(consumeDetailDTO.getAmount()) < 0) {
            log.warn("商家券账户权益不足以抵扣,账户券数据:{} 使用券数量:{}",account.getBalance(), consumeDetailDTO.getAmount());
            return null;
        }
        if (deduction) {
            BigDecimal balance = account.getBalance().subtract(consumeDetailDTO.getAmount());
            UpdateWrapper wrapper = new UpdateWrapper<AccountBenefit>()
                    .set("balance", balance)
                    .set("update_time",new Date())
                    .set("update_by", consume.getOperator()==null?UserMemberSysConstants.DEFAULT_OPERATION_USER_ID: consume.getOperator())
                    .eq("merchant_id", consume.getMerchantId())
                    .eq("id",account.getId());

            boolean handerFlag =  SqlHelper.retBool(accountMapper.update(null,wrapper));
            if(handerFlag){
                log.debug("商家券消耗处理成功");
            }
        }
        return account;
    }

    @Override
    public Map<Integer, BigDecimal> benefitCount(AccountQueryDTO param) {
        Long userId = param.getUserId();
        Long merchantId = param.getMerchantId();
        if (param.getMerchantUserId() == null) {
            //根据商户ID和用户id获取商户用户信息
            MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(userId, merchantId);
            if (merchantUser == null) {
                log.warn("权益数量获取商户用户失败,userId:{},merchantId:{}", userId, merchantId);
                throw new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR);
            }
            param.setMerchantUserId(merchantUser.getId());
        }
        // 查询出指定类型的所有权益记录
        List<Integer> benefitClassify = param.getBenefitClassify();
        List<AccountBenefitWithScopeDTO> list = this.queryBenefitForConsume(param.getMerchantUserId(), merchantId,
                benefitClassify, null, param.getExcludeClassify(), false, null);
        // 根据条件筛选出适用的权益
        if (!CollectionUtils.isEmpty(param.getEquipmentTypeIds()) || !CollectionUtils.isEmpty(param.getStoreIds())) {
            list = list.stream()
                    .filter(item -> checkScope(item.getScopeMap(), param.getEquipmentTypeIds(), param.getStoreIds(), null, null))
                    .collect(Collectors.toList());
        }
        Map<Integer, BigDecimal> result = new HashMap<>(benefitClassify.size());
        for (Integer classify : benefitClassify) {
            if (BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON.getCode().equals(classify)) {
                result.put(classify,countMerchantUserAccount(param));
            } else {
                result.put(classify, list.stream().filter(item -> Objects.equals(item.getClassify(), classify))
                        .map(AccountBenefitWithScopeDTO::getAmount)
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            }

        }
        return result;
    }


    /**
     * 查券数量
     * @param param
     * @return
     */
    private BigDecimal countMerchantUserAccount(AccountQueryDTO param) {
        Account account = getCouponAccount(param.getMerchantId(), param.getMerchantUserId(), param.getUserId());
        if (account == null) {
            return BigDecimal.ZERO;
        }
        return account.getBalance();
    }

    private Account getCouponAccount(Long merchantId, Long merchantUserId, Long userId) {
        Account account = null;
        if (Objects.isNull(merchantUserId) && Objects.isNull(userId)) {
            return null;
        }
        LambdaQueryWrapper<Account> queryWrapper = Wrappers.<Account>lambdaQuery()
                .eq(Account::getMerchantId, merchantId)
                .eq(Objects.nonNull(merchantUserId), Account::getMerchantUserId, merchantUserId)
                .eq(Objects.nonNull(userId), Account::getUserId, userId)
                .eq(Account::getClassify, BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON.getCode());
        try {
            account = accountMapper.selectOne(queryWrapper);
        } catch (MybatisPlusException exception) {
            log.error("查询券账户异常", exception);
            if (exception.getMessage().contains("One record is expected, but the query result is multiple records")) {
                log.error("券账户重复,merchantId:{},userId:{},merchantUserId:{}", merchantId, userId, merchantUserId);
                List<Account> accounts = accountMapper.selectList(queryWrapper);
                List<Long> deleteAccountIds = accounts.stream().skip(1L).map(Account::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(deleteAccountIds)) {
                    log.info("删除重复券账户,accountIds:{}", deleteAccountIds);
                    LambdaQueryWrapper<Account> deleteWrapper = Wrappers.<Account>lambdaQuery()
                            .eq(Account::getMerchantId, merchantId)
                            .eq(Objects.nonNull(merchantUserId), Account::getMerchantUserId, merchantUserId)
                            .eq(Objects.nonNull(userId), Account::getUserId, userId)
                            .in(Account::getId, deleteAccountIds);
                    accountMapper.delete(deleteWrapper);
                }
                account = accounts.get(0);
            }
        }
        return account;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AccountBenefitResultDTO> clearBalanceAndCoin(AccountBenefitQueryDTO param) {
        if (CollectionUtils.isEmpty(param.getClassify())) {
            return Collections.emptyList();
        }
        //只允许余额、余币类型的权益，但不能清除延迟结算的余额，余币
        param.getClassify().remove(BenefitClassifyEnum.DELAYED_SETTELEMENT_COIN.getCode());
        param.getClassify().remove(BenefitClassifyEnum.DELAYED_SETTELEMENT_BALANCE.getCode());
        if (CollectionUtils.isEmpty(param.getClassify())) {
            return Collections.emptyList();
        }
        Assert.isTrue(ALL_COINS_AND_MONEY_CLASSIFY.containsAll(param.getClassify()), "illegal value of classify.");

        //获取条件匹配的权益
        List<AccountBenefitDTO> accountBenefits = accountBenefitMapper.allBenefitRecord(param);
        List<AccountBenefitAdjustDTO> adjustDTOS = accountBenefits.stream()
                .filter(ab -> ab.getBalance().compareTo(BigDecimal.ZERO) > 0)
                .map(ab -> {
                    AccountBenefitAdjustDTO adjustDTO = new AccountBenefitAdjustDTO();
                    adjustDTO.setId(ab.getId());
                    adjustDTO.setMerchantId(param.getMerchantId());
                    adjustDTO.setMerchantUserId(param.getMerchantUserId());
                    adjustDTO.setClassify(ab.getClassify());
                    adjustDTO.setAmount(ab.getBalance());
                    adjustDTO.setDescription("商家清除权益");
                    adjustDTO.setAdjustType(AdjustTypeEnum.DECREMENT);
                    adjustDTO.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_All.getCode());
                    adjustDTO.setOperator(param.getOperatorId());
                    return adjustDTO;
                }).collect(Collectors.toList());
        AccountServiceImpl proxy = (AccountServiceImpl) AopContext.currentProxy();
        proxy.decreaseAccountBenefit(adjustDTOS, true, null);

        return processingBenefit(param, accountBenefits);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void benefitAdd(AccountBenefitAdjustDTO accountBenefitAdjustDTO,boolean isAddTotal) {

        checkParam(accountBenefitAdjustDTO);
        UserInfoDTO userInfoDTO = userService.getUserInfoNoTagByUserIdAndMerchantId(accountBenefitAdjustDTO.getUserId(),accountBenefitAdjustDTO.getMerchantId());
        Assert.notNull(userInfoDTO,"商户用户信息不能为空");
        if(adjustCouponBenefit(accountBenefitAdjustDTO,userInfoDTO,isAddTotal)){
            log.debug("券权益单独处理");
            return;
        }
        // 根据用户-商户加锁，重试时间3000ms，锁超时5000ms
        String lockKey = RedisKey.ACCOUNT_BENEFIT_UPDATE_LOCK
                + accountBenefitAdjustDTO.getMerchantId()
                + ":" + accountBenefitAdjustDTO.getUserId()
                + ":" + accountBenefitAdjustDTO.getClassify();
        if (redisLock.lock(lockKey, UserMemberSysConstants.YES, 5000, 3000) == null) {
            log.error("获取锁失败 -> {}", lockKey);
            throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_IS_UPDATING);
        }

        Long storeId = accountBenefitAdjustDTO.getStoreId();
        // 商家派送余额 余币,广告余币、红包、第三方导入余额、第三方导入余币 ，后台作为场地通用处理，忽略场地传值
        if(BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE.getCode().equals(accountBenefitAdjustDTO.getClassify())
                || BenefitClassifyEnum.ADVERT_RED_PACKET.getCode().equals(accountBenefitAdjustDTO.getClassify())
                || BenefitClassifyEnum.THIRD_PLATFORM_COINS.getCode().equals(accountBenefitAdjustDTO.getClassify())
                || BenefitClassifyEnum.THIRD_PLATFORM_AMOUNT.getCode().equals(accountBenefitAdjustDTO.getClassify())) {
            accountBenefitAdjustDTO.setStoreId(null);
        }

        Integer applicable = null;
        Long associatedId = null;
        Integer expiryDateCategory = ofNullable(accountBenefitAdjustDTO.getExpiryDateCategory()).orElse(ExpiryDateCategoryEnum.NO_LIMIT).getValue();
        if(accountBenefitAdjustDTO.getStoreId() != null){
            applicable = ApplicableEnum.GROUP.getValue();
            associatedId = accountBenefitAdjustDTO.getStoreId();
        }
        BenefitInfoDTO benefitInfoDTO = null;
        if (Objects.nonNull(accountBenefitAdjustDTO.getBenfitId())) {
            benefitInfoDTO = benefitService.getBenefitById(accountBenefitAdjustDTO.getBenfitId(), userInfoDTO.getMerchantId());
            benefitInfoDTO.setOldBenefit(true);
        } else {
            benefitInfoDTO = benefitService.getMerchantBenefit(userInfoDTO.getMerchantId(),
                    accountBenefitAdjustDTO.getClassify(), applicable, associatedId, expiryDateCategory,
                    accountBenefitAdjustDTO.getUpTime(), accountBenefitAdjustDTO.getDownTime());
        }
        // 广告红包按照传递的有效时间来生成权益记录
        if (Objects.equals(accountBenefitAdjustDTO.getClassify(), BenefitClassifyEnum.ADVERT_RED_PACKET.getCode()) ||
                //不为无限期的，都应该需要增加有效时间
                expiryDateCategory != ExpiryDateCategoryEnum.NO_LIMIT.getValue()) {
            benefitInfoDTO.setExpiryDateCategory(expiryDateCategory);
            benefitInfoDTO.setUpTime(accountBenefitAdjustDTO.getUpTime());
            benefitInfoDTO.setDownTime(accountBenefitAdjustDTO.getDownTime());
        }

        Assert.notNull(benefitInfoDTO, "商户权益不能为空");
        //更新请求的 accountBenefitAdjustDTO 参数
        adaptAccountBenefitAdjustParam(accountBenefitAdjustDTO,userInfoDTO,benefitInfoDTO);

        try {
            //账户获取
            AccountInitDTO accountInitDTO = new AccountInitDTO();
            accountInitDTO.setMerchantUserId(userInfoDTO.getMerchantUserId());
            accountInitDTO.setUserId(userInfoDTO.getLyyUserId());
            accountInitDTO.setClassify(accountBenefitAdjustDTO.getClassify());
            accountInitDTO.setMerchantId(accountBenefitAdjustDTO.getMerchantId());
            if(INCREMENT.equals(accountBenefitAdjustDTO.getAdjustType())){
                accountInitDTO.setBalance(accountBenefitAdjustDTO.getAmount());
                accountInitDTO.setTotal(accountBenefitAdjustDTO.getAmount());
            }else if(AdjustTypeEnum.EMPTY.equals(accountBenefitAdjustDTO.getAdjustType())){
                accountInitDTO.setBalance(BigDecimal.ZERO);
                accountInitDTO.setTotal(BigDecimal.ZERO);
            }
            accountInitDTO.setOperatorId(ofNullable(accountBenefitAdjustDTO.getOperator())
                    .orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
            AccountInitResultDTO initResultDTO = smallVenueAccountRepository.initAccount(accountInitDTO);
            saveAccountRecord(accountBenefitAdjustDTO, benefitInfoDTO, initResultDTO, isAddTotal, storeId);
        }finally {
            redisLock.unlock(lockKey, "Y");
        }
    }

    /**
     * 检查参数
     * @param accountBenefitAdjustDTO
     */
    private void checkParam(AccountBenefitAdjustDTO accountBenefitAdjustDTO) {
        if(ExpiryDateCategoryEnum.TIME_INTERVAL.equals(accountBenefitAdjustDTO.getExpiryDateCategory())){
            if(!StringUtils.isEmpty(accountBenefitAdjustDTO.getUpTime())){
                if(accountBenefitAdjustDTO.getUpTime().length() != timePattern.length()){
                    throw new IllegalArgumentException("uptime 参数不合法");
                }
            }
            if(!StringUtils.isEmpty(accountBenefitAdjustDTO.getDownTime())){
                if(accountBenefitAdjustDTO.getDownTime().length() != timePattern.length()){
                    throw new IllegalArgumentException("downtime 参数不合法");
                }
            }
        }
        if(AdjustTypeEnum.DECREMENT.equals(accountBenefitAdjustDTO.getAdjustType())){
            throw new IllegalArgumentException("此接口不支持权益扣减");
        }
    }

    /**
     * 账户数据变更处理（包括账号变更流水）
     *
     * @param accountBenefitAdjustDTO
     * @param benefitInfoDTO
     * @param initResultDTO
     * @param isAddTotal
     * @param storeId
     */
    private void saveAccountRecord(AccountBenefitAdjustDTO accountBenefitAdjustDTO, BenefitInfoDTO benefitInfoDTO,
            AccountInitResultDTO initResultDTO, boolean isAddTotal, Long storeId) {
        Account account = initResultDTO.getAccount();
        Date now = new Date();
        AccountRecord accountRecord = new AccountRecord();
        if (initResultDTO.getIsCreate()) {
            AccountBenefit newAccountBenefit = createAccountBenefit(account, benefitInfoDTO, now, accountBenefitAdjustDTO.getAmount(),
                    accountBenefitAdjustDTO.getAmount());
            accountRecord.setInitialBenefit(BigDecimal.ZERO);
            accountRecord.setAccountBenefitId(newAccountBenefit.getId());
            updateUserStatistics(account.getUserId(), account.getMerchantUserId(), account.getMerchantId(),
                    accountBenefitAdjustDTO.getClassify(), account.getTotal(), account.getBalance());
        } else {
            List<AccountBenefit> accountBenefitList = accountBenefitMapper.selectList(new QueryWrapper<AccountBenefit>().lambda()
                    .eq(AccountBenefit::getMerchantId, account.getMerchantId())
                    .eq(AccountBenefit::getAccountId, account.getId())
                    .eq(AccountBenefit::getBenefitId, benefitInfoDTO.getId())
                    .eq(Objects.nonNull(accountBenefitAdjustDTO.getId()), AccountBenefit::getId, accountBenefitAdjustDTO.getId())
                    .eq(AccountBenefit::getStatus, AccountBenefitStatusEnum.NORMAL.getStatus())
            );
            //百川充值权益有有效期，benefit表记录使用的是没有有效期的，在account_benefit对应批次中记录down_time
            //因此没有有效期的增加权益时可能会加到有有效期的批次中去，增加无有效期权益时，对权益批次做下过滤
            ExpiryDateCategoryEnum expiryDateCategory = ofNullable(accountBenefitAdjustDTO.getExpiryDateCategory())
                    .orElse(ExpiryDateCategoryEnum.NO_LIMIT);
            //指定权益时无需判断
            if (Objects.equals(expiryDateCategory, ExpiryDateCategoryEnum.NO_LIMIT) && Objects.isNull(accountBenefitAdjustDTO.getId())) {
                accountBenefitList = accountBenefitList.stream()
                        .filter(benefit -> Objects.isNull(benefit.getUpTime()) && Objects.isNull(benefit.getDownTime()))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(accountBenefitList)) {
                //总权益与余额信息需要设置为0
                accountRecord.setInitialBenefit(BigDecimal.ZERO);
            } else {
                BigDecimal initialBenefit = BigDecimal.ZERO;
                for (AccountBenefit accountBenefit : accountBenefitList) {
                    initialBenefit = initialBenefit.add(accountBenefit.getBalance());
                }
                accountRecord.setInitialBenefit(initialBenefit);
            }
            Long updateAccountBenefitId = updateAccountBenefitBalanceOfAdjust(accountBenefitAdjustDTO, accountBenefitList,
                    accountRecord.getInitialBenefit(), account, benefitInfoDTO, isAddTotal);
            accountRecord.setAccountBenefitId(updateAccountBenefitId);
            if (updateAccountBenefitId == null) {
                log.warn("{} 用户的 {} 账户权益更新失败-->{}", account.getUserId(), account.getId(), accountBenefitAdjustDTO);
                throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_NOT_ENOUGH);
            }
            //更新账号权益
            if (!updateAccountBalance(accountBenefitAdjustDTO, account, isAddTotal)) {
                log.warn("{} 用户的 {} 账户余额更新失败-->{}", account.getUserId(), account.getId(), accountBenefitAdjustDTO);
                throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_NOT_ENOUGH);
            }
            if (INCREMENT.getType().equals(accountBenefitAdjustDTO.getAdjustType().getType())) {
                updateUserStatistics(account.getUserId(), account.getMerchantUserId(), account.getMerchantId(),
                        accountBenefitAdjustDTO.getClassify(), isAddTotal ? accountBenefitAdjustDTO.getAmount().abs() : BigDecimal.ZERO,
                        accountBenefitAdjustDTO.getAmount().abs());
            } else if (EMPTY.getType().equals(accountBenefitAdjustDTO.getAdjustType().getType())) {
                updateUserStatistics(account.getUserId(), account.getMerchantUserId(), account.getMerchantId(),
                        accountBenefitAdjustDTO.getClassify(), null, account.getBalance().negate());
            }
        }
        //保存记录信息
        saveAcountBenefit(accountBenefitAdjustDTO, benefitInfoDTO, accountRecord, account, now);
        accountRecord.setStoreId(storeId);
        //自动打支付类型标签和场地标签
        TagUserParamDTO tagUserParamDTO = new TagUserParamDTO();
        tagUserParamDTO.setTradeType(accountRecord.getTradeType());
        tagUserParamDTO.setUserType(null);
        tagUserParamDTO.setUserId(account.getUserId());
        tagUserParamDTO.setMerchantUserId(account.getMerchantUserId());
        tagUserParamDTO.setMerchantId(account.getMerchantId());
        tagUserParamDTO.setStoreName(accountRecord.getStoreName());
        // 支付用户的性别标签已屏蔽，后期考虑再此加上
        accountRecordMapper.insert(accountRecord);
        saveUserMerchantRelation(account.getUserId(), account.getMerchantId(), accountBenefitAdjustDTO.getStoreId());
        //退款来源不打标签
        if (Objects.equals(accountBenefitAdjustDTO.getExcludeUserTag(), true)) {
            log.debug("退款来源不打标签");
            return;
        }
        merchantAutoTagAsyncHandler.autoCreateTag(tagUserParamDTO);
    }

    private static void saveAcountBenefit(AccountBenefitAdjustDTO accountBenefitAdjustDTO, BenefitInfoDTO benefitInfoDTO, AccountRecord accountRecord, Account account, Date now) {
        accountRecord.setAccountId(account.getId());
        accountRecord.setUserId(account.getUserId());
        accountRecord.setMerchantUserId(account.getMerchantUserId());
        accountRecord.setMerchantId(account.getMerchantId());
        accountRecord.setStoreId(accountBenefitAdjustDTO.getStoreId());
        accountRecord.setEquipmentId(accountBenefitAdjustDTO.getEquipmentId());
        accountRecord.setBenefitClassify(accountBenefitAdjustDTO.getClassify());
        accountRecord.setBenefitId(benefitInfoDTO.getId());
        accountRecord.setOriginalBenefit(accountBenefitAdjustDTO.getAmount().abs());
        accountRecord.setActualBenefit(accountBenefitAdjustDTO.getAmount().abs());
        accountRecord.setOutTradeNo(accountBenefitAdjustDTO.getOutTradeNo());
        accountRecord.setOrderNo(accountBenefitAdjustDTO.getOrderNo());
        accountRecord.setRefundNo(accountBenefitAdjustDTO.getRefundNo());
        accountRecord.setMode(accountBenefitAdjustDTO.getAdjustType().getType());
        StringBuffer sb = new StringBuffer();
        sb.append(BenefitClassifyEnum.getDesc(accountBenefitAdjustDTO.getClassify()));
        if(INCREMENT.equals(accountBenefitAdjustDTO.getAdjustType())){
            sb.append("增加");
        }else{
            sb.append("扣减");
        }
        sb.append("权益");
        // 临时初始化，待产品出准确的描述
        String resourceDesc = sb.toString();
        if(StringUtils.isEmpty(accountBenefitAdjustDTO.getResource())){
            accountRecord.setResource(resourceDesc);
        }else{
            accountRecord.setResource(accountBenefitAdjustDTO.getResource());
        }
        accountRecord.setCreateTime(now);
        accountRecord.setCreatedby(ofNullable(accountBenefitAdjustDTO.getOperator()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        accountRecord.setDescription(accountBenefitAdjustDTO.getDescription());
        accountRecord.setTradeType(accountBenefitAdjustDTO.getTradeType());
        accountRecord.setTradeAmount(accountBenefitAdjustDTO.getTradeAmount());
        accountRecord.setCommodityName(accountBenefitAdjustDTO.getCommodityName());
        if (!StringUtils.isEmpty(accountBenefitAdjustDTO.getStoreName())) {
            // 只取32位，避免过长保存报错
            accountRecord.setStoreName(LyyStringUtil.substring(accountBenefitAdjustDTO.getStoreName(), 32));
        }
        accountRecord.setEquipmentValue(accountBenefitAdjustDTO.getEquipmentValue());
        accountRecord.setEquipmentTypeId(accountBenefitAdjustDTO.getEquipmentTypeId());
        accountRecord.setEquipmentTypeName(accountBenefitAdjustDTO.getEquipmentTypeName());
        accountRecord.setGroupNumber(accountBenefitAdjustDTO.getGroupNumber());
        accountRecord.setRecordType(accountBenefitAdjustDTO.getRecordType());
    }

    /**
     * 更新账户的余额信息
     * @param accountBenefitAdjustDTO 权益变更信息
     * @param account  涉及账户
     * @param isAddTotal 是否增加统计
     * @return
     */
    private boolean updateAccountBalance(AccountBenefitAdjustDTO accountBenefitAdjustDTO, Account account, boolean isAddTotal) {

        AdjustTypeEnum adjustType = accountBenefitAdjustDTO.getAdjustType();
        BigDecimal amount = accountBenefitAdjustDTO.getAmount().abs();

        UpdateWrapper wrapper = new UpdateWrapper<Account>()
                .set("update_time",new Date())
                .set("update_by",accountBenefitAdjustDTO.getOperator())
                .eq("id",account.getId())
                .eq("merchant_id",accountBenefitAdjustDTO.getMerchantId());
        switch (adjustType){
            case INCREMENT:
                //增加
                wrapper.setSql("balance = balance + " + amount);
                wrapper.setSql(isAddTotal,"total = total + " + amount);
                break;
            case EMPTY:
                wrapper.setSql("balance = 0 ");
                break;
            default:

        }
        return SqlHelper.retBool(accountMapper.update(null,wrapper));
    }

    /**
     * 更新账号权益相关权益数据(只做账户权益相加)
     * @param accountBenefitAdjustDTO
     * @param accountBenefitList
     * @param isAddTotal 是否加累计
     * @return
     */
    private Long updateAccountBenefitBalanceOfAdjust(AccountBenefitAdjustDTO accountBenefitAdjustDTO, List<AccountBenefit> accountBenefitList, BigDecimal totalBalance, Account account, BenefitInfoDTO benefitInfoDTO, boolean isAddTotal) {
        //增加 情况，账户权益表新增一条记录，以满足存在 红包余额 余币过期的问题
        BigDecimal total = isAddTotal?accountBenefitAdjustDTO.getAmount():BigDecimal.ZERO;
        //合并到旧批次权益
        Boolean mergeOld = ofNullable(accountBenefitAdjustDTO.getMergeOld()).orElse(false);
        if ((ExpiryDateCategoryEnum.NO_LIMIT.getValue() == benefitInfoDTO.getExpiryDateCategory() || mergeOld)
                && benefitInfoDTO.getOldBenefit() != null && Boolean.TRUE.equals(benefitInfoDTO.getOldBenefit())) {
            if (!CollectionUtils.isEmpty(accountBenefitList)) {
                return this.updateAccountBenefit(new Date(),accountBenefitList,accountBenefitAdjustDTO,isAddTotal);
            }else {
                return this.createAccountBenefit(account,benefitInfoDTO,new Date(),total,accountBenefitAdjustDTO.getAmount()).getId();
            }
        }else {
            return this.createAccountBenefit(account,benefitInfoDTO,new Date(),total,accountBenefitAdjustDTO.getAmount()).getId();
        }
    }

    /**
     * 账户权益相加处理，包括负数相加
     * @param now 时间
     * @param accountBenefitList 用户权益id相同的账户权益数据
     * @param accountBenefitAdjustDTO
     * @param isAddTotal 是否加统计
     * @return 返回有更新记录的accountBenefitId
     */
    private Long updateAccountBenefit(Date now, List<AccountBenefit> accountBenefitList, AccountBenefitAdjustDTO accountBenefitAdjustDTO,boolean isAddTotal) {
        AccountBenefit first = accountBenefitList.get(0);
        List<AccountBenefit> negativeBenefitList = accountBenefitList.stream()
                .filter(e -> BigDecimal.ZERO.compareTo(e.getBalance()) > 0).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(negativeBenefitList)) {
            accountBenefitList.removeAll(negativeBenefitList);
        }
        BigDecimal remain = accountBenefitAdjustDTO.getAmount();
        log.debug("[权益增加负数处理]-userId:{} 增加的金额: classify:{}  amount:{},isAddTotal: {}",
                accountBenefitAdjustDTO.getUserId(), accountBenefitAdjustDTO.getClassify(), accountBenefitAdjustDTO.getAmount(),isAddTotal);

        // 有负数权益 优先抵扣第一条，抵扣多条会导致account_record 表记录混乱
        for (AccountBenefit negativeAccountBenefit : negativeBenefitList) {
            BigDecimal balance = negativeAccountBenefit.getBalance().negate();
            log.debug("[权益增加负数处理]-权益小于balance的正值,id:{} ,remain:{} , balance:{}", negativeAccountBenefit.getId(), remain, balance);
            updateAccountBenefitBalance(now, accountBenefitAdjustDTO.getOperator(), remain, negativeAccountBenefit,isAddTotal);
             // 负数抵扣完，程序不再重复计算
            return negativeAccountBenefit.getId();
        }
        log.debug("[权益增加负数处理]-处理负值后remain: {}", remain);
        if (!CollectionUtils.isEmpty(accountBenefitList)) {
            first = accountBenefitList.get(0);
        }
        log.debug("[权益增加负数处理]-最后更新ID: {}", first.getId());
        updateAccountBenefitBalance(now, accountBenefitAdjustDTO.getOperator(), remain, first, isAddTotal);
        return first.getId();
    }

    private void updateAccountBenefitBalance(Date now, Long operator, BigDecimal changeValue, AccountBenefit entity, boolean isAddTotal) {
        UpdateWrapper wrapper = new UpdateWrapper<AccountBenefit>()
                .setSql("balance = balance + " + changeValue)
                .set("update_time", now)
                .set("update_by", operator)
                .eq("merchant_id", entity.getMerchantId())
                .eq("account_id", entity.getAccountId())
                .eq("id", entity.getId());
        if (isAddTotal) {
            wrapper.setSql("total = total + " + changeValue);
        }
        SqlHelper.retBool(accountBenefitMapper.update(null, wrapper));
    }

    /**
     * 更新补充参数
     * @param accountBenefitAdjustDTO
     * @param userInfoDTO
     * @param benefitInfoDTO
     */
    private void adaptAccountBenefitAdjustParam(AccountBenefitAdjustDTO accountBenefitAdjustDTO, UserInfoDTO userInfoDTO, BenefitInfoDTO benefitInfoDTO) {
        if(accountBenefitAdjustDTO.getMerchantUserId() == null){
            accountBenefitAdjustDTO.setMerchantUserId(userInfoDTO.getMerchantUserId());
        }
        if(accountBenefitAdjustDTO.getClassify() == null){
            accountBenefitAdjustDTO.setClassify(benefitInfoDTO.getBenefitClassify());
        }

    }

    /**
     * 创建新的账户权益信息
     * @param account
     * @param benefitInfoDTO
     * @param now
     * @param total
     * @param balance
     * @return
     */
    private AccountBenefit createAccountBenefit(Account account,BenefitInfoDTO benefitInfoDTO, Date now,BigDecimal total,BigDecimal balance) {
        AccountBenefit newAccountBenefit = new AccountBenefit();
        newAccountBenefit.setAccountId(account.getId());
        newAccountBenefit.setBenefitId(benefitInfoDTO.getId());
        newAccountBenefit.setUserId(account.getUserId());
        newAccountBenefit.setMerchantId(account.getMerchantId());
        newAccountBenefit.setMerchantUserId(account.getMerchantUserId());
        newAccountBenefit.setClassify(account.getClassify());
        newAccountBenefit.setStatus(AccountBenefitStatusEnum.NORMAL.getStatus());
        newAccountBenefit.setBalance(ofNullable(balance).orElse(account.getBalance()));
        newAccountBenefit.setTotal(ofNullable(total).orElse(account.getTotal() ));
        newAccountBenefit.setCreateTime(now);
        newAccountBenefit.setUpdateTime(now);
        newAccountBenefit.setExpiryDateCategory(benefitInfoDTO.getExpiryDateCategory());
        newAccountBenefit.setUpTime(benefitInfoDTO.getUpTime());
        newAccountBenefit.setDownTime(benefitInfoDTO.getDownTime());
        accountBenefitMapper.insert(newAccountBenefit);
        return newAccountBenefit;
    }

    /**
     * 权益回滚
     *
     * @param rollback
     * @param retryFlag 是否重试
     */
    @Override
    public void benefitRollback(BenefitRollbackDTO rollback, boolean retryFlag) {
        String cacheStr = RedisClient.get(RedisKey.MEMBER_CONSUME_CACHE.concat(rollback.getOrderNo()));
        if (cacheStr != null) {
            rollBackByCache(rollback, cacheStr);
        } else {
            if (!retryFlag) {
                BenefitRollbackDelayedMessage benefitRollbackDelayedMessage = new BenefitRollbackDelayedMessage();
                benefitRollbackDelayedMessage.setBenefitRollbackDTO(rollback);
                benefitRollbackDelayedMessage.setCreateTime(new Date());
                benefitRollbackDelayedMessage.setFailCount(0);
                rollbackDelayedProducer.send(benefitRollbackDelayedMessage, delay * 1000);
            }
        }
    }

    @Override
    public boolean benefitRollbackDelayQueue(BenefitRollbackDTO rollback) {
        String cacheStr = RedisClient.get(RedisKey.MEMBER_CONSUME_CACHE.concat(rollback.getOrderNo()));
        if (cacheStr != null) {
            rollBackByCache(rollback, cacheStr);
            return true;
        }
        return false;
    }

    private void rollBackByCache(BenefitRollbackDTO rollback, String cacheStr) {
        CollectionType listType = objectMapper.getTypeFactory().constructCollectionType(ArrayList.class, AccountBenefitAdjustDTO.class);
        try {
            List<AccountBenefitAdjustDTO> adjustList = objectMapper.readValue(cacheStr, listType);
            adjustList.forEach(adjustItem -> {
                //0元启动不处理
                if (AccountRecordTypeEnum.FREE_TO_USE.getCode().equals(adjustItem.getRecordType())) {
                    return;
                }
                adjustItem.setResource(rollback.getResource());
                adjustItem.setAdjustType(AdjustTypeEnum.DECREMENT.equals(adjustItem.getAdjustType()) ? INCREMENT : AdjustTypeEnum.DECREMENT);
                if (BenefitClassifyGroupEnum.COINS.getClassify().contains(adjustItem.getClassify())) {
                    adjustItem.setRecordType(AccountRecordTypeEnum.REFUND_START_FAIL_COIN.getCode());
                }else if(BenefitClassifyGroupEnum.MONEY.getClassify().contains(adjustItem.getClassify())) {
                    adjustItem.setRecordType(AccountRecordTypeEnum.REFUND_START_FAIL_BALANCE_REFUND.getCode());
                }
            });
            log.debug("回退权益 -> {}", adjustList);
            this.decreaseAccountBenefit(adjustList);
        } catch (JsonProcessingException e) {
            log.error("反序列化失败, {}", cacheStr);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void merchantClearBatchBenefit(List<AccountBenefitAdjustDTO> accountBenefitAdjustList) {

        // 生成唯一订单号
        Long orderNo = snowflakeIdWorker.nextId();
        accountBenefitAdjustList.forEach(dto -> {
            Assert.notNull(dto.getUserId(),"用户ID不能为空");
            Assert.notNull(dto.getMerchantId(),"商户ID不能为空");
            Assert.notNull(dto.getClassify(),"权益类型不能为空");

            MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(dto.getUserId(), dto.getMerchantId());
            Assert.notNull(merchantUser,AccountErrorCode.ACCOUNT_NOT_FOUND.getMessage());
            dto.setMerchantUserId(merchantUser.getId());

            //清空用户下指定类型全部或类型下指定批次
            Boolean isEmpty = ofNullable(dto.getIsEmpty()).orElse(false);
            if (!isEmpty) {
                Assert.notNull(dto.getId(),"权益ID不能为空");
            }

            // 根据用户-商户加锁，重试时间3000ms，锁超时5000ms
            String lockKey = RedisKey.ACCOUNT_BENEFIT_UPDATE_LOCK
                + dto.getMerchantId()
                + ":" + dto.getUserId()
                + ":" + dto.getClassify();
            if (redisLock.lock(lockKey, UserMemberSysConstants.YES, 5000, 3000) == null) {
                log.error("获取锁失败 -> {}", lockKey);
                throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_IS_UPDATING);
            }

            try {
                LambdaQueryWrapper<AccountBenefit> lambdaQueryWrapper =  new LambdaQueryWrapper<AccountBenefit>()
                    .eq(!isEmpty, AccountBenefit::getId, dto.getId())
                    .eq(AccountBenefit::getClassify, dto.getClassify())
                    .eq(AccountBenefit::getMerchantUserId,dto.getMerchantUserId())
                    .eq(AccountBenefit::getMerchantId,dto.getMerchantId());
                List<AccountBenefit> accountBenefitList = accountBenefitMapper.selectList(lambdaQueryWrapper);
                if (CollUtil.isEmpty(accountBenefitList)) {
                    return;
                }
                Account account = accountMapper.selectOne(
                    new LambdaQueryWrapper<Account>().eq(Account::getMerchantId, dto.getMerchantId())
                        .eq(Account::getUserId, dto.getUserId()).eq(Account::getClassify, dto.getClassify()));
                if (account == null) {
                    throw new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND);
                }

                for (AccountBenefit accountBenefit : accountBenefitList) {
                    BigDecimal initBenefit = accountBenefit.getBalance();
                    if (initBenefit.compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }
                    BigDecimal increment = initBenefit.negate();
                    // 更新账户和权益的余额
                    accountBenefitMapper.increaseBalanceAndTotal(accountBenefit.getId(), accountBenefit.getMerchantId(), dto.getOperator(), null, increment);
                    accountMapper.increaseBalanceAndTotal(account.getId(), account.getMerchantId(), dto.getOperator(), null, increment);

                    //更新用户统计
                    updateUserStatistics(account.getUserId(), account.getMerchantUserId(), account.getMerchantId(),
                        dto.getClassify(), null, increment);

                    AccountRecord accountRecord = getAccountRecord(dto, accountBenefit, account, initBenefit, orderNo);
                    accountRecordMapper.insert(accountRecord);
                }
            } finally {
                redisLock.unlock(lockKey, "Y");
            }
        });
    }

    private static AccountRecord getAccountRecord(AccountBenefitAdjustDTO dto, AccountBenefit accountBenefit, Account account, BigDecimal initBenefit, Long orderNo) {
        AccountRecord accountRecord = new AccountRecord();
        accountRecord.setAccountId(account.getId());
        accountRecord.setUserId(account.getUserId());
        accountRecord.setMerchantUserId(account.getMerchantUserId());
        accountRecord.setMerchantId(account.getMerchantId());
        accountRecord.setBenefitClassify(dto.getClassify());
        accountRecord.setBenefitId(accountBenefit.getBenefitId());
        accountRecord.setInitialBenefit(initBenefit);
        accountRecord.setOriginalBenefit(initBenefit);
        accountRecord.setActualBenefit(initBenefit);

        accountRecord.setOutTradeNo(dto.getOutTradeNo());
        accountRecord.setOrderNo(orderNo.toString());
        accountRecord.setMode(AdjustTypeEnum.DECREMENT.getType());
        StringBuilder sb = new StringBuilder();
        sb.append(BenefitClassifyEnum.getDesc(dto.getClassify()));
        if(INCREMENT.equals(dto.getAdjustType())){
            sb.append("增加");
        }else{
            sb.append("扣减");
        }
        sb.append("权益");
        // 临时初始化，待产品出准确的描述
        String resourceDesc = sb.toString();
        if(StringUtils.isEmpty(dto.getResource())){
            accountRecord.setResource(resourceDesc);
        }else{
            accountRecord.setResource(dto.getResource());
        }
        accountRecord.setCreatedby(ofNullable(dto.getOperator())
                .orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        accountRecord.setCreateTime(new Date());
        accountRecord.setDescription(dto.getDescription());
        accountRecord.setTradeType(dto.getTradeType());
        accountRecord.setTradeAmount(dto.getTradeAmount());
        accountRecord.setCommodityName(dto.getCommodityName());
        if (!StringUtils.isEmpty(dto.getStoreName())) {
            // 只取32位，避免过长保存报错
            accountRecord.setStoreName(LyyStringUtil.substring(dto.getStoreName(), 32));
        }
        accountRecord.setEquipmentValue(dto.getEquipmentValue());
        accountRecord.setEquipmentTypeId(dto.getEquipmentTypeId());
        accountRecord.setEquipmentTypeName(dto.getEquipmentTypeName());
        accountRecord.setRecordType(dto.getRecordType());
        accountRecord.setAccountBenefitId(accountBenefit.getId());
        return accountRecord;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void merchantClearBenefit(List<AccountBenefitAdjustDTO> param) {

        // 生成唯一订单号
        Long orderNo = snowflakeIdWorker.nextId();
        param.forEach(accountBenefitAdjustDTO -> {
            Assert.notNull(accountBenefitAdjustDTO.getUserId(),"用户ID不能为空");
            Assert.notNull(accountBenefitAdjustDTO.getMerchantId(),"商户ID不能为空");
            Assert.notNull(accountBenefitAdjustDTO.getClassify(),"权益类型不能为空");
            LambdaQueryWrapper<Account> lambdaQueryWrapper =  new QueryWrapper<Account>().lambda().eq(Account::getMerchantId,accountBenefitAdjustDTO.getMerchantId())
                    .eq(Account::getUserId,accountBenefitAdjustDTO.getUserId())
                    .eq(Account::getClassify,accountBenefitAdjustDTO.getClassify());
            Account account = accountMapper.selectOne(lambdaQueryWrapper);
            if(account != null){
                // 账户余额余币 置空，账户下的权益 也置空
                //account.setBalance(BigDecimal.ZERO);
                //account.setUpdateTime(new Date());
                LambdaUpdateWrapper<Account> accountLambdaUpdateWrapper = new UpdateWrapper<Account>().lambda().set(Account::getBalance,BigDecimal.ZERO)
                        .set(Account::getUpdateTime,new Date())
                        .set(Account::getUpdateBy,accountBenefitAdjustDTO.getOperator())
                        .eq(Account::getId,account.getId())
                        .eq(Account::getMerchantId,account.getMerchantId());
                int updateNum =  accountMapper.update(null,accountLambdaUpdateWrapper);
                if(updateNum > 0 ){
                    updateUserStatistics(account.getUserId(), account.getMerchantUserId(), account.getMerchantId(),
                            account.getClassify(), null, account.getBalance().negate());
                    // 更新成功，更新权益账户表信息
                    LambdaQueryWrapper<AccountBenefit> accountBenefitLambdaQueryWrapper = new QueryWrapper<AccountBenefit>().lambda()
                            .eq(AccountBenefit::getAccountId,account.getId())
                            .eq(AccountBenefit::getMerchantId,account.getMerchantId());

                    List<AccountBenefit> accountBenefitList =  accountBenefitMapper.selectList(accountBenefitLambdaQueryWrapper);
                    for(AccountBenefit accountBenefit:accountBenefitList){

                        BigDecimal initBenefit = accountBenefit.getBalance();

                        LambdaUpdateWrapper<AccountBenefit> accountBenefitLambdaUpdateWrapper = new UpdateWrapper<AccountBenefit>().lambda().set(AccountBenefit::getBalance,BigDecimal.ZERO)
                                .set(AccountBenefit::getUpdateTime,new Date())
                                .set(AccountBenefit::getUpdateBy,accountBenefitAdjustDTO.getOperator())
                                .eq(AccountBenefit::getId,accountBenefit.getId())
                                .eq(AccountBenefit::getMerchantId,accountBenefit.getMerchantId());
                        int updateAccountBenefitNum =  accountBenefitMapper.update(null,accountBenefitLambdaUpdateWrapper);
                        log.debug("updateAccountBenefitNum:{}",updateAccountBenefitNum);
                        if(updateAccountBenefitNum > 0 && initBenefit.compareTo(BigDecimal.ZERO) > 0){
                            AccountRecord accountRecord = getRecord(accountBenefitAdjustDTO, accountBenefit, account, initBenefit, orderNo);
                            accountRecordMapper.insert(accountRecord);
                            log.debug("账户变更记录插入成功,id:{}",accountRecord.getId());
                        }
                    }
                }

            }
        });

    }

    private static AccountRecord getRecord(AccountBenefitAdjustDTO accountBenefitAdjustDTO, AccountBenefit accountBenefit, Account account, BigDecimal initBenefit, Long orderNo) {
        AccountRecord accountRecord = new AccountRecord();
        accountRecord.setAccountId(account.getId());
        accountRecord.setUserId(account.getUserId());
        accountRecord.setMerchantUserId(account.getMerchantUserId());
        accountRecord.setMerchantId(account.getMerchantId());
        accountRecord.setBenefitClassify(accountBenefitAdjustDTO.getClassify());
        accountRecord.setBenefitId(accountBenefit.getBenefitId());
        accountRecord.setInitialBenefit(initBenefit);
        accountRecord.setOriginalBenefit(initBenefit);
        accountRecord.setActualBenefit(initBenefit);

        accountRecord.setOutTradeNo(accountBenefitAdjustDTO.getOutTradeNo());
        accountRecord.setOrderNo(orderNo.toString());
        accountRecord.setMode(AdjustTypeEnum.DECREMENT.getType());
        StringBuffer sb = new StringBuffer();
        sb.append(BenefitClassifyEnum.getDesc(accountBenefitAdjustDTO.getClassify()));
        if(INCREMENT.equals(accountBenefitAdjustDTO.getAdjustType())){
            sb.append("增加");
        }else{
            sb.append("扣减");
        }
        sb.append("权益");
        // 临时初始化，待产品出准确的描述
        String resourceDesc = sb.toString();
        if(StringUtils.isEmpty(accountBenefitAdjustDTO.getResource())){
            accountRecord.setResource(resourceDesc);
        }else{
            accountRecord.setResource(accountBenefitAdjustDTO.getResource());
        }
        accountRecord.setCreatedby(ofNullable(accountBenefitAdjustDTO.getOperator())
                .orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        accountRecord.setCreateTime(new Date());
        accountRecord.setDescription(accountBenefitAdjustDTO.getDescription());
        accountRecord.setTradeType(accountBenefitAdjustDTO.getTradeType());
        accountRecord.setTradeAmount(accountBenefitAdjustDTO.getTradeAmount());
        accountRecord.setCommodityName(accountBenefitAdjustDTO.getCommodityName());
        if (!StringUtils.isEmpty(accountBenefitAdjustDTO.getStoreName())) {
            // 只取32位，避免过长保存报错
            accountRecord.setStoreName(LyyStringUtil.substring(accountBenefitAdjustDTO.getStoreName(), 32));
        }
        accountRecord.setEquipmentValue(accountBenefitAdjustDTO.getEquipmentValue());
        accountRecord.setEquipmentTypeId(accountBenefitAdjustDTO.getEquipmentTypeId());
        accountRecord.setEquipmentTypeName(accountBenefitAdjustDTO.getEquipmentTypeName());
        accountRecord.setRecordType(accountBenefitAdjustDTO.getRecordType());
        accountRecord.setAccountBenefitId(accountBenefit.getId());
        return accountRecord;
    }

    /**
     * 直接更新account的数据，主要用于券数据的更新
     *
     * @param merchantId
     * @param merchantUserId
     * @param classify
     * @param amount
     * @param adjustType
     * @param isAddTotal
     * @return
     */
    @Override
    public int updateAccountAmount(Long merchantId, Long merchantUserId, Integer classify, BigDecimal amount, AdjustTypeEnum adjustType, boolean isAddTotal) {
        LambdaUpdateWrapper<Account> wrapper=  new UpdateWrapper<Account>()
                .lambda()
                .eq(Account::getMerchantId,merchantId)
                .eq(Account::getMerchantUserId,merchantUserId)
                .eq(Account::getClassify,classify)
                .set(Account::getUpdateTime,new Date())
                .setSql(isAddTotal && AdjustTypeEnum.INCREMENT.equals(adjustType),"total = total + " + amount.abs())
                .setSql( AdjustTypeEnum.INCREMENT.equals(adjustType),"balance = balance + " + amount.abs())
//                .setSql(AdjustTypeEnum.DECREMENT.equals(adjustType),"balance = balance - " + amount.abs())
                .setSql(AdjustTypeEnum.DECREMENT.equals(adjustType),"balance = greatest(balance - " + amount.abs()+",0) ")

                .setSql(AdjustTypeEnum.EMPTY.equals(adjustType),"balance = 0 ");
        return accountMapper.update(null,wrapper);

    }

    /**
     * 券权益单独处理
     *       券只有数量，不涉及到钱或币，故无流水记录
     * @param accountBenefitAdjustDTO
     * @param userInfoDTO
     * @return
     */
    @Override
    public boolean adjustCouponBenefit(AccountBenefitAdjustDTO accountBenefitAdjustDTO, UserInfoDTO userInfoDTO, boolean isAddTotal) {
        if(!BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON.getCode().equals(accountBenefitAdjustDTO.getClassify()) ){
            return false;
        }
        //调整/初始化券账户
        AccountServiceImpl accountServiceImpl = (AccountServiceImpl)AopContext.currentProxy();
        accountServiceImpl.adjustOrInitCouponAccount(accountBenefitAdjustDTO, userInfoDTO, isAddTotal);
        return true;
    }

    @Klock(keys = "'user-member:klock:couponAdjust-'+#userInfoDTO.merchantId+'_'+#userInfoDTO.merchantUserId+'_'+#accountBenefitAdjustDTO.classify", waitTime = 5)
    public void adjustOrInitCouponAccount(AccountBenefitAdjustDTO accountBenefitAdjustDTO, UserInfoDTO userInfoDTO, boolean isAddTotal) {
        int i = updateAccountAmount(userInfoDTO.getMerchantId(), userInfoDTO.getMerchantUserId(), accountBenefitAdjustDTO.getClassify(),
                accountBenefitAdjustDTO.getAmount(), accountBenefitAdjustDTO.getAdjustType(), isAddTotal);
        if(i <= 0){
            AccountInitDTO initDTO = new AccountInitDTO();
            initDTO.setUserId(userInfoDTO.getLyyUserId());
            initDTO.setMerchantId(userInfoDTO.getMerchantId());
            initDTO.setMerchantUserId(userInfoDTO.getMerchantUserId());
            initDTO.setClassify(accountBenefitAdjustDTO.getClassify());
            if (AdjustTypeEnum.INCREMENT.equals(accountBenefitAdjustDTO.getAdjustType())) {
                initDTO.setTotal(accountBenefitAdjustDTO.getAmount().abs());
                initDTO.setBalance(accountBenefitAdjustDTO.getAmount().abs());
            } else {
                initDTO.setTotal(BigDecimal.ZERO);
                initDTO.setBalance(BigDecimal.ZERO);
            }
            if (log.isInfoEnabled()) {
                log.warn("{} 用户 {} 商户 {} 商户用户，没有对应的券权益信息，需要补充--->{}", userInfoDTO.getLyyUserId(),
                        userInfoDTO.getMerchantId(), userInfoDTO.getMerchantUserId(), initDTO);
            }
            smallVenueAccountRepository.initAccount(initDTO);
        }
    }

    /**
     * 分页获取账户的券列表数据
     * @param merchantId    商户id只作为确定数据库的作用，不用于搜索
     * @param classify  权益类型
     * @param page  分页参数
     * @return
     */
    @Override
    public Page<Account> findAccountPage(Long merchantId, Integer classify, Page<Account> page, Date start, Date end) {
        //不需要统计数据
        page.setSearchCount(false);
        QueryWrapper<Account> queryWrapper = new QueryWrapper<Account>()
                .eq("classify", classify).between("update_time", start, end)
                .and(wrapper->wrapper.eq("merchant_id",merchantId).or().exists("select 1"));
       return  accountMapper.selectPage(page, queryWrapper);
    }

    /**
     * 统计账户权益
     *
     * @param merchantId
     * @param merchantUserId
     * @param classifyList
     * @return
     */
    @Override
    public AccountBenefitCountDTO sumAccountBenefit(Long merchantId, Long merchantUserId, List<Integer> classifyList) {
        AccountBenefitCountDTO accountBenefitCountDTO = new AccountBenefitCountDTO();
        if(merchantId == null || merchantUserId == null || CollectionUtils.isEmpty(classifyList)){
            log.warn("sumAccountBenefit 参数异常--> merchantId:{},merchantUserId:{},classifyList:{}",merchantId,merchantUserId,classifyList);
           return accountBenefitCountDTO;
        }
        ofNullable(sumActiveBalance(merchantId,merchantUserId,classifyList))
                .ifPresent(accountBenefitCountDTO::setActiveBalance);
        ofNullable(sumConsumeBalance(merchantId,merchantUserId,classifyList))
                .ifPresent(accountBenefitCountDTO::setConsumeBalance);
        ofNullable(sumOverdueBalance(merchantId,merchantUserId,classifyList))
                .ifPresent(accountBenefitCountDTO::setOverdueBalance);
        return accountBenefitCountDTO;
    }



    /**
     * 统计生效中权益数量
     * @param merchantId
     * @param merchantUserId
     * @param classifyList
     * @return
     */
    private BigDecimal sumActiveBalance(Long merchantId, Long merchantUserId, List<Integer> classifyList) {
        QueryWrapper wrapper = new QueryWrapper<Account>()
                .select("sum(balance) balance_sum")
                .eq("merchant_id", merchantId)
                .eq("merchant_user_id", merchantUserId)
                .eq("status",1)
                .in("classify",classifyList)
                .last("limit 1");
        BigDecimal balanceSum = selectBigDecimal(accountMapper,wrapper);
        return balanceSum;
    }



    /**
     * 统计已消耗权益数量
     * @param merchantId
     * @param merchantUserId
     * @param classifyList
     * @return
     */
    private BigDecimal sumConsumeBalance(Long merchantId, Long merchantUserId, List<Integer> classifyList) {
        QueryWrapper wrapper = new QueryWrapper<AccountRecord>()
                .select("sum(actual_benefit) balance_sum")
                .eq("merchant_id", merchantId)
                .eq("merchant_user_id", merchantUserId)
                .eq("mode",2)
                .in("benefit_classify",classifyList)
                .notIn("resource","过期")
                .last("limit 1");
        BigDecimal balanceSum = selectBigDecimal(accountRecordMapper,wrapper);
        return balanceSum;
    }
    /**
     * 统计已过期权益数量
     * @param merchantId
     * @param merchantUserId
     * @param classifyList
     * @return
     */
    private BigDecimal sumOverdueBalance(Long merchantId, Long merchantUserId, List<Integer> classifyList) {
        QueryWrapper wrapper = new QueryWrapper<AccountRecord>()
                .select("sum(actual_benefit) balance_sum")
                .eq("merchant_id", merchantId)
                .eq("merchant_user_id", merchantUserId)
                .eq("mode",2)
                .in("benefit_classify",classifyList)
                .eq("resource","过期")
                .last("limit 1");
        BigDecimal balanceSum = selectBigDecimal(accountRecordMapper,wrapper);
        return balanceSum;
    }


    /**
     * 查询出 BigDecimal 类型数据
     * @param baseMapper
     * @param wrapper
     * @return
     */
    private <T> BigDecimal selectBigDecimal(BaseMapper<T> baseMapper, QueryWrapper<T> wrapper) {
        return (BigDecimal) baseMapper.selectObjs(wrapper).stream()
                .filter(Objects::nonNull)
                .map(balance->{
                    if(balance instanceof BigDecimal){
                        return balance;
                    }else if(balance instanceof Number){
                        return new BigDecimal(balance.toString());
                    }
                    return BigDecimal.ZERO;
                })
                .findFirst()
                .orElse(BigDecimal.ZERO);
    }
    /**
     * 是否允许扣成负数的品类
     * @param equipmentTypeId
     * @return
     */
    private boolean checkNegativeEquipType(Long equipmentTypeId) {
        if (equipmentTypeId == null) {
            return false;
        }
        return allowNegativeEquipmentTypeIds.contains(equipmentTypeId);
    }


    private List<AccountBenefitAdjustDTO> calculate(ConsumeDTO consume, ConsumeDetailDTO c,
                                                   List<AccountBenefitConsumeDTO> benefitGroupByClassify,
                                                   Long merchantUserId) {
        if (consume.getMerchantId() != null && UserMemberSysConstants.PLATFORM_MERCHANT_ID.equals(consume.getMerchantId())
                && consume.getServiceType() != null
                && consume.getServiceType() == 1) {
            return calculateMerchantConsume(consume, c, benefitGroupByClassify, merchantUserId);
        }
        List<AccountBenefitAdjustDTO> adjustList = new ArrayList<>();
        BigDecimal remain = c.getAmount().abs();
        int sort = 1;
        boolean negativeAllow =consume.getAllowNegative() != null && consume.getAllowNegative();
        boolean specialEquipType = checkNegativeEquipType(consume.getEquipmentTypeId());
        for (AccountBenefitConsumeDTO accountBenefitConsume : benefitGroupByClassify) {
            if (remain.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            Account account = accountMapper.selectOne(new QueryWrapper<>(new Account())
                    .eq("merchant_id", consume.getMerchantId())
                    .eq("user_id", consume.getUserId())
                    .eq("classify", accountBenefitConsume.getBenefitClassify()));
            if (account == null) {
                throw new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND);
            }
            List<AccountBenefitWithScopeDTO> benefitWithScope = accountBenefitConsume.getBenefitWithScope();
            BigDecimal classifyAccount = benefitWithScope.stream()
                    .map(AccountBenefitWithScopeDTO::getAmount)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            BigDecimal accountBalance = account.getBalance();

            /**
             * 正常品类校验
             */
            if (!negativeAllow &&  accountBalance.compareTo(BigDecimal.ZERO) <= 0 && !specialEquipType) {
                log.debug("[账户] 权益扣减-账号余额:{}，用户id:{} 权益类型:{}", accountBalance, consume.getUserId(), accountBenefitConsume.getBenefitClassify());
                continue;
            }

            //可以使用权益集合(包括本权益)
            List<AccountBenefitWithScopeDTO> enableBenefitWithScope = benefitWithScope.stream().filter(e -> {
                if (!checkScope(e.getScopeMap(), consume)) {
                    log.debug("判断账户权益范围不可用,benefit:{}", e.getBenefitId());
                    return false;
                }
                return true;
            }).collect(Collectors.toList());

            BigDecimal canUseMoney = enableBenefitWithScope.stream()
                    .map(AccountBenefitWithScopeDTO::getAmount)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            if (canUseMoney.compareTo(BigDecimal.ZERO) < 0) {
                //负数不允许再使用
                continue;
            }

            //明细是否多过总账
            boolean balanceLessThan = false;
            /**
             * 不是售水机先出水后计费模式或售水机 才会比较权益差异
             */
            if (!negativeAllow &&!specialEquipType && accountBalance.compareTo(classifyAccount) < 0) {
                balanceLessThan = true;
            }
            log.debug("[账户] 权益扣减-用户id:{} 权益类型:{}  总账余额:{},权益明细汇总:{} ,balanceLessThan:{}",
                    consume.getUserId(), accountBenefitConsume.getBenefitClassify(), accountBalance, classifyAccount, balanceLessThan);

            for (AccountBenefitWithScopeDTO accountBenefitWithScope : enableBenefitWithScope) {
                //权益负数不再处理
                if (accountBenefitWithScope.getAmount().compareTo(BigDecimal.ZERO) < 0) {
                    log.debug("权益余额负数不再处理,{}", accountBenefitWithScope.getBenefitId());
                    continue;
                }
                if (remain.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
                AccountBenefitAdjustDTO adjust = getAccountBenefitAdjustDTO(consume, merchantUserId, accountBenefitWithScope);

                //权益可使用范围
                adjust.setBenfitId(accountBenefitWithScope.getBenefitId());
                adjust.setBenefitScopeList(handleBenefitScopeList(accountBenefitWithScope.getScope()));

                //排序
                adjust.setSort(sort);
                sort++;
                //明细统计多于总账
                if (balanceLessThan) {
                    //总账比当前权益明细多
                    if (accountBalance.compareTo(accountBenefitWithScope.getAmount()) >= 0) {
                        if (accountBenefitWithScope.getAmount().compareTo(remain) >= 0) {
                            adjust.setAmount(remain);
                            remain = BigDecimal.ZERO;
                            log.debug("[账户] 权益扣减-[A]当前权益明细扣除:{}", adjust.getAmount());
                        } else {
                            adjust.setAmount(accountBenefitWithScope.getAmount().abs());
                            remain = remain.add(accountBenefitWithScope.getAmount().abs().negate());
                            log.debug("[账户] 权益扣减-[B]当前权益明细扣除:{}", adjust.getAmount());
                        }
                        accountBalance = accountBalance.subtract(adjust.getAmount());
                    } else {
                        if (accountBalance.compareTo(remain) >= 0) {
                            adjust.setAmount(remain);
                            remain = BigDecimal.ZERO;
                            log.debug("[账户] 权益扣减-[C]当前权益明细扣除:{}", adjust.getAmount());
                        } else {
                            adjust.setAmount(accountBalance.abs());
                            remain =remain.add(accountBalance.abs().negate());
                            log.debug("[账户] 权益扣减-[D]当前权益明细扣除:{}", adjust.getAmount());
                        }
                    }
                } else {
                    if (accountBenefitWithScope.getAmount().compareTo(remain) >= 0) {
                        adjust.setAmount(remain);
                        remain = BigDecimal.ZERO;
                    } else {
                        adjust.setAmount(accountBenefitWithScope.getAmount().abs());
                        remain = remain.add(accountBenefitWithScope.getAmount().abs().negate());
                    }
                }
                adjust.setExcludeUserTag(consume.getExcludeUserTag());
                adjustList.add(adjust);
            }
        }
        /**
         * 售水机先出水后计费允许负数
         * 先计算各种扣除明细，再统计总额，再与消耗金额比较(售水机先出水后计费)
         */
        Boolean checkBalance = consume.getCheckBalance();
        BigDecimal balanceAllClassify = adjustList.stream()
                .map(AccountBenefitAdjustDTO::getAmount)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
         if (!specialEquipType && balanceAllClassify.compareTo(c.getAmount()) < 0 && checkBalance) {
            log.warn("[账户] 权益扣减明细之和:{} 少于待扣金额:{}", balanceAllClassify, c.getAmount());
            throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_NOT_ENOUGH);
        }

        if ( negativeAllow && remain.compareTo(BigDecimal.ZERO) > 0) {
            //售水机先出水后计费允许负数
            log.debug("[账户] 权益扣减-用户id:{} 计费允许负数,remain:{}", consume.getUserId(), remain);
            remain = handleWater(consume, adjustList, remain, sort);
        }

        log.info("[账户] remain:{} 权益扣减(最终扣减的记录) -> {}", remain, adjustList);
        if (remain.compareTo(BigDecimal.ZERO) > 0) {
            log.warn("[账户] 权益扣减-未完全扣除,设备id:{} ,用户id:{},场地id:{},剩余待扣除金额:{}",
                    consume.getEquipmentId(), consume.getUserId(), consume.getStoreId(), remain);
            //throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_NOT_ENOUGH);
        }
        return adjustList;
    }

    private static AccountBenefitAdjustDTO getAccountBenefitAdjustDTO(ConsumeDTO consume, Long merchantUserId, AccountBenefitWithScopeDTO accountBenefitWithScope) {
        AccountBenefitAdjustDTO adjust = new AccountBenefitAdjustDTO();
        adjust.setId(accountBenefitWithScope.getAccountBenefitId());
        adjust.setAdjustType(AdjustTypeEnum.DECREMENT);
        adjust.setClassify(accountBenefitWithScope.getClassify());
        adjust.setUserId(consume.getUserId());
        adjust.setMerchantId(consume.getMerchantId());
        adjust.setMerchantUserId(merchantUserId);
        adjust.setStoreId(consume.getStoreId());
        adjust.setEquipmentId(consume.getEquipmentId());
        adjust.setOutTradeNo(consume.getOutTradeNo());
        adjust.setOrderNo(consume.getOrderNo());
        adjust.setResource(consume.getResource());
        adjust.setOperator(consume.getOperator());

        adjust.setDescription(consume.getDescription());
        adjust.setTradeType(consume.getTradeType());
        adjust.setTradeAmount(consume.getTradeAmount());
        adjust.setCommodityId(consume.getCommodityId());
        adjust.setCommodityName(consume.getCommodityName());
        if (!StringUtils.isEmpty(consume.getStoreName())) {
            // 只取32位，避免过长保存报错
            adjust.setStoreName(LyyStringUtil.substring(consume.getStoreName(), 32));
        }
        adjust.setEquipmentTypeName(consume.getEquipmentTypeName());
        adjust.setEquipmentTypeId(consume.getEquipmentTypeId());
        adjust.setEquipmentValue(consume.getEquipmentValue());
        adjust.setGroupNumber(consume.getGroupNumber());
        adjust.setRecordType(consume.getRecordType());
        return adjust;
    }

    /**
     * 计算商户消费
     *
     * @param consume
     * @param c
     * @param benefitGroupByClassify
     * @param merchantUserId
     * @return
     */
    private List<AccountBenefitAdjustDTO> calculateMerchantConsume(ConsumeDTO consume, ConsumeDetailDTO c,
                                                                   List<AccountBenefitConsumeDTO> benefitGroupByClassify,
                                                                   Long merchantUserId) {
        if (log.isDebugEnabled()) {
            log.debug("B端商户扣费,{}", consume);
        }
        List<AccountBenefitAdjustDTO> adjustList = new ArrayList<>();
        BigDecimal remain = c.getAmount().abs();
        int sort = 1;
        boolean negativeAllow = consume.getAllowNegative() != null && consume.getAllowNegative();
        boolean specialEquipType = checkNegativeEquipType(consume.getEquipmentTypeId());
        for (AccountBenefitConsumeDTO accountBenefitConsume : benefitGroupByClassify) {
            if (remain.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            Account account = accountMapper.selectOne(new QueryWrapper<>(new Account())
                    .eq("merchant_id", consume.getMerchantId())
                    .eq("user_id", consume.getUserId())
                    .eq("classify", accountBenefitConsume.getBenefitClassify()));
            if (account == null) {
                throw new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND);
            }
            List<AccountBenefitWithScopeDTO> benefitWithScope = accountBenefitConsume.getBenefitWithScope();
            BigDecimal classifyAccount = benefitWithScope.stream()
                    .map(AccountBenefitWithScopeDTO::getAmount)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            BigDecimal accountBalance = account.getBalance();
            /**
             * 正常品类校验
             */
            if (!negativeAllow && accountBalance.compareTo(BigDecimal.ZERO) <= 0 && !specialEquipType) {
                log.debug("[账户] 权益扣减-账号余额:{}，用户id:{} 权益类型:{}", accountBalance, consume.getUserId(), accountBenefitConsume.getBenefitClassify());
                continue;
            }

            //可以使用权益集合(包括本权益)
            List<AccountBenefitWithScopeDTO> enableBenefitWithScope = benefitWithScope.stream().filter(e -> {
                if (!checkScope(e.getScopeMap(), consume)) {
                    log.debug("判断账户权益范围不可用,benefit:{}", e.getBenefitId());
                    return false;
                }
                return true;
            }).collect(Collectors.toList());

            //明细是否多过总账
            boolean balanceLessThan = false;
            /**
             * 不是售水机先出水后计费模式或售水机 才会比较权益差异
             */
            if (!negativeAllow && !specialEquipType && accountBalance.compareTo(classifyAccount) < 0) {
                balanceLessThan = true;
            }
            if (log.isDebugEnabled()) {
                log.debug("[账户] 权益扣减-用户id:{} 权益类型:{}  总账余额:{},权益明细汇总:{} ,balanceLessThan:{}",
                        consume.getUserId(), accountBenefitConsume.getBenefitClassify(), accountBalance, classifyAccount, balanceLessThan);
            }

            for (AccountBenefitWithScopeDTO accountBenefitWithScope : enableBenefitWithScope) {
                //权益负数不再处理
                if (accountBenefitWithScope.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    if (log.isDebugEnabled()) {
                        log.debug("权益余额为0或者负数不再处理,{}", accountBenefitWithScope.getBenefitId());
                    }
                    continue;
                }
                if (remain.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
                AccountBenefitAdjustDTO adjust = getAccountBenefitAdjustDTO(consume, merchantUserId, accountBenefitWithScope);

                //权益可使用范围
                adjust.setBenfitId(accountBenefitWithScope.getBenefitId());
                adjust.setBenefitScopeList(handleBenefitScopeList(accountBenefitWithScope.getScope()));

                //排序
                adjust.setSort(sort);
                sort++;
                //明细统计多于总账
                if (balanceLessThan) {
                    //总账比当前权益明细多
                    if (accountBalance.compareTo(accountBenefitWithScope.getAmount()) >= 0) {
                        if (accountBenefitWithScope.getAmount().compareTo(remain) >= 0) {
                            adjust.setAmount(remain);
                            remain = BigDecimal.ZERO;
                            log.debug("[账户] 权益扣减-[A]当前权益明细扣除:{}", adjust.getAmount());
                        } else {
                            adjust.setAmount(accountBenefitWithScope.getAmount().abs());
                            remain = remain.add(accountBenefitWithScope.getAmount().abs().negate());
                            log.debug("[账户] 权益扣减-[B]当前权益明细扣除:{}", adjust.getAmount());
                        }
                        accountBalance = accountBalance.subtract(adjust.getAmount());
                    } else {
                        if (accountBalance.compareTo(remain) >= 0) {
                            adjust.setAmount(remain);
                            remain = BigDecimal.ZERO;
                            log.debug("[账户] 权益扣减-[C]当前权益明细扣除:{}", adjust.getAmount());
                        } else {
                            adjust.setAmount(accountBalance.abs());
                            remain = remain.add(accountBalance.abs().negate());
                            log.debug("[账户] 权益扣减-[D]当前权益明细扣除:{}", adjust.getAmount());
                        }
                    }
                } else {
                    if (accountBenefitWithScope.getAmount().compareTo(remain) >= 0) {
                        adjust.setAmount(remain);
                        remain = BigDecimal.ZERO;
                    } else {
                        adjust.setAmount(accountBenefitWithScope.getAmount().abs());
                        remain = remain.add(accountBenefitWithScope.getAmount().abs().negate());
                    }
                }
                adjustList.add(adjust);
            }
        }
        if (negativeAllow && remain.compareTo(BigDecimal.ZERO) > 0) {
            if (CollectionUtils.isEmpty(adjustList)) {
                AccountBenefitAdjustDTO adjust = getAccountBenefitAdjustDTO(consume, merchantUserId, benefitGroupByClassify.get(0).getBenefitWithScope().get(0));
                adjust.setAmount(remain);
                //权益可使用范围
                adjust.setBenfitId(benefitGroupByClassify.get(0).getBenefitWithScope().get(0).getBenefitId());
                adjust.setBenefitScopeList(handleBenefitScopeList(benefitGroupByClassify.get(0).getBenefitWithScope().get(0).getScope()));
                //排序
                adjust.setSort(sort);
                adjustList.add(adjust);
                remain = BigDecimal.ZERO;
            } else {
                AccountBenefitAdjustDTO accountBenefitAdjustDTO = adjustList.get(0);
                AccountBenefitAdjustDTO dto = new AccountBenefitAdjustDTO();
                BeanUtils.copyProperties(accountBenefitAdjustDTO, dto);
                dto.setSort(sort + 1);
                dto.setAmount(remain);
                log.debug("[账户] 权益扣减-允许负数 -> {}", dto);
                adjustList.add(dto);
                remain = BigDecimal.ZERO;
            }
        }
        log.info("[账户] remain:{} 权益扣减(最终扣减的记录) -> {}", remain, adjustList);
        return adjustList;
    }



    private List<BenefitScopeSaveDTO> handleBenefitScopeList(List<BenefitScope> scope) {
        log.debug("[新会员中心先使用]-预计算扣除-权益范围,scope：{}",scope);
        if (CollectionUtils.isEmpty(scope)) {
            return null;
        }
        return  scope.stream().map(benefitScope->{
            BenefitScopeSaveDTO dto = new BenefitScopeSaveDTO();
            dto.setBenefitId(benefitScope.getBenefitId());
            dto.setApplicable(ApplicableEnum.findByValue(benefitScope.getApplicable()));
            dto.setAssociatedId(benefitScope.getAssociatedId());
            return dto;
        }).collect(Collectors.toList());
    }

    private BigDecimal handleWater(ConsumeDTO consume, List<AccountBenefitAdjustDTO> adjustList, BigDecimal remain, Integer sort) {
        Optional<AccountBenefitAdjustDTO> first = adjustList.stream()
                .filter(e -> consume.getStoreId().equals(e.getStoreId())).findFirst();
        if (first.isPresent()) {
            AccountBenefitAdjustDTO accountBenefitAdjustDTO = first.get();
            AccountBenefitAdjustDTO dto = new AccountBenefitAdjustDTO();
            BeanUtils.copyProperties(accountBenefitAdjustDTO, dto);
            dto.setSort(sort+1);
            dto.setAmount(remain);
            log.debug("[账户] 售水机先出水后计费 权益扣减-允许负数 -> {}", dto);
            remain = BigDecimal.ZERO;
            adjustList.add(dto);
        }
        return remain;
    }

    /**
     * 判断账户权益范围是否可用
     * @param scopeMap
     * @param consume
     * @return
     */
    private boolean checkScope(Map<Integer, Set<Long>> scopeMap, ConsumeDTO consume) {
        if (CollectionUtils.isNotEmpty(consume.getStoreIds())) {
            if (!CollectionUtils.isEmpty(scopeMap.get(ApplicableEnum.GROUP.getValue()))
                    && Collections.disjoint(scopeMap.get(ApplicableEnum.GROUP.getValue()), consume.getStoreIds())) {
                return false;
            }
            return true;
        }
        if (!CollectionUtils.isEmpty(scopeMap.get(ApplicableEnum.CATEGORY.getValue()))
                && !scopeMap.get(ApplicableEnum.CATEGORY.getValue()).contains(consume.getEquipmentTypeId())) {
            return false;
        }
        if (!CollectionUtils.isEmpty(scopeMap.get(ApplicableEnum.COMMODITY.getValue()))
                && !scopeMap.get(ApplicableEnum.COMMODITY.getValue()).contains(consume.getCommodityId())) {
            return false;
        }
        if (!CollectionUtils.isEmpty(scopeMap.get(ApplicableEnum.DEVICE.getValue()))
                && !scopeMap.get(ApplicableEnum.DEVICE.getValue()).contains(consume.getEquipmentId())) {
            return false;
        }
        Boolean excludeGroup = ofNullable(consume.getExcludeGroup()).orElse(false);
        if (!excludeGroup) {
            if (!CollectionUtils.isEmpty(scopeMap.get(ApplicableEnum.GROUP.getValue()))
                && !scopeMap.get(ApplicableEnum.GROUP.getValue()).contains(consume.getStoreId())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断账户权益范围是否可用
     * @return
     */
    private boolean checkScope(Map<Integer, Set<Long>> scopeMap,  List<Long> equipmentTypeId,
                               List<Long> storeId, List<Long> equipmentId, List<Long> commodityId) {
        boolean flag = true;
        if (!CollectionUtils.isEmpty(scopeMap.get(ApplicableEnum.CATEGORY.getValue()))
                && !CollectionUtils.isEmpty(equipmentTypeId)) {
            flag = !Collections.disjoint(scopeMap.get(ApplicableEnum.CATEGORY.getValue()), equipmentTypeId);
        }
        if (!CollectionUtils.isEmpty(scopeMap.get(ApplicableEnum.COMMODITY.getValue()))
                && !CollectionUtils.isEmpty(commodityId)) {
            flag = !Collections.disjoint(scopeMap.get(ApplicableEnum.COMMODITY.getValue()), commodityId);
        }
        if (!CollectionUtils.isEmpty(scopeMap.get(ApplicableEnum.DEVICE.getValue()))
                && !CollectionUtils.isEmpty(equipmentId)) {
            flag = !Collections.disjoint(scopeMap.get(ApplicableEnum.DEVICE.getValue()), equipmentId);
        }
        if (!CollectionUtils.isEmpty(scopeMap.get(ApplicableEnum.GROUP.getValue()))
                && !CollectionUtils.isEmpty(storeId)) {
            flag = !Collections.disjoint(scopeMap.get(ApplicableEnum.GROUP.getValue()), storeId);
        }
        return flag;
    }

    /**
     * 消耗权益时查询出所有的账户权益
     * @param merchantUserId
     * @param merchantId
     * @param classify
     * @return
     */
    public List<AccountBenefitWithScopeDTO> queryBenefitForConsume(Long merchantUserId, Long merchantId,
                                                                   List<Integer> classify, List<Long> benefitId,
                                                                   List<Integer> excludeClassify, Boolean isAll,
                                                                   Integer serviceType) {
        // 获取所有权益
        List<AccountBenefitWithScopeDTO> result = accountBenefitMapper.queryForConsume(merchantUserId, merchantId, classify,
                benefitId, excludeClassify, isAll, serviceType);
        // 获取权益范围
        if (!CollectionUtils.isEmpty(result) && !isAll) {
            // 过滤掉生效时间外的记录
            result = result.stream().filter(record -> {
                if (Objects.equals(record.getExpiryDateCategory(), ExpiryDateCategoryEnum.TIME_INTERVAL.getValue())) {
                    LocalDateTime begin = LocalDateTime.parse(ofNullable(record.getUpTime()).orElse("1970-01-01 00:00:00"),
                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    LocalDateTime end = LocalDateTime.parse(ofNullable(record.getDownTime()).orElse("9999-01-01 00:00:00"),
                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    return begin.isBefore(LocalDateTime.now()) && end.isAfter(LocalDateTime.now());
                }
                if (Objects.equals(record.getExpiryDateCategory(), ExpiryDateCategoryEnum.ONE_DAY_TIME_INTERVAL.getValue())) {
                    LocalTime begin = LocalTime.parse(ofNullable(record.getUpTime()).orElse("00:00:00"),
                            DateTimeFormatter.ofPattern("HH:mm:ss"));
                    LocalTime end = LocalTime.parse(ofNullable(record.getDownTime()).orElse("23:59:59"),
                            DateTimeFormatter.ofPattern("HH:mm:ss"));
                    return begin.isBefore(LocalTime.now()) && end.isAfter(LocalTime.now());
                }
                return true;
            }).collect(Collectors.toList());
            // 避免in查询报错
            if(result.size() > 0) {
                List<Long> benefitIdList = result.stream()
                        .map(AccountBenefitWithScopeDTO::getBenefitId).collect(Collectors.toList());
                List<BenefitScope> scopeList = benefitRepository.listScopeByBenefitIds(merchantId, benefitIdList);
                Map<Long, List<BenefitScope>> scopeGroup = scopeList.stream()
                        .collect(Collectors.groupingBy(BenefitScope::getBenefitId));

                result.forEach(benefit -> {
                    benefit.setScope(scopeGroup.get(benefit.getBenefitId()));
                });
            }
        }
        return result;
    }


    private List<AccountBenefitResultDTO> processingBenefit(AccountBenefitQueryDTO benefitQueryDTO, List<AccountBenefitDTO> list) {
        List<AccountBenefitResultDTO> benefitResultDTOS = new ArrayList<>();
        boolean empty = CollectionUtils.isEmpty(list);
        List<Integer> classifies = benefitQueryDTO.getClassify();
        for (Integer classify : classifies) {
            AccountBenefitResultDTO result = new AccountBenefitResultDTO();
            result.setBalance(empty?BigDecimal.ZERO:list.stream().filter(item -> Objects.equals(item.getClassify(), classify))
                    .map(AccountBenefitDTO::getBalance)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            result.setClassify(classify);
            result.setClassifyName(modifyClassifyName(classify));
            benefitResultDTOS.add(result);
        }
        return benefitResultDTOS;
    }

    @Override
    public AccountBenefitDataDTO findAccountBenefitData(AccountBenefitQueryDTO benefitQueryDTO) {
        AccountBenefitDataDTO dataDTO = new AccountBenefitDataDTO();
        Long userId = benefitQueryDTO.getUserId();
        Long merchantId = benefitQueryDTO.getMerchantId();
        if(benefitQueryDTO.getMerchantUserId() == null){
            //根据商户ID和用户id获取商户用户信息
            MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(userId, merchantId);
            if(merchantUser == null){
                throw new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR);
            }
            benefitQueryDTO.setMerchantUserId(merchantUser.getId());
        }

        //// 获取所有权益
        List<AccountBenefitDTO> list = accountBenefitMapper.allBenefitRecord(benefitQueryDTO);

        //排除过期权益判断
        if (benefitQueryDTO.isExcludeExpire()) {
            list = list.stream().filter(accountBenefitDTO -> {
                if (Objects.equals(accountBenefitDTO.getExpiryDateCategory(), ExpiryDateCategoryEnum.TIME_INTERVAL.getValue())) {
                    LocalDateTime begin = LocalDateTime.parse(ofNullable(accountBenefitDTO.getUpTime()).orElse("1970-01-01 00:00:00"),
                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    LocalDateTime end = LocalDateTime.parse(ofNullable(accountBenefitDTO.getDownTime()).orElse("9999-01-01 00:00:00"),
                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    return begin.isBefore(LocalDateTime.now()) && end.isAfter(LocalDateTime.now());
                }
                if (Objects.equals(accountBenefitDTO.getExpiryDateCategory(), ExpiryDateCategoryEnum.ONE_DAY_TIME_INTERVAL.getValue())) {
                    LocalTime begin = LocalTime.parse(ofNullable(accountBenefitDTO.getUpTime()).orElse("00:00:00"),
                            DateTimeFormatter.ofPattern("HH:mm:ss"));
                    LocalTime end = LocalTime.parse(ofNullable(accountBenefitDTO.getDownTime()).orElse("23:59:59"),
                            DateTimeFormatter.ofPattern("HH:mm:ss"));
                    return begin.isBefore(LocalTime.now()) && end.isAfter(LocalTime.now());
                }
                return true;
            }).collect(Collectors.toList());
        }

      dataDTO.setData(processingBenefit(benefitQueryDTO,list));

        dataDTO.setEffect(list.stream()
                .filter(item -> Objects.equals(item.getStatus(), AccountBenefitStatusEnum.NORMAL.getStatus()))
                .filter(item -> item.getBalance().compareTo(BigDecimal.ZERO) > 0)
                .map(AccountBenefitDTO::getBalance)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));

        dataDTO.setExpired(list.stream()
                .filter(item -> Objects.equals(item.getStatus(), AccountBenefitStatusEnum.EXPIRED.getStatus()))
                .filter(item -> item.getBalance().compareTo(BigDecimal.ZERO) > 0)
                .map(AccountBenefitDTO::getBalance)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));

        dataDTO.setConsumed(list.stream()
                .filter(item -> Objects.equals(item.getStatus(), AccountBenefitStatusEnum.NORMAL.getStatus()))
                .filter(item -> item.getTotal().compareTo(item.getBalance()) > 0)
                .map(AccountBenefitDTO::getBalance)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));

        return dataDTO;
    }

    private String modifyClassifyName(Integer classify) {

        BenefitClassifyEnum classifyEnum = BenefitClassifyEnum.of(classify);
        if (classifyEnum == null) {
            return "";
        }
        return classifyEnum.getDesc();
        //switch (classifyEnum) {
        //    case USER_RECHARGE_BALANCE:
        //        return "充值余额";
        //    case USER_RECHARGE_COIN:
        //        return "充值余币";
        //    case MERCHANT_PAYOUT_BALANCE:
        //        return "商户派发";
        //    case MERCHANT_PAYOUT_BALANCE:
        //        return "商户派发";
        //    default:
        //        return "";
        //}

    }


    @Override
    public Page<AccountBenefitDTO> listBenefitDetail(AccountBenefitQueryDTO benefitQueryDTO) {
        if(benefitQueryDTO.getMerchantUserId() == null) {
            //根据商户ID和用户id获取商户用户信息
            MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(benefitQueryDTO.getUserId(), benefitQueryDTO.getMerchantId());
            if(merchantUser == null){
                return new Page<>();
            }
            benefitQueryDTO.setMerchantUserId(merchantUser.getId());
        }
        Page<AccountBenefitDTO>  page = new Page<>(benefitQueryDTO.getPageIndex(),benefitQueryDTO.getPageSize());
        //(通用权益 -除了专用权益)  + 专用权益
        Long count = 0L;
        if (Boolean.FALSE.equals(benefitQueryDTO.getCountSql())) {
            List<AccountBenefitDTO> list = accountBenefitMapper.listBenefitRecord(benefitQueryDTO, page.offset(), page.getSize(),null);
            list.forEach(e -> e.setClassifyName(modifyClassifyName(e.getClassify())));
            page.setRecords(list);
        } else {
            count = ofNullable(accountBenefitMapper.countBenefitRecord(benefitQueryDTO)).orElse(0L);
            if (count > 0) {
                List<AccountBenefitDTO> list = accountBenefitMapper.listBenefitRecord(benefitQueryDTO, page.offset(), page.getSize(),null);
                list.forEach(e -> e.setClassifyName(modifyClassifyName(e.getClassify())));
                page.setRecords(list);
            }
        }
        page.setTotal(count);
       return page;

    }

    @Override
    public Page<AccountConsumption> listRecord(AccountRecordQueryDTO param) {
        StopWatch stopWatch = new StopWatch();
        Page<AccountConsumption> pageResult;
        try {
            stopWatch.start();
            Page<AccountRecord> list = doFindAccountRecords(param);
            List<AccountRecord> records = list.getRecords();
            List<AccountConsumption> result = new ArrayList<>();
            if (!CollectionUtils.isEmpty(records)) {
                result = records.stream().map(this::transferConsumption).collect(Collectors.toList());
            }
            pageResult = new Page<>(list.getCurrent(), list.getSize(), list.getTotal(), list.isSearchCount());
            pageResult.setRecords(result);
        } finally {
            stopWatch.stop();
            if (stopWatch.getTotalTimeSeconds() > 3) {
                log.info("listRecord 耗时超过3秒：{}, {}", stopWatch.getTotalTimeSeconds(), param);
            }
        }
        return pageResult;
    }

    private Page<AccountRecord> doFindAccountRecords(AccountRecordQueryDTO param) {

        if(param.getMerchantUserId() == null){
            Assert.notNull(param.getUserId(),"用户Id不能为空");
            MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(param.getUserId(),param.getMerchantId());
            ofNullable(merchantUser).ifPresent(r -> param.setMerchantUserId(r.getId()));
        }
        QueryWrapper<AccountRecord> queryWrapper = getAccountRecordQueryWrapper(param);
        Page<AccountRecord> page = new Page<>(param.getPageIndex(), param.getPageSize());
        if (param.getCountSql() != null) {
            page.setSearchCount(param.getCountSql());
        }
        return accountRecordMapper.selectPage(page, queryWrapper);
    }

    private static QueryWrapper<AccountRecord> getAccountRecordQueryWrapper(AccountRecordQueryDTO param) {
        QueryWrapper<AccountRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("merchant_id", param.getMerchantId());
        queryWrapper.eq("merchant_user_id", param.getMerchantUserId());
        if (param.getBalanceType() != null) {
            List<Integer> classifies = BenefitClassifyGroupEnum.getClassifyByType(param.getBalanceType());
            if (param.isQueryHistoryBalance()) {
                classifies = Lists.newArrayList(classifies);
                classifies.add(BenefitClassifyEnum.HISTORY_BALANCE.getCode());
            }
            queryWrapper.in("benefit_classify", classifies);
        }
        if (param.getBenefitClassify() != null) {
            queryWrapper.eq("benefit_classify", param.getBenefitClassify());
        }
        if (!CollectionUtils.isEmpty(param.getBalanceTypes())) {
            List<Integer> classifies = new ArrayList<>();
            for (Integer balanceType : param.getBalanceTypes()) {
                classifies.addAll(BenefitClassifyGroupEnum.getClassifyByType(balanceType));
            }
            //增加免费使用类型
            classifies.add(BenefitClassifyEnum.FREE_TO_USE.getCode());
            if (param.isQueryHistoryBalance()) {
                classifies.add(BenefitClassifyEnum.HISTORY_BALANCE.getCode());
            }
            queryWrapper.in("benefit_classify", classifies);
        }
        if (param.getStartTime() != null) {
            queryWrapper.gt("create_time", param.getStartTime());
        } else {
            //充电桩查询冻结和解冻流水增加查询时间限制
            if (param.getRecordType() != null &&
                    (AccountRecordTypeEnum.FREEZE_RECORD.getCode().equals(param.getRecordType())
                            || AccountRecordTypeEnum.UN_FREEZE_RECORD.getCode().equals(param.getRecordType()))) {
                queryWrapper.gt("create_time", DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -7));
            }
        }
        if (param.getEndTime() != null) {
            queryWrapper.lt("create_time", param.getEndTime());
        }
        if (!CollectionUtils.isEmpty(param.getStoreIds())) {
            queryWrapper.in("store_id", param.getStoreIds());
        }
        if (!CollectionUtils.isEmpty(param.getEquipmentTypeIds())) {
            queryWrapper.in("equipment_type_id", param.getEquipmentTypeIds());
        }
        if (!CollectionUtils.isEmpty(param.getTradeType())) {
            queryWrapper.in("record_type",
                    AccountRecordTypeGroupEnum.getTradeTypesByGroupPayType(param.getTradeType()));
        }

        if (param.getRecordType() != null) {
            queryWrapper.eq("record_type", param.getRecordType());
        }
        if (!StringUtils.isEmpty(param.getOutTradeNo())) {
            queryWrapper.eq("out_trade_no", param.getOutTradeNo().trim());
        }
        if (param.getMode() != null) {
            queryWrapper.eq("mode", param.getMode());
        }
        //recordType 数组查询
        if (!CollectionUtils.isEmpty(param.getRecordTypeList())) {
            queryWrapper.in("record_type", param.getRecordTypeList());
        }
        //outTradeNo 数组查询
        if (!CollectionUtils.isEmpty(param.getOutTradeNoList())) {
            List<String> list = param.getOutTradeNoList()
                    .stream()
                    .filter(org.apache.commons.lang.StringUtils::isNotEmpty)
                    .collect(Collectors.toList());
            queryWrapper.in(CollectionUtils.isNotEmpty(list), "out_trade_no", list);
        }
        if (param.getQueryServerSource() != null && param.getQueryServerSource() == 1) {
            //C端查询,过滤掉过期数据
            queryWrapper.notIn("record_type",
                    AccountRecordTypeEnum.getAllByGroupId(Collections.singletonList(AccountRecordTypeGroupEnum.EXPIRED.getType())));
        }

        if (CollectionUtils.isNotEmpty(param.getExcludedRecordTypeList())) {
            queryWrapper.notIn("record_type", param.getExcludedRecordTypeList());
        }

        queryWrapper.lt("record_type", AccountRecordTypeEnum.SYSTEM_NOT_QUERY_RECORD.getCode());
        if (param.getHasFreezeAndUnFreezeRecord()==null || !param.getHasFreezeAndUnFreezeRecord()) {
            queryWrapper.notIn("record_type",
                    AccountRecordTypeEnum.FREEZE_RECORD.getCode(),
                    AccountRecordTypeEnum.UN_FREEZE_RECORD.getCode());
        }
        queryWrapper.not(wrapper -> wrapper.eq("benefit_classify", 15).eq("record_type", 80));
        queryWrapper.orderByDesc("create_time");
        return queryWrapper;
    }

    private AccountConsumption transferConsumption(AccountRecord record) {
        AccountConsumption accountConsumption = new AccountConsumption();
        BeanUtils.copyProperties(record, accountConsumption);
        boolean opsFlag = MemberGrowRecordModeEnum.DECREMENT.getMode().equals(record.getMode());
        String action = opsFlag ? "-" : "+";
        //
        BigDecimal coins  =null;
        BigDecimal amount  =null;
        BigDecimal payAmount  =null;

        String classifyName = "";
        List<Integer> coinClassifyValues = BenefitClassifyEnum.getBenefitClassifyCoinEnum().stream().map(BenefitClassifyEnum::getCode).collect(Collectors.toList());
        List<Integer> balanceClassifyValues = BenefitClassifyEnum.getBenefitClassifyBalanceEnum().stream().map(BenefitClassifyEnum::getCode).collect(Collectors.toList());
        balanceClassifyValues.add(BenefitClassifyEnum.HISTORY_BALANCE.getCode());
        String unit = "";
        if (coinClassifyValues.contains(record.getBenefitClassify())) {
            coins=opsFlag?record.getActualBenefit().negate():record.getActualBenefit();
            classifyName =  "余币";
            unit = "币";
        }else if (balanceClassifyValues.contains(record.getBenefitClassify())) {
            amount=opsFlag?record.getActualBenefit().negate():record.getActualBenefit();
            classifyName =  "余额";
            unit = "元";
        }
        if (StringUtils.isEmpty(unit)) {
            unit = BenefitClassifyEnum.DEVICE_USE_COUNT.getCode().equals(record.getBenefitClassify()) ? "元" : "";
        }
        accountConsumption.setUnit(unit);
        StringBuilder builder = new StringBuilder();
        accountConsumption.setLeftTopTitle(handleLeftTopTitle(record, builder));
        accountConsumption.setLeftTitleText(builder.toString());
        if (record.getTradeAmount() != null && record.getTradeAmount().compareTo(BigDecimal.ZERO)>0) {
            if (record.getTradeType() != null) {
                cn.lyy.base.communal.constant.TradeTypeEnum tradeTypeEnum = TradeTypeEnum.findTradeType(record.getTradeType());
                if (tradeTypeEnum == null) {
                    // TODO: 2021-06-22  支付类型未知
                    throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_NOT_ENOUGH);
                }
                accountConsumption.setTradeTypeName(TradeTypeGroupEnum.findGroupPayType(record.getTradeType()));
            }else {
                accountConsumption.setTradeTypeName("线上支付");
            }

            payAmount=record.getTradeAmount();
        }

        //端用户会员详情中若为启动，则无需显示 “+0元”
        if (record.getActualBenefit().compareTo(BigDecimal.ZERO) <= 0 || StringUtils.isEmpty(classifyName)) {
            accountConsumption.setRightMiddleTitle("");
        }else {
            BigDecimal bigDecimal = record.getActualBenefit();
            boolean hasNumberBenefit = bigDecimal != null && BigDecimal.ZERO.compareTo(bigDecimal) != 0;
            accountConsumption.setRightTopTitle(classifyName);
            accountConsumption.setRightMiddleValue(action + (hasNumberBenefit ? handleCoinOnlyInteger(record) : ""));
            accountConsumption.setRightMiddleTitle(classifyName + ":" + action + (hasNumberBenefit ? handleCoinOnlyInteger(record) : ""));
        }

        accountConsumption.setClassifyName(BenefitClassifyEnum.getDesc(record.getBenefitClassify()));


        //
        accountConsumption.setEquipmentValue(record.getEquipmentValue());
        accountConsumption.setActualBenefit(record.getActualBenefit());

        /**
         * 以下3个字段的数据统计不正确，待产品新出方案
         */
        accountConsumption.setCoins(ofNullable(coins).orElse(BigDecimal.ZERO));
        accountConsumption.setAmount(ofNullable(amount).orElse(BigDecimal.ZERO));
        accountConsumption.setPayAmount(ofNullable(payAmount).orElse(BigDecimal.ZERO));
        return accountConsumption;
    }

    /**
     * 余币类权益只显示整数
     * @param record
     * @return
     */
    private BigDecimal handleCoinOnlyInteger(AccountRecord record) {
        BigDecimal bigDecimal = record.getActualBenefit();
        if (bigDecimal != null && BigDecimal.ZERO.compareTo(bigDecimal) != 0) {
            Integer benefitClassify = record.getBenefitClassify();
            bigDecimal = BenefitClassifyGroupEnum.COINS.getClassify().contains(benefitClassify) ? record.getActualBenefit().setScale(0, BigDecimal.ROUND_DOWN) : record.getActualBenefit();
        }
        return bigDecimal;
    }

    /**
     * 1.免费使用(recordType=10) 即直接返回
     * 2.收款码或项目码付款的,优先项目名称（在描述中）,根据对应记录的分组，
     *      2.1) 有description 则取description
     *      2.2)else==>   显示 showText
     * 3.根据对应记录的分组id，“设备启动”则 [ record.getCommodityName()+":" + record.getActualBenefit() ]
     * 4.支付启动 classify=22 或 classify=7 ,显示 showText
     * 5.会员卡权益处理 classify=15 ,
     *      5.1） accountRecordType的分组ID=5（退款），则显示“会员卡退款”
     *      5.2）else==>  显示 showText
     * 6.判断 实际权益 actualBenefit
     *      6.1)如果 actualBenefit 是空 或等于0 ，则显示对应的 showText
     *      6.2）else==>  showText + ":" + ( 如果是币类型权益【classify为2,4,6,18,21,16,24】,即向下取整,类似4.3 取4 ; 非币类型权益直接返回实际权益数值 actualBenefit ）
     * @param record
     * @return
     */
    private String handleLeftTopTitle(AccountRecord record, StringBuilder newTopTitle) {

        if (AccountRecordTypeEnum.FREE_TO_USE.getCode().equals(record.getRecordType())) {
            String showText = AccountRecordTypeEnum.FREE_TO_USE.getShowText();
            newTopTitle.append(showText);
            return showText;
        }
        AccountRecordTypeEnum recordTypeEnum = AccountRecordTypeEnum.findByCode(record.getRecordType());
        if (recordTypeEnum == null) {
            log.error("找不到对应的账户流水类型,id:{} record_type:{}",record.getId(),record.getRecordType());
            throw new RuntimeException("找不到对应的账户流水类型");
        }

        //收款码或项目码付款的,优先项目名称（在描述中）
        if(AccountRecordTypeGroupEnum.RECEIVE_CODE.getType().equals(recordTypeEnum.getGroupId())){
            String showText =  Optional.ofNullable(record.getDescription()).orElse(recordTypeEnum.getShowText());
            newTopTitle.append(showText);
            return showText;
        }
        if (AccountRecordTypeGroupEnum.EQUIP_START.getType().equals(recordTypeEnum.getGroupId())) {
            if (!StringUtils.isEmpty(record.getCommodityName())) {
                String showText =  record.getCommodityName()+":" + record.getActualBenefit();
                newTopTitle.append(showText);
                return showText;
            }
        }
        //支付启动这两种类型特殊处理
        if(BenefitClassifyEnum.DEVICE_USE_COUNT_FAIL.getCode().equals(record.getBenefitClassify())
                || BenefitClassifyEnum.DEVICE_USE_COUNT.getCode().equals(record.getBenefitClassify())){
            String showText =  recordTypeEnum.getShowText();
            newTopTitle.append(showText);
            return showText;
        }
        //会员卡权益处理
        if(BenefitClassifyEnum.MEMBER_CARD.getCode().equals(record.getBenefitClassify())){
            if(AccountRecordTypeGroupEnum.REFUND.getType().equals(recordTypeEnum.getGroupId())){
                //退款的
                newTopTitle.append("会员卡退款");
                return "会员卡退款";
            }
            String showText = recordTypeEnum.getShowText();
            newTopTitle.append(showText);
            return showText;
        }
        BigDecimal bigDecimal = record.getActualBenefit();
        boolean hasNumberBenefit = bigDecimal != null && BigDecimal.ZERO.compareTo(bigDecimal) != 0;
        String showText = recordTypeEnum.getShowText();
        if (recordTypeEnum.getGroupId().equals(AccountRecordTypeGroupEnum.ADJUST.getType())) {
            if (MemberGrowRecordModeEnum.DECREMENT.getMode().equals(record.getMode())) {
                showText = "商家扣减";
            }else {
                showText = "商家增加";
            }
        }
        newTopTitle.append(showText);
        return hasNumberBenefit?recordTypeEnum.getShowText()+":" + handleCoinOnlyInteger(record):recordTypeEnum.getShowText();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BenefitConsumeInfoDTO callbackRealConsume(ConsumeDTO consume) {
        List<AccountBenefitAdjustDTO> list = realConsumeList(consume);
        log.debug("[权益扣减返回实扣金额]-list:{}",list );
        if (CollectionUtils.isEmpty(list)) {
            log.warn("[权益扣减返回实扣金额]-list为空");
            //虚构对象
            return makeUpBenefitConsumeInfoDTO(consume);
        }
        return convertToBenefitConsumeInfoDTO(list);
    }



    private BenefitConsumeInfoDTO convertToBenefitConsumeInfoDTO(List<AccountBenefitAdjustDTO> list) {
        BenefitConsumeInfoDTO result = new BenefitConsumeInfoDTO();
        List<BenefitConsumeDetailInfoDTO> detailInfoDTOS = new ArrayList<>();
        list.forEach(e->{
            BenefitConsumeDetailInfoDTO dto = new BenefitConsumeDetailInfoDTO();
            dto.setBenefitClassify(BenefitClassifyEnum.of(e.getClassify()));
            dto.setConsume(e.getAmount());
            detailInfoDTOS.add(dto);
        });
        result.setDetailInfos(detailInfoDTOS);
        result.setBalanceAmount(countBalanceAmount(detailInfoDTOS));
        return result;
    }

    private List<AccountBenefitAdjustDTO> realConsumeList(ConsumeDTO consume) {
        List<AccountBenefitAdjustDTO> adjustList = new ArrayList<>();
        //商家券
        List<AccountBenefitAdjustDTO>  merchantCouponList = new ArrayList<>();
        MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(consume.getUserId(), consume.getMerchantId());
        Assert.notNull(merchantUser, AccountErrorCode.ACCOUNT_NOT_FOUND.getMessage());
        //真实扣减
        boolean deduction = consume.hasDeduction();
        log.debug("[权益扣减返回实扣金额] 返回真实-是否真实扣除,true为真实扣除,deduction：{}", deduction);
        // 计算筛选出具体怎么扣权益
        consume.getConsume().forEach(consumeDetailDTO -> {
            //商家券处理
            AccountBenefitAdjustDTO  coupon  =  handleMerchantCoupon(consume,merchantUser,consumeDetailDTO,deduction) ;
            if (coupon != null) {
                merchantCouponList.add(coupon);
                return;
            }
            //0元处理
            if (deduction && BigDecimal.ZERO.compareTo(consumeDetailDTO.getAmount()) == 0) {
                handleZeroConsume(consume, merchantUser);
                return;
            }

            List<AccountBenefitWithScopeDTO> benefitList = queryBenefitForConsume(merchantUser.getId(), consume.getMerchantId(), consumeDetailDTO.getClassify(),
                    consumeDetailDTO.getBenefitId(), consume.getExcludeClassify(),false, consume.getServiceType());
            // 判断余额是否足以抵扣
            if (CollectionUtils.isEmpty(benefitList)) {
                    throw new BusinessException(AccountErrorCode.BENEFIT_NOT_FOUND);
            }
            List<AccountBenefitConsumeDTO> benefitGroupByClassify = getAccountBenefitConsumeList(consume.getMerchantId(), benefitList);
            log.debug("[权益扣减返回实扣金额] 权益扣减(查出来的所有权益) -> {}", benefitGroupByClassify);
            adjustList.addAll(calculateRealConsume(consume, consumeDetailDTO, benefitGroupByClassify, merchantUser.getId()));

        });
        if (deduction) {
            // 实际扣减权益
            this.decreaseRealConsumeAccountBenefit(adjustList);
        }

        /**
         * 真实扣除，不需要缓存
         */
        if (consume.getOrderNo() != null) {
            adjustList.addAll(merchantCouponList);
            if (!deduction) {
                return adjustList;
            }
            if (CollectionUtils.isEmpty(adjustList)) {
                log.debug("[权益扣减返回实扣金额] 权益扣减-adjustList为空:");
                return adjustList;
            }
        }
        return adjustList;
    }

    private List<AccountBenefitAdjustDTO> calculateRealConsume(ConsumeDTO consume, ConsumeDetailDTO c,
                                                    List<AccountBenefitConsumeDTO> benefitGroupByClassify,
                                                    Long merchantUserId) {
        List<AccountBenefitAdjustDTO> adjustList = new ArrayList<>();
        BigDecimal remain = c.getAmount().abs();
        int sort = 1;
        boolean negativeAllow =consume.getAllowNegative() != null && consume.getAllowNegative();
        boolean specialEquipType = checkNegativeEquipType(consume.getEquipmentTypeId());
        for (AccountBenefitConsumeDTO accountBenefitConsume : benefitGroupByClassify) {
            if (remain.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            Account account = accountMapper.selectOne(new QueryWrapper<>(new Account())
                    .eq("merchant_id", consume.getMerchantId())
                    .eq("user_id", consume.getUserId())
                    .eq("classify", accountBenefitConsume.getBenefitClassify()));
            if (account == null) {
                throw new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND);
            }
            List<AccountBenefitWithScopeDTO> benefitWithScope = accountBenefitConsume.getBenefitWithScope();
            BigDecimal classifyAccount = benefitWithScope.stream()
                    .map(AccountBenefitWithScopeDTO::getAmount)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            BigDecimal accountBalance = account.getBalance();

            /**
             * 正常品类校验
             */
            if (!negativeAllow &&  accountBalance.compareTo(BigDecimal.ZERO) <= 0 && !specialEquipType) {
                log.debug("[权益扣减返回实扣金额] 权益扣减-账号余额:{}，用户id:{} 权益类型:{}", accountBalance, consume.getUserId(), accountBenefitConsume.getBenefitClassify());
                continue;
            }

            //可以使用权益集合(包括本权益)
            List<AccountBenefitWithScopeDTO> enableBenefitWithScope = benefitWithScope.stream().filter(e -> {
                if (!checkScope(e.getScopeMap(), consume)) {
                    log.debug("判断账户权益范围不可用,benefit:{}", e.getBenefitId());
                    return false;
                }
                return true;
            }).collect(Collectors.toList());

            BigDecimal canUseMoney = enableBenefitWithScope.stream()
                    .map(AccountBenefitWithScopeDTO::getAmount)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            if (canUseMoney.compareTo(BigDecimal.ZERO) < 0) {
                //负数不允许再使用
                continue;
            }

            //明细是否多过总账
            boolean balanceLessThan = false;
            /**
             * 不是售水机先出水后计费模式或售水机 才会比较权益差异
             */
            if (!negativeAllow &&!specialEquipType && accountBalance.compareTo(classifyAccount) < 0) {
                balanceLessThan = true;
            }
            log.debug("[权益扣减返回实扣金额] 权益扣减-用户id:{} 权益类型:{}  总账余额:{},权益明细汇总:{} ,balanceLessThan:{}",
                    consume.getUserId(), accountBenefitConsume.getBenefitClassify(), accountBalance, classifyAccount, balanceLessThan);

            for (AccountBenefitWithScopeDTO accountBenefitWithScope : enableBenefitWithScope) {
                //权益负数不再处理
                if (accountBenefitWithScope.getAmount().compareTo(BigDecimal.ZERO) < 0) {
                    log.debug("权益余额负数不再处理 {}", accountBenefitWithScope.getBenefitId());
                    continue;
                }
                if (remain.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
                AccountBenefitAdjustDTO adjust = getAccountBenefitAdjustDTO(consume, merchantUserId, accountBenefitWithScope, sort);
                sort++;
                //明细统计多于总账
                if (balanceLessThan) {
                    //总账比当前权益明细多
                    if (accountBalance.compareTo(accountBenefitWithScope.getAmount()) >= 0) {
                        if (accountBenefitWithScope.getAmount().compareTo(remain) >= 0) {
                            adjust.setAmount(remain);
                            remain = BigDecimal.ZERO;
                            log.debug("[权益扣减返回实扣金额] 权益扣减-[A]当前权益明细扣除:{}", adjust.getAmount());
                        } else {
                            adjust.setAmount(accountBenefitWithScope.getAmount().abs());
                            remain = remain.add(accountBenefitWithScope.getAmount().abs().negate());
                            log.debug("[权益扣减返回实扣金额] 权益扣减-[B]当前权益明细扣除:{}", adjust.getAmount());
                        }
                        accountBalance = accountBalance.subtract(adjust.getAmount());
                    } else {
                        if (accountBalance.compareTo(remain) >= 0) {
                            adjust.setAmount(remain);
                            remain = BigDecimal.ZERO;
                            log.debug("[权益扣减返回实扣金额] 权益扣减-[C]当前权益明细扣除:{}", adjust.getAmount());
                        } else {
                            adjust.setAmount(accountBalance.abs());
                            remain =remain.add(accountBalance.abs().negate());
                            log.debug("[权益扣减返回实扣金额] 权益扣减-[D]当前权益明细扣除:{}", adjust.getAmount());
                        }
                    }
                } else {
                    if (accountBenefitWithScope.getAmount().compareTo(remain) >= 0) {
                        adjust.setAmount(remain);
                        remain = BigDecimal.ZERO;
                    } else {
                        adjust.setAmount(accountBenefitWithScope.getAmount().abs());
                        remain = remain.add(accountBenefitWithScope.getAmount().abs().negate());
                    }
                }
                adjustList.add(adjust);
            }
        }

        log.info("[权益扣减返回实扣金额] remain:{} 权益扣减(最终扣减的记录) -> {}", remain,adjustList);
        if (remain.compareTo(BigDecimal.ZERO) > 0) {
            log.warn("[权益扣减返回实扣金额] 权益扣减-未完全扣除,设备id:{} ,用户id:{},场地id:{},剩余待扣除金额:{}",
                    consume.getEquipmentId(), consume.getUserId(), consume.getStoreId(), remain);
            //throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_NOT_ENOUGH);
        }
        return adjustList;
    }

    private AccountBenefitAdjustDTO getAccountBenefitAdjustDTO(ConsumeDTO consume, Long merchantUserId, AccountBenefitWithScopeDTO accountBenefitWithScope, int sort) {
        AccountBenefitAdjustDTO adjust = new AccountBenefitAdjustDTO();
        adjust.setId(accountBenefitWithScope.getAccountBenefitId());
        adjust.setAdjustType(AdjustTypeEnum.DECREMENT);
        adjust.setClassify(accountBenefitWithScope.getClassify());
        adjust.setUserId(consume.getUserId());
        adjust.setMerchantId(consume.getMerchantId());
        adjust.setMerchantUserId(merchantUserId);
        adjust.setStoreId(consume.getStoreId());
        adjust.setEquipmentId(consume.getEquipmentId());
        adjust.setOutTradeNo(consume.getOutTradeNo());
        adjust.setOrderNo(consume.getOrderNo());
        adjust.setResource(consume.getResource());
        adjust.setOperator(consume.getOperator());

        adjust.setDescription(consume.getDescription());
        adjust.setTradeType(consume.getTradeType());
        adjust.setTradeAmount(consume.getTradeAmount());
        adjust.setCommodityId(consume.getCommodityId());
        adjust.setCommodityName(consume.getCommodityName());
        if (!StringUtils.isEmpty(consume.getStoreName())) {
            // 只取32位，避免过长保存报错
            adjust.setStoreName(LyyStringUtil.substring(consume.getStoreName(), 32));
        }
        adjust.setEquipmentTypeName(consume.getEquipmentTypeName());
        adjust.setEquipmentTypeId(consume.getEquipmentTypeId());
        adjust.setEquipmentValue(consume.getEquipmentValue());
        adjust.setRecordType(consume.getRecordType());

        //权益可使用范围
        adjust.setBenfitId(accountBenefitWithScope.getBenefitId());
        adjust.setBenefitScopeList(handleBenefitScopeList(accountBenefitWithScope.getScope()));

        //排序
        adjust.setSort(sort);
        return adjust;
    }

    private void decreaseRealConsumeAccountBenefit(List<AccountBenefitAdjustDTO> adjust) {
        if (CollectionUtils.isEmpty(adjust)) {
            return;
        }
        // 根据用户-商户用户级加锁，重试时间3000ms，锁超时5000ms
        String lockKey = RedisKey.ACCOUNT_BENEFIT_USER_UPDATE_LOCK
                + adjust.get(0).getMerchantId()
                + ":" + adjust.get(0).getUserId()
                ;
        if (redisLock.lock(lockKey, UserMemberSysConstants.YES, 5000, 3000) == null) {
            log.error("获取锁失败 -> {}", lockKey);
            throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_IS_UPDATING);
        }
        try {
            Date now = new Date();
            adjust.forEach(adjustItem -> {
                if (BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON.getCode().equals(adjustItem.getClassify())) {
                    rollBackMerchantCoupon(adjustItem);
                    return;
                }
                LambdaQueryWrapper<AccountBenefit> lambdaQueryWrapper=  new QueryWrapper<AccountBenefit>().lambda()
                        .eq(AccountBenefit::getId,adjustItem.getId())
                        .eq(AccountBenefit::getMerchantUserId,adjustItem.getMerchantUserId())
                        .eq(AccountBenefit::getMerchantId,adjustItem.getMerchantId());
                AccountBenefit accountBenefit = accountBenefitMapper.selectOne(lambdaQueryWrapper);
                if (accountBenefit == null) {
                    throw new BusinessException(AccountErrorCode.BENEFIT_NOT_FOUND);
                }
                Account account = accountMapper.selectOne(new QueryWrapper<>(new Account())
                        .eq("merchant_id", accountBenefit.getMerchantId())
                        .eq("id", accountBenefit.getAccountId()));
                if (account == null) {
                    throw new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND);
                }
                // 判断余额是否够扣减
                if (AdjustTypeEnum.DECREMENT.equals(adjustItem.getAdjustType())
                        && accountBenefit.getBalance().compareTo(adjustItem.getAmount()) < 0) {
                        throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_NOT_ENOUGH);

                }
                BigDecimal increment = adjustItem.getAmount();
                if (AdjustTypeEnum.DECREMENT.equals(adjustItem.getAdjustType())) {
                    increment = increment.negate();
                }
                // 更新账户和权益的余额
                accountBenefitMapper.increaseBalanceAndTotal(accountBenefit.getId(), accountBenefit.getMerchantId(), adjustItem.getOperator(), null, increment);
                accountMapper.increaseBalanceAndTotal(account.getId(), account.getMerchantId(), adjustItem.getOperator(), null, increment);

                // 保存变更流水
                AccountRecord accountRecord = getRecord(adjustItem, account, accountBenefit, increment, now);
                //自动打支付类型标签
                TagUserParamDTO tagUserParamDTO = new TagUserParamDTO();
                tagUserParamDTO.setTradeType(accountRecord.getTradeType());
                tagUserParamDTO.setUserType(null);
                tagUserParamDTO.setUserId(account.getUserId());
                tagUserParamDTO.setMerchantUserId(account.getMerchantUserId());
                tagUserParamDTO.setStoreName(accountRecord.getStoreName());
                tagUserParamDTO.setMerchantId(account.getMerchantId());
                merchantAutoTagAsyncHandler.autoCreateTag(tagUserParamDTO);
                accountRecordMapper.insert(accountRecord);
            });
        } finally {
            redisLock.unlock(lockKey, "Y");
        }
    }

    private static AccountRecord getRecord(AccountBenefitAdjustDTO adjustItem, Account account, AccountBenefit accountBenefit, BigDecimal increment, Date now) {
        AccountRecord accountRecord = new AccountRecord();
        accountRecord.setAccountId(account.getId());
        accountRecord.setUserId(account.getUserId());
        accountRecord.setMerchantUserId(adjustItem.getMerchantUserId());
        accountRecord.setMerchantId(account.getMerchantId());
        accountRecord.setStoreId(adjustItem.getStoreId());
        accountRecord.setEquipmentId(adjustItem.getEquipmentId());
        accountRecord.setBenefitClassify(account.getClassify());
        accountRecord.setBenefitId(accountBenefit.getBenefitId());
        accountRecord.setInitialBenefit(account.getBalance());
        accountRecord.setOriginalBenefit(increment.abs());
        accountRecord.setActualBenefit(increment.abs());
        accountRecord.setOutTradeNo(adjustItem.getOutTradeNo());
        accountRecord.setOrderNo(adjustItem.getOrderNo());
        accountRecord.setMode(adjustItem.getAdjustType().getType());
        accountRecord.setCreateTime(now);
        accountRecord.setResource(adjustItem.getResource());
        accountRecord.setDescription(adjustItem.getDescription());

        accountRecord.setTradeType(adjustItem.getTradeType());
        accountRecord.setTradeAmount(adjustItem.getTradeAmount());
        accountRecord.setCommodityName(adjustItem.getCommodityName());
        if (!StringUtils.isEmpty(adjustItem.getStoreName())) {
            // 只取32位，避免过长保存报错
            accountRecord.setStoreName(LyyStringUtil.substring(adjustItem.getStoreName(), 32));
        }
        accountRecord.setEquipmentTypeId(adjustItem.getEquipmentTypeId());
        accountRecord.setEquipmentTypeName(adjustItem.getEquipmentTypeName());
        accountRecord.setEquipmentValue(adjustItem.getEquipmentValue());
        accountRecord.setRecordType(adjustItem.getRecordType());
        accountRecord.setSort(adjustItem.getSort());
        accountRecord.setAccountBenefitId(accountBenefit.getId());
        return accountRecord;
    }


    /**
     * 虚构对象
     * @param consume
     * @return
     */
    private BenefitConsumeInfoDTO makeUpBenefitConsumeInfoDTO(ConsumeDTO consume) {
        BenefitConsumeInfoDTO result = new BenefitConsumeInfoDTO();
        List<BenefitConsumeDetailInfoDTO> detailInfos = new ArrayList<>();
        //实扣金额
        List<ConsumeDetailDTO> consumeDetailDTOS = consume.getConsume();
        for (ConsumeDetailDTO consumeDetailDTO : consumeDetailDTOS) {
            boolean hasDetailInfo = false;
            for (Integer classify : consumeDetailDTO.getClassify()) {
                //构造一个非广告红包的余额类型
                //先暂时屏蔽
                // if ( !BenefitClassifyEnum.ADVERT_RED_PACKET.getCode().equals(classify)
                //&&BenefitClassifyGroupEnum.MONEY.getClassify().contains(classify)) {
                BenefitConsumeDetailInfoDTO consumeDetailInfoDTO = new BenefitConsumeDetailInfoDTO();
                consumeDetailInfoDTO.setBenefitClassify(BenefitClassifyEnum.of(classify));
                consumeDetailInfoDTO.setConsume(BigDecimal.ZERO);
                detailInfos.add(consumeDetailInfoDTO);
                hasDetailInfo = true;
                break;
                //}
            }
            if (hasDetailInfo) {
                break;
            }
        }
        result.setDetailInfos(detailInfos);
        result.setBalanceAmount(BigDecimal.ZERO);
        return result;
    }
    /**
     * 计算余额
     * @param detailInfos
     * @return
     */
    private BigDecimal countBalanceAmount(List<BenefitConsumeDetailInfoDTO> detailInfos) {
        if (CollectionUtils.isEmpty(detailInfos)) {
            return BigDecimal.ZERO;
        }
        return detailInfos.stream()
                .filter(e ->
                        //先暂时屏蔽
                        //!BenefitClassifyEnum.ADVERT_RED_PACKET.getCode().equals(e.getBenefitClassify().getCode())
                        //&&
                        BenefitClassifyGroupEnum.MONEY.getClassify().contains(e.getBenefitClassify().getCode())
                )
                .map(BenefitConsumeDetailInfoDTO::getConsume)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    /**
     * 更新用户统计
     *
     * @param userId         用户id
     * @param merchantUserId 用户商户id
     * @param merchantId     商户id
     * @param classify       权益类型
     * @param total          总数
     * @param balance        剩余数
     */
    private void updateUserStatistics(Long userId, Long merchantUserId, Long merchantId, Integer classify, BigDecimal total, BigDecimal balance) {
        if (total == null && balance == null) {
            return;
        }
        if ((total != null && total.compareTo(BigDecimal.ZERO) == 0)
                && (balance != null && balance.compareTo(BigDecimal.ZERO) == 0)) {
            return;
        }
        UserStatisticsUpdateDTO statisticsUpdateDTO = new UserStatisticsUpdateDTO();
        statisticsUpdateDTO.setUserId(userId);
        statisticsUpdateDTO.setMerchantUserId(merchantUserId);
        statisticsUpdateDTO.setMerchantId(merchantId);
        if (BenefitClassifyGroupEnum.COINS.getClassify().contains(classify)) {
            statisticsUpdateDTO.setTotalCoins(total);
            statisticsUpdateDTO.setBalanceCoins(balance);
        } else if (BenefitClassifyGroupEnum.MONEY.getClassify().contains(classify)) {
            statisticsUpdateDTO.setTotalAmount(total);
            statisticsUpdateDTO.setBalanceAmount(balance);
        } else {
            return;
        }
        statisticsUpdateDTO.setUpdateTime(new Date());
        if (log.isDebugEnabled()) {
            log.debug("更新用户统计数据,{}", statisticsUpdateDTO);
        }
        statisticsService.updateStatistics(statisticsUpdateDTO);
    }



    /**
     * 启动-添加设备类型标签或场地类型标签
     *
     * @param merchantUser 商家用户
     * @param consume
     */
    private void taggingGroupNameOrEquipmentTypeName(MerchantUser merchantUser, ConsumeDTO consume) {
        //退款来源不打标签
        if (Objects.equals(consume.getExcludeUserTag(),true)){
            log.debug("退款来源不打标签");
            return;
        }

        boolean flag = false;
        TagUserParamDTO tagUserParamDTO = new TagUserParamDTO();
        //设备类型标签
        if (org.apache.commons.lang.StringUtils.isNotBlank(consume.getEquipmentTypeName())) {
            tagUserParamDTO.setEquipmentTypeName(consume.getEquipmentTypeName().trim());
            flag = true;
        }
        //场地类型标签
        if (org.apache.commons.lang.StringUtils.isNotBlank(consume.getStoreName())) {
            tagUserParamDTO.setStoreName(consume.getStoreName().trim());
            flag = true;
        }
        if (flag) {
            tagUserParamDTO.setMerchantId(merchantUser.getMerchantId());
            tagUserParamDTO.setMerchantUserId(merchantUser.getId());
            merchantAutoTagAsyncHandler.autoCreateTag(tagUserParamDTO);
        }
    }

}
