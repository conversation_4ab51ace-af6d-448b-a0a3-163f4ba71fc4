package com.lyy.user.domain.member.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.lyy.user.account.infrastructure.constant.MemberLiftingRuleCategoryEnum;
import com.lyy.user.account.infrastructure.member.dto.MemberLiftingRuleDTO;
import com.lyy.user.application.member.IMemberLiftingRuleService;
import com.lyy.user.domain.member.dto.MemberLiftingRuleTouchCheckDTO;
import com.lyy.user.domain.member.dto.MemberLiftingRuleTouchResultDTO;
import com.lyy.user.domain.member.entity.MemberLiftingRule;
import com.lyy.user.domain.member.repository.MemberLiftingRuleMapper;
import com.lyy.user.infrastructure.base.MerchantBaseServiceImpl;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 会员升级策略规则 服务实现类
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
@Service
@Slf4j
//@Transactional(rollbackFor = Exception.class)
public class MemberLiftingRuleServiceImpl extends MerchantBaseServiceImpl<MemberLiftingRuleMapper, MemberLiftingRule> implements IMemberLiftingRuleService {

    /**
     * 根据会员策略获取规则列表
     *
     *
     * @param merchantId
     * @param memberLiftingIdList
     * @return
     */
    @Override
    public List<MemberLiftingRule> findByMemberLifting(Long merchantId, List<Long> memberLiftingIdList) {
        return findByMemberLifting(merchantId, memberLiftingIdList, true);
    }

    /**
     * 根据会员策略获取规则列表
     *
     * @param merchantId          商户id
     * @param memberLiftingIdList 升级策略id
     * @param active              是否删除
     * @return
     */
    @Override
    public List<MemberLiftingRule> findByMemberLifting(Long merchantId, List<Long> memberLiftingIdList, boolean active) {
        QueryWrapper<MemberLiftingRule> queryWrapper = new QueryWrapper<MemberLiftingRule>()
                .eq(getShardingFieldKey(), merchantId)
                .in("member_lifting_id",memberLiftingIdList)
                .orderByAsc("member_lifting_id");
        if (active) {
            queryWrapper.and(wrapper -> wrapper.isNull("active").or().eq("active", true));
        }
        return list(queryWrapper);
    }

    /**
     * 根据策略保存规则列表信息
     *
     *
     * @param merchantId
     * @param memberLiftingId
     * @param ruleList
     * @return
     */
    @Override
    public boolean saveOfMemberLifting(Long merchantId, Long memberLiftingId, List<MemberLiftingRuleDTO> ruleList) {
        //删除原有规则
        UpdateWrapper<MemberLiftingRule> queryWrapper = new UpdateWrapper<MemberLiftingRule>()
                .eq(getShardingFieldKey(), merchantId)
                .and(wrapper -> wrapper.isNull("active").or().eq("active", true))
                .eq("member_lifting_id", memberLiftingId)
                .set("active", false);
        int del = getBaseMapper().update(null, queryWrapper);
        //增加规则
        int add;
        if (ruleList != null && !ruleList.isEmpty()) {
            List<MemberLiftingRule> list = ruleList.stream()
                    .map(rule -> {
                        MemberLiftingRule memberLiftingRule = new MemberLiftingRule();
                        BeanUtils.copyProperties(rule, memberLiftingRule, "id", "memberLiftingId");
                        memberLiftingRule.setMemberLiftingId(memberLiftingId);
                        memberLiftingRule.setMerchantId(merchantId);
                        memberLiftingRule.setCreateTime(new Date());
                        memberLiftingRule.setUpdateTime(new Date());
                        if (memberLiftingRule.getJudgeCondition() == null) {
                            memberLiftingRule.setJudgeCondition(true);
                        }
                        memberLiftingRule.setActive(true);
                        return memberLiftingRule;
                    }).collect(Collectors.toList());
            MemberLiftingRuleServiceImpl proxy = (MemberLiftingRuleServiceImpl) AopContext.currentProxy();
            proxy.saveBatch(list);
            add = list.size();
        } else {
            add = 0;
        }
        log.info("{} 会员升级策略共删除 {} 条规则记录，新增 {} 条规则记录", memberLiftingId, del, add);
        return true;
    }

    @Override
    public MemberLiftingRule saveDefaultMemberLiftingRule(MemberLiftingRuleDTO memberLiftingRuleDTO) {
        MemberLiftingRule memberLiftingRule = new MemberLiftingRule();
        memberLiftingRule.setMemberLiftingId(memberLiftingRuleDTO.getMemberLiftingId());
        memberLiftingRule.setMerchantId(memberLiftingRuleDTO.getMerchantId());
        memberLiftingRule.setCategory(MemberLiftingRuleCategoryEnum.CATEGORY_CONSUMPTION_MONEY.getValue());
        memberLiftingRule.setRangeDate((short) 0);
        memberLiftingRule.setJudgeCondition(true);

        memberLiftingRule.setCreateTime(new Date());
        memberLiftingRule.setUpdateTime(new Date());
        memberLiftingRule.setActive(true);
        if (memberLiftingRule.getJudgeCondition() == null) {
            memberLiftingRule.setJudgeCondition(true);
        }
        int result = this.baseMapper.insert(memberLiftingRule);
        if (result != 1) {
            log.error("升降级规则保存失败");
        }

        return memberLiftingRule;
    }

    /**
     * 根据升级策略删除对应的规则数据
     *
     *
     * @param merchantId
     * @param memberLiftingIds
     * @return
     */
    @Override
    public int removeByMemberLifting(Long merchantId, Collection<Long> memberLiftingIds) {
        UpdateWrapper<MemberLiftingRule> updateWrapper = new UpdateWrapper<MemberLiftingRule>()
                .eq(getShardingFieldKey(), merchantId)
                .and(wrapper -> wrapper.isNull("active").or().eq("active", true))
                .set("active", false)
                .in("member_lifting_id", memberLiftingIds);
        return getBaseMapper().update(null, updateWrapper);
    }

    /**
     * 检查触发的记录列表
     *
     * @param memberLiftingRuleTouchCheckDTO
     * @return
     */
    @Override
    public List<MemberLiftingRuleTouchResultDTO> touchLiftingRule(MemberLiftingRuleTouchCheckDTO memberLiftingRuleTouchCheckDTO) {
        if(memberLiftingRuleTouchCheckDTO.getMemberGroupIdList() == null ||
                memberLiftingRuleTouchCheckDTO.getMemberGroupIdList().isEmpty()||
                memberLiftingRuleTouchCheckDTO.getCategory() == null){
            log.warn(" 触发规则参数不合法,自动返回没有规则,{}", memberLiftingRuleTouchCheckDTO);
            return Collections.emptyList();
        }
        return getBaseMapper().touchLiftingRule(memberLiftingRuleTouchCheckDTO);
    }

}
