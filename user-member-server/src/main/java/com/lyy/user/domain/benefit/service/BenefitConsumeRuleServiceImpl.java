package com.lyy.user.domain.benefit.service;

import static java.util.Optional.ofNullable;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lyy.user.account.infrastructure.benefit.dto.ConsumeRuleSaveDTO;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.application.benefit.BenefitConsumeRuleService;
import com.lyy.user.domain.account.service.AccountServiceImpl;
import com.lyy.user.domain.benefit.entity.BenefitConsumeRule;
import com.lyy.user.domain.benefit.repository.BenefitConsumeRuleMapper;
import com.lyy.user.infrastructure.repository.benefit.BenefitCacheRepository;
import com.lyy.user.infrastructure.repository.benefit.BenefitRepository;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @create 2021/4/6 17:08
 */
@Service
@Slf4j
public class BenefitConsumeRuleServiceImpl extends ServiceImpl<BenefitConsumeRuleMapper, BenefitConsumeRule> implements BenefitConsumeRuleService {

    @Resource
    private BenefitConsumeRuleMapper benefitConsumeRuleMapper;

    private final static Integer BATCH_SIZE = 100;
    @Autowired
    private BenefitRepository benefitRepository;

    @Autowired
    private BenefitCacheRepository benefitCacheRepository;

    /**
     * 保存商户的权益消耗规则表
     *
     * @param merchantId  商户Id
     * @param operationId 操作人Id
     */
    @Override
    public void saveAllBenefitConsume(Long merchantId, Long operationId) {
        log.debug("商户权益消耗规则初始化,merchantId:{},operationId:{}", merchantId, operationId);
        //初始化默认所有的权益消耗规则
        List<BenefitConsumeRule> list = new ArrayList<>(10);
        // 充值余额
        BenefitConsumeRule ruleRechargeAmount = createBenefitConsumeRule(merchantId, 1, 1, 0, operationId, true);
        // 充值币
        BenefitConsumeRule rule = createBenefitConsumeRule(merchantId, 2, 1, 0, operationId, true);
        // 派发余额
        BenefitConsumeRule ruleGrantAmount = createBenefitConsumeRule(merchantId, 3, 2, 0, operationId, true);
        // 派发币
        BenefitConsumeRule ruleGrantCoins = createBenefitConsumeRule(merchantId, 4, 2, 0, operationId, true);
        //商家派发券
        BenefitConsumeRule ruleMerchantPayoutCoupon = createBenefitConsumeRule(merchantId, 14, 5, 2, operationId, true);
        // 红包币
        BenefitConsumeRule ruleRedCoins = createBenefitConsumeRule(merchantId, 16, 3, 2, operationId, true);
        // 红包余额
        BenefitConsumeRule ruleRedAmount = createBenefitConsumeRule(merchantId, 17, 3, 2, operationId, true);
        // 广告币
        BenefitConsumeRule ruleAdCoins = createBenefitConsumeRule(merchantId, 18, 0, 2, operationId, true);
        // 广告红包
        BenefitConsumeRule ruleRedPacket = createBenefitConsumeRule(merchantId, 19, 0, 2, operationId, true);
        // 闲时币
        BenefitConsumeRule ruleIdleCoins = createBenefitConsumeRule(merchantId, 21, 4, 0, operationId, true);

        list.add(ruleRechargeAmount);
        list.add(rule);
        list.add(ruleGrantAmount);
        list.add(ruleGrantCoins);
        list.add(ruleMerchantPayoutCoupon);
        list.add(ruleRedCoins);
        list.add(ruleRedAmount);
        list.add(ruleAdCoins);
        list.add(ruleRedPacket);
        list.add(ruleIdleCoins);
        BenefitConsumeRuleServiceImpl proxy = (BenefitConsumeRuleServiceImpl) AopContext.currentProxy();

        proxy.saveBatch(list);
        benefitCacheRepository.clearBenefitConsumeRuleCache(merchantId);
    }

    /**
     * 批量权益消耗规则处理
     *
     * @param consumeRuleSaveDTO
     */
    @Override
    public void batchSaveOrUpdateBenefitConsume(ConsumeRuleSaveDTO consumeRuleSaveDTO) {
        if (consumeRuleSaveDTO.getMerchantId() != null) {
            List<Long> list = consumeRuleSaveDTO.getMerchantIds();
            if (CollectionUtils.isEmpty(list)) {
                list = new ArrayList<>();
            }
            if (!list.contains(consumeRuleSaveDTO.getMerchantId())) {
                list.add(consumeRuleSaveDTO.getMerchantId());
            }
            consumeRuleSaveDTO.setMerchantIds(list);
        }
        if (CollectionUtils.isEmpty(consumeRuleSaveDTO.getMerchantIds())) {
            log.error("权益消耗规则保存,商户为空");
            return;
        }
        List<BenefitConsumeRule> updateList = new ArrayList<>();
        List<BenefitConsumeRule> saveList = new ArrayList<>();
        consumeRuleSaveDTO.getMerchantIds().forEach(merchantId -> {
            BenefitConsumeRule benefitConsumeRule = benefitRepository.getBenefitConsumeRule(merchantId,
                consumeRuleSaveDTO.getBenefitClassify().getCode());
            if (benefitConsumeRule == null) {
                benefitConsumeRule = createBenefitConsumeRule(merchantId, consumeRuleSaveDTO.getBenefitClassify().getCode(),
                        consumeRuleSaveDTO.getWeight(), consumeRuleSaveDTO.getExpirePriority(), consumeRuleSaveDTO.getOperator(),
                        consumeRuleSaveDTO.getIsDefault());
                saveList.add(benefitConsumeRule);
            } else {
                benefitConsumeRule.setExpirePriority(ofNullable(consumeRuleSaveDTO.getExpirePriority()).orElse(benefitConsumeRule.getExpirePriority()));
                benefitConsumeRule.setWeight(ofNullable(consumeRuleSaveDTO.getWeight()).orElse(benefitConsumeRule.getWeight()));
                benefitConsumeRule.setIsDefault(ofNullable(consumeRuleSaveDTO.getIsDefault()).orElse(consumeRuleSaveDTO.getIsDefault()));
                benefitConsumeRule.setUpdateTime(new Date());
                benefitConsumeRule.setUpdateBy(ofNullable(consumeRuleSaveDTO.getOperator()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
                updateList.add(benefitConsumeRule);
            }
        });
        if (!CollectionUtils.isEmpty(updateList)) {
            updateList.forEach(rule -> benefitConsumeRuleMapper.updateBenefitConsumeRule(rule));
        }

        if (!CollectionUtils.isEmpty(saveList)) {
            BenefitConsumeRuleServiceImpl proxy = (BenefitConsumeRuleServiceImpl) AopContext.currentProxy();
            proxy.saveBatch(saveList, BATCH_SIZE);
        }
        consumeRuleSaveDTO.getMerchantIds().forEach(benefitCacheRepository::clearBenefitConsumeRuleCache);
    }

    /**
     * 获取默认的消费规则
     *
     * @return
     */
    @Override
    public List<BenefitConsumeRule> getDefaultBenefitConsumeRule(List<Integer> classifies) {
        return benefitRepository.listBenefitConsumeRule(UserMemberSysConstants.PLATFORM_MERCHANT_ID, classifies, Boolean.FALSE);
    }

    /**
     * 创建权益消耗规则
     *
     * @param merchantId      商户id
     * @param benefitClassify 权益类型
     * @param weight          权重
     * @param expirePriority  优先级
     * @param operationId     操作人
     * @param isDefault       是否默认
     * @return
     */
    private BenefitConsumeRule createBenefitConsumeRule(Long merchantId, Integer benefitClassify,
                                                        Integer weight, Integer expirePriority,
                                                        Long operationId, Boolean isDefault) {
        BenefitConsumeRule benefitConsumeRule = new BenefitConsumeRule();
        benefitConsumeRule.setMerchantId(merchantId);
        benefitConsumeRule.setBenefitClassify(benefitClassify);
        if(benefitClassify.equals(BenefitClassifyEnum.THIRD_PLATFORM_COINS.getCode())
            || benefitClassify.equals(BenefitClassifyEnum.THIRD_PLATFORM_AMOUNT.getCode())){
            benefitConsumeRule.setWeight(ofNullable(weight).orElse(6));
            benefitConsumeRule.setExpirePriority(ofNullable(expirePriority).orElse(0));
        } else {
            benefitConsumeRule.setWeight(weight);
            benefitConsumeRule.setExpirePriority(expirePriority);
        }
        benefitConsumeRule.setIsDefault(isDefault);
        benefitConsumeRule.setIsActive(Boolean.TRUE);
        benefitConsumeRule.setCreateBy(ofNullable(operationId).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        benefitConsumeRule.setUpdateBy(ofNullable(operationId).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        benefitConsumeRule.setCreateTime(new Date());
        benefitConsumeRule.setUpdateTime(new Date());
        return benefitConsumeRule;
    }
}
