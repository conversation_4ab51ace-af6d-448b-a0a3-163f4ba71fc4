package com.lyy.user.domain.benefit.service;

import static java.util.Optional.ofNullable;

import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.infrastructure.execption.BusinessException;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2021/4/6 14:46
 */
@Component
public class GenerateFactory {


    private final Map<String, BenefitGenerateHandler> handlerMap = new HashMap<>();

    @Autowired
    public GenerateFactory(Map<String, BenefitGenerateHandler> handlerMap) {
        this.handlerMap.clear();
        handlerMap.forEach(this.handlerMap::put);
    }

    public BenefitGenerateHandler getHandler(String generateType){
        return ofNullable(handlerMap.get(generateType)).orElseThrow(() -> new BusinessException(GlobalErrorCode.BEAN_INIT_EXCEPTION.getCode(), String.format("beanName:%s, 不存在", generateType)));
    }
}
