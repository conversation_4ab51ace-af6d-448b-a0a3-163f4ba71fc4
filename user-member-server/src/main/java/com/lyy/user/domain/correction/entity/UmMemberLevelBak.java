package com.lyy.user.domain.correction.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 会员等级
 * @TableName um_member_level_bak
 */
@Data
public class UmMemberLevelBak implements Serializable {

    private static final long serialVersionUID = -7193800353015253135L;
    /**
     * 
     */
    private Long id;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 等级名称
     */
    private String name;

    /**
     * 成长值
     */
    private Long growValue;

    /**
     * 会员组
     */
    private Long memberGroupId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 是否有效 true 有效,false 无效
     */
    private Boolean active;
}