package com.lyy.user.domain.member.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import com.lyy.user.account.infrastructure.member.dto.MemberInfoDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberUserPageDTO;
import com.lyy.user.domain.member.dto.MemberLevelInfoDTO;
import com.lyy.user.domain.member.entity.Member;
import com.lyy.user.domain.member.entity.MemberLifting;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 会员表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
public interface MemberMapper extends BaseMapper<Member> {

    Member selectByIdForUpdate(Long id);

    Integer updateMemberByIdAndMerchantId(@Param("member") Member member,@Param("id") Long id,@Param("merchantId") Long merchantId);

    /**
     * 根据记录信息
     * @param memberLifting
     * @param startTime
     * @param endTime
     * @param memberStartTime
     * @return
     */
    List<Member> getNotRecordOfTime(@Param("memberLifting") MemberLifting memberLifting, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime,@Param("memberStartTime")  LocalDateTime memberStartTime);
    /**
     * 分页获取用户的会员信息
     *
     * @param merchantId
     * @param userId
     * @return
     */
    Page<MemberUserPageDTO> findPageMemberUser(@Param("merchantId") Long merchantId, @Param("userId") Long userId);

    /**
     * 获取会员组中的会员信息
     * @param merchantId
     * @param memberGroupId
     * @param memberLevelId
     * @return
     */
    Page<MemberInfoDTO> findMemberByMemberGroup(@Param("merchantId") Long merchantId,@Param("memberGroupId") Long memberGroupId,@Param("memberLevelId") Long memberLevelId);

    /**
     * 查询有效未过期的用户会员
     * @param page
     * @param merchantId
     * @param userId
     * @param now  当前时间
     */
    com.baomidou.mybatisplus.extension.plugins.pagination.Page<MemberUserPageDTO> findValidMemberUser(com.baomidou.mybatisplus.extension.plugins.pagination.Page page,@Param("merchantId") Long merchantId,@Param("userId") Long userId,@Param("now") Date now);

    /**
     * 查询商户会员等级
     * @param merchantId
     * @param userId
     * @return
     */
    MemberLevelInfoDTO selectMemberLevelName(@Param("merchantId") Long merchantId, @Param("userId") Long userId);

    /**
     * 批量根据商户用户id查询会员等级信息
     * @param merchantId
     * @param merchantUserIdSet
     * @return
     */
    List<MemberLevelInfoDTO> batchSelectMemberLevelInfo(@Param("merchantId") Long merchantId, @Param("merchantUserIdSet") Set<Long> merchantUserIdSet);

    /**
     * 根据id更新会员的结束时间
     * @param merchantId
     * @param id
     * @param memberEndTime 会员结束时间
     * @return
     */
    Integer updateMemberEndTimeById(@Param("merchantId") Long merchantId, @Param("id") Long id, @Param("memberEndTime") Date memberEndTime);
}
