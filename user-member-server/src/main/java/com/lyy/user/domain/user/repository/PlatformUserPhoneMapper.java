package com.lyy.user.domain.user.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneDTO;
import com.lyy.user.domain.user.entity.PlatformUserPhone;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PlatformUserPhoneMapper extends BaseMapper<PlatformUserPhone> {
    /**
     * 更新平台用户电话状态为不可用
     * @param ids
     * @return
     */
    int updateTelephoneByIds(@Param("ids") List<Long> ids,
                             @Param("active") Boolean active ,
                             @Param("dto") PlatformUserPhoneDTO dto
    );

    PlatformUserPhone selectVenue(@Param("merchantId") Long merchantId,
                                  @Param("telephone") String telephone);

    /**
     * 更新手机号状态
     *
     * @param userId     用户id
     * @param merchantId 商户id
     * @param active     状态
     * @param operatorId 操作人
     * @return
     */
    int updateByMerchantIdAndUserId(@Param("userId") Long userId,
                                    @Param("merchantId") Long merchantId,
                                    @Param("active") Boolean active,
                                    @Param("operatorId") Long operatorId);
}