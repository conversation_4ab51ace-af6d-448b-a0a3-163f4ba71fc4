package com.lyy.user.domain.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.lyy.user.account.infrastructure.account.dto.BenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.UserAccountBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.*;
import com.lyy.user.account.infrastructure.common.DataList;
import com.lyy.user.application.account.AccountBenefitService;
import com.lyy.user.application.benefit.SmallVenueAccountService;
import com.lyy.user.domain.account.entity.AccountBenefit;
import com.lyy.user.domain.account.repository.AccountBenefitMapper;
import com.lyy.user.domain.statistics.repository.SmallVenueStoredStatisticsMapper;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/5/11 16:44
 */
@Slf4j
@Service
public class AccountBenefitServiceImpl implements AccountBenefitService {

    @Resource
    private AccountBenefitMapper accountBenefitMapper;

    @Autowired
    private SmallVenueAccountService smallVenueAccountService;

    @Autowired
    private SmallVenueStoredStatisticsMapper smallVenueStoredStatisticsMapper;

    /**
     * 权益详情
     * @param benefitDetailSelectDTO
     * @return
     */
    @Override
    public DataList<SmallVenueBenefitDetailDTO> smallVenueBenefitDetail(BenefitDetailSelectDTO benefitDetailSelectDTO) {
        Integer pageIndex = benefitDetailSelectDTO.getPageIndex();
        Integer pageSize = benefitDetailSelectDTO.getPageSize();
        if (benefitDetailSelectDTO.getMerchantBenefitClassifyId() != null) {
            benefitDetailSelectDTO.setMerchantBenefitClassifyIds(Arrays.asList(benefitDetailSelectDTO.getMerchantBenefitClassifyId()));
        }
        Page<SmallVenueBenefitDetailDTO> page = new Page<>(pageIndex, pageSize);
        if (benefitDetailSelectDTO.getCountSql() != null) {
            page.setSearchCount(benefitDetailSelectDTO.getCountSql());
        }
        IPage<SmallVenueBenefitDetailDTO> smallVenueBenefitDetailDTOIPage = accountBenefitMapper.smallVenueBenefitDetail(page, benefitDetailSelectDTO);
        List<SmallVenueBenefitDetailDTO> records = smallVenueBenefitDetailDTOIPage.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            /*//获取权益明细使用记录
            AvailableBenefitQueryDTO availableBenefitQueryDTO = new AvailableBenefitQueryDTO()
                    .setMerchantId(benefitDetailSelectDTO.getMerchantId())
                    .setUserId(benefitDetailSelectDTO.getUserId())
                    .setCardNo(records.get(0).getCardNo())
                    .setMerchantBenefitIds(benefitDetailSelectDTO.getMerchantBenefitClassifyIds());

            List<SmallVenueUserAvailableBenefitDTO> userAvailableBenefit = smallVenueAccountService.findUserAvailableBenefit(availableBenefitQueryDTO);
            //转accountBenefitId和useNum的map
            Map<Long, BigDecimal> useNumMap = userAvailableBenefit.stream()
                    .collect(HashMap::new, (m, v) -> m.put(v.getAccountBenefitId(), v.getUseNum()), HashMap::putAll);*/

            List<Long> accountBenefitIds = records.stream().map(SmallVenueBenefitDetailDTO::getAccountBenefitId).collect(Collectors.toList());
            Map<Long, BigDecimal> useNumMap = Optional.ofNullable(smallVenueAccountService.getAccountBenefitUse(benefitDetailSelectDTO.getMerchantId(),
                                                benefitDetailSelectDTO.getUserId(), accountBenefitIds, records.get(0).getMerchantUserId())).orElse(new HashMap<>());

            //设置useNum
            records = records.stream().map(smallVenueBenefitDetailDTO -> {
                smallVenueBenefitDetailDTO.setTodayUseNum(useNumMap.get(smallVenueBenefitDetailDTO.getAccountBenefitId()));
                return smallVenueBenefitDetailDTO;
            }).collect(Collectors.toList());
        }
        return new DataList<>(smallVenueBenefitDetailDTOIPage.getTotal(), records, smallVenueBenefitDetailDTOIPage.getPages());
    }

    @Override
    public DataList<SmallVenueBenefitDetailDTO> queryAccountBenefitList(AccountBenefitReqDTO accountBenefitReqDTO) {
        Integer pageIndex = accountBenefitReqDTO.getPageIndex();
        Integer pageSize = accountBenefitReqDTO.getPageSize();
        Page<SmallVenueBenefitDetailDTO> page = new Page<>(pageIndex, pageSize);
        page.setSearchCount(false);
        IPage<SmallVenueBenefitDetailDTO> smallVenueBenefitDetailDTOIPage = accountBenefitMapper.queryAccountBenefitList(page, accountBenefitReqDTO);
        List<SmallVenueBenefitDetailDTO> records = smallVenueBenefitDetailDTOIPage.getRecords();
        return new DataList<>(smallVenueBenefitDetailDTOIPage.getTotal(), records, smallVenueBenefitDetailDTOIPage.getPages());
    }

    @Override
    public List<UserAccountBenefitDTO> findBenefitByIds(BenefitQueryDTO query) {
        List<AccountBenefit> accountBenefits = accountBenefitMapper.selectList(
            Wrappers.<AccountBenefit>lambdaQuery().eq(AccountBenefit::getMerchantId, query.getMerchantId())
                .eq(Objects.nonNull(query.getUserId()), AccountBenefit::getUserId, query.getUserId())
                .in(AccountBenefit::getId, query.getAccountBenefitIds()));
        if (CollectionUtils.isEmpty(accountBenefits)) {
            return Lists.newArrayList();
        }
        return accountBenefits.stream().map(accountBenefit -> {
            UserAccountBenefitDTO userAccountBenefit = new UserAccountBenefitDTO();
            userAccountBenefit.setAccountBenefitId(accountBenefit.getId());
            userAccountBenefit.setMerchantId(accountBenefit.getMerchantId());
            userAccountBenefit.setUserId(accountBenefit.getUserId());
            userAccountBenefit.setMerchantUserId(accountBenefit.getMerchantUserId());
            userAccountBenefit.setStoreId(accountBenefit.getStoreId());
            userAccountBenefit.setClassify(accountBenefit.getClassify());
            userAccountBenefit.setMerchantBenefitClassifyId(accountBenefit.getMerchantBenefitClassifyId());
            userAccountBenefit.setUseRuleId(accountBenefit.getUseRuleId());
            userAccountBenefit.setTotal(accountBenefit.getTotal());
            userAccountBenefit.setBalance(accountBenefit.getBalance());
            userAccountBenefit.setCreateTime(accountBenefit.getCreateTime());
            userAccountBenefit.setUpdateTime(accountBenefit.getUpdateTime());
            userAccountBenefit.setExpiryDateCategory(accountBenefit.getExpiryDateCategory());
            userAccountBenefit.setUpTime(accountBenefit.getUpTime());
            userAccountBenefit.setDownTime(accountBenefit.getDownTime());
            return userAccountBenefit;
        }).collect(Collectors.toList());
    }

    /**
     * 查询权益统计数据（根据门店进行分组）
     * @param merchantId
     * @param userId
     * @param accountId
     * @return
     */
    @Override
    public BenefitStatisticsDetailDTO benefitStatisticsGroupByStoreId(Long merchantId, Long userId, Long accountId) {
        //权益统计数据
        List<UserBenefitStatisticsDTO> userBenefitStatisticsDTOS = accountBenefitMapper.benefitStatisticsGroupByStoreId(merchantId, userId, accountId);
        if (CollectionUtils.isEmpty(userBenefitStatisticsDTOS)) {
            return null;
        }

        //根据storeId分组
        Map<Long, List<UserBenefitStatisticsDTO>> benefitStatisticsGroupByStoreId = userBenefitStatisticsDTOS.stream()
                .filter(userBenefitStatisticsDTO -> userBenefitStatisticsDTO.getStoreId() != null)
                .collect(Collectors.groupingBy(UserBenefitStatisticsDTO::getStoreId));
        Set<Map.Entry<Long, List<UserBenefitStatisticsDTO>>> entrySet = benefitStatisticsGroupByStoreId.entrySet();
        List<BenefitStatisticsListDTO> benefitStatisticsListDTOS = new ArrayList<>();
        for (Map.Entry<Long, List<UserBenefitStatisticsDTO>> entry : entrySet) {
            //数据转换
            List<BenefitStatisticsDTO> benefitStatisticsList = entry.getValue().stream().map(userBenefitStatisticsDTO -> new BenefitStatisticsDTO()
                    .setBalance(userBenefitStatisticsDTO.getBalance())
                    .setNum(userBenefitStatisticsDTO.getBalance().compareTo(BigDecimal.ZERO) > 0 ? 1 : 0)
                    .setMerchantBenefitClassifyId(userBenefitStatisticsDTO.getMerchantBenefitClassifyId())).collect(Collectors.toList());

            //MerchantBenefitClassifyId相同需要合并一下
            benefitStatisticsList = benefitStatisticsList.stream()
                    .collect(Collectors.toMap(BenefitStatisticsDTO::getMerchantBenefitClassifyId, a -> a,
                            (o1, o2) -> {
                                //设置balance
                                BigDecimal o1Balanace = Optional.ofNullable(o1.getBalance()).orElse(BigDecimal.ZERO);
                                BigDecimal o2Balanace = Optional.ofNullable(o2.getBalance()).orElse(BigDecimal.ZERO);
                                o1.setBalance(o1Balanace.add(o2Balanace));
                                //设置num
                                Integer o1Num = Optional.ofNullable(o1.getNum()).orElse(0);
                                Integer o2Num = Optional.ofNullable(o2.getNum()).orElse(0);
                                o1.setNum(o1Num + o2Num);
                                return o1;
                            })).values().stream().collect(Collectors.toList());

            benefitStatisticsListDTOS.add(new BenefitStatisticsListDTO()
                    .setStoreId(entry.getKey())
                    .setBenefit(benefitStatisticsList));
        }

        return new BenefitStatisticsDetailDTO()
                .setAccountId(accountId)
                .setBenefitStatistics(benefitStatisticsListDTOS);
    }
}
