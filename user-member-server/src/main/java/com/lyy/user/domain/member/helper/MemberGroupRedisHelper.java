package com.lyy.user.domain.member.helper;

import com.lyy.user.infrastructure.constants.RedisKey;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/03
 */
@Slf4j
@Component
public class MemberGroupRedisHelper {

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 设置商户会员组存在标记
     */
    public boolean setMemberGroupFlag(Long merchantId, boolean hasGroups) {
        if (merchantId == null) {
            return false;
        }
        try {
            String key = RedisKey.MEMBER_GROUP_FLAG + merchantId;
            RBucket<Boolean> bucket = redissonClient.getBucket(key);
            bucket.set(hasGroups, 24, TimeUnit.HOURS);
            return true;
        } catch (Exception e) {
            log.error("设置商户会员组标记失败, merchantId: {}", merchantId, e);
            return false;
        }
    }

    /**
     * 获取商户会员组存在标记
     */
    public Boolean getMemberGroupFlag(Long merchantId) {
        if (merchantId == null) {
            return null;
        }
        try {
            String key = RedisKey.MEMBER_GROUP_FLAG + merchantId;
            RBucket<Boolean> bucket = redissonClient.getBucket(key);
            return bucket.get();
        } catch (Exception e) {
            log.error("获取商户会员组标记失败, merchantId: {}", merchantId, e);
            return null;
        }
    }
}
