package com.lyy.user.domain.member.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MemberLevelInfoDTO {

    /**
     * 用户等级id
     */
    private Long memberLevelId;

    /**
     * 用户等级名称
     */
    private String memberLevelName;

    /**
     * 成长值
     */
    private Long growValue;

    /**
     * 商户用户id
     */
    private Long merchantUserId;
}
