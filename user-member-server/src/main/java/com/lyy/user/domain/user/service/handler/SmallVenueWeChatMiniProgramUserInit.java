package com.lyy.user.domain.user.service.handler;

import static java.util.Optional.ofNullable;

import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneDTO;
import com.lyy.user.app.interfaces.facade.dto.UserVO;
import com.lyy.user.application.user.IPlatformUserPhoneService;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.domain.user.service.AbstractUserInitService;
import com.lyy.user.domain.user.service.UserInitHandler;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.interfaces.assembler.UserAssembler;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName: SmallVenueWeChatMiniProgramUserInit
 * @description: 小场地微信小程序用户初始化
 * @author: pengkun
 * @date: 2022/01/08
 **/
@Slf4j
@Service("smallVenueWeChatMiniProgramUserInit")
public class SmallVenueWeChatMiniProgramUserInit extends AbstractUserInitService implements UserInitHandler {

    @Autowired
    private IPlatformUserPhoneService platformUserPhoneService;
    /**
     * 用户初始化处理方法
     *
     * @param dto
     * @return
     */
    @Override
    public UserInfoDTO handle(UserCreateDTO dto) {
        if (log.isDebugEnabled()) {
            log.debug("小场地微信小程序用户初始化:{}", dto);
        }
        if (StringUtils.isBlank(dto.getOpenid())) {
            throw new BusinessException(UserErrorCode.USER_OPEN_ID_EMPTY_ERROR);
        }
        updateOpenIdAndUnionId(dto);
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        if (StringUtils.isBlank(dto.getTelephone())) {
            userInfoDTO = smallVenueWeChatUserInitWithoutTelephone(dto);
        } else {
            MerchantUser merchantUser = getMerchantUser(dto);
            UserVO userDTO = null;
            if (merchantUser == null) {
                if (StringUtils.isNotBlank(dto.getUnionid())) {
                    userDTO = super.getByUnionId(dto.getUnionid());
                }

                if (userDTO == null && StringUtils.isNotBlank(dto.getAppId())
                        && StringUtils.isNotBlank(dto.getOpenid())) {
                    userDTO = super.getByAppIdAndOpenId(dto.getAppId(), dto.getOpenid());
                    if (userDTO != null && StringUtils.isBlank(userDTO.getUnionId())) {
                        //unionId为空需要更新平台用户unionId
                        UserVO updateDTO = UserAssembler.INSTANCE.toUserVO(dto);
                        updateDTO.setId(userDTO.getId());
                        updatePlatformUser(updateDTO);
                    }
                }
                Long userId;
                if (userDTO == null) {
                    //初始化平台用户信息
                    userId = super.initPlatformUser(dto, true);
                    super.initUserApp(dto.getAppId(), dto.getOpenid(), userId);
                } else {
                    //根据手机号码查询平台用户
                    UserVO telephoneUser = getPlatformUserByTelephone(dto.getTelephone(), UserMemberSysConstants.PLATFORM_MERCHANT_ID);
                    if (telephoneUser != null) {
                        if (!userDTO.getId().equals(telephoneUser.getId())) {
                            log.info("多金宝平台用户合并处理,旧用户id:{},新用户id:{}", userDTO.getId(), telephoneUser.getId());
                            super.updateUserAndIgnore(userDTO.getId(), telephoneUser.getId());
                            updatePlatformUserTelephoneActive(userDTO.getId(), UserMemberSysConstants.PLATFORM_MERCHANT_ID, false, telephoneUser.getId());
                            //更新会员信息
                            telephoneUser.setUnionId(userDTO.getUnionId());
                            telephoneUser.setOpenId(userDTO.getOpenId());
                            telephoneUser.setAppId(userDTO.getAppId());
                            telephoneUser.setHeadImg(userDTO.getHeadImg());
                            telephoneUser.setName(userDTO.getName());
                            super.updatePlatformUser(telephoneUser);
                            userId = telephoneUser.getId();
                        } else {
                            userId = userDTO.getId();
                        }
                    } else {
                        userId = userDTO.getId();
                        if (StringUtils.isNotBlank(dto.getTelephone()) && !dto.getTelephone().equals(userDTO.getTelephone())) {
                            UserVO updateDTO = new UserVO();
                            updateDTO.setId(userDTO.getId());
                            updateDTO.setTelephone(dto.getTelephone());
                            updatePlatformUser(updateDTO);
                            savePlatformUserPhone(userDTO.getId(), dto.getTelephone());
                        }
                    }
                }
                userInfoDTO.setLyyUserId(userId);
                userInfoDTO.setIsactive(userDTO.getIsActive());
                //初始化商户用户信息
                if (Objects.nonNull(dto.getMerchantUserCreateDTO())
                        && dto.getMerchantUserCreateDTO().getMerchantId() != null
                        && dto.getMerchantUserCreateDTO().getMerchantId() > 0) {
                    //初始化商户用户
                    dto.getMerchantUserCreateDTO().setUserId(ofNullable(dto.getMerchantUserCreateDTO().getUserId()).orElse(userId));
                    if (StringUtils.isBlank(dto.getMerchantUserCreateDTO().getTelephone())) {
                        dto.getMerchantUserCreateDTO().setTelephone(dto.getTelephone());
                    }
                    Long merchantUserId = initSmallVenueMerchantUser(dto);
                    //初始化用户会员等级
                    super.initUserMember(userId, dto.getMerchantUserCreateDTO().getMerchantId());
                    userInfoDTO.setMerchantUserId(merchantUserId);
                    userInfoDTO.setMerchantId(dto.getMerchantUserCreateDTO().getMerchantId());
                }
            } else {
                if (StringUtils.isNotBlank(dto.getAppId())
                        && StringUtils.isNotBlank(dto.getOpenid())) {
                    userDTO = super.getByAppIdAndOpenId(dto.getAppId(), dto.getOpenid());
                }

                if (userDTO == null && StringUtils.isNotBlank(dto.getUnionid())) {
                    userDTO = super.getByUnionId(dto.getUnionid());
                }

                if (userDTO == null) {
                    userDTO = super.userRepository.getByUserId(merchantUser.getUserId());
                    if (!userDTO.getOpenId().equals(dto.getOpenid())
                            || (StringUtils.isNotBlank(dto.getUnionid()) && !userDTO.getUnionId().equals(dto.getUnionid()))) {
                        //更新平台用户信息
                        UserVO updateDTO = UserAssembler.INSTANCE.toUserVO(dto);
                        updateDTO.setId(userDTO.getId());
                        super.updatePlatformUser(updateDTO);
                        if (StringUtils.isNotBlank(dto.getAppId()) && StringUtils.isNotBlank(dto.getOpenid())) {
                            super.initUserApp(dto.getAppId(), dto.getOpenid(), userDTO.getId());
                        }
                    }
                } else {
                    if (!userDTO.getId().equals(merchantUser.getUserId())) {
                        //平台用户存在
                        //账户合并
                        log.info("多金宝账户合并处理,旧用户id:{},新用户id:{},商户id:{}", userDTO.getId(), merchantUser.getUserId(), merchantUser.getMerchantId());
                        super.updateUserAndIgnore(userDTO.getId(), merchantUser.getUserId());
                        updatePlatformUserTelephoneActive(userDTO.getId(), UserMemberSysConstants.PLATFORM_MERCHANT_ID, false, merchantUser.getUserId());
                        updatePlatformUserTelephoneActive(userDTO.getId(), merchantUser.getMerchantId(), false, merchantUser.getUserId());
                        UserVO updateUser = super.getPlatformUserByUserId(merchantUser.getUserId());
                        //更新会员信息
                        updateUser.setUnionId(userDTO.getUnionId());
                        updateUser.setOpenId(userDTO.getOpenId());
                        updateUser.setAppId(userDTO.getAppId());
                        updateUser.setHeadImg(userDTO.getHeadImg());
                        updateUser.setName(userDTO.getName());
                        if (StringUtils.isBlank(updateUser.getTelephone())) {
                            updateUser.setTelephone(merchantUser.getTelephone());
                            //保存手机号码记录
                            savePlatformUserPhone(merchantUser.getUserId(), merchantUser.getTelephone());
                        }
                        super.updatePlatformUser(updateUser);

                        updateMerchantUser(merchantUser, userDTO.getHeadImg(), userDTO.getName());
                    } else {
                        if (StringUtils.isBlank(userDTO.getTelephone())) {
                            UserVO user = new UserVO();
                            user.setTelephone(merchantUser.getTelephone());
                            user.setId(userDTO.getId());
                            super.updatePlatformUser(user);
                            savePlatformUserPhone(merchantUser.getUserId(), merchantUser.getTelephone());
                        }
                    }
                }
                userInfoDTO.setMerchantUserId(merchantUser.getId());
                userInfoDTO.setMerchantId(merchantUser.getMerchantId());
                userInfoDTO.setLyyUserId(merchantUser.getUserId());
                userInfoDTO.setIsactive(userDTO.getIsActive());
            }
        }
        return userInfoDTO;
    }

    /**
     * 多金宝微信小程序用户初始化(不授权手机号码)
     *
     * @param dto
     * @return
     */
    private UserInfoDTO smallVenueWeChatUserInitWithoutTelephone(UserCreateDTO dto) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        UserVO userDTO = null;
        if (StringUtils.isNotBlank(dto.getUnionid())) {
            userDTO = super.getByUnionId(dto.getUnionid());
        }
        if (userDTO == null && StringUtils.isNotBlank(dto.getAppId())
                && StringUtils.isNotBlank(dto.getOpenid())) {
            userDTO = super.getByAppIdAndOpenId(dto.getAppId(), dto.getOpenid());
            if (userDTO != null && StringUtils.isBlank(userDTO.getUnionId())) {
                //unionId为空需要更新平台用户unionId
                UserVO updateDTO = UserAssembler.INSTANCE.toUserVO(dto);
                updateDTO.setId(userDTO.getId());
                updatePlatformUser(updateDTO);
            }
        }
        Long userId;
        if (userDTO == null) {
            //初始化平台用户信息
            userId = super.initPlatformUser(dto, true);
            super.initUserApp(dto.getAppId(), dto.getOpenid(), userId);
        } else {
            userId = userDTO.getId();
        }
        userInfoDTO.setLyyUserId(userId);
        userInfoDTO.setIsactive(userInfoDTO.getIsactive());
        if (Objects.nonNull(dto.getMerchantUserCreateDTO())
                && dto.getMerchantUserCreateDTO().getMerchantId() != null
                && dto.getMerchantUserCreateDTO().getMerchantId() > 0) {
            //初始化商户用户
            dto.getMerchantUserCreateDTO().setUserId(ofNullable(dto.getMerchantUserCreateDTO().getUserId()).orElse(userId));
            Long merchantUserId = super.initSmallVenueMerchantUser(dto);
            //初始化用户会员等级
            super.initUserMember(userId, dto.getMerchantUserCreateDTO().getMerchantId());
            userInfoDTO.setMerchantId(dto.getMerchantUserCreateDTO().getMerchantId());
            userInfoDTO.setMerchantUserId(merchantUserId);
        }
        return userInfoDTO;
    }

    /**
     * 保存平台用户手机号码
     *
     * @param userId    用户id
     * @param telephone 手机号码
     */
    private void savePlatformUserPhone(Long userId, String telephone) {
        PlatformUserPhoneDTO userPhoneDTO = new PlatformUserPhoneDTO();
        userPhoneDTO.setUserId(userId);
        userPhoneDTO.setMerchantId(UserMemberSysConstants.PLATFORM_MERCHANT_ID);
        userPhoneDTO.setTelephone(telephone);
        userPhoneDTO.setOperatorId(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID);
        userPhoneDTO.setSystemFlag(UserMemberSysConstants.SMALL_VENUE_SYSTEM_FLAG);
        platformUserPhoneService.saveOrUpdatePlatformUserPhone(userPhoneDTO);
    }

    /**
     * 更新商户用户头像和名称
     *
     * @param merchantUser
     * @param headImg
     * @param name
     */
    private void updateMerchantUser(MerchantUser merchantUser, String headImg, String name) {
        //商户用户头像和昵称更新
        MerchantUser updateMerchantUser = new MerchantUser();
        updateMerchantUser.setId(merchantUser.getId());
        updateMerchantUser.setMerchantId(merchantUser.getMerchantId());
        updateMerchantUser.setHeadImg(headImg);
        updateMerchantUser.setName(name);
        updateMerchantUserInfo(updateMerchantUser);
    }

    /**
     * 根据系统前缀更新openId和unionId
     * @param dto
     */
    private void updateOpenIdAndUnionId(UserCreateDTO dto) {
        if (StringUtils.isNotBlank(dto.getSysPrefix())) {
            if (StringUtils.isNotBlank(dto.getOpenid()) && !dto.getOpenid().startsWith(dto.getSysPrefix())) {
                dto.setOpenid(dto.getSysPrefix() + dto.getOpenid());
            }
            if (StringUtils.isNotBlank(dto.getUnionid()) && !dto.getUnionid().startsWith(dto.getSysPrefix())) {
                dto.setUnionid(dto.getSysPrefix() + dto.getUnionid());
            }
        }
    }
}
