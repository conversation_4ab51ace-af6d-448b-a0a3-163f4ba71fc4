package com.lyy.user.domain.statistics.service;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.member.infrastructure.AccountErrorCode;
import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.idempotent.core.annotation.Idempotent;
import com.lyy.lock.redis.RedisLock;
import com.lyy.user.account.infrastructure.constant.TimeScopeEnum;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantStatisticsRecordDTO;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserRankConditionDTO;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserRankRecordDTO;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserStatisticsDailyDTO;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserStatisticsListDTO;
import com.lyy.user.account.infrastructure.statistics.dto.StatisticsUserQueryDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsConditionDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsRecordDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsUpdateDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserDTO;
import com.lyy.user.application.statistics.StatisticsService;
import com.lyy.user.application.user.IMerchantUserService;
import com.lyy.user.domain.statistics.entity.MerchantStatistics;
import com.lyy.user.domain.statistics.entity.Statistics;
import com.lyy.user.domain.statistics.entity.StatisticsDaily;
import com.lyy.user.domain.statistics.repository.MerchantStatisticsMapper;
import com.lyy.user.domain.statistics.repository.StatisticsDailyMapper;
import com.lyy.user.domain.statistics.repository.StatisticsMapper;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.domain.user.repository.MerchantUserMapper;
import com.lyy.user.infrastructure.config.HintManagerHolder;
import com.lyy.user.infrastructure.constants.RedisKey;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.repository.user.MerchantUserRepository;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/19 14:43
 */
@Slf4j
@Service
public class StatisticsServiceImpl implements StatisticsService {

    @Resource
    private StatisticsMapper statisticsMapper;

    @Resource
    private StatisticsDailyMapper statisticsDailyMapper;

    @Resource
    private MerchantStatisticsMapper merchantStatisticsMapper;

    @Resource
    private MerchantUserMapper merchantUserMapper;
    @Resource
    private MerchantUserRepository merchantUserRepository;
    @Autowired
    private IMerchantUserService merchantUserService;

    @Resource
    private RedisLock redisLock;

    @Override
    public void updateStatistics(UserStatisticsUpdateDTO param) {

        if (param.getMerchantUserId() == null) {
            Assert.notNull(param.getUserId(),"用户ID不能为空");
            MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(param.getUserId(), param.getMerchantId());
            if (merchantUser == null) {
                throw new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR);
            }
            param.setMerchantUserId(merchantUser.getId());
        }
        // 新用户中心存在没有userId
        if(param.getUserId() == null){
            Assert.notNull(param.getMerchantUserId(),"商户用户ID不能为空");
            MerchantUser merchantUser = merchantUserRepository.getByIdAndMerchantId(param.getMerchantUserId(),param.getMerchantId());
            Assert.notNull(merchantUser,"商户用户不存在");
            param.setUserId(merchantUser.getUserId());
        }
        String lockKey = RedisKey.STATISTICS_UPDATE_LOCK  + param.getMerchantId()  + ":" + param.getUserId();
        try {
            if (redisLock.lock(lockKey, UserMemberSysConstants.YES, 5000, 3000) == null) {
                log.warn("用户统计获取锁失败 -> {}", param);
                throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_IS_UPDATING);
            }
            // 更新总统计(包含初始化)
            upsertTotalStatistics(param);
            // 更新日统计(包含初始化)
            updateDailyStatistics(param);
        } finally {
            redisLock.unlock(lockKey, "Y");
        }
    }

    @Override
    public void updateStatisticsWithUserInit(UserStatisticsUpdateDTO param) {
        if (param.getMerchantUserId() == null) {
            Assert.notNull(param.getUserId(),"用户ID不能为空");
            MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(param.getUserId(), param.getMerchantId());
            if (merchantUser == null) {
                MerchantUserDTO dto = new MerchantUserDTO();
                dto.setMerchantId(param.getMerchantId());
                dto.setUserId(param.getUserId());
                dto.setIsCreateTag(false);
                Long id = merchantUserService.saveOrUpdateMerchantUser(dto);
                param.setMerchantUserId(id);
            } else {
                param.setMerchantUserId(merchantUser.getId());
            }
        }
        // 新用户中心存在没有userId
        if(param.getUserId() == null){
            Assert.notNull(param.getMerchantUserId(),"商户用户ID不能为空");
            MerchantUser merchantUser = merchantUserRepository.getByIdAndMerchantId(param.getMerchantUserId(),param.getMerchantId());
            Assert.notNull(merchantUser,"商户用户不存在");
            param.setUserId(merchantUser.getUserId());
        }
        String lockKey = RedisKey.STATISTICS_UPDATE_LOCK  + param.getMerchantId()  + ":" + param.getUserId();
        try {
            if (redisLock.lock(lockKey, "Y", 5000, 3000) == null) {
                log.warn("用户统计获取锁失败 -> {}", param);
                throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_IS_UPDATING);
            }
            // 更新总统计(包含初始化)
            upsertTotalStatistics(param);
            // 更新日统计(包含初始化)
            updateDailyStatistics(param);
        } finally {
            redisLock.unlock(lockKey, "Y");
        }
    }

    private void upsertTotalStatistics(UserStatisticsUpdateDTO param) {
        statisticsMapper.upsertStatistics(param);
    }

    private void updateDailyStatistics(UserStatisticsUpdateDTO param) {
        if (param.getAmountConsumption() == null && param.getCoinsConsumption() == null
                && param.getStartTimes() == null && param.getPayTimes() == null
                && param.getPayAmount() == null && param.getPayForServiceTimes() == null
                && param.getPayForServiceAmount() == null) {
            return;
        }
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT+8"));
        calendar.setTime(param.getUpdateTime());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        statisticsDailyMapper.upsertStatistics(param, calendar.getTime());
    }

    @Override
    @Idempotent(keys = {"#param.bizNo", "#param.type", "#param.mode"})
    public void updateStatisticsWithIdempotent(UserStatisticsUpdateDTO param, boolean initUser) {
        if (initUser) {
            updateStatisticsWithUserInit(param);
            return;
        }
        updateStatistics(param);
    }


    @Override
    public UserStatisticsRecordDTO find(UserStatisticsConditionDTO param) {
        if (param.getMerchantUserId() == null) {
            MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(param.getUserId(), param.getMerchantId());
            if (merchantUser == null) {
                throw new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR);
            }
            param.setMerchantUserId(merchantUser.getId());
        }
        Statistics statistics = null;
        try {
            HintManagerHolder.getInstance().setMasterRouteOnly();
            // 总统计(包含初始化)
            statistics = statisticsMapper.selectOne(new QueryWrapper<Statistics>()
                    .eq("user_id", param.getUserId())
                    .eq("merchant_id", param.getMerchantId())
                    .eq("merchant_user_id", param.getMerchantUserId()));
        } finally {
            HintManagerHolder.clear();
        }

        if (statistics == null) {
            return null;
        }
        UserStatisticsRecordDTO userStatisticsRecordDTO = new UserStatisticsRecordDTO();
        BeanUtils.copyProperties(statistics,userStatisticsRecordDTO);

        if(userStatisticsRecordDTO.getPayForServiceAmount() == null){
            userStatisticsRecordDTO.setPayForServiceAmount(BigDecimal.ZERO);
        }
        if(userStatisticsRecordDTO.getCoinsConsumption()  == null){
            userStatisticsRecordDTO.setCoinsConsumption(BigDecimal.ZERO);
        }
        if(userStatisticsRecordDTO.getAmountConsumption() ==null){
            userStatisticsRecordDTO.setAmountConsumption(BigDecimal.ZERO);
        }
        if(userStatisticsRecordDTO.getPayAmount() == null){
            userStatisticsRecordDTO.setPayAmount(BigDecimal.ZERO);
        }
        return userStatisticsRecordDTO;
    }

    @Override
    public MerchantStatisticsRecordDTO getMerchantStatistics(Long merchantId) {
        MerchantStatistics merchantStatistics  = merchantStatisticsMapper.selectOne(new QueryWrapper<MerchantStatistics>()
                .eq("merchant_id", merchantId));
        if(merchantStatistics!= null){
            MerchantStatisticsRecordDTO merchantStatisticsRecordDTO = new MerchantStatisticsRecordDTO();
            BeanUtils.copyProperties(merchantStatistics,merchantStatisticsRecordDTO);
            return merchantStatisticsRecordDTO;
        }
        return null;
    }

    @Override
    public Page<MerchantUserStatisticsListDTO> queryStatisticsUserList(StatisticsUserQueryDTO statisticsUserQueryDTO) {
        Date statisticsDate = null;
        if(statisticsUserQueryDTO.getTimeScope() == TimeScopeEnum.TODAY){
            statisticsDate = DateUtil.parseDate(DateUtil.formatDate(new Date()));
        }else if(statisticsUserQueryDTO.getTimeScope() == TimeScopeEnum.YESTERDAY){
            statisticsDate = DateUtil.parseDate(DateUtil.formatDate(DateUtil.offsetDay(new Date(), -1)));
            statisticsDate = DateUtil.parseDate(DateUtil.formatDate(statisticsDate));
        }


        List<OrderItem> orders = new ArrayList<>();
        String orderField = null,orderValue = null;
        if(!StringUtils.isEmpty(statisticsUserQueryDTO.getTotalRechargeMoneySort())){
            orderField = "pay_amount";
            orderValue = statisticsUserQueryDTO.getTotalRechargeMoneySort();
        }else if(!StringUtils.isEmpty(statisticsUserQueryDTO.getTotalPayMoneySort())){
            orderField = "pay_for_service_amount";
            orderValue = statisticsUserQueryDTO.getTotalPayMoneySort();
        }else if(!StringUtils.isEmpty(statisticsUserQueryDTO.getTotalConsumeBalanceSort())){
            orderField = "amount_consumption";
            orderValue = statisticsUserQueryDTO.getTotalConsumeBalanceSort();
        }else if(!StringUtils.isEmpty(statisticsUserQueryDTO.getTotalConsumeCoinSort())){
            orderField = "coins_consumption";
            orderValue = statisticsUserQueryDTO.getTotalConsumeCoinSort();
        }

        // 设置排序序列
        if (orderField != null) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(orderField);
            String orderDefaultValue = "asc";
            if (orderDefaultValue.equalsIgnoreCase(orderValue)) {
                orderItem.setAsc(true);
            } else {
                orderItem.setAsc(false);
            }
            orders.add(orderItem);
        }
        //增加除设置的排序外的默认的排序
        OrderItem orderItem = new OrderItem();
        orderItem.setColumn("umu.user_id");
        orderItem.setAsc(true);
        orders.add(orderItem);

        Page<MerchantUserStatisticsListDTO> merchantUserListDTOPage = new Page<>(statisticsUserQueryDTO.getPageIndex(), statisticsUserQueryDTO.getPageSize());
        merchantUserListDTOPage.setOrders(orders);
        if (Boolean.FALSE.equals(statisticsUserQueryDTO.getCountSql())) {
            merchantUserListDTOPage.setSearchCount(false);
        }

        if(statisticsDate != null) {
            List<MerchantUserStatisticsListDTO> merchantUserStatisticsListDTOList = statisticsDailyMapper.queryStatisticsUserList(merchantUserListDTOPage,statisticsUserQueryDTO.getMerchantId(),
                    statisticsUserQueryDTO.getUserId(), statisticsUserQueryDTO.getTelephone(), statisticsDate,orderField,orderValue);
            merchantUserListDTOPage.setRecords(merchantUserStatisticsListDTOList);
        }else{
            // 全统计
            List<MerchantUserStatisticsListDTO> merchantUserStatisticsListDTOList = statisticsMapper.queryStatisticsUserList(merchantUserListDTOPage,statisticsUserQueryDTO.getMerchantId(),
                    statisticsUserQueryDTO.getUserId(), statisticsUserQueryDTO.getTelephone(), orderField,orderValue);
            merchantUserListDTOPage.setRecords(merchantUserStatisticsListDTOList);
        }

        return merchantUserListDTOPage;
    }

    @Override
    public MerchantUserStatisticsDailyDTO getUserCurrentDateStatistics(UserStatisticsConditionDTO param) {
        MerchantUserStatisticsDailyDTO merchantUserStatisticsDaily = new MerchantUserStatisticsDailyDTO();
        if (param.getMerchantUserId() == null) {
            MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(param.getUserId(), param.getMerchantId());
            if (merchantUser == null) {
                throw new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR);
            }
            param.setMerchantUserId(merchantUser.getId());
        }
        StatisticsDaily statisticsDaily = statisticsDailyMapper.selectOne(new QueryWrapper<StatisticsDaily>().lambda()
                .eq(StatisticsDaily::getUserId, param.getUserId())
                .eq(StatisticsDaily::getMerchantUserId, param.getMerchantUserId())
                .eq(StatisticsDaily::getMerchantId, param.getMerchantId())
                .eq(StatisticsDaily::getStatisticsDate, new Date()));
        if (statisticsDaily != null) {
            BeanUtils.copyProperties(statisticsDaily, merchantUserStatisticsDaily);
        }
        return merchantUserStatisticsDaily;
    }

    @Override
    public List<MerchantUserRankRecordDTO> getRechargeRank(MerchantUserRankConditionDTO condition) {
        return statisticsDailyMapper.getRechargeRank(condition);
    }

    /**
     * 初始化指定商户下的 商户用户统计数据
     *
     * @param merchantId     商户id
     * @param userId         用户id
     * @param merchantUserId 商户用户id
     */
    @Override
    public void initStatistics(Long merchantId, Long userId, Long merchantUserId) {
        if (log.isDebugEnabled()) {
            log.debug("商户用户统计初始化,userId:{},merchantUserId:{},merchantId:{}", userId, merchantUserId, merchantId);
        }
        try {
            // 总统计(包含初始化)
            Statistics statistics = statisticsMapper.selectOne(new QueryWrapper<Statistics>()
                    .eq("user_id", userId)
                    .eq("merchant_id", merchantId)
                    .eq("merchant_user_id", merchantUserId));
            if (statistics == null) {
                Long count = statisticsMapper.selectCount(new QueryWrapper<Statistics>()
                        .eq("user_id", userId)
                        .eq("merchant_id", merchantId));
                if (count!=null &&  count > 0) {
                    statisticsMapper.delete(new QueryWrapper<Statistics>()
                            .eq("user_id", userId)
                            .eq("merchant_id", merchantId));
                }
                Date now = new Date();
                statistics = new Statistics();
                statistics.setMerchantUserId(merchantUserId);
                statistics.setMerchantId(merchantId);
                statistics.setUserId(userId);
                statistics.setStartTimes(0);
                statistics.setPayTimes(0);
                statistics.setPayAmount(BigDecimal.ZERO);
                statistics.setPayForServiceTimes(0);
                statistics.setPayForServiceAmount(BigDecimal.ZERO);
                statistics.setCoinsConsumption(BigDecimal.ZERO);
                statistics.setAmountConsumption(BigDecimal.ZERO);
                statistics.setTotalCoins(BigDecimal.ZERO);
                statistics.setBalanceCoins(BigDecimal.ZERO);
                statistics.setBalanceAmount(BigDecimal.ZERO);
                statistics.setCreateTime(now);
                statistics.setUpdateTime(now);
                statistics.setTotalAmount(BigDecimal.ZERO);
                statisticsMapper.insert(statistics);
            }
        } catch (DuplicateKeyException de) {
            log.warn("商户用户统计数据已存在,userId:{},merchantUserId:{},merchantId:{},message:{}", userId, merchantUserId, merchantId,
                    de.getMessage(), de);
        } catch (Exception e) {
            log.error("商户用户统计初始化失败,userId:{},merchantUserId:{},merchantId:{},message:{},e", userId, merchantUserId, merchantId,
                    e.getMessage(), e);
        }
    }
}
