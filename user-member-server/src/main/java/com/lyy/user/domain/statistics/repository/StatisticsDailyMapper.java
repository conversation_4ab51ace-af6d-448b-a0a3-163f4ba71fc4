package com.lyy.user.domain.statistics.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserRankConditionDTO;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserRankRecordDTO;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserStatisticsListDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsUpdateDTO;
import com.lyy.user.domain.statistics.entity.StatisticsDaily;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StatisticsDailyMapper extends BaseMapper<StatisticsDaily> {

    void updateStatistics(UserStatisticsUpdateDTO param);

    /**
     *
     * @param ipage
     * @param merchantId
     * @param statisticsDate
     * @param orderField
     * @param orderValue
     * @return
     */
    List<MerchantUserStatisticsListDTO> queryStatisticsUserList(IPage ipage, @Param("merchantId") Long merchantId, @Param("userId") Long userId, @Param("telephone") String telephone,
                                                                @Param("statisticsDate") Date statisticsDate, @Param("orderField") String orderField, @Param("orderValue") String orderValue);

    /**
     * 查询每日已使用量
     * @param merchantId
     * @param userId
     * @param statisticsDate
     * @return
     */
    BigDecimal selectDateUseNum(@Param("merchantId") Long merchantId, @Param("userId") Long userId, @Param("statisticsDate") Date statisticsDate);

    /**
     * 获取商家用户充值排行榜
     * @param condition
     * @return
     */
    List<MerchantUserRankRecordDTO> getRechargeRank(MerchantUserRankConditionDTO condition);

    void upsertStatistics(@Param("param") UserStatisticsUpdateDTO param, @Param("statisticsDate") Date time);
}