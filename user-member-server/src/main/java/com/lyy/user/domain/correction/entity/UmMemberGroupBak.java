package com.lyy.user.domain.correction.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 会员组表
 *
 * @TableName um_member_group_bak
 */
@Data
public class UmMemberGroupBak implements Serializable {

    private static final long serialVersionUID = 8138613777044894622L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 是否可用
     */
    @TableField("is_active")
    private Boolean active;

    /**
     * 开通方式,0 自动;1 手动
     */
    private Integer openMethod;

    /**
     * 升降策略(0 无策略,1 升级策略,2 降级策略)
     */
    private Integer liftingStrategy;

    /**
     * 规则策略(0、叠加,1、覆盖)
     */
    private Integer ruleStrategy;

    /**
     * 有效期为多少天，若为-1,则为永久有效，若为-2则按固定时间生效
     */
    private Integer memberEffectiveTime;

    /**
     * 会员有效开始时间,member_effective_time为-2时生效
     */
    private Date memberStartTime;

    /**
     * 会员有效结束时间,member_effective_time为-2时生效
     */
    private Date memberEndTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     *
     */
    private Long updateBy;

    /**
     * 是否已删除，true:是，false：否
     */
    @TableField("is_del")
    private Boolean del;

}