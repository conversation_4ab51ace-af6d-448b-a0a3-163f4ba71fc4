package com.lyy.user.domain.statistics.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@TableName(value = "um_small_venue_stored_statistics")
public class SmallVenueStoredStatistics {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 商户id
     */
    @TableField(value = "merchant_id")
    private Long merchantId;

    /**
     * 商户用户id
     */
    @TableField(value = "merchant_user_id")
    private Long merchantUserId;

    /**
     * 商户权益类型id
     */
    @TableField(value = "merchant_benefit_classify_id")
    private Long merchantBenefitClassifyId;

    /**
     * 总额度/次数
     */
    private BigDecimal total;

    /**
     * 剩余额度/次数
     */
    private BigDecimal balance;

    /**
     * 总消耗额度/次数
     */
    @TableField(value = "total_consume")
    private BigDecimal totalConsume;

    /**
     * 累计过期额度/次数
     */
    @TableField(value = "total_invalid")
    private BigDecimal totalInvalid;

    /**
     * 剩余张数（次卡/年卡）
     */
    @TableField(value = "remain_num")
    private Integer remainNum;

    /**
     * 总张数
     */
    @TableField(value = "total_num")
    private Integer totalNum;

    /**
     * 累计消耗张数
     */
    @TableField(value = "total_num_consume")
    private Integer totalNumConsume;

    /**
     * 累计过期张数
     */
    @TableField(value = "total_num_invalid")
    private Integer totalNumInvalid;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 更新时间
     */
    private Date updated;

    /**
     * 创建人
     */
    private Long createdby;

    /**
     * 更新人
     */
    private Long updatedby;

}