package com.lyy.user.domain.member.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.member.infrastructure.MemberErrorCode;
import com.lyy.user.account.infrastructure.member.dto.MemberGrowRecordDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGrowRecordListDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGrowRecordQueryDTO;
import com.lyy.user.application.member.IMemberGrowRecordService;
import com.lyy.user.application.member.IMemberService;
import com.lyy.user.domain.member.entity.Member;
import com.lyy.user.domain.member.entity.MemberGrowRecord;
import com.lyy.user.domain.member.repository.MemberGrowRecordMapper;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.infrastructure.base.MerchantBaseServiceImpl;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.repository.user.MerchantUserRepository;
import com.lyy.user.infrastructure.util.CommonConverterTools;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p>
 * 会员成长值记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Service
@Slf4j
public class MemberGrowRecordServiceImpl extends MerchantBaseServiceImpl<MemberGrowRecordMapper, MemberGrowRecord> implements IMemberGrowRecordService {

    @Resource
    private MemberGrowRecordMapper memberGrowRecordMapper;
    @Resource
    private IMemberService memberService;
    @Resource
    private MerchantUserRepository merchantUserRepository;

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void saveMemberGrowRecord(MemberGrowRecordDTO dto) {

        Member lastRecord = memberService.getById(dto.getMerchantId(), dto.getMemberId());
        MemberGrowRecord entity   = CommonConverterTools.convert(MemberGrowRecord.class,dto);
        entity.setMode(dto.getRecordModeEnum().getMode());
        entity.setDescription(dto.getGrowRuleEnum().getDesc());
        entity.setCreateTime(new Date());
        entity.setCreatedby(dto.getOperatorId());
        entity.setInitGrowValue(Optional.ofNullable(lastRecord).map(Member::getGrowValue).orElse(0L));
        int insert = memberGrowRecordMapper.insert(entity);


        if (insert <= 0) {
            throw new BusinessException(MemberErrorCode.MEMBER_GROW_RECORD_SAVE_ERROR);
        }
    }

    /**
     * 根据升级策略统计某个用户的记录数据
     *
     * @param merchantId
     * @param userId
     * @param memberLiftingIds
     * @return
     */
    @Override
    public Map<Long, Integer> countRecordByMemberLifting(Long merchantId, Long userId, Collection<Long> memberLiftingIds) {
        if(memberLiftingIds == null || memberLiftingIds.isEmpty()){
            return Collections.emptyMap();
        }
        MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(userId, merchantId);
        if (merchantUser == null) {
            log.warn("商户用户信息为空,{},{}", merchantId, userId);
            return Collections.emptyMap();
        }

        String idsStr = memberLiftingIds.stream()
                .map(id->id.toString())
                .collect(Collectors.joining(",","(",")"));
        QueryWrapper<MemberGrowRecord> wrapper = new QueryWrapper<MemberGrowRecord>()
                .select("member_lifting_id","count(*) as member_lifting_count")
                .eq(getShardingFieldKey(), merchantId)
                .eq("merchant_user_id", merchantUser.getId())
                .eq("user_id",userId)
                .groupBy("member_lifting_id")
                .having("member_lifting_id in " + idsStr);
        List<Map<String, Object>> list =  listMaps(wrapper);
        log.debug("countRecordByMemberLifting#list-->{}",list);
        //转换为map数据
        Map<Long, Integer> countMap = new HashMap<>(list.size()*2);
        for(Map<String, Object> map:list) {
            Object key = map.get("member_lifting_id");
            Object value = map.get("member_lifting_count");
            if(key instanceof Number && value instanceof Number){
                countMap.put(((Number) key).longValue(),((Number) value).intValue());
            }
        }
        return countMap;

    }

    @Override
    public Page<MemberGrowRecordListDTO> list(MemberGrowRecordQueryDTO param) {
        Page<MemberGrowRecord> page = new Page<>(param.getPageIndex(), param.getPageSize());
        QueryWrapper<MemberGrowRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("merchant_id", param.getMerchantIds());
        if (!CollectionUtils.isEmpty(param.getMerchantUserIds())) {
            queryWrapper.in("merchant_user_id", param.getMerchantUserIds());
        }
        if (!CollectionUtils.isEmpty(param.getMemberIds())) {
            queryWrapper.in("member_id", param.getMemberIds());
        }
        if (param.getStartTime() != null) {
            queryWrapper.gt("create_time", param.getStartTime());
        }
        if (param.getEndTime() != null) {
            queryWrapper.lt("create_time", param.getEndTime());
        }
        queryWrapper.orderByDesc("create_time");
        Page<MemberGrowRecord> list = memberGrowRecordMapper.selectPage(page, queryWrapper);
        List<MemberGrowRecordListDTO> result = CommonConverterTools.convert(MemberGrowRecordListDTO.class, list.getRecords());
        Page<MemberGrowRecordListDTO> pageResult = new Page<>(page.getCurrent(),page.getSize(),page.getTotal(),page.isSearchCount());
        pageResult.setRecords(result);
        return pageResult;
    }



}
