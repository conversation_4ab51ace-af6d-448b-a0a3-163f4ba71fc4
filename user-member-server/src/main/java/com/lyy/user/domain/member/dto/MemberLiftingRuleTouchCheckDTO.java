package com.lyy.user.domain.member.dto;

import java.util.List;
import lombok.Data;

/**
 * 升级策略规则触发
 * <AUTHOR>
 * @className: MemberLiftingRuleTouchCheckDTO
 * @date 2021/4/6
 */
@Data
public class MemberLiftingRuleTouchCheckDTO {

    private List<Long> memberGroupIdList;
    /**
     * 策略(1 登录次数,2 支付笔数,3 消费金额
     */
    private Short category;

    /**
     * 商户ID
     */
    private Long merchantId;
}
