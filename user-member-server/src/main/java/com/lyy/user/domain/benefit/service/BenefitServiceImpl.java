package com.lyy.user.domain.benefit.service;

import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.account.dto.BenefitIncludeClassifyDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitInfoDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitListDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitScopeSelectDTO;
import com.lyy.user.account.infrastructure.benefit.dto.GeneralGroupBenefitSaveDTO;
import com.lyy.user.account.infrastructure.benefit.dto.GenerateAccountDTO;
import com.lyy.user.account.infrastructure.benefit.dto.GroupBenefitMergeDTO;
import com.lyy.user.account.infrastructure.benefit.vo.GeneralGroupBenefitSaveVO;
import com.lyy.user.account.infrastructure.constant.ApplicableEnum;
import com.lyy.user.account.infrastructure.constant.BalanceSharedGroupType;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.BenefitGenerateTypeEnum;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.application.benefit.BenefitService;
import com.lyy.user.domain.benefit.dto.BenefitRecordCountDTO;
import com.lyy.user.domain.benefit.entity.Benefit;
import com.lyy.user.domain.benefit.entity.BenefitConsumeRule;
import com.lyy.user.domain.benefit.entity.BenefitRule;
import com.lyy.user.domain.benefit.entity.BenefitScope;
import com.lyy.user.domain.benefit.repository.BenefitConsumeRuleMapper;
import com.lyy.user.domain.benefit.repository.BenefitMapper;
import com.lyy.user.domain.benefit.repository.BenefitRuleMapper;
import com.lyy.user.domain.benefit.repository.BenefitScopeMapper;
import com.lyy.user.infrastructure.repository.benefit.BenefitCacheRepository;
import com.lyy.user.infrastructure.repository.benefit.BenefitRepository;
import com.lyy.user.infrastructure.util.CommonConverterTools;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

/**
 * 权益业务处理类
 *
 * <AUTHOR>
 * @create 2021/3/31 17:06
 */
@Slf4j
@Service
public class BenefitServiceImpl extends ServiceImpl<BenefitMapper, Benefit> implements BenefitService {

    @Resource
    private BenefitMapper benefitMapper;

    @Resource
    private BenefitScopeMapper benefitScopeMapper;

    @Resource
    private BenefitRuleMapper benefitRuleMapper;

    @Resource
    private BenefitConsumeRuleMapper benefitConsumeRuleMapper;

    @Resource
    private GenerateFactory generateFactory;
    @Resource
    private BenefitRepository benefitRepository;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private BenefitCacheRepository benefitCacheRepository;

    @Autowired
    @Qualifier("giveBenefitScopeHandlerExecutor")
    private Executor scopeHandlerExecutor;


    @Override
    public List<BenefitListDTO> getLyyUserBenefitList(Long lyyUserId, Long merchantId) {

        List<Benefit> list = benefitMapper.queryBenefitList(lyyUserId, merchantId);
        List<BenefitListDTO> benefitListDTOList = list.stream().map(benefit -> {
            BenefitListDTO benefitListDTO = new BenefitListDTO();
            BeanUtils.copyProperties(benefit, benefitListDTO);
            benefitListDTO.setLyyUserId(lyyUserId);
            return benefitListDTO;
        }).collect(Collectors.toList());

        return benefitListDTOList;
    }

    @Override
    public void generateBenefitAccount(GenerateAccountDTO generateAccountDTO) {
        BenefitGenerateHandler benefitGenerateHandler = generateFactory.getHandler(generateAccountDTO.getGenerateType().getName());
        benefitGenerateHandler.doGenerate(generateAccountDTO);
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public BenefitInfoDTO generateGroupEquipmentTypeBenefit(Long merchantId, BenefitClassifyEnum benefitClassifyEnum, List<Long> groupIdList, List<Long> equipmentTypeIdList, Integer expiryDateCategory, String upTime, String downTime) {

        Date now = new Date();
        Benefit benefit = new Benefit();
        benefit.setClassify(benefitClassifyEnum.getCode());
        benefit.setExpiryDateCategory(expiryDateCategory);
        benefit.setUpTime(upTime);
        benefit.setDownTime(downTime);
        benefit.setMerchantId(merchantId);
        benefit.setActive(true);
        benefit.setCreateTime(now);
        benefitMapper.insert(benefit);

        ofNullable(groupIdList).ifPresent(r -> {
            r.forEach(groupId -> {
                BenefitScope benefitScope = new BenefitScope();
                benefitScope.setMerchantId(merchantId);
                benefitScope.setApplicable(ApplicableEnum.GROUP.getValue());
                benefitScope.setAssociatedId(groupId);
                benefitScope.setCreateTime(now);
                benefitScope.setBenefitId(benefit.getId());
                benefitScopeMapper.insert(benefitScope);
            });
        });

        ofNullable(equipmentTypeIdList).ifPresent(r -> {
            r.forEach(equipmentTypeId -> {
                BenefitScope benefitScope = new BenefitScope();
                benefitScope.setMerchantId(merchantId);
                benefitScope.setApplicable(ApplicableEnum.CATEGORY.getValue());
                benefitScope.setAssociatedId(equipmentTypeId);
                benefitScope.setCreateTime(now);
                benefitScope.setBenefitId(benefit.getId());
                benefitScopeMapper.insert(benefitScope);
            });
        });

        return CommonConverterTools.convert(BenefitInfoDTO.class, benefit);
    }

    @Override
    public BenefitInfoDTO getMerchantBenefit(Long merchantId, Integer classifyCode, Integer applicable, Long associatedId,
                                             Integer expiryDateCategory, String upTime, String downTime) {
        //去除有效期类型
        ArrayList<Integer> excludeExpireClassifyList = Lists.newArrayList(BenefitClassifyEnum.USER_RECHARGE_BALANCE.getCode(),
                BenefitClassifyEnum.USER_RECHARGE_COIN.getCode(),
                BenefitClassifyEnum.USER_RECHARGE_GIVE_BALANCE.getCode(),
                BenefitClassifyEnum.USER_RECHARGE_GIVE_COIN.getCode(),
                BenefitClassifyEnum.MERCHANT_PAYOUT_GROUP_BALANCE.getCode(),
                BenefitClassifyEnum.MERCHANT_PAYOUT_GROUP_COIN.getCode());
        if (classifyCode.equals(BenefitClassifyEnum.DEDUCTION_AMOUNT.getCode())
                || excludeExpireClassifyList.contains(classifyCode)
                || classifyCode.equals(BenefitClassifyEnum.ADVERT_RED_PACKET.getCode())) {
            //抵扣金类型去除有效时间
            upTime = null;
            downTime = null;
            if (excludeExpireClassifyList.contains(classifyCode)) {
                expiryDateCategory = ExpiryDateCategoryEnum.NO_LIMIT.getValue();
            }
        }
        List<Benefit> benefitList = benefitMapper.queryBenefitListByScope(merchantId, classifyCode, applicable, associatedId, expiryDateCategory, upTime, downTime);
        log.debug("benefitList.size:{}", benefitList.size());
        if (benefitList.size() == 0) {
            // 商户权益不存在
            Benefit benefit = addMerchantBenefit(merchantId, BenefitGenerateTypeEnum.SELL, classifyCode, applicable, associatedId, expiryDateCategory, upTime, downTime);
            BenefitInfoDTO benefitInfoDTO = new BenefitInfoDTO();
            BeanUtils.copyProperties(benefit, benefitInfoDTO);

            //赠送权益的权益范围处理
            if (BenefitClassifyEnum.USER_RECHARGE_GIVE_BALANCE.getCode().equals(classifyCode)
                    || BenefitClassifyEnum.MERCHANT_PAYOUT_GROUP_BALANCE.getCode().equals(classifyCode)) {
                handleGiveBenefitScope(benefit.getId(), BenefitClassifyEnum.USER_RECHARGE_BALANCE.getCode(), merchantId, applicable, associatedId, expiryDateCategory, upTime, downTime);
            }
            if (BenefitClassifyEnum.USER_RECHARGE_GIVE_COIN.getCode().equals(classifyCode)
                    || BenefitClassifyEnum.MERCHANT_PAYOUT_GROUP_COIN.getCode().equals(classifyCode)) {
                handleGiveBenefitScope(benefit.getId(), BenefitClassifyEnum.USER_RECHARGE_COIN.getCode(), merchantId, applicable, associatedId, expiryDateCategory, upTime, downTime);
            }

            return benefitInfoDTO;
        } else {
            // 此处list 已按时间倒序排序，第一条即为最新的权益数据
            Benefit benefit = benefitList.get(0);
            log.debug("商户权益已存在,benefitId:{}", benefit.getId());
            BenefitInfoDTO benefitInfoDTO = new BenefitInfoDTO();
            BeanUtils.copyProperties(benefit, benefitInfoDTO);
            benefitInfoDTO.setOldBenefit(Boolean.TRUE);
            return benefitInfoDTO;
        }

    }


    private void handleGiveBenefitScope(Long giveBenefitId, Integer rechargeClassifyCode, Long merchantId, Integer applicable, Long associatedId,
                                        Integer expiryDateCategory, String upTime, String downTime) {
        log.info("handleGiveBenefitScope参数:giveBenefitId:{},merchantId:{},applicable:{},associatedId:{},expiryDateCategory:{},upTime:{},downTime:{}",
                giveBenefitId, merchantId, applicable, associatedId, expiryDateCategory, upTime, downTime);
        CompletableFuture.runAsync(() -> {
            try {
                //查询充值权益的权益范围
                List<Benefit> benefitList = benefitMapper.queryBenefitListByScope(merchantId, rechargeClassifyCode, applicable, associatedId, expiryDateCategory, upTime, downTime);
                if (CollUtil.isEmpty(benefitList)) {
                    return;
                }
                Benefit rechargeBenefit = benefitList.get(0);
                List<Long> allScopeId = benefitScopeMapper.findAllScopeId(merchantId, Lists.newArrayList(rechargeBenefit.getId()), ApplicableEnum.GROUP.getValue());
                if (CollUtil.isEmpty(allScopeId)) {
                    return;
                }
                //去掉已经新增的范围id
                allScopeId.remove(associatedId);
                //批量保存赠送权益的权益范围
                List<BenefitScope> benefitScopeSaveList = allScopeId.stream().map(id -> {
                    BenefitScope benefitScope = new BenefitScope();
                    benefitScope.setBenefitId(giveBenefitId);
                    benefitScope.setApplicable(applicable);
                    benefitScope.setAssociatedId(id);
                    benefitScope.setMerchantId(merchantId);
                    benefitScope.setCreateTime(new Date());
                    return benefitScope;
                }).collect(Collectors.toList());
                log.debug("批量保存赠送权益的权益范围详情:{}", benefitScopeSaveList);
                if (CollUtil.isNotEmpty(benefitScopeSaveList)) {
                    benefitScopeMapper.insertBatch(benefitScopeSaveList);
                    benefitCacheRepository.clearBenefitScopeCache(merchantId, giveBenefitId);
                }
            } catch (Exception e) {
                log.error("处理赠送权益的权益范围出现异常", e);
            }
        }, scopeHandlerExecutor);
    }

    @Override
    public BenefitInfoDTO getBenefitById(Long id, Long merchantId) {
        LambdaQueryWrapper<Benefit> lambdaQueryWrapper = new QueryWrapper<Benefit>().lambda().eq(Benefit::getId, id);
        if (merchantId != null) {
            lambdaQueryWrapper.eq(Benefit::getMerchantId, merchantId);
        }
        Benefit benefit = benefitMapper.selectOne(lambdaQueryWrapper);
        Assert.notNull(benefit, "权益数据不存在");
        BenefitInfoDTO benefitInfoDTO = new BenefitInfoDTO();
        BeanUtils.copyProperties(benefit, benefitInfoDTO);
        benefitInfoDTO.setBenefitClassify(benefit.getClassify());
        return benefitInfoDTO;
    }

    @Override
    public List<GeneralGroupBenefitSaveVO> saveGeneralGroupBenefit(GeneralGroupBenefitSaveDTO saveDTO) {
        //判断场地是否 已关联权益
        List<GeneralGroupBenefitSaveVO> list = Lists.newArrayList();
        Long merchantId = saveDTO.getMerchantId();
        saveDTO.getBenefitClassifyEnumList().forEach(benefitClassifyEnum -> {
            Benefit benefit;
            if (Objects.nonNull(saveDTO.getExpiryDateCategory())) {
                benefit = saveBenefit(BenefitClassifyEnum.getDesc(benefitClassifyEnum.getCode()),
                        benefitClassifyEnum.getCode(), merchantId, saveDTO.getExpiryDateCategory(), saveDTO.getUpTime(), saveDTO.getDownTime());
            } else {
                benefit = saveBenefit(BenefitClassifyEnum.getDesc(benefitClassifyEnum.getCode()),
                        benefitClassifyEnum.getCode(), merchantId, ExpiryDateCategoryEnum.NO_LIMIT.getValue());
            }
            GeneralGroupBenefitSaveVO vo = new GeneralGroupBenefitSaveVO();
            vo.setBenefitId(benefit.getId());
            vo.setClassify(benefit.getClassify());
            list.add(vo);
            saveDTO.getGroupIdList()
                    .forEach(groupId -> saveBenefitScope(benefit.getId(), ApplicableEnum.GROUP.getValue(), groupId, merchantId));
        });
        return list;
    }

    @Override
    @Async("benefitTaskExecutor")
    public void saveGroupBenefit(GeneralGroupBenefitSaveDTO generalGroupBenefitSaveDTO) {
        generalGroupBenefitSaveDTO.getGroupIdList().forEach(groupId -> {
            // 逻辑调整，
            generalGroupBenefitSaveDTO.getBenefitClassifyEnumList().forEach(benefitClassifyEnum -> {

                //查询单场地的benefit数据
                Long benefitId = benefitMapper.getBenefitId(generalGroupBenefitSaveDTO.getMerchantId(), benefitClassifyEnum.getCode(),
                        ApplicableEnum.GROUP.getValue(), groupId);
                if (benefitId == null) {
                    //创建权益
                    Benefit benefit = saveBenefit(BenefitClassifyEnum.getDesc(benefitClassifyEnum.getCode()),
                            benefitClassifyEnum.getCode(), generalGroupBenefitSaveDTO.getMerchantId(), ExpiryDateCategoryEnum.NO_LIMIT.getValue());
                    // 创建范围
                    saveBenefitScope(benefit.getId(), ApplicableEnum.GROUP.getValue(), groupId, generalGroupBenefitSaveDTO.getMerchantId());
                    log.debug("saveGroupBenefit创建权益,benefit:{}", benefit);
                } else {
                    //更新权益的时间
                    benefitMapper.updateBenefit(benefitId, generalGroupBenefitSaveDTO.getMerchantId());
                    log.debug("saveGroupBenefit更新权益,benefitId:{}", benefitId);
                }
            });

        });

    }

    /**
     * 权益保存
     *
     * @param title              标题
     * @param classify           权益类型
     * @param merchantId         商户id
     * @param expiryDateCategory 过期类型
     * @return
     */
    private Benefit saveBenefit(String title, Integer classify, Long merchantId, Integer expiryDateCategory) {
        return saveBenefit(title, classify, merchantId, expiryDateCategory, null, null);
    }

    private Benefit saveBenefit(String title, Integer classify, Long merchantId, Integer expiryDateCategory, String upTime, String downTime) {
        Benefit benefit = new Benefit();
        benefit.setTitle(title);
        benefit.setClassify(classify);
        benefit.setMerchantId(merchantId);
        benefit.setCreateTime(new Date());
        benefit.setActive(Boolean.TRUE);
        benefit.setExpiryDateCategory(expiryDateCategory);
        if (!Objects.equals(ExpiryDateCategoryEnum.NO_LIMIT.getValue(), expiryDateCategory)) {
            benefit.setUpTime(upTime);
            benefit.setDownTime(downTime);
        }
        benefitMapper.insert(benefit);
        return benefit;
    }

    /**
     * 权益使用范围
     *
     * @param benefitId    权益id
     * @param applicable   类型
     * @param associatedId 关联id
     * @param merchantId   商户id
     */
    private void saveBenefitScope(Long benefitId, Integer applicable, Long associatedId, Long merchantId) {
        BenefitScope benefitScope = new BenefitScope();
        benefitScope.setBenefitId(benefitId);
        benefitScope.setApplicable(applicable);
        benefitScope.setAssociatedId(associatedId);
        benefitScope.setMerchantId(merchantId);
        benefitScope.setCreateTime(new Date());
        benefitScopeMapper.insert(benefitScope);
    }

    /**
     * 根据账号记录获取对应的权益信息,主要通过支付单号(outTradeNo)或业务单号(orderNo)来查找
     *
     * @param accountBenefitAdjustDTO
     * @param mode                    订单的模式 1:增加，2：扣减
     * @param classifyList            检查的类型列表
     * @return
     */
    @Override
    public List<BenefitRecordCountDTO> getBenefitFromAccountRecord(AccountBenefitAdjustDTO accountBenefitAdjustDTO, int mode, List<Integer> classifyList) {
        if (StringUtils.isBlank(accountBenefitAdjustDTO.getOutTradeNo()) && StringUtils.isBlank(accountBenefitAdjustDTO.getOrderNo())) {
            return null;
        }
        if (CollectionUtils.isEmpty(classifyList)) {
            return null;
        }

        return benefitMapper.getBenefitFromAccountRecord(accountBenefitAdjustDTO, mode, classifyList);
    }

    /**
     * 2.0开启场地通用更新权益使用范围
     *
     * @param generalGroupBenefitSaveDTO
     * @return
     */
    @Override
    public Boolean updateSaaSBenefit(GeneralGroupBenefitSaveDTO generalGroupBenefitSaveDTO) {
        QueryWrapper<Benefit> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id")
                .eq("merchant_id", generalGroupBenefitSaveDTO.getMerchantId())
                .eq("classify", generalGroupBenefitSaveDTO.getBenefitClassifyEnumList().get(0).getCode());
        List<Benefit> list = benefitMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        List<Long> benefitIds = list.stream().map(Benefit::getId).collect(Collectors.toList());
        List<List<Long>> lBenefitIds = Lists.partition(benefitIds, 50);
        lBenefitIds.forEach(ids -> benefitScopeMapper.deleteByBenefitIdsAndMerchantId(generalGroupBenefitSaveDTO.getMerchantId(), ids));
        return true;
    }

    /**
     * 场地通用权益合并
     *
     * @param groupBenefitMergeDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean groupBenefitMerge(GroupBenefitMergeDTO groupBenefitMergeDTO) {

        Long merchantId = groupBenefitMergeDTO.getMerchantId();
        List<Long> oldGroupList = groupBenefitMergeDTO.getOldGroupList();
        List<Long> newGroupList = groupBenefitMergeDTO.getNewGroupList();
        Integer groupType = groupBenefitMergeDTO.getGroupType();
        if (CollectionUtils.isEmpty(oldGroupList) || CollectionUtils.isEmpty(newGroupList)) {
            return false;
        }
        //groupType处理
        List<Integer> classifyList = getClassifyListByGroupType(groupType);
        //查询仅在旧场地使用的benefitId
        List<Long> oldGroupBenefitIds = findBenefitIdsByAssociatedIds(merchantId, oldGroupList);
        //过滤classify
        oldGroupBenefitIds = filterBenefitIdIncludeClassify(oldGroupBenefitIds, merchantId, classifyList);
        //查询仅在新场地使用的benefitId
        List<Long> newGroupBenefitIds = findBenefitIdsByAssociatedIds(merchantId, newGroupList);
        //过滤classify
        newGroupBenefitIds = filterBenefitIdIncludeClassify(newGroupBenefitIds, merchantId, classifyList);

        log.info("仅在新旧场地的权益id信息,oldGroupBenefitIds:{},newGroupBenefitIds:{}", oldGroupBenefitIds, newGroupBenefitIds);
        List<BenefitScope> benefitScopesAll = new ArrayList<>();
        List<Long> benefitIdAll = new ArrayList<>();
        //扩大旧场地的权益
        if (!CollectionUtils.isEmpty(oldGroupBenefitIds)) {
            List<BenefitScope> benefitScopes = assembleBenefitScope(merchantId, newGroupList, oldGroupBenefitIds);
            benefitScopesAll.addAll(benefitScopes);
            benefitIdAll.addAll(benefitScopes.stream().map(BenefitScope::getBenefitId).collect(Collectors.toList()));
        }
        //扩大新场地的权益
        if (!CollectionUtils.isEmpty(newGroupBenefitIds)) {
            List<BenefitScope> benefitScopes = assembleBenefitScope(merchantId, oldGroupList, newGroupBenefitIds);
            benefitScopesAll.addAll(benefitScopes);
            benefitIdAll.addAll(benefitScopes.stream().map(BenefitScope::getBenefitId).collect(Collectors.toList()));
        }

        //批量处理
        log.info("场地合并批量处理信息,benefitScopesAll:{},benefitIdAll:{}", benefitScopesAll, benefitIdAll);
        if (!CollectionUtils.isEmpty(benefitScopesAll)) {
            batchInsertBenefitScope(benefitScopesAll);
        }
        if (!CollectionUtils.isEmpty(benefitIdAll)) {
            benefitMapper.updateBenefitByIds(merchantId, benefitIdAll);
        }
        return true;
    }

    /**
     * 根据groupType获取classify集合
     * @param groupType
     * @return
     */
    public List<Integer> getClassifyListByGroupType(Integer groupType) {
        List<Integer> classifyList = new ArrayList<>();
        if (BalanceSharedGroupType.MONEY.getType().equals(groupType)) {
            classifyList.add(BenefitClassifyEnum.USER_RECHARGE_BALANCE.getCode());
            classifyList.add(BenefitClassifyEnum.USER_RECHARGE_GIVE_BALANCE.getCode());
            classifyList.add(BenefitClassifyEnum.MERCHANT_PAYOUT_GROUP_BALANCE.getCode());
        } else if (BalanceSharedGroupType.COIN.getType().equals(groupType)) {
            classifyList.add(BenefitClassifyEnum.USER_RECHARGE_COIN.getCode());
            classifyList.add(BenefitClassifyEnum.ADVERT_COIN.getCode());
            classifyList.add(BenefitClassifyEnum.IDLE_TIME_COIN.getCode());
            classifyList.add(BenefitClassifyEnum.USER_RECHARGE_GIVE_COIN.getCode());
            classifyList.add(BenefitClassifyEnum.MERCHANT_PAYOUT_GROUP_COIN.getCode());
        }
        return classifyList;
    }

    private void batchInsertBenefitScope(List<BenefitScope> benefitScopesAll) {
        benefitRepository.insertBatch(benefitScopesAll);
    }

    @Override
    public Boolean groupBenefitMergeWithNewGroupList(GroupBenefitMergeDTO groupBenefitMergeDTO) {
        Long merchantId = groupBenefitMergeDTO.getMerchantId();
        List<Long> newGroupList = groupBenefitMergeDTO.getNewGroupList();
        Integer groupType = groupBenefitMergeDTO.getGroupType();
        if (CollectionUtils.isEmpty(newGroupList)) {
            return false;
        }
        List<List<Long>> newGroupLists = Lists.partition(newGroupList, 20);
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        //新发起一个事务
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        // 获得事务状态
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            newGroupLists.forEach(ids -> {
                handleGroupBenefit(newGroupList, ids, merchantId, getClassifyListByGroupType(groupType));
            });
            //手动提交事务
            transactionManager.commit(status);
        } catch (Exception e) {
            log.error("手动提交事务失败", e);
            transactionManager.rollback(status);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private void handleGroupBenefit(List<Long> allGroupList, List<Long> ids, Long merchantId, List<Integer> classifyList) {
        List<BenefitScope> benefitScopesAll = new ArrayList<>();
        List<Long> benefitIdAll = new ArrayList<>();
        for (Long groupId : ids) {
            //查询仅在groupId范围的权益
            List<Long> newGroupBenefitIds = findBenefitIdsByAssociatedIds(merchantId, Collections.singletonList(groupId));
            //过滤classify
            newGroupBenefitIds = filterBenefitIdIncludeClassify(newGroupBenefitIds, merchantId, classifyList);
            //扩大权益到其他groupId
            List<Long> otherGroupIds = new ArrayList<>(allGroupList);
            otherGroupIds.remove(groupId);
            List<BenefitScope> benefitScopes = assembleBenefitScope(merchantId, otherGroupIds, newGroupBenefitIds);
            benefitScopesAll.addAll(benefitScopes);
            benefitIdAll.addAll(benefitScopes.stream().map(BenefitScope::getBenefitId).collect(Collectors.toList()));
        }
        //批量处理
        log.info("新场地场地合并批量处理信息,benefitScopesAll:{},benefitIdAll:{}", benefitScopesAll.size(), benefitIdAll.size());
        if (!CollectionUtils.isEmpty(benefitScopesAll)) {
            batchInsertBenefitScope(benefitScopesAll);
        }
        if (!CollectionUtils.isEmpty(benefitIdAll)) {
            benefitMapper.updateBenefitByIds(merchantId, benefitIdAll);
        }
    }


    /**
     * 根据场地集合获取权益id集合
     * @param merchantId
     * @param groupList
     * @return
     */
    public List<Long> findBenefitIdsByAssociatedIds(Long merchantId, List<Long> groupList) {

        BenefitScopeSelectDTO oldBenefitScopeSelectDTO = new BenefitScopeSelectDTO()
                .setMerchantId(merchantId)
                .setApplicable(ApplicableEnum.GROUP.getValue())
                .setAssociatedIdList(groupList)
                .setAssociatedIdSize(groupList.size());
        return benefitScopeMapper.findBenefitIdsByAssociatedIds(oldBenefitScopeSelectDTO);
    }

    /**
     * 权益id根据classify过滤
     * @param benefitIds
     * @param merchantId
     * @return
     */
    public List<Long> filterBenefitIdIncludeClassify(List<Long> benefitIds, Long merchantId, List<Integer> includeClassify) {

        if (CollectionUtils.isEmpty(benefitIds) || CollUtil.isEmpty(includeClassify)) {
            return Lists.newArrayList();
        }
        //过滤掉商户派币分类的权益id
        BenefitIncludeClassifyDTO benefitIncludeClassifyDTO = new BenefitIncludeClassifyDTO().setMerchantId(merchantId)
                .setIncludeClassifys(includeClassify)
                .setBenefitIds(benefitIds);
        return benefitMapper.findBenefitIdByIncludeClassify(benefitIncludeClassifyDTO);
    }

    /**
     * 组装新增权益范围
     *
     * @param merchantId
     * @param groupList
     * @param groupBenefitIds
     * @return
     */
    public List<BenefitScope> assembleBenefitScope(Long merchantId, List<Long> groupList, List<Long> groupBenefitIds) {
        List<BenefitScope> benefitScopes = new ArrayList<>();
        if (CollectionUtils.isEmpty(groupBenefitIds)) {
            return benefitScopes;
        }
        for (Long benefitId : groupBenefitIds) {
            for (Long groupId : groupList) {
                BenefitScope benefitScope = new BenefitScope();
                benefitScope.setMerchantId(merchantId);
                benefitScope.setBenefitId(benefitId);
                benefitScope.setApplicable(ApplicableEnum.GROUP.getValue());
                benefitScope.setAssociatedId(groupId);
                benefitScope.setCreateTime(new Date());
                benefitScope.setUpdateTime(new Date());
                benefitScopes.add(benefitScope);
            }
        }
        return benefitScopes;
    }


    /**
     * 新增商户权益操作
     *
     * @param merchantId
     * @param benefitGenerateTypeEnum 权益生成方式
     * @param benefitClassifyCode     权益类型
     * @param expiryDateCategory
     * @param upTime
     * @param downTime
     */
    private Benefit addMerchantBenefit(Long merchantId, BenefitGenerateTypeEnum benefitGenerateTypeEnum,
                                       Integer benefitClassifyCode, Integer applicable,
                                       Long associatedId, Integer expiryDateCategory, String upTime, String downTime) {
        log.debug("创建新权益,merchantId:{}, classifyCode:{}", merchantId, benefitClassifyCode);
        Benefit benefit = new Benefit();
        benefit.setTitle(BenefitClassifyEnum.getDesc(benefitClassifyCode));
        benefit.setClassify(benefitClassifyCode);
        benefit.setMerchantId(merchantId);
        benefit.setCreateTime(new Date());
        benefit.setActive(Boolean.TRUE);
        benefit.setExpiryDateCategory(ofNullable(expiryDateCategory).orElse(ExpiryDateCategoryEnum.NO_LIMIT.getValue()));
        benefit.setUpTime(upTime);
        benefit.setDownTime(downTime);
        benefitMapper.insert(benefit);

        // 保存规则
        BenefitRule benefitRule = new BenefitRule();
        benefitRule.setBenefitId(benefit.getId());
        benefitRule.setMerchantId(merchantId);
        benefitRule.setGenerateType(benefitGenerateTypeEnum.getType());
        benefitRule.setBenefitClassify(benefit.getClassify());
        benefitRule.setCreateTime(LocalDateTime.now());
        benefitRuleMapper.insert(benefitRule);

        if (applicable != null && associatedId != null) {
            // 保存范围
            BenefitScope benefitScope = new BenefitScope();
            benefitScope.setBenefitId(benefit.getId());
            // 储值服务绑定到设备
            benefitScope.setApplicable(applicable);
            benefitScope.setAssociatedId(associatedId);
            benefitScope.setMerchantId(merchantId);
            benefitScope.setCreateTime(new Date());
            benefitScopeMapper.insert(benefitScope);
        }

        List<Integer> thirdPlatFormBenefitClassifyList = Arrays.asList(BenefitClassifyEnum.THIRD_PLATFORM_AMOUNT.getCode(), BenefitClassifyEnum.THIRD_PLATFORM_COINS.getCode());
        //延迟结算权益
        List<Integer> delayedSettelementBenefitClassifyList = Arrays.asList(BenefitClassifyEnum.DELAYED_SETTELEMENT_BALANCE.getCode(), BenefitClassifyEnum.DELAYED_SETTELEMENT_COIN.getCode());
        if (thirdPlatFormBenefitClassifyList.contains(benefitClassifyCode) || delayedSettelementBenefitClassifyList.contains(benefitClassifyCode)) {
            BenefitConsumeRule benefitConsumeRule = benefitRepository.getBenefitConsumeRule(merchantId, benefitClassifyCode);
            if (benefitConsumeRule == null) {
                Integer weight = 0;
                if(thirdPlatFormBenefitClassifyList.contains(benefitClassifyCode)){
                    weight = 6;
                }
                //新增消耗规则
                benefitConsumeRule = new BenefitConsumeRule();
                benefitConsumeRule.setMerchantId(merchantId);
                benefitConsumeRule.setBenefitClassify(benefitClassifyCode);
                benefitConsumeRule.setWeight(weight);
                benefitConsumeRule.setExpirePriority(0);
                benefitConsumeRule.setIsDefault(Boolean.TRUE);
                benefitConsumeRule.setIsActive(Boolean.TRUE);
                benefitConsumeRule.setCreateBy(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID);
                benefitConsumeRule.setUpdateBy(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID);
                benefitConsumeRule.setCreateTime(new Date());
                benefitConsumeRule.setUpdateTime(new Date());
                benefitConsumeRuleMapper.insert(benefitConsumeRule);
                benefitCacheRepository.clearBenefitConsumeRuleCache(merchantId);
            }
        }
        return benefit;
    }

}
