package com.lyy.user.domain.statistics.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 统计总表
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName(value = "um_statistics")
public class Statistics {
    /**
     * 商家用户id
     */
    @TableField(value = "merchant_user_id")
    private Long merchantUserId;

    /**
     * 商家id
     */
    @TableField(value = "merchant_id")
    private Long merchantId;

    /**
     * 平台用户id
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 启动次数
     */
    @TableField(value = "start_times")
    private Integer startTimes;

    /**
     * 充值次数
     */
    @TableField(value = "pay_times")
    private Integer payTimes;

    /**
     * 充值金额
     */
    @TableField(value = "pay_amount")
    private BigDecimal payAmount;

    /**
     * 支付购买次数
     */
    @TableField(value = "pay_for_service_times")
    private Integer payForServiceTimes;

    /**
     * 支付购买金额
     */
    @TableField(value = "pay_for_service_amount")
    private BigDecimal payForServiceAmount;

    /**
     * 消耗币数（累计投币）
     */
    @TableField(value = "coins_consumption")
    private BigDecimal coinsConsumption;

    /**
     * 消耗金额（累计消耗金额）
     */
    @TableField(value = "amount_consumption")
    private BigDecimal amountConsumption;

    /**
     * 储值币数
     */
    @TableField(value = "total_coins")
    private BigDecimal totalCoins;

    /**
     * 剩余币数
     */
    @TableField(value = "balance_coins")
    private BigDecimal balanceCoins;

    /**
     * 剩余金额
     */
    @TableField(value = "balance_amount")
    private BigDecimal balanceAmount;

    /**
     * 最近消费时间
     */
    @TableField(value = "recent_consumption_time")
    private Date recentConsumptionTime;

    /**
     * 记录创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 记录更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 储值余额
     */
    @TableField(value = "total_amount")
    private BigDecimal totalAmount;

}