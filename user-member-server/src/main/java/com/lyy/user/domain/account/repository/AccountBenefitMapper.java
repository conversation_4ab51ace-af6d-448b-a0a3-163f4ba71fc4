package com.lyy.user.domain.account.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitScopeQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.*;
import com.lyy.user.domain.account.dto.AccountBenefitScopeDO;
import com.lyy.user.domain.account.dto.AccountBenefitWithScopeDTO;
import com.lyy.user.domain.account.entity.AccountBenefit;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 账户权益查询Mapper
 * <AUTHOR>
 */
@Repository
public interface AccountBenefitMapper extends BaseMapper<AccountBenefit> {

    Integer insertBatch(List<AccountBenefit> list);

    /**
     * 增加余额和总数
     * @param id
     * @param merchantId
     * @param operator
     * @param total
     * @param balance
     */
    void increaseBalanceAndTotal(@Param("id") Long id,
                                 @Param("merchantId") Long merchantId,
                                 @Param("operator") Long operator,
                                 @Param("total") BigDecimal total,
                                 @Param("balance") BigDecimal balance);

    Integer changeBalance(@Param("id") Long id, @Param("merchantId") Long merchantId, @Param("change") BigDecimal change,
        @Param("operator") Long operator, @Param("updateTime") Date updateTime);


    List<AccountBenefitWithScopeDTO> queryForConsume(@Param("merchantUserId") Long merchantUserId,
                                                     @Param("merchantId") Long merchantId,
                                                     @Param("classify") List<Integer> classify,
                                                     @Param("benefitId") List<Long> benefitId,
                                                     @Param("excludeClassify") List<Integer> excludeClassify,
                                                     @Param("isAll") Boolean isAll,
                                                     @Param("serviceType") Integer serviceType);

    List<AccountBenefit> selectInvalidateRecord(@Param("merchantId") long merchantId);

    /**
     *
     * @param dto
     * @return
     */
    List<AccountBenefitDTO> allBenefitRecord(@Param("dto") AccountBenefitQueryDTO dto);
    /**
     *
     * @param dto
     * @return
     */
    List<AccountBenefitDTO> listBenefitRecord(@Param("dto") AccountBenefitQueryDTO dto,
                                              @Param("start") Long start,
                                              @Param("pageSize") Long pageSize,
                                              @Param("queryAll") Boolean queryAll
                                              );


    Long countBenefitRecord(@Param("dto") AccountBenefitQueryDTO benefitQueryDTO);

    /**
     * 根据条件获取最新的账户权益明细
     *
     * @param merchantId     商户id
     * @param userId         用户id
     * @param merchantUserId 商户用户id
     * @param benefitId      权益id
     * @param accountId      账户id
     * @return
     */
    AccountBenefit getAccountBenefit(@Param("merchantId") Long merchantId, @Param("userId") Long userId,
                                     @Param("merchantUserId") Long merchantUserId, @Param("benefitId") Long benefitId,
                                     @Param("accountId") Long accountId);

    /**
     * 获取用户所有的权益
     * @param dto
     * @return
     */
    List<AccountBenefit> findUserAllBenefit(@Param("dto") AvailableBenefitQueryDTO dto);


    /**
     * 权益详情(余额详情)
     * @param page
     * @param benefitDetailSelectDTO
     * @return
     */
    IPage<SmallVenueBenefitDetailDTO> smallVenueBenefitDetail(IPage page, @Param("selectDTO") BenefitDetailSelectDTO benefitDetailSelectDTO);

    /**
     * 查询权益统计数据（根据门店进行分组）
     * @param merchantId
     * @param userId
     * @param accountId
     * @return
     */
    List<UserBenefitStatisticsDTO> benefitStatisticsGroupByStoreId(@Param("merchantId") Long merchantId, @Param("userId") Long userId, @Param("accountId") Long accountId);

    /**
     * 更新用户权益明细所属账户id
     * @param oldAccountId
     * @param newAccountId
     * @param merchantId
     * @return
     */
    Integer updateAccountBenefitByAccountId(@Param("oldAccountId") Long oldAccountId, @Param("newAccountId") Long newAccountId, @Param("merchantId") Long merchantId);

    /**
     * 更新AccountBenefit的id信息
     * @param merchantId
     * @param userId
     * @param accountId
     * @param merchantUserId
     * @return
     */
    Integer updateAccountBenefitIdInfo(@Param("merchantId") Long merchantId,
                                       @Param("userId") Long userId,
                                       @Param("accountId") Long accountId,
                                       @Param("merchantUserId") Long merchantUserId);


    /**
     * 批量更新权益余额
     * @param accountBenefitIds
     * @param balance
     * @param operatorId
     * @return
     */
    Integer batchUpdateAccountBenefitBalance(@Param("accountBenefitIds") List<Long> accountBenefitIds,
                                             @Param("balance") BigDecimal balance,
                                             @Param("operatorId") Long operatorId,
                                             @Param("merchantId") Long merchantId);

    List<AccountBenefitScopeDO> listBenefitWithScope(@Param("query") AccountBenefitScopeQueryDTO query);

    /**
     * 移动端会员卡列表储值信息
     * @param merchantId
     * @param accountBenefitIds
     * @return
     */
    List<MobileTerminalAccountBenefitInfo> findBenefitByAccountIds(@Param("merchantId") Long merchantId,
                                                                   @Param("accountBenefitIds") List<Long> accountBenefitIds);

    /**
     * 更新AccountBenefit的id信息
     *
     * @param merchantId
     * @param oldUserId
     * @param oldMerchantUserId
     * @param userId
     * @param merchantUserId
     * @return
     */
    Integer updateAccountBenefitIds(@Param("merchantId") Long merchantId,
                                       @Param("oldUserId") Long oldUserId,
                                       @Param("oldMerchantUserId") Long oldMerchantUserId,
                                       @Param("userId") Long userId,
                                       @Param("merchantUserId") Long merchantUserId,
                                       @Param("accountId") Long accountId);


    /**
     * 查询会员在娱乐下的场地的储值
     *
     * @param smallVenueAccountBenefitTransferQueryDTO
     * @return
     */
    List<SmallVenueAccountBenefitTransferDTO> selectTotalTransferAccountBenefit(@Param("query") SmallVenueAccountBenefitTransferQueryDTO smallVenueAccountBenefitTransferQueryDTO);

    /**
     * 查询会员在娱乐下的场地的储值
     *
     * @param smallVenueAccountBenefitTransferQueryDTO
     * @return
     */
    List<SmallVenueAccountBenefitTransferDTO> selectTransferAccountBenefit(@Param("query") SmallVenueAccountBenefitTransferQueryDTO smallVenueAccountBenefitTransferQueryDTO);

    IPage<SmallVenueBenefitDetailDTO> queryAccountBenefitList(Page<SmallVenueBenefitDetailDTO> page, @Param("selectDTO")  AccountBenefitReqDTO selectDTO);
}