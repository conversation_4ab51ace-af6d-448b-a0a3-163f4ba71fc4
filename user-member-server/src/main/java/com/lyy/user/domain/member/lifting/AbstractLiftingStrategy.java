package com.lyy.user.domain.member.lifting;

import com.lyy.user.account.infrastructure.constant.MemberLiftingConditionEnum;
import com.lyy.user.account.infrastructure.constant.MemberLiftingRuleCategoryEnum;
import com.lyy.user.account.infrastructure.constant.MemberLiftingRuleRecordStatusEnum;
import com.lyy.user.application.member.IMemberLiftingRuleRecordService;
import com.lyy.user.application.member.IMemberService;
import com.lyy.user.domain.member.dto.MemberLiftingStrategyDTO;
import com.lyy.user.domain.member.dto.MemberLiftingStrategyResultDTO;
import com.lyy.user.domain.member.entity.Member;
import com.lyy.user.domain.member.entity.MemberLifting;
import com.lyy.user.domain.member.entity.MemberLiftingRule;
import com.lyy.user.domain.member.entity.MemberLiftingRuleRecord;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @className: AbstractLiftingStrategy
 * @date 2021/4/6
 */
@Slf4j
public abstract class AbstractLiftingStrategy implements LiftingStrategyInterface {

    @Autowired
    protected IMemberLiftingRuleRecordService memberLiftingRuleRecordService;
    @Autowired
    private IMemberService memberService;

    /**
     * 统计次数的策略类型
     */
    private final static List<MemberLiftingRuleCategoryEnum> COUNT_CATEGORY_LIST = Arrays.asList(
            MemberLiftingRuleCategoryEnum.CATEGORY_LOGIN_NUM,
            MemberLiftingRuleCategoryEnum.CATEGORY_PAY_NUM,
            MemberLiftingRuleCategoryEnum.CATEGORY_PERFECT_INFORMATION,
            MemberLiftingRuleCategoryEnum.CATEGORY_BIND_PHONE,
            MemberLiftingRuleCategoryEnum.CATEGORY_FOLLOW_PUBLIC_ACCOUNT
            );
    /**
     * 统计累计总数的策略类型
     */
    private final static List<MemberLiftingRuleCategoryEnum>  SUM_CATEGORY_LIST = Arrays.asList(
            MemberLiftingRuleCategoryEnum.CATEGORY_CONSUMPTION_MONEY
    );


    /**
     * 处理得到升级结果，但是还没有进行升级
     * @param memberLiftingStrategyDTO
     * @return
     */
    @Override
    public MemberLiftingStrategyResultDTO handlerResult(MemberLiftingStrategyDTO memberLiftingStrategyDTO) {
        if (memberLiftingStrategyDTO.getMemberLiftingRuleRecordList() == null || memberLiftingStrategyDTO.getMemberLiftingRuleRecordList().isEmpty()) {
            //没有记录的直接返回false
            return null;
        }
        Member member = memberService.getMember(memberLiftingStrategyDTO.getMerchantId(), memberLiftingStrategyDTO.getUserId(), memberLiftingStrategyDTO.getMemberLifting().getMemberGroupId());
        if (member == null || member.getDel()) {
            log.warn("{} 升级处理的会员信息非法，为{}", memberLiftingStrategyDTO, member);
            return null;
        }

        Map<Long, List<MemberLiftingRuleRecord>> memberLiftingRuleRecordMap = memberLiftingStrategyDTO.getMemberLiftingRuleRecordList().stream()
                .collect(Collectors.groupingBy(MemberLiftingRuleRecord::getMemberLiftingRuleId));
        MemberLifting memberLifting = memberLiftingStrategyDTO.getMemberLifting();
        MemberLiftingStrategyResultDTO memberLiftingStrategyResultDTO = new MemberLiftingStrategyResultDTO();
        memberLiftingStrategyResultDTO.setMember(member);
        memberLiftingStrategyResultDTO.setMemberLifting(memberLifting);
        //判断是否达到上限
        if (memberLifting.getEffectiveNumber() > 0 && memberLifting.getEffectiveNumber() <= memberLiftingStrategyDTO.getCurrentEffectiveNumber()) {
            //达到次数,需要把记录置为失效
            memberLiftingStrategyResultDTO.setFailureRecord(memberLiftingStrategyDTO.getMemberLiftingRuleRecordList());
            return memberLiftingStrategyResultDTO;
        }

        Map<MemberLiftingRule, Collection<MemberLiftingRuleRecord>> memberLiftingRuleMap = new HashMap<>(16);
        for (MemberLiftingRule memberLiftingRule : memberLiftingStrategyDTO.getMemberLiftingRuleList()) {
            List<MemberLiftingRuleRecord> recordList = memberLiftingRuleRecordMap.get(memberLiftingRule.getId());
            if (recordList == null || recordList.isEmpty()) {
                continue;
            }
            MemberLiftingRuleCategoryEnum categoryEnum = MemberLiftingRuleCategoryEnum.getByValue(memberLiftingRule.getCategory());
            if (categoryEnum == null) {
                log.warn("找不到 {} 策略规则对应的策略类型", memberLiftingRule);
                continue;
            }
            //检查规则与记录是否匹配
            if (COUNT_CATEGORY_LIST.contains(categoryEnum)) {
                //统计次数的
                //登录次数，支付笔数，完善信息，绑定手机，关注公众号
                int num = memberLiftingRule.getRangeValue().intValue();
                if (num <= recordList.size()) {
                    memberLiftingRuleMap.put(memberLiftingRule, recordList);
                }
            } else if (SUM_CATEGORY_LIST.contains(categoryEnum)) {
                //统计总数的
                //消费金额
                BigDecimal count = recordList.stream()
                        .map(MemberLiftingRuleRecord::getRangeValue)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                if (count.compareTo(memberLiftingRule.getRangeValue()) >= 0) {
                    //实际的总数大于等于规则规定的总数
                    memberLiftingRuleMap.put(memberLiftingRule, recordList);
                }
            }
        }
        //根据条件判断是否符合记录
        log.info("{} 升级策略的升级条件为 {},当前升级规则共有 {} 个,实际通过校验的有 {} 个", memberLifting.getId(), memberLifting.getCondition(),
                memberLiftingStrategyDTO.getMemberLiftingRuleList().size(), memberLiftingRuleMap.size());
        if (memberLifting.getCondition().equals(MemberLiftingConditionEnum.OR_ONE.getValue())) {
            //只需要一项就可以触发
            if (!memberLiftingRuleMap.isEmpty()) {
                memberLiftingStrategyResultDTO.setMemberLiftingRuleMap(memberLiftingRuleMap);
            }
        } else if (memberLifting.getCondition().equals(MemberLiftingConditionEnum.AND_ALL.getValue())) {
            //需要全部符合才能触发
            if (memberLiftingStrategyDTO.getMemberLiftingRuleList().size() <= memberLiftingRuleMap.size()) {
                memberLiftingStrategyResultDTO.setMemberLiftingRuleMap(memberLiftingRuleMap);
            }
        } else {
            //条件异常直接返回失败
            log.warn("找不到 {} 升级策略对应的条件", memberLifting);
            return null;
        }
        return memberLiftingStrategyResultDTO;
    }

    /**
     * 处理失效的记录,只是把记录设置为失败状态
     * @param memberLiftingStrategyResultDTO
     * @return
     */
    @Override
    public int handlerFailure(MemberLiftingStrategyResultDTO memberLiftingStrategyResultDTO) {
        if (memberLiftingStrategyResultDTO.getFailureRecord() == null || memberLiftingStrategyResultDTO.getFailureRecord().isEmpty()) {
            return 0;
        }
        List<Long> recordIds = memberLiftingStrategyResultDTO.getFailureRecord().stream()
                .map(MemberLiftingRuleRecord::getId)
                .collect(Collectors.toList());
        if (recordIds.isEmpty()) {
            log.info(" {} 会员没有对应需要更新状态的规则记录", memberLiftingStrategyResultDTO.getMember().getId());
            return 0;
        }
        return memberLiftingRuleRecordService.updateStatusOfIds(memberLiftingStrategyResultDTO.getMember().getMerchantId(), recordIds, MemberLiftingRuleRecordStatusEnum.INIT.getValue(), MemberLiftingRuleRecordStatusEnum.FAILURE.getValue());
    }
    /**
     * 处理成功的记录,只是把记录设置为完成状态
     * @param memberLiftingStrategyResultDTO
     * @return
     */
    @Override
    public int handlerSuccess(MemberLiftingStrategyResultDTO memberLiftingStrategyResultDTO) {
        //提取所有成功记录的id
        List<Long> recordIds = memberLiftingStrategyResultDTO.getMemberLiftingRuleMap().values().stream()
                .flatMap(memberLiftingRuleRecords -> memberLiftingRuleRecords.stream()
                        .map(MemberLiftingRuleRecord::getId))
                .collect(Collectors.toList());
        if (recordIds.isEmpty()) {
            log.info(" {} 会员没有对应需要更新状态的规则记录", memberLiftingStrategyResultDTO.getMember().getId());
            return 0;
        }
        return memberLiftingRuleRecordService.updateStatusOfIds(memberLiftingStrategyResultDTO.getMember().getMerchantId(), recordIds, MemberLiftingRuleRecordStatusEnum.INIT.getValue(), MemberLiftingRuleRecordStatusEnum.FINISH.getValue());

    }

    /**
     * 计算成长值
     * @param memberLifting
     * @param memberLiftingRuleMap
     * @return
     */
    protected Long calculateGrowValue(MemberLifting memberLifting, Map<MemberLiftingRule, Collection<MemberLiftingRuleRecord>> memberLiftingRuleMap) {
        if (memberLifting.getCondition().equals(MemberLiftingConditionEnum.OR_ONE.getValue())) {
            //只需要一项就可以触发
            return memberLiftingRuleMap.entrySet().stream()
                    .mapToLong(entry -> {
                        MemberLiftingRule memberLiftingRule = entry.getKey();
                        MemberLiftingRuleCategoryEnum categoryEnum = MemberLiftingRuleCategoryEnum.getByValue(memberLiftingRule.getCategory());
                        if (categoryEnum == null) {
                            log.warn("找不到 {} 策略规则对应的策略类型", memberLiftingRule);
                            return 0;
                        }
                        //检查规则与记录是否匹配
                        if (COUNT_CATEGORY_LIST.contains(categoryEnum)) {
                            //统计次数的
                            int num = memberLiftingRule.getRangeValue().intValue();
                            Long growValue = (entry.getValue().size() / num) * memberLifting.getGrowValue();
                            log.info(" {} 升降级策略的 {} 规则有 {} 符合规则，其中每 {} 个为 {} 成长值,计算得到 {} 成长值", memberLifting.getId(),
                                    memberLiftingRule.getId(), entry.getValue().size(),
                                    num, memberLifting.getGrowValue(), growValue);
                            return growValue;
                        } else if (SUM_CATEGORY_LIST.contains(categoryEnum)) {
                            //统计总数的
                            BigDecimal count = entry.getValue().stream()
                                    .map(MemberLiftingRuleRecord::getRangeValue)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                            BigDecimal growValue = count.divide(memberLiftingRule.getRangeValue(), 2, RoundingMode.FLOOR)
                                    .multiply(new BigDecimal(memberLifting.getGrowValue()));
                            log.info("{} 升降级策略的 {} 规则有累计总数为 {} ,其中每 {} 个为 {} 成长值,计算得到 {} 成长值", memberLifting.getId(),
                                    memberLiftingRule.getId(), count,
                                    memberLiftingRule.getRangeValue(), memberLifting.getGrowValue(), growValue);
                            return growValue.longValue();
                        } else if (MemberLiftingRuleCategoryEnum.CATEGORY_DEFAULT.equals(categoryEnum)) {
                            log.info(" {} 升降级策略的 {} 规则为默认规则，直接使用策略对应成长值 : {}", memberLifting.getId(),
                                    memberLiftingRule.getId(), memberLifting.getGrowValue());
                            return memberLifting.getGrowValue();
                        }
                        log.warn(" {} 升降级策略的 {} 规则非法策略 {}, 默认为获得配置的 {} 成长值", memberLifting.getId(), memberLiftingRule.getId(),
                                categoryEnum, memberLifting.getGrowValue());
                        return memberLifting.getGrowValue();
                    }).sum();

        }
        log.debug("{} 升降级策略条件为 {} 直接获得配置的 {} 成长值", memberLifting.getId(), memberLifting.getCondition(), memberLifting.getGrowValue());
        return memberLifting.getGrowValue();
    }
}
