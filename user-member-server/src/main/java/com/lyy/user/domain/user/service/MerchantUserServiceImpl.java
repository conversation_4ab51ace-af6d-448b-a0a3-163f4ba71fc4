package com.lyy.user.domain.user.service;

import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReUtil;
import cn.lyy.cache.annotation.Cacheable;
import cn.lyy.cache.annotation.Level2Cache;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitCreateDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountCreateDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitInfoDTO;
import com.lyy.user.account.infrastructure.common.DataList;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountStatusEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyGroupEnum;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.account.infrastructure.member.dto.SmallVenueMemberLevelInfoDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsUpdateDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantIntegralUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserCheckPasswordDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserUpdatePasswordDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserUpdateTelephoneDTO;
import com.lyy.user.account.infrastructure.user.dto.PayoutBenefitDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueMobileUserListSelectDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueUserDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueUserListSelectDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueUserMobileVO;
import com.lyy.user.account.infrastructure.user.dto.UpdateMerchantUserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MemberCardInfoBenefitDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MemberCardInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserAndLevelInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserConsumeStatisticsDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserInfoAndAccountDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserInfoByKeywordDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserStatisticsDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserStoredStatisticsDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.SupplementCardsDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.TagInfoDTO;
import com.lyy.user.app.interfaces.facade.dto.UserDTO;
import com.lyy.user.application.account.AccountService;
import com.lyy.user.application.benefit.BenefitService;
import com.lyy.user.application.statistics.StatisticsService;
import com.lyy.user.application.user.IMerchantUserService;
import com.lyy.user.application.user.IPlatformUserPhoneService;
import com.lyy.user.application.user.IRemoveCacheService;
import com.lyy.user.domain.account.dto.SmallVenueCountCardNoDTO;
import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.domain.account.repository.AccountMapper;
import com.lyy.user.domain.member.dto.MemberLevelInfoDTO;
import com.lyy.user.domain.member.entity.MemberLevel;
import com.lyy.user.domain.member.repository.MemberLevelMapper;
import com.lyy.user.domain.member.repository.MemberMapper;
import com.lyy.user.domain.statistics.entity.SmallVenueStoredStatistics;
import com.lyy.user.domain.statistics.repository.SmallVenueStoredStatisticsMapper;
import com.lyy.user.domain.statistics.repository.StatisticsMapper;
import com.lyy.user.domain.user.dto.MerchantUserTagDTO;
import com.lyy.user.domain.user.dto.TagUserParamDTO;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.domain.user.entity.TagUser;
import com.lyy.user.domain.user.repository.MerchantUserMapper;
import com.lyy.user.domain.user.repository.MerchantUserTagMapper;
import com.lyy.user.domain.user.repository.TagUserMapper;
import com.lyy.user.infrastructure.base.MerchantBaseServiceImpl;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.repository.user.MerchantUserRepository;
import com.lyy.user.infrastructure.repository.user.UserRepository;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * @ClassName: MerchantUserServiceImpl
 * @description: 商户用户
 * @author: pengkun
 * @date: 2021/03/30
 **/
@Service
@Slf4j
public class MerchantUserServiceImpl extends MerchantBaseServiceImpl<MerchantUserMapper, MerchantUser> implements IMerchantUserService {

    @Resource
    private MerchantUserMapper merchantUserMapper;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private TagUserMapper tagUserMapper;
    @Autowired
    @Lazy
    private MerchantAutoTagAsyncHandler autoTagAsyncHandler;
    @Autowired
    private IPlatformUserPhoneService platformUserPhoneService;
    @Resource
    private BenefitService benefitService;
    @Resource
    private AccountService accountService;
    @Resource
    private StatisticsService statisticsService;

    @Resource
    private IRemoveCacheService removeCacheService;

    @Autowired
    private MerchantUserTagMapper merchantUserTagMapper;

    @Autowired
    private MemberMapper memberMapper;

    @Autowired
    private SmallVenueStoredStatisticsMapper smallVenueStoredStatisticsMapper;

    @Autowired
    private StatisticsMapper statisticsMapper;

    @Autowired
    private MemberLevelMapper memberLevelMapper;

    @Resource
    private UserRepository userRepository;
    @Resource
    private MerchantUserRepository merchantUserRepository;

    @Autowired
    @Qualifier("selectMemberInfoTaskExecutor")
    private Executor selectMemberInfoTaskExecutor;

    @Value("${batch.singleHandlerNum:1000}")
    private Long singleHandlerNum;

    @Value("${merchant.user.optimize:Y}")
    private String optimizeFlag;

    /**
     * 保存或修改商户用户
     *
     * @param dto
     * @return java.lang.Long
     */
    @Override
    public Long saveOrUpdateMerchantUser(MerchantUserDTO dto) {
        try {
            return saveOrUpdateMerchantUser(dto, "", null);
        } catch (DuplicateKeyException e) {
            log.warn("用户重复直接返回, merchantId: {} userId:{}", dto.getMerchantId(), dto.getUserId());
            MerchantUser user = merchantUserRepository.getByUserIdAndMerchantId(dto.getUserId(), dto.getMerchantId());
            if (user == null) {
                return saveOrUpdateMerchantUser(dto, "", null);
            }
            return user.getId();
        }
    }

    /**
     * 保存或修改商户用户信息
     *
     * @param dto
     * @param password
     * @return
     */
    @Override
    public Long saveOrUpdateMerchantUser(MerchantUserDTO dto, String password, Integer systemFlag) {
        MerchantUser merchantUser = new MerchantUser();
        if(StringUtils.isBlank(dto.getProvinceCity())){
            dto.setProvinceCity(ofNullable(dto.getProvince()).orElse("")+ofNullable(dto.getCityName()).orElse(""));
        }
        if(StringUtils.isNotBlank(dto.getCityName())){
            if(dto.getCityId() == null){
                //查询城市id
                Long cityId = userRepository.getCityId(dto.getCityName());
                dto.setCityId(cityId);
            }
        }
        BeanUtils.copyProperties(dto, merchantUser);
        if (StringUtils.isNotBlank(password)) {
            merchantUser.setPassWord(password);
        }
        if (dto.getId() != null) {
            merchantUser.setCreatedby(null);
            updateByIdAndMerchantId(merchantUser);
            // 有需要才创建标签,默认创建
            if(!Boolean.FALSE.equals(dto.getIsCreateTag())) {
                taggingMerchantUser(merchantUser, dto.getStoreName());
            }
            return dto.getId();
        } else {
            if (dto.getMerchantId() == null || dto.getUserId() == null) {
                log.error("商户用户保存参数异常,merchantId:{},userId:{}", dto.getMerchantId(), dto.getUserId());
                throw new BusinessException(UserErrorCode.MERCHANT_USER_SAVE_PARAM_ERROR.getCode(), UserErrorCode.MERCHANT_USER_SAVE_PARAM_ERROR.getMessage());
            }
            MerchantUser user = merchantUserRepository.getByUserIdAndMerchantId(dto.getUserId(), dto.getMerchantId());
            if (user != null) {
                merchantUser.setId(user.getId());
                updateByIdAndMerchantId(merchantUser);
            } else {
                //保存商户用户信息
                saveMerchantUser(merchantUser);
                if (StringUtils.isNotBlank(merchantUser.getTelephone())) {
                    //手机号不为空,保存平台用户电话
                    PlatformUserPhoneDTO userPhoneDTO = new PlatformUserPhoneDTO();
                    userPhoneDTO.setUserId(merchantUser.getUserId());
                    userPhoneDTO.setMerchantId(merchantUser.getMerchantId());
                    userPhoneDTO.setTelephone(merchantUser.getTelephone());
                    userPhoneDTO.setOperatorId(ofNullable(dto.getCreatedby()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
                    userPhoneDTO.setMerchantUserId(merchantUser.getId());
                    userPhoneDTO.setSystemFlag(systemFlag);
                    platformUserPhoneService.saveOrUpdatePlatformUserPhone(userPhoneDTO);
                }
            }
            // 有需要才创建标签,默认创建
            if(!Boolean.FALSE.equals(dto.getIsCreateTag())) {
                taggingMerchantUser(merchantUser, dto.getStoreName());
            }
        }
        return merchantUser.getId();
    }

    private void taggingMerchantUser(MerchantUser user, String storeName) {
        log.debug("新会员体系-自动打标签,user:{}", user);
        try {
            if (user == null) {
                return;
            }
            //性别标签 +  场地标签
            TagUserParamDTO tagUserParamDTO = new TagUserParamDTO();
            tagUserParamDTO.setTradeType(null);
            tagUserParamDTO.setUserType(user.getUserType());
            tagUserParamDTO.setUserId(user.getUserId());
            tagUserParamDTO.setMerchantUserId(user.getId());
            tagUserParamDTO.setMerchantId(user.getMerchantId());
            tagUserParamDTO.setGender(StringUtils.isBlank(user.getGender())?"未知":user.getGender());
            if (StringUtils.isNotBlank(storeName)) {
                int length = storeName.trim().length();
                tagUserParamDTO.setStoreName(storeName.trim().substring(0, Math.min(length, 20)));
            }
            autoTagAsyncHandler.autoCreateTag(tagUserParamDTO);
        } catch (Exception e) {
            // 标签异常不影响业务
            log.warn("商家用户:{} 自动打标签异常", user.getId(), e);
        }
    }

    /**
     * 根据商户用户Id获取商户用户信息
     *
     * @param merchantUserId 商户用户id
     * @param merchantId     商户id
     * @return com.lyy.user.user.domain.entity.MerchantUser
     */
    @Override
    @Deprecated
    @Cacheable(value = "multilevel:cache:member:merchantUser", key = "#merchantId  + '-' + #merchantUserId ",
            level2Cache = @Level2Cache(useCacheCenter = false, expireTime = 3, timeUnit = TimeUnit.HOURS, preloadTime = 1, forceRefresh = true))
    public MerchantUser findById(Long merchantUserId, Long merchantId) {
        MerchantUser merchantUser = merchantUserMapper.selectByIdAndMerchantId(merchantUserId, merchantId);
        if(merchantUser != null && StringUtils.isNotBlank(merchantUser.getProvinceCity()) && merchantUser.getProvinceCity().contains("null")){
            merchantUser.setProvinceCity("");
        }
        return merchantUser;
    }

    /**
     * 根据平台用户Id和商户Id获取商户用户信息
     *
     * @param userId     平台用户Id
     * @param merchantId 商户Id
     * @return com.lyy.user.user.domain.entity.MerchantUser
     */
    @Override
    @Deprecated
    @Cacheable(value = "multilevel:cache:member:user", key = "#userId  + '-' + #merchantId ",
            level2Cache = @Level2Cache(useCacheCenter = false, expireTime = 3, timeUnit = TimeUnit.HOURS, preloadTime = 1, forceRefresh = true))
    public MerchantUser findByUserIdAndMerchantId(Long userId, Long merchantId) {
        MerchantUser merchantUser;
        merchantUser = merchantUserMapper.selectByUserIdAndMerchantId(userId, merchantId);
        if(merchantUser != null && StringUtils.isNotBlank(merchantUser.getProvinceCity()) && merchantUser.getProvinceCity().contains("null")) {
            merchantUser.setProvinceCity("");
        }
        log.debug("获取商户用户信息：{}", merchantUser);
        return merchantUser;
    }

    /**
     * 根据手机号和商户Id获取商户用户信息
     *
     * @param telephone  手机号码
     * @param merchantId 商户Id
     * @return java.util.List<com.lyy.user.user.domain.entity.MerchantUser>
     */
    @Override
    public List<MerchantUser> findByTelephoneAndMerchantId(String telephone, Long merchantId) {
        return merchantUserMapper.selectAllByTelephoneAndMerchantId(telephone, merchantId);
    }

    /**
     * 更新商户用户手机号码
     *
     * @param merchantId     商户id
     * @param merchantUserId 商户用户id
     * @param userId         平台用户id
     * @param telephone      手机号
     * @param updateby       操作人
     * @return java.lang.Boolean
     */
    @Override
    public Boolean updateTelephone(Long merchantId, Long merchantUserId, Long userId, String telephone, Long updateby) {
        MerchantUser merchantUser;
        if (merchantUserId == null) {
            merchantUser = merchantUserRepository.getByIdAndMerchantId(userId, merchantId);
        } else {
            merchantUser = merchantUserRepository.getByIdAndMerchantId(merchantUserId, merchantId);
        }
        if (merchantUser == null) {
            log.info("没有商户用户，不需要进行更新。userId:{},merchantUserId:{},merchantId:{}", userId, merchantUserId, merchantId);
            return true;
        }

        if (merchantUser.getTelephone().equals(telephone)) {
            log.info("不需要更新商户用户手机号码");
            return true;
        }
        MerchantUserDTO merchantUserDTO = new MerchantUserDTO();
        merchantUserDTO.setId(merchantUser.getId());
        merchantUserDTO.setCreatedby(updateby);
        merchantUserDTO.setUserId(userId);
        merchantUserDTO.setMerchantId(merchantId);
        merchantUserDTO.setTelephone(telephone);
        saveOrUpdateMerchantUser(merchantUserDTO);
        return true;
    }


    /**
     * 根据用户id和商户id更新商户用户信息
     *
     * @param merchantUser
     * @return
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByIdAndMerchantId(MerchantUser merchantUser) {
        if (merchantUser.getMerchantId() == null && merchantUser.getId() == null) {
            throw new BusinessException(UserErrorCode.MERCHANT_USER_UPDATE_PARAM_ERROR.getCode(), UserErrorCode.MERCHANT_USER_UPDATE_PARAM_ERROR.getMessage());
        }
        if (merchantUser.getActive() != null && !merchantUser.getActive()) {
            //用户注销处理
            Long max = merchantUserMapper.getMax(merchantUser.getMerchantId(), merchantUser.getUserId());
            merchantUser.setCreatedby(max + 1);
        }
        Date now = new Date();
        merchantUser.setUpdateTime(ofNullable(merchantUser.getUpdateTime()).orElse(now));
        merchantUser.setUpdatedby(ofNullable(merchantUser.getUpdatedby()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        int result = merchantUserMapper.updateMerchantUserByIdAndMerchantId(merchantUser);
        if (result <= 0) {
            //更新失败，抛异常
            throw new BusinessException(UserErrorCode.MERCHANT_USER_UPDATE_ERROR.getCode(),
                    UserErrorCode.MERCHANT_USER_UPDATE_ERROR.getMessage());
        }
        if (Objects.nonNull(merchantUser.getUserId())) {
            removeCacheService.removeUserMerchantCache(merchantUser.getUserId(), merchantUser.getMerchantId());
        }
        removeCacheService.removeMerchantUserCache(merchantUser.getId(), merchantUser.getMerchantId());

        return true;
    }

    @Override
    public Page<MerchantUserListDTO> merchantUserTotalByCondition(MerchantUserQueryDTO merchantUserQueryDTO) {
        Page<MerchantUserListDTO> merchantUserListDTOPage = new Page<>(merchantUserQueryDTO.getPageIndex(), merchantUserQueryDTO.getPageSize());
        buildMerchantUserListPage(merchantUserQueryDTO, merchantUserListDTOPage);
        return merchantUserListDTOPage;
    }



    @Override
    public Page<MerchantUserListDTO> queryUserListByMerchant(MerchantUserQueryDTO merchantUserQueryDTO) {

        Page<MerchantUserListDTO> merchantUserListDTOPage = new Page<>(merchantUserQueryDTO.getPageIndex(), merchantUserQueryDTO.getPageSize());

        if(StringUtils.isNotEmpty(merchantUserQueryDTO.getKeyword())){
            // 存在个关键字搜索的时候 屏蔽其他条件的过滤
            merchantUserQueryDTO.setGroupTagIdList(Collections.emptyList());
            merchantUserQueryDTO.setSexTagIdList(Collections.emptyList());
            merchantUserQueryDTO.setEquipmentTypeTagIdList(Collections.emptyList());
            merchantUserQueryDTO.setOtherTagIdList(Collections.emptyList());
            merchantUserQueryDTO.setTotalConsumeMoneyMin(null);
            merchantUserQueryDTO.setTotalConsumeMoneyMax(null);
            merchantUserQueryDTO.setTotalCoinMin(null);
            merchantUserQueryDTO.setTotalCoinMax(null);
            merchantUserQueryDTO.setTotalBalanceMin(null);
            merchantUserQueryDTO.setTotalBalanceMax(null);
        }
        long startTime = System.currentTimeMillis();
        // 非多标签才能进行统计
        buildMerchantUserListPage(merchantUserQueryDTO, merchantUserListDTOPage);
        listMerchantUser(merchantUserQueryDTO, merchantUserListDTOPage);
        if (log.isDebugEnabled()) {
            log.debug("查询耗时毫秒数：{}", (System.currentTimeMillis() - startTime));
        }

        if (UserMemberSysConstants.YES.equals(optimizeFlag)) {
            if (!CollectionUtils.isEmpty(merchantUserListDTOPage.getRecords())) {
                List<Long> merchantUserIds = merchantUserListDTOPage.getRecords().stream()
                        .map(MerchantUserListDTO::getId).collect(Collectors.toList());
                //查询普通标签
                List<Integer> tagBusinessList = Arrays.asList(TagBusinessTypeEnum.NORMAL.getStatus(),
                        TagBusinessTypeEnum.SEX.getStatus(), TagBusinessTypeEnum.PAY_TYPE.getStatus(),
                        TagBusinessTypeEnum.USER_SOURCE.getStatus());
                List<MerchantUserTagDTO> tagUserList = tagUserMapper.selectNewListByMerchantUser(merchantUserIds,
                        merchantUserQueryDTO.getMerchantId(), UserMemberSysConstants.TAG_MERCHANT_USER, tagBusinessList);
                List<Integer> classifies = new ArrayList<>(BenefitClassifyGroupEnum.COINS.getClassify());
                classifies.addAll(BenefitClassifyGroupEnum.MONEY.getClassify());
                //查询用户账户统计数据
                List<Account> list = accountMapper.findAccountByMerchantUserIds(merchantUserQueryDTO.getMerchantId(), merchantUserIds, classifies);
                if (!CollectionUtils.isEmpty(tagUserList) || !CollectionUtils.isEmpty(list)) {
                    Map<Long, List<MerchantUserTagDTO>> map = null;
                    if(!CollectionUtils.isEmpty(tagUserList)) {
                        map = tagUserList.stream().collect(Collectors.groupingBy(MerchantUserTagDTO::getMerchantUserId));
                    }
                    Map<Long, List<Account>> accountMap = null;
                    if(!CollectionUtils.isEmpty(list)) {
                        accountMap = list.stream().collect(Collectors.groupingBy(Account::getMerchantUserId));
                    }

                    Map<Long, List<MerchantUserTagDTO>> finalMap = map;
                    Map<Long, List<Account>> finalAccountMap = accountMap;
                    merchantUserListDTOPage.getRecords().forEach(merchantUserListDTO -> {
                        //标签处理
                        if (finalMap != null && finalMap.containsKey(merchantUserListDTO.getId())) {
                            List<TagUserDTO> tagUserDTOList = finalMap.get(merchantUserListDTO.getId())
                                    .stream().map(merchantUserTagDTO -> {
                                        TagUserDTO tagUserDTO = new TagUserDTO();
                                        BeanUtils.copyProperties(merchantUserTagDTO, tagUserDTO);
                                        return tagUserDTO;
                                    }).collect(Collectors.toList());
                            merchantUserListDTO.setOtherTagList(tagUserDTOList);
                        }
                        //用户余额处理
                        if (finalAccountMap != null && finalAccountMap.containsKey(merchantUserListDTO.getId())) {
                            List<Account> accounts = finalAccountMap.get(merchantUserListDTO.getId());
                            BigDecimal balance = BigDecimal.ZERO;
                            BigDecimal coins = BigDecimal.ZERO;
                            for (Account account : accounts) {
                                if (BenefitClassifyGroupEnum.COINS.getClassify().contains(account.getClassify())) {
                                    coins = coins.add(account.getBalance());
                                } else if (BenefitClassifyGroupEnum.MONEY.getClassify().contains(account.getClassify())) {
                                    balance = balance.add(account.getBalance());
                                }
                            }
                            merchantUserListDTO.setTotalBalance(balance);
                            merchantUserListDTO.setTotalCoin(coins);
                        }
                    });
                }
            }
        } else {
            merchantUserListDTOPage.getRecords().forEach(merchantUserListDTO -> {
                // 查询普通标签
                List<Integer> tagBusinessList  = Arrays.asList(TagBusinessTypeEnum.NORMAL.getStatus(),TagBusinessTypeEnum.SEX.getStatus(),TagBusinessTypeEnum.PAY_TYPE.getStatus());
                List<TagUser> tagUserList = tagUserMapper.selectListByMerchantUser(merchantUserListDTO.getId(), merchantUserQueryDTO.getMerchantId(), UserMemberSysConstants.TAG_MERCHANT_USER, tagBusinessList);
                List<TagUserDTO> tagUserDTOList = tagUserList.stream().map(tagUser -> {
                    TagUserDTO tagUserDTO = new TagUserDTO();
                    BeanUtils.copyProperties(tagUser, tagUserDTO);
                    return tagUserDTO;
                }).collect(Collectors.toList());
                merchantUserListDTO.setOtherTagList(tagUserDTOList);
            });
        }

        if (log.isDebugEnabled()) {
            log.debug("最终耗时毫秒数：{}",(System.currentTimeMillis() - startTime));
        }
        return merchantUserListDTOPage;
    }

    @Override
    public List<MerchantUserListDTO> listMerchantUser(MerchantUserQueryDTO merchantUserQueryDTO, Page<MerchantUserListDTO> merchantUserListDTOPage) {
        Long numericKeyword = null;
        String strKeyWord = null;
        String keyWord = merchantUserQueryDTO.getKeyword();
        Integer keywordType = merchantUserQueryDTO.getKeywordType();
        boolean isNum = true;
        try {
            numericKeyword = Objects.isNull(merchantUserQueryDTO.getKeyword())?null:Long.parseLong(merchantUserQueryDTO.getKeyword());
        }catch (Exception ex){
            isNum = false;
            strKeyWord = merchantUserQueryDTO.getKeyword();
        }
        if(!isNum && Objects.nonNull(keywordType) && keywordType==2){
            return null;
        }
        boolean hasTag = false;

        merchantUserQueryDTO.setGroupTagIdList(Optional.ofNullable(merchantUserQueryDTO.getGroupTagIdList()).orElse(Collections.emptyList()));
        merchantUserQueryDTO.setEquipmentTypeTagIdList(Optional.ofNullable(merchantUserQueryDTO.getEquipmentTypeTagIdList()).orElse(Collections.emptyList()));
        merchantUserQueryDTO.setOtherTagIdList(Optional.ofNullable(merchantUserQueryDTO.getOtherTagIdList()).orElse(Collections.emptyList()));
        merchantUserQueryDTO.setSexTagIdList(Optional.ofNullable(merchantUserQueryDTO.getSexTagIdList()).orElse(Collections.emptyList()));

        Long[] groupTagIds =  merchantUserQueryDTO.getGroupTagIdList().toArray(new Long[merchantUserQueryDTO.getGroupTagIdList().size()]);
        Long[] equipmentTypeTagIds = merchantUserQueryDTO.getEquipmentTypeTagIdList().toArray(new Long[merchantUserQueryDTO.getEquipmentTypeTagIdList().size()]);
        Long[] otherTagIds =  merchantUserQueryDTO.getOtherTagIdList().toArray(new Long[merchantUserQueryDTO.getOtherTagIdList().size()]);
        Long[] sexTagIds = merchantUserQueryDTO.getSexTagIdList().toArray(new Long[merchantUserQueryDTO.getSexTagIdList().size()]);
        if (groupTagIds.length > 0 || equipmentTypeTagIds.length > 0 || otherTagIds.length > 0 || sexTagIds.length > 0) {
            hasTag = true;
        }

        List<OrderItem> orderItems = merchantUserListDTOPage.getOrders();
        //避免插件对自定义排序造成影响
        merchantUserListDTOPage.setOrders(null);

        IPage<MerchantUserListDTO> pageResult = null;
        List<MerchantUserListDTO> merchantUserList = null;
        if (isNoQueryConditions(merchantUserQueryDTO)) {
            if (log.isDebugEnabled()) {
                log.debug("{} 商户查询用户列表没有限制条件", merchantUserQueryDTO.getMerchantId());
            }
            // 无条件查询 um_statistics 数据,然后做数据组装
            pageResult = merchantUserMapper.selectNoQueryConditionsList(merchantUserListDTOPage, merchantUserQueryDTO.getMerchantId(),
                    orderItems, merchantUserListDTOPage.offset(), merchantUserListDTOPage.getSize());
            merchantUserList = pageResult.getRecords();
            if (CollectionUtil.isNotEmpty(merchantUserList)) {
                //数据补充
                List<Long> merchantUserIds = merchantUserList.stream().map(MerchantUserListDTO::getId).collect(Collectors.toList());
                List<MerchantUser> merchantUsers = merchantUserMapper.selectByIds(merchantUserIds, merchantUserQueryDTO.getMerchantId());
                Map<Long, MerchantUser> map = merchantUsers.stream().collect(Collectors.toMap(MerchantUser::getId, Function.identity()));
                for (MerchantUserListDTO dto : merchantUserList) {
                    if (map.containsKey(dto.getId())) {
                        MerchantUser merchantUser = map.get(dto.getId());
                        dto.setGender(merchantUser.getGender());
                        dto.setTelephone(merchantUser.getTelephone());
                        dto.setHeadImg(merchantUser.getHeadImg());
                        dto.setNickName(merchantUser.getName());
                    }
                }
                pageResult.setRecords(merchantUserList);
            }
        } else {
            pageResult = merchantUserMapper.selectUserList(merchantUserListDTOPage, numericKeyword, strKeyWord,
                    merchantUserQueryDTO.getMerchantId(), groupTagIds, equipmentTypeTagIds, otherTagIds, sexTagIds, hasTag,
                    merchantUserQueryDTO.getTotalConsumeMoneyMin(), merchantUserQueryDTO.getTotalConsumeMoneyMax(),
                    merchantUserQueryDTO.getTotalBalanceMin(), merchantUserQueryDTO.getTotalBalanceMax(),
                    merchantUserQueryDTO.getTotalCoinMin(), merchantUserQueryDTO.getTotalCoinMax(), orderItems,
                    merchantUserListDTOPage.offset(), merchantUserListDTOPage.getSize(),keyWord,merchantUserQueryDTO.getKeywordType());
            merchantUserList = pageResult.getRecords();
        }
        return merchantUserList;
    }

    private void buildMerchantUserListPage(MerchantUserQueryDTO merchantUserQueryDTO, Page<MerchantUserListDTO> merchantUserListDTOPage) {
        // 处理排序规则
        List<OrderItem> orderItemList = new ArrayList<>();
        String orderAsc = "asc";
        if (StringUtils.isNotEmpty(merchantUserQueryDTO.getLastConsumeTimeSort())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn("last_consume_time");
            if(orderAsc.equalsIgnoreCase(merchantUserQueryDTO.getLastConsumeTimeSort())) {
                orderItem.setAsc(true);
            }else{
                orderItem.setAsc(false);
            }
            orderItemList.add(orderItem);
        } else if (StringUtils.isNotEmpty(merchantUserQueryDTO.getTotalConsumeMoneySort())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn("total_pay_consume");
            orderItem.setAsc(orderAsc.equalsIgnoreCase(merchantUserQueryDTO.getTotalConsumeMoneySort()));
            orderItemList.add(orderItem);
        } else if (StringUtils.isNotEmpty(merchantUserQueryDTO.getTotalBalanceSort())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn("us.balance_amount");
            orderItem.setAsc(orderAsc.equalsIgnoreCase(merchantUserQueryDTO.getTotalBalanceSort()));
            orderItemList.add(orderItem);
        } else if (StringUtils.isNotEmpty(merchantUserQueryDTO.getTotalCoinSort())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn("us.balance_coins");
            orderItem.setAsc(orderAsc.equalsIgnoreCase(merchantUserQueryDTO.getTotalCoinSort()));
            orderItemList.add(orderItem);
        }
        merchantUserListDTOPage.setOrders(orderItemList);
        merchantUserListDTOPage.setSearchCount(false);

    }

    //@Async
    @Override
    public void payoutWelfare(PayoutBenefitDTO payoutWelfareDTO) {
        String numericKeyword = null;
        String strKeyWord = null;
        if(StringUtils.isNumeric(payoutWelfareDTO.getKeyword())){
            numericKeyword = payoutWelfareDTO.getKeyword();
        }else{
            strKeyWord = payoutWelfareDTO.getKeyword();
        }
        List<Long> tagIdList  = new ArrayList<>();
        AtomicInteger tagTypeNum = new AtomicInteger();
        Optional.ofNullable(payoutWelfareDTO.getGroupTagIdList()).ifPresent(r -> {
            if(r.size()>0) {
                tagIdList.addAll(r);
                tagTypeNum.getAndIncrement();
            }
        });
        Optional.ofNullable(payoutWelfareDTO.getEquipmentTypeTagIdList()).ifPresent(r -> {
            if(r.size()>0) {
                tagIdList.addAll(r);
                tagTypeNum.getAndIncrement();
            }
        });
        Optional.ofNullable(payoutWelfareDTO.getOtherTagIdList()).ifPresent(r -> {
            if(r.size()>0) {
                tagIdList.addAll(r);
                tagTypeNum.getAndIncrement();
            }
        });
        Optional.ofNullable(payoutWelfareDTO.getSexTagIdList()).ifPresent(r -> {
            if(r.size() > 0) {
                tagIdList.addAll(r);
                tagTypeNum.getAndIncrement();
            }
        });

        if(Boolean.TRUE.equals(payoutWelfareDTO.getIsAllUser())){
            // 商户用户通过查询条件查出
            Long count = merchantUserMapper.countUserListByMerchantOrder(numericKeyword,strKeyWord, payoutWelfareDTO.getMerchantId(), tagIdList,tagTypeNum.get(),
                    payoutWelfareDTO.getTotalConsumeMoneyMin(),
                    payoutWelfareDTO.getTotalConsumeMoneyMax(),
                    payoutWelfareDTO.getTotalBalanceMin(),
                    payoutWelfareDTO.getTotalBalanceMax(),
                    payoutWelfareDTO.getTotalCoinMin(),
                    payoutWelfareDTO.getTotalCoinMax());
            log.info("查询用户总数，count:{}", count);
            if(count == 0){
                return;
            }
            if(count > singleHandlerNum){
                // 分批次处理
                long batchNum = (count % singleHandlerNum) + 1 ;
                for(int i= 0;i< batchNum;i++){
                    Long  start = Long.valueOf(i * singleHandlerNum);
                    List<MerchantUserListDTO> merchantUserListDTOList = merchantUserMapper.selectUserListByMerchantOrder(numericKeyword,strKeyWord, payoutWelfareDTO.getMerchantId(), tagIdList,tagTypeNum.get(),
                            payoutWelfareDTO.getTotalConsumeMoneyMin(),
                            payoutWelfareDTO.getTotalConsumeMoneyMax(),
                            payoutWelfareDTO.getTotalBalanceMin(),
                            payoutWelfareDTO.getTotalBalanceMax(),
                            payoutWelfareDTO.getTotalCoinMin(),
                            payoutWelfareDTO.getTotalCoinMax(),
                            null,
                            start,
                            singleHandlerNum);
                    List<Long> merchantUserIdList =  merchantUserListDTOList.stream().map(MerchantUserListDTO::getId).collect(Collectors.toList());
                    ofNullable(payoutWelfareDTO.getExceptMerchantUserIdList()).ifPresent(r -> {
                        if(r.size() > 0){
                            merchantUserIdList.removeAll(r);
                        }
                    });
                    payoutHandler(payoutWelfareDTO,merchantUserIdList);
                }

            }else{
                List<MerchantUserListDTO> merchantUserListDTOList = merchantUserMapper.selectUserListByMerchantOrder(numericKeyword,strKeyWord, payoutWelfareDTO.getMerchantId(), tagIdList,tagTypeNum.get(),
                        payoutWelfareDTO.getTotalConsumeMoneyMin(),
                        payoutWelfareDTO.getTotalConsumeMoneyMax(),
                        payoutWelfareDTO.getTotalBalanceMin(),
                        payoutWelfareDTO.getTotalBalanceMax(),
                        payoutWelfareDTO.getTotalCoinMin(),
                        payoutWelfareDTO.getTotalCoinMax(),
                        null,
                        0L,
                        singleHandlerNum);
                List<Long> merchantUserIdList =  merchantUserListDTOList.stream().map(MerchantUserListDTO::getId).collect(Collectors.toList());
                ofNullable(payoutWelfareDTO.getExceptMerchantUserIdList()).ifPresent(r -> {
                    if(r.size() > 0){
                        merchantUserIdList.removeAll(r);
                    }
                });
                payoutHandler(payoutWelfareDTO,merchantUserIdList);
            }
        }else {
            // 对选择的商户用户处理
            Assert.notNull(payoutWelfareDTO.getMerchantUserIdList(),"商户选择的商户用户不能为空");
            payoutHandler(payoutWelfareDTO,payoutWelfareDTO.getMerchantUserIdList());
        }
    }

    @Override
    public Long countUserListByMerchant(MerchantUserQueryDTO merchantUserQueryDTO) {
        Long count;
        //初始查询没有限制条件，只查询merchant_user的数据，多表join会因为表数据量过多无法进行查询
        if (isNoQueryConditions(merchantUserQueryDTO)) {
            if (log.isDebugEnabled()) {
                log.debug("{}商户查询用户列表没有限制条件", merchantUserQueryDTO.getMerchantId());
            }
            count = merchantUserMapper.countByMerchantId(merchantUserQueryDTO.getMerchantId());
        } else {
            String numericKeyword = null;
            String strKeyWord = null;
            if (StringUtils.isNumeric(merchantUserQueryDTO.getKeyword())) {
                numericKeyword = merchantUserQueryDTO.getKeyword();
            } else {
                strKeyWord = merchantUserQueryDTO.getKeyword();
            }

            boolean hasTag = false;

            merchantUserQueryDTO.setGroupTagIdList(Optional.ofNullable(merchantUserQueryDTO.getGroupTagIdList()).orElse(Collections.emptyList()));
            merchantUserQueryDTO.setEquipmentTypeTagIdList(Optional.ofNullable(merchantUserQueryDTO.getEquipmentTypeTagIdList()).orElse(Collections.emptyList()));
            merchantUserQueryDTO.setOtherTagIdList(Optional.ofNullable(merchantUserQueryDTO.getOtherTagIdList()).orElse(Collections.emptyList()));
            merchantUserQueryDTO.setSexTagIdList(Optional.ofNullable(merchantUserQueryDTO.getSexTagIdList()).orElse(Collections.emptyList()));

            Long[] groupTagIds = merchantUserQueryDTO.getGroupTagIdList().toArray(new Long[merchantUserQueryDTO.getGroupTagIdList().size()]);
            Long[] equipmentTypeTagIds = merchantUserQueryDTO.getEquipmentTypeTagIdList().toArray(new Long[merchantUserQueryDTO.getEquipmentTypeTagIdList().size()]);
            Long[] otherTagIds = merchantUserQueryDTO.getOtherTagIdList().toArray(new Long[merchantUserQueryDTO.getOtherTagIdList().size()]);
            Long[] sexTagIds = merchantUserQueryDTO.getSexTagIdList().toArray(new Long[merchantUserQueryDTO.getSexTagIdList().size()]);
            if (groupTagIds.length > 0) {
                hasTag = true;
            } else if (equipmentTypeTagIds.length > 0) {
                hasTag = true;
            } else if (otherTagIds.length > 0) {
                hasTag = true;
            } else if (sexTagIds.length > 0) {
                hasTag = true;
            }

            //非多标签才能进行统计
            count = merchantUserMapper.countUserList(numericKeyword, strKeyWord, merchantUserQueryDTO.getMerchantId(),
                    groupTagIds,
                    equipmentTypeTagIds,
                    otherTagIds,
                    sexTagIds,
                    hasTag,
                    merchantUserQueryDTO.getTotalConsumeMoneyMin(),
                    merchantUserQueryDTO.getTotalConsumeMoneyMax(),
                    merchantUserQueryDTO.getTotalBalanceMin(),
                    merchantUserQueryDTO.getTotalBalanceMax(),
                    merchantUserQueryDTO.getTotalCoinMin(),
                    merchantUserQueryDTO.getTotalCoinMax());
        }
        log.info("商户:{},用户count总数：{}", merchantUserQueryDTO.getMerchantId(), count);
        return count;
    }

    @Override
    public MerchantUserInfoByKeywordDTO searchByKeywords(Long merchantId, String keywords, Integer keyWordsType) {
        List<MerchantUserInfoAndAccountDTO> merchantUserInfoAndAccountDTOList = getMerchantUserAndLevelInfoDTO(merchantId, keywords, keyWordsType);
        if (CollectionUtils.isEmpty(merchantUserInfoAndAccountDTOList)) {
            return null;
        }

        //设置用户数据
        MerchantUserInfoAndAccountDTO merchantUserInfoAndAccountDTO = merchantUserInfoAndAccountDTOList.get(0);
        MerchantUserAndLevelInfoDTO merchantUserAndLevelInfoDTO = new MerchantUserAndLevelInfoDTO();
        BeanUtils.copyProperties(merchantUserInfoAndAccountDTO, merchantUserAndLevelInfoDTO);

        //设置会员卡信息,先根据accountId分组
        Map<Long, List<MerchantUserInfoAndAccountDTO>> userInfoAndAccountDTOMap = merchantUserInfoAndAccountDTOList.stream()
                .filter(merchantUserInfoAndAccount -> merchantUserInfoAndAccount.getAccountId() != null)
                .collect(Collectors.groupingBy(MerchantUserInfoAndAccountDTO::getAccountId));
        Set<Map.Entry<Long, List<MerchantUserInfoAndAccountDTO>>> entrySet = userInfoAndAccountDTOMap.entrySet();
        //会员卡信息列表数据
        List<MemberCardInfoDTO> memberCardInfoDTOList = new ArrayList<>();
        for (Map.Entry<Long, List<MerchantUserInfoAndAccountDTO>> entry : entrySet) {

            List<MerchantUserInfoAndAccountDTO> accountDTOS = entry.getValue();
            if (CollectionUtils.isNotEmpty(accountDTOS)) {
                //复制会员卡数据
                MerchantUserInfoAndAccountDTO infoAndAccountDTO = accountDTOS.get(0);
                MemberCardInfoDTO memberCardInfoDTO = new MemberCardInfoDTO();
                BeanUtils.copyProperties(infoAndAccountDTO, memberCardInfoDTO);
                //设置权益列表信息
                List<MemberCardInfoBenefitDTO> memberCardInfoBenefitDTOList = accountDTOS.stream()
                        .filter(accountDTO -> accountDTO.getMerchantBenefitClassifyId() != null)
                        .map(accountDTO -> new MemberCardInfoBenefitDTO()
                                .setUseRuleId(accountDTO.getUseRuleId())
                                .setBalance(accountDTO.getBalance())
                                .setUpTime(accountDTO.getBenefitUpTime())
                                .setDownTime(accountDTO.getBenefitDownTime())
                                .setStoreId(accountDTO.getBenefitStoreId())
                                .setNum(ofNullable(accountDTO.getBalance()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0 ? 1 : 0)
                                .setMerchantBenefitClassifyId(accountDTO.getMerchantBenefitClassifyId()))
                        .collect(Collectors.toList());

                //相同的MerchantBenefitClassifyId需要算Balance的和
                memberCardInfoDTO.setBenefit(memberCardInfoBenefitDTOList);
                memberCardInfoDTOList.add(memberCardInfoDTO);
            }
        }

        //找出附属卡
        Map<Long, List<MemberCardInfoDTO>> memberCardInfoDTOMap = memberCardInfoDTOList.stream()
                .filter(memberCardInfoDTO -> memberCardInfoDTO.getParentAccountId() != null && !AccountStatusEnum.CANCELLATION.getStatus().equals(memberCardInfoDTO.getStatus()))
                .collect(Collectors.groupingBy(MemberCardInfoDTO::getParentAccountId));
        //过滤已注销的卡
        memberCardInfoDTOList = memberCardInfoDTOList.stream()
                .filter(memberCardInfoDTO -> !AccountStatusEnum.CANCELLATION.getStatus().equals(memberCardInfoDTO.getStatus()))
                .map(memberCardInfoDTO -> {
                    //获取附属卡集合
                    List<MemberCardInfoDTO> memberCardInfoDTOS = memberCardInfoDTOMap.get(memberCardInfoDTO.getAccountId());
                    if (!CollectionUtils.isEmpty(memberCardInfoDTOS)) {
                        //对象转换
                        List<SupplementCardsDTO> supplementCardsDTOList = memberCardInfoDTOS.stream().map(memberCardInfo -> {
                            SupplementCardsDTO supplementCardsDTO = new SupplementCardsDTO();
                            BeanUtils.copyProperties(memberCardInfo, supplementCardsDTO);
                            return supplementCardsDTO;
                        }).collect(Collectors.toList());
                        memberCardInfoDTO.setSupplementCards(supplementCardsDTOList);
                    }
                    return memberCardInfoDTO;
                }).collect(Collectors.toList());

        //是否实名状态
        boolean realNameStatus = StringUtils.isNotBlank(merchantUserInfoAndAccountDTO.getTelephone());

        //用户标签信息
        CompletableFuture<Void> tagsCf = CompletableFuture.runAsync(() -> {
            assembleTagsInfo(merchantUserAndLevelInfoDTO, merchantId, merchantUserInfoAndAccountDTO.getMerchantUserId());
        }, selectMemberInfoTaskExecutor);

        //会员等级相关信息
        SmallVenueMemberLevelInfoDTO smallVenueMemberLevelInfoDTO = new SmallVenueMemberLevelInfoDTO();
        CompletableFuture<Void> memberLevelCf = CompletableFuture.runAsync(() -> {
            assembleMemberLevelInfo(smallVenueMemberLevelInfoDTO, merchantId, merchantUserInfoAndAccountDTO.getUserId());
        }, selectMemberInfoTaskExecutor);

        //用户统计信息
        MerchantUserStatisticsDTO merchantUserStatisticsDTO = new MerchantUserStatisticsDTO();
        CompletableFuture<Void> merchantUserStatisticsCf = CompletableFuture.runAsync(() -> {
            assembleMerchantUserStatisticsInfo(merchantUserStatisticsDTO, merchantId, merchantUserInfoAndAccountDTO.getUserId(), merchantUserInfoAndAccountDTO.getMerchantUserId());
        }, selectMemberInfoTaskExecutor);

        CompletableFuture.allOf(tagsCf, memberLevelCf, merchantUserStatisticsCf).join();

        log.info("return userStatistics:{}", merchantUserStatisticsDTO);

        //返回数据
        return new MerchantUserInfoByKeywordDTO()
                .setUserInfo(merchantUserAndLevelInfoDTO)
                .setMemberLevelInfo(smallVenueMemberLevelInfoDTO)
                .setMemberCardInfos(memberCardInfoDTOList)
                .setRealNameStatus(realNameStatus)
                .setUserStatistics(merchantUserStatisticsDTO);
    }

    @Override
    public Boolean updateMerchantUserPassword(MerchantUserUpdatePasswordDTO merchantUserUpdatePasswordDTO) {
        Long merchantId = merchantUserUpdatePasswordDTO.getMerchantId();
        String oldPassWord = merchantUserUpdatePasswordDTO.getOldPassWord();
        String newPassWord = merchantUserUpdatePasswordDTO.getNewPassWord();
        Long userId = merchantUserUpdatePasswordDTO.getUserId();
        //校验旧密码
        if (StringUtils.isNotBlank(oldPassWord)) {
            String userPassword = merchantUserMapper.selectMerchantUserPassword(merchantId, userId);
            //校验失败
            if (StringUtils.isBlank(userPassword) || (!userPassword.equals(oldPassWord))) {
                return false;
            }
        }
        return merchantUserMapper.updateMerchantUserPassword(merchantId, userId, newPassWord) > 0;
    }

    @Override
    public Boolean checkMerchantUserPassword(MerchantUserCheckPasswordDTO merchantUserCheckPasswordDTO) {
        Long merchantId = merchantUserCheckPasswordDTO.getMerchantId();
        Long userId = merchantUserCheckPasswordDTO.getUserId();
        String passWord = merchantUserCheckPasswordDTO.getPassWord();
        String oldPassword = merchantUserMapper.selectMerchantUserPassword(merchantId, userId);
        //校验失败
        if (StringUtils.isBlank(oldPassword) || (!oldPassword.equals(passWord))) {
            return false;
        }
        return true;
    }

    @Override
    public DataList<SmallVenueUserDTO> selectSmallVenueUserList(SmallVenueUserListSelectDTO smallVenueUserListSelectDTO) {
        Integer pageIndex = smallVenueUserListSelectDTO.getPageIndex();
        Integer pageSize = smallVenueUserListSelectDTO.getPageSize();
        Page<SmallVenueUserDTO> page = new Page<>(pageIndex, pageSize);
        List<Long> tagIdList = ofNullable(smallVenueUserListSelectDTO.getTagIds()).orElse(Collections.emptyList());
        Long[] tagIds = tagIdList.toArray(new Long[tagIdList.size()]);
        //查询总数
        page.setSearchCount(false);

        IPage<SmallVenueUserDTO> smallVenueUserDTOIPage = merchantUserMapper.selectSmallVenueUserList(page, smallVenueUserListSelectDTO,tagIds);
        List<SmallVenueUserDTO> records = smallVenueUserDTOIPage.getRecords();
        // 查询会员消费信息
        if (!CollectionUtils.isEmpty(records)) {
            List<Long> userMemberIdList = records.stream().map(SmallVenueUserDTO::getId).collect(Collectors.toList());
            List<MerchantUserConsumeStatisticsDTO> merchantUserConsumeStatisticsDTOS = statisticsMapper.querySmallVenueStatisticsList(smallVenueUserListSelectDTO.getMerchantId(), userMemberIdList);
            Map<Long, MerchantUserConsumeStatisticsDTO> merchantUserConsumeStatisticsDTOMap = merchantUserConsumeStatisticsDTOS.stream().collect(Collectors.toMap(MerchantUserConsumeStatisticsDTO::getMerchantUserId, Function.identity(), (v1, v2) -> v2));
            records.forEach(r -> {
                r.setAmountConsumption(merchantUserConsumeStatisticsDTOMap.getOrDefault(r.getId(), new MerchantUserConsumeStatisticsDTO()).getAmountConsumption());
                r.setRecentConsumptionTime(merchantUserConsumeStatisticsDTOMap.getOrDefault(r.getId(), new MerchantUserConsumeStatisticsDTO()).getRecentConsumptionTime());
            });
        }
        //获取所有会员等级id
        return new DataList<>(smallVenueUserDTOIPage.getTotal(), records, smallVenueUserDTOIPage.getPages());
    }

    @Override
    public DataList<SmallVenueUserMobileVO> smallVenueMobileUserList(SmallVenueMobileUserListSelectDTO dto) {
        Page<SmallVenueUserMobileVO> page = new Page<>(dto.getPageIndex(), dto.getPageSize());

        // 排序
//        buildSmallVenueMobileListPage(dto, page);

        if (StringUtils.isBlank(dto.getKeyword())) {
            dto.setKeyword(null);
        }
        boolean hasTag = false;
        dto.setGroupTagIdList(Optional.ofNullable(dto.getGroupTagIdList()).orElse(Collections.emptyList()));
        dto.setEquipmentTypeTagIdList(Optional.ofNullable(dto.getEquipmentTypeTagIdList()).orElse(Collections.emptyList()));
        dto.setOtherTagIdList(Optional.ofNullable(dto.getOtherTagIdList()).orElse(Collections.emptyList()));
        dto.setSexTagIdList(Optional.ofNullable(dto.getSexTagIdList()).orElse(Collections.emptyList()));

        Long[] groupTagIds =  dto.getGroupTagIdList().toArray(new Long[dto.getGroupTagIdList().size()]);
        Long[] equipmentTypeTagIds = dto.getEquipmentTypeTagIdList().toArray(new Long[dto.getEquipmentTypeTagIdList().size()]);
        Long[] otherTagIds =  dto.getOtherTagIdList().toArray(new Long[dto.getOtherTagIdList().size()]);
        Long[] sexTagIds = dto.getSexTagIdList().toArray(new Long[dto.getSexTagIdList().size()]);
        if(groupTagIds.length > 0){
            hasTag = true;
        }else if(equipmentTypeTagIds.length > 0){
            hasTag = true;
        }else if(otherTagIds.length > 0){
            hasTag = true;
        }else if(sexTagIds.length > 0){
            hasTag = true;
        }


        long  startTime = System.currentTimeMillis();
        // TODO: 2022/6/22  (会员卡数 区间 ) 筛选 todo 未实现
        page.setSearchCount(false);
        List<OrderItem> orderItems = page.getOrders();
        //避免插件对自定义排序造成影响
        page.setOrders(null);

        IPage<SmallVenueUserMobileVO> smallVenueUserDTOIPage = new Page<>();
        List<MerchantUserConsumeStatisticsDTO> merchantUserConsumeStatisticsDTOS = new ArrayList<>();
        // 根据会员储值消费时间排序
        if (StringUtils.isNotBlank(dto.getKeyword()) && !UserMemberSysConstants.SMALL_VENUE_SEARCH_TYPE_CARDNO.equals(dto.getKeywordType())) {
            if(UserMemberSysConstants.SMALL_VENUE_SEARCH_TYPE_USERID.equals(dto.getKeywordType())) {
                try {
                    dto.setUserId(Long.valueOf(dto.getKeyword()));
                } catch (NumberFormatException e) {
                    log.info("数字类型类型转换异常:{}", e.getMessage());
                    return new DataList<>();
                }
            }
            // 根据标签/会员基本信息查询会员列表
            smallVenueUserDTOIPage = merchantUserMapper.smallVenueMobileUserList(page, dto.getKeywordType(),dto,hasTag,groupTagIds,equipmentTypeTagIds,otherTagIds,sexTagIds,orderItems,null);
        } else if (StringUtils.isNotBlank(dto.getKeyword()) && UserMemberSysConstants.SMALL_VENUE_SEARCH_TYPE_CARDNO.equals(dto.getKeywordType())) {
            // 根据卡号查询会员列表，先去重查出 merchantUserIdList，再查询会员信息
            IPage<Long> smallVenueMerchantUserIdIPage = accountMapper.selectDistinctMerchantUserIdByMerchantIdAndCardNo(page, dto.getMerchantId(), dto.getKeyword());
            List<Long> smallVenueMerchantUserIdList = smallVenueMerchantUserIdIPage.getRecords();
            if (!CollectionUtils.isEmpty(smallVenueMerchantUserIdList)) {
                smallVenueUserDTOIPage = merchantUserMapper.smallVenueMobileUserList(page, dto.getKeywordType(),dto,hasTag,groupTagIds,equipmentTypeTagIds,otherTagIds,sexTagIds,orderItems, smallVenueMerchantUserIdList);
            }
        } else if (hasTag || (dto.getSpecificFilterInfoDTO() != null && CollectionUtils.isNotEmpty(dto.getSpecificFilterInfoDTO().getUserTypeList()))) {
            smallVenueUserDTOIPage = merchantUserMapper.smallVenueMobileUserList(page, dto.getKeywordType(),dto,hasTag,groupTagIds,equipmentTypeTagIds,otherTagIds,sexTagIds,orderItems,null);
        } else {
            // 无传参，会员列表根据会员储值消费时间倒序展示
            // 先查储值统计表，再查会员表
            // 会员数量 == 储值消费统计表数量
            IPage<MerchantUserConsumeStatisticsDTO> merchantUserConsumeStatisticsDTOIPage = statisticsMapper.queryPageSmallVenueStatisticsList(page,dto.getMerchantId());
            merchantUserConsumeStatisticsDTOS = merchantUserConsumeStatisticsDTOIPage.getRecords();
            if (CollectionUtils.isNotEmpty(merchantUserConsumeStatisticsDTOS)) {
                Map<Long, MerchantUserConsumeStatisticsDTO> orderByRecentConsumptionTimeMap = merchantUserConsumeStatisticsDTOS.stream().collect(Collectors.toMap(MerchantUserConsumeStatisticsDTO::getMerchantUserId, Function.identity(), (v1, v2) -> v1));

                List<SmallVenueUserMobileVO> smallVenueUserMobileVOS = merchantUserMapper.smallVenueMobileMemberUserList(dto, new ArrayList<>(orderByRecentConsumptionTimeMap.keySet()));
                smallVenueUserDTOIPage.setRecords(smallVenueUserMobileVOS);
                smallVenueUserDTOIPage.setPages(merchantUserConsumeStatisticsDTOIPage.getPages());
                smallVenueUserDTOIPage.setTotal(merchantUserConsumeStatisticsDTOIPage.getTotal());
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("查询耗时毫秒数：{}", (System.currentTimeMillis() - startTime));
        }

        List<SmallVenueUserMobileVO> records = smallVenueUserDTOIPage.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            //用户ID
            List<Long> merchantUserIds = records.stream().map(SmallVenueUserMobileVO::getId).collect(Collectors.toList());

            //补充消费信息
            List<MerchantUserConsumeStatisticsDTO> merchantUserConsumeStatisticsList = statisticsMapper.querySmallVenueStatisticsList(dto.getMerchantId(), merchantUserIds);
            Map<Long, MerchantUserConsumeStatisticsDTO> merchantUserConsumeStatisticsDTOMap = merchantUserConsumeStatisticsList.stream().collect(Collectors.toMap(MerchantUserConsumeStatisticsDTO::getMerchantUserId, Function.identity(), (v1, v2) -> v2));
            records.forEach(r -> {
                r.setAmountConsumption(merchantUserConsumeStatisticsDTOMap.getOrDefault(r.getId(), new MerchantUserConsumeStatisticsDTO()).getAmountConsumption());
                r.setRecentConsumptionTime(merchantUserConsumeStatisticsDTOMap.getOrDefault(r.getId(), new MerchantUserConsumeStatisticsDTO()).getRecentConsumptionTime());
            });
            // 按recentConsumptionTime字段倒序排序
            List<SmallVenueUserMobileVO> sortedList = records.stream()
                    .sorted(Comparator.comparing(SmallVenueUserMobileVO::getRecentConsumptionTime,Comparator.nullsLast(Comparator.reverseOrder())))
                    .collect(Collectors.toList());
            smallVenueUserDTOIPage.setRecords(sortedList);
            records = smallVenueUserDTOIPage.getRecords();

            //标签信息
            Map<Long, List<MerchantUserTagDTO>> tagInfoListMap = getTagInfoListMap(dto, merchantUserIds);

            //等级会员信息

            //会员卡数量（不包括附属卡数量，显示为标签样式）
            Map<Long, Integer> masterCardNumber  = getMasterCardNumberMap(merchantUserIds,dto.getMerchantId());


            //设置会员名称,也可连表查询
            records = records.stream().peek(smallVenueUserDTO -> {
                Long merchantUserId = smallVenueUserDTO.getId();
                //标签处理
                if (tagInfoListMap.containsKey(merchantUserId)) {
                    List<TagUserDTO> tagUserDTOList = tagInfoListMap.get(merchantUserId)
                            .stream().map(merchantUserTagDTO -> {
                                TagUserDTO tagUserDTO = new TagUserDTO();
                                tagUserDTO.setId(merchantUserTagDTO.getId());
                                tagUserDTO.setName(merchantUserTagDTO.getName());
                                tagUserDTO.setCode(merchantUserTagDTO.getCode());
                                tagUserDTO.setMerchantId(merchantUserTagDTO.getMerchantId());
                                tagUserDTO.setCategory(merchantUserTagDTO.getCategory());
                                tagUserDTO.setBusinessType(merchantUserTagDTO.getBusinessType());
                                tagUserDTO.setBusinessTypeName(Optional.ofNullable(TagBusinessTypeEnum.of(merchantUserTagDTO.getBusinessType())).map(TagBusinessTypeEnum::getDesc).orElse(null));
                                return tagUserDTO;
                            }).collect(Collectors.toList());
                    smallVenueUserDTO.setTagInfoList(tagUserDTOList);
                }
                //设置 等级会员 名称
                //会员卡数量（不包括附属卡数量)
                smallVenueUserDTO.setCardNum(masterCardNumber.getOrDefault(merchantUserId, 0));
            }).collect(Collectors.toList());
        }
        return new DataList<>(smallVenueUserDTOIPage.getTotal(), records, smallVenueUserDTOIPage.getPages());
    }

    /**
     * 会员卡数量（不包括附属卡数量，显示为标签样式）
     * @param merchantUserIds
     * @param merchantId
     * @return
     */
    private Map<Long, Integer> getMasterCardNumberMap(List<Long> merchantUserIds, Long merchantId) {

        /**
         * 会员卡状态 1=正常，2=禁用3=挂失，4=过期
         */
        List<Integer> cardStatus = Collections.singletonList(1);
        List<SmallVenueCountCardNoDTO> smallVenueCountCardNoDTOS = accountMapper.selectCountCardNumber(merchantId, merchantUserIds, null,false);
        log.debug("查询会员主卡数量:{}",smallVenueCountCardNoDTOS);
        return  smallVenueCountCardNoDTOS.stream()
                .collect(HashMap::new, (m, v) -> m.put(v.getMerchantUserId(), v.getCardNumber()), HashMap::putAll);
    }

    /**
     * 查询标签信息
     * @param smallVenueUserListSelectDTO
     * @param merchantUserIds
     * @return
     */
    private Map<Long, List<MerchantUserTagDTO>> getTagInfoListMap(SmallVenueMobileUserListSelectDTO smallVenueUserListSelectDTO, List<Long> merchantUserIds) {
        //查询普通标签、性别、支付
        List<Integer> tagBusinessList = Arrays.asList(TagBusinessTypeEnum.NORMAL.getStatus()
                ,  TagBusinessTypeEnum.SEX.getStatus()
                , TagBusinessTypeEnum.PAY_TYPE.getStatus()
                , TagBusinessTypeEnum.GROUP_NAME.getStatus()
                , TagBusinessTypeEnum.MEMBER.getStatus()
             );
        List<MerchantUserTagDTO> tagUserList = tagUserMapper.selectNewListByMerchantUser(merchantUserIds,
                smallVenueUserListSelectDTO.getMerchantId(), UserMemberSysConstants.TAG_MERCHANT_USER, tagBusinessList);
        if (!CollectionUtils.isEmpty(tagUserList)) {
            return tagUserList.stream().collect(Collectors.groupingBy(MerchantUserTagDTO::getMerchantUserId));
        }
        return new HashMap<>(2);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateMerchantUserInfo(UpdateMerchantUserInfoDTO updateDTO) {
        return merchantUserMapper.updateMerchantUserInfo(updateDTO);
    }


    /**
     * 更新会员手机号码
     * @param dto
     * @return
     */
    @Override
    public Boolean updateMerchantUserTelephone(MerchantUserUpdateTelephoneDTO dto) {
        boolean result = false;
        MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(dto.getUserId(), dto.getMerchantId());
        if (merchantUser != null) {
            result = merchantUserMapper.updateMerchantUserTelephone(dto.getMerchantId(), dto.getUserId(), dto.getNewTelephone(),
                    dto.getOldTelephone()) > 0;
            //修改手机号码保存记录
            if (result) {
                updateUserTelephone(merchantUser.getUserId(), dto.getNewTelephone());
                saveOrUpdatePlatformUserPhone(merchantUser.getUserId(), UserMemberSysConstants.PLATFORM_MERCHANT_ID, null,
                        UserMemberSysConstants.DEFAULT_OPERATION_USER_ID, dto.getNewTelephone(),
                        UserMemberSysConstants.SMALL_VENUE_SYSTEM_FLAG);

                saveOrUpdatePlatformUserPhone(merchantUser.getUserId(), merchantUser.getMerchantId(), merchantUser.getId(),
                        UserMemberSysConstants.DEFAULT_OPERATION_USER_ID, dto.getNewTelephone(),
                        UserMemberSysConstants.SMALL_VENUE_SYSTEM_FLAG);

                //更新商户的手机号码更新记录
                if (Objects.nonNull(merchantUser.getUserId())) {
                    removeCacheService.removeUserMerchantCache(merchantUser.getUserId(), merchantUser.getMerchantId());
                }
                removeCacheService.removeMerchantUserCache(merchantUser.getId(), merchantUser.getMerchantId());
            }
        }
        return result;
    }

    @Override
    public MerchantUserDTO getMerchantUserByCondition(MerchantIntegralUserQueryDTO queryDTO) {
        return merchantUserMapper.getByQuery(queryDTO);
    }

    @Override
    public Integer mobileTerminalMerchantUserCancellation(Long merchantId, Long merchantUserId, Long operatorId) {
        return merchantUserMapper.mobileTerminalMerchantUserCancellation(merchantId, merchantUserId, operatorId);
    }

    @Override
    public MerchantUserAndLevelInfoDTO searchBaseInfoKeywordsAndType(Long merchantId, String keywords, Integer keyWordsType) {
        List<MerchantUserInfoAndAccountDTO> merchantUserInfoAndAccountDTOList = getMerchantUserAndLevelInfoDTO(merchantId, keywords,
                keyWordsType);
        if (CollectionUtils.isNotEmpty(merchantUserInfoAndAccountDTOList)) {
            MerchantUserInfoAndAccountDTO merchantUserInfoAndAccountDTO = merchantUserInfoAndAccountDTOList.get(0);
            MerchantUserAndLevelInfoDTO merchantUserAndLevelInfoDTO = new MerchantUserAndLevelInfoDTO();
            BeanUtils.copyProperties(merchantUserInfoAndAccountDTO, merchantUserAndLevelInfoDTO);
            return merchantUserAndLevelInfoDTO;
        } else {
            return null;
        }
    }


    private List<MerchantUserInfoAndAccountDTO> getMerchantUserAndLevelInfoDTO(Long merchantId, String keywords, Integer keyWordsType) {
        String telephoneReg = "^1(3[0-9]|4[********]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\\d{8}$";
        String userReg = "^[0-9]*$";
        boolean isPhoneNum = ReUtil.isMatch(telephoneReg, keywords);
        boolean isUserId = ReUtil.isMatch(userReg, keywords);
        List<MerchantUserInfoAndAccountDTO> merchantUserInfoAndAccountDTOList;
        //查询会员和会员卡信息
        if (isPhoneNum || UserMemberSysConstants.SMALL_VENUE_SEARCH_TYPE_PHONENUM.equals(keyWordsType)) {
            merchantUserInfoAndAccountDTOList = merchantUserMapper.searchByPhone(merchantId, keywords);
        } else {
            Long userId = isUserId ? Long.valueOf(keywords) : null;
            merchantUserInfoAndAccountDTOList = merchantUserMapper.searchByKeywords(merchantId, keywords, keyWordsType, userId);
        }
        return merchantUserInfoAndAccountDTOList;
    }



    private void payoutHandler(PayoutBenefitDTO payoutWelfareDTO,List<Long> merchantUserIdList) {
        log.debug("payoutHandler处理用户数：{}",merchantUserIdList.size());
        List<Long> groupIdList = payoutWelfareDTO.getGroupIdList();
        if (payoutWelfareDTO.getPayoutBalance() != null && payoutWelfareDTO.getPayoutBalance().compareTo(BigDecimal.ZERO) > 0) {

            BenefitInfoDTO benefitInfoDTO = benefitService.generateGroupEquipmentTypeBenefit(payoutWelfareDTO.getMerchantId(), BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE,groupIdList,
                    payoutWelfareDTO.getEquipmentTypeIdList(), ExpiryDateCategoryEnum.NO_LIMIT.getValue(), null, null);
                    // 给用户账户 添加权益
            Assert.notNull(benefitInfoDTO, "场地权益不能为空");

            merchantUserIdList.forEach(merchantUserId ->{
                List<AccountCreateDTO> accountCreateDTOList = new ArrayList<>();
                AccountCreateDTO accountCreateDTO = new AccountCreateDTO();
                accountCreateDTO.setMerchantUserId(merchantUserId);
                accountCreateDTO.setMerchantId(payoutWelfareDTO.getMerchantId());
                accountCreateDTO.setClassify(BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE.getCode());
                accountCreateDTO.setResource("商家派送余额");
                accountCreateDTO.setAccountRecordTypeEnum(AccountRecordTypeEnum.GIFT_MERCHANT_GIVE_BALANCE);
                List<AccountBenefitCreateDTO> accountBenefitCreateDTOList = new ArrayList<>();
                AccountBenefitCreateDTO accountBenefitCreateDTO = new AccountBenefitCreateDTO();
                accountBenefitCreateDTO.setClassify(BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE.getCode());
                accountBenefitCreateDTO.setBenefitId(benefitInfoDTO.getId());
                accountBenefitCreateDTO.setTotal(payoutWelfareDTO.getPayoutBalance());
                // 商家赠送余额 暂不过期
                accountBenefitCreateDTO.setExpiryDateCategory(ExpiryDateCategoryEnum.NO_LIMIT.getValue());
                accountBenefitCreateDTOList.add(accountBenefitCreateDTO);
                accountCreateDTO.setAccountBenefit(accountBenefitCreateDTOList);
                accountCreateDTOList.add(accountCreateDTO);
                accountService.increaseAccountBenefit(accountCreateDTOList);

                // 处理统计信息
                UserStatisticsUpdateDTO userStatisticsUpdateDTO = new UserStatisticsUpdateDTO();
                userStatisticsUpdateDTO.setMerchantUserId(merchantUserId);
                userStatisticsUpdateDTO.setMerchantId(payoutWelfareDTO.getMerchantId());
                userStatisticsUpdateDTO.setBalanceAmount(payoutWelfareDTO.getPayoutBalance());
                userStatisticsUpdateDTO.setUpdateTime(new Date());
                statisticsService.updateStatistics(userStatisticsUpdateDTO);
            });
        }

        if (payoutWelfareDTO.getPayoutCoin() != null && payoutWelfareDTO.getPayoutCoin().compareTo(BigDecimal.ZERO) > 0) {
            // 处理余币赠送
            BenefitInfoDTO benefitInfoDTO = benefitService.generateGroupEquipmentTypeBenefit(payoutWelfareDTO.getMerchantId(), BenefitClassifyEnum.MERCHANT_PAYOUT_COIN,groupIdList,
                    payoutWelfareDTO.getEquipmentTypeIdList(),ExpiryDateCategoryEnum.NO_LIMIT.getValue(), null, null);
            // 给用户账户 添加权益
            Assert.notNull(benefitInfoDTO, "场地权益不能为空");
            merchantUserIdList.forEach(merchantUserId -> {
                List<AccountCreateDTO> accountCreateDTOList = new ArrayList<>();
                AccountCreateDTO accountCreateDTO = new AccountCreateDTO();
                accountCreateDTO.setMerchantUserId(merchantUserId);
                accountCreateDTO.setMerchantId(payoutWelfareDTO.getMerchantId());
                accountCreateDTO.setClassify(BenefitClassifyEnum.MERCHANT_PAYOUT_COIN.getCode());
                accountCreateDTO.setResource("商家派送余币");
                accountCreateDTO.setAccountRecordTypeEnum(AccountRecordTypeEnum.GIFT_MERCHANT_GIVE_COIN);
                List<AccountBenefitCreateDTO> accountBenefitCreateDTOList = new ArrayList<>();
                AccountBenefitCreateDTO accountBenefitCreateDTO = new AccountBenefitCreateDTO();
                accountBenefitCreateDTO.setClassify(BenefitClassifyEnum.MERCHANT_PAYOUT_COIN.getCode());
                accountBenefitCreateDTO.setBenefitId(benefitInfoDTO.getId());
                accountBenefitCreateDTO.setTotal(payoutWelfareDTO.getPayoutCoin());
                // 商家赠送余额 暂不过期
                accountBenefitCreateDTO.setExpiryDateCategory(ExpiryDateCategoryEnum.NO_LIMIT.getValue());
                accountBenefitCreateDTOList.add(accountBenefitCreateDTO);
                accountCreateDTO.setAccountBenefit(accountBenefitCreateDTOList);
                accountCreateDTOList.add(accountCreateDTO);
                accountService.increaseAccountBenefit(accountCreateDTOList);

                UserStatisticsUpdateDTO userStatisticsUpdateDTO = new UserStatisticsUpdateDTO();
                userStatisticsUpdateDTO.setMerchantUserId(merchantUserId);
                userStatisticsUpdateDTO.setMerchantId(payoutWelfareDTO.getMerchantId());
                userStatisticsUpdateDTO.setBalanceCoins(payoutWelfareDTO.getPayoutCoin());
                userStatisticsUpdateDTO.setUpdateTime(new Date());
                statisticsService.updateStatistics(userStatisticsUpdateDTO);
            });
        }
    }

    /**
     * 保存商户用户信息
     *
     * @param merchantUser
     */
    private void saveMerchantUser(MerchantUser merchantUser) {
        Date now = new Date();
        //保存商户用户信息
        merchantUser.setActive(true);
        merchantUser.setCreatedby(ofNullable(merchantUser.getCreatedby()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        merchantUser.setUpdatedby(ofNullable(merchantUser.getCreatedby()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        merchantUser.setCreateTime(ofNullable(merchantUser.getCreateTime()).orElse(now));
        merchantUser.setUpdateTime(ofNullable(merchantUser.getUpdateTime()).orElse(now));
        int result = merchantUserMapper.insert(merchantUser);
        if (Objects.nonNull(merchantUser.getUserId())) {
            removeCacheService.removeUserMerchantCache(merchantUser.getUserId(), merchantUser.getMerchantId());
        }
        removeCacheService.removeMerchantUserCache(merchantUser.getId(), merchantUser.getMerchantId());
        if (result <= 0) {
            //保存失败，抛异常
            throw new BusinessException(UserErrorCode.MERCHANT_USER_SAVE_ERROR.getCode(),
                    UserErrorCode.MERCHANT_USER_SAVE_ERROR.getMessage());
        }
    }


    /**
     * 设置标签信息
     * @param merchantUserAndLevelInfoDTO
     * @param merchantId
     * @param userId
     */
    private void assembleTagsInfo(MerchantUserAndLevelInfoDTO merchantUserAndLevelInfoDTO, Long merchantId, Long userId) {
        List<TagInfoDTO> tagInfoDTOS = merchantUserTagMapper.selectTagListByUserId(1, merchantId, userId);
        if (!CollectionUtils.isEmpty(tagInfoDTOS)) {
            merchantUserAndLevelInfoDTO.setTags(tagInfoDTOS);
        }
    }

    /**
     * 设置会员等级相关信息
     * @param smallVenueMemberLevelInfoDTO
     * @param merchantId
     * @param userId
     */
    private void assembleMemberLevelInfo(SmallVenueMemberLevelInfoDTO smallVenueMemberLevelInfoDTO, Long merchantId, Long userId) {
        //查询会员等级相关信息
        MemberLevelInfoDTO memberLevelInfoDTO = memberMapper.selectMemberLevelName(merchantId, userId);
        if (memberLevelInfoDTO != null) {
            BeanUtils.copyProperties(memberLevelInfoDTO, smallVenueMemberLevelInfoDTO);
            //查询下一个等级
            List<MemberLevel> nextLevel = memberLevelMapper.findNextLevel(merchantId, memberLevelInfoDTO.getMemberLevelId(), 1, true);
            if (CollectionUtils.isNotEmpty(nextLevel)) {
                MemberLevel memberLevel = nextLevel.get(0);
                //设置升级差值
                Long lackValue = memberLevel.getGrowValue() - smallVenueMemberLevelInfoDTO.getGrowValue();
                smallVenueMemberLevelInfoDTO.setLackValue(lackValue > 0 ? lackValue : 0);
            }
        }
    }

    /**
     * 设置用户统计信息
     *
     * @param merchantUserStatisticsDTO
     * @param merchantId
     * @param userId
     * @param merchantUserId
     */
    private void assembleMerchantUserStatisticsInfo(MerchantUserStatisticsDTO merchantUserStatisticsDTO, Long merchantId, Long userId, Long merchantUserId) {
        //查询小场地统计信息
        List<SmallVenueStoredStatistics> smallVenueStoredStatistics = smallVenueStoredStatisticsMapper.selectStoredStatisticsByUserId(userId, merchantUserId, merchantId);
        log.info("smallVenueStoredStatistics before copy:{}", smallVenueStoredStatistics);
        //查询用户统计信息
        MerchantUserConsumeStatisticsDTO merchantUserConsumeStatisticsDTO = statisticsMapper.selectStatisticsByUserId(merchantId, userId);
        List<MerchantUserStoredStatisticsDTO> merchantUserStoredStatisticsDTOs = smallVenueStoredStatistics.stream().map(storedStatistics -> {
            MerchantUserStoredStatisticsDTO merchantUserStoredStatisticsDTO = new MerchantUserStoredStatisticsDTO();
            BeanUtils.copyProperties(storedStatistics, merchantUserStoredStatisticsDTO);
            return merchantUserStoredStatisticsDTO;
        }).collect(Collectors.toList());

        log.info("smallVenueStoredStatistics after copy:{}", merchantUserStoredStatisticsDTOs);
        merchantUserStatisticsDTO
                .setStatistics(merchantUserStoredStatisticsDTOs)
                .setConsumeStatistics(merchantUserConsumeStatisticsDTO);
    }

    /**
     * 更新手机号码记录
     *
     * @param userId         用户id
     * @param merchantId     商户id
     * @param merchantUserId 商户用户id
     * @param operatorId     操作人
     * @param telephone      手机号
     */
    private void saveOrUpdatePlatformUserPhone(Long userId, Long merchantId, Long merchantUserId, Long operatorId, String telephone, Integer systemFlag) {
        PlatformUserPhoneDTO userPhoneDTO = new PlatformUserPhoneDTO();
        userPhoneDTO.setUserId(userId);
        userPhoneDTO.setMerchantId(ofNullable(merchantId).orElse(UserMemberSysConstants.PLATFORM_MERCHANT_ID));
        userPhoneDTO.setTelephone(telephone);
        userPhoneDTO.setOperatorId(ofNullable(operatorId).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        userPhoneDTO.setMerchantUserId(merchantUserId);
        userPhoneDTO.setSystemFlag(systemFlag);
        platformUserPhoneService.saveOrUpdatePlatformUserPhone(userPhoneDTO);
    }

    private void updateUserTelephone(Long userId, String telephone) {
        try {
            UserDTO userDTO = userRepository.get(userId);
            UserDTO updateUser = new UserDTO();
            updateUser.setId(userId);
            updateUser.setTelephone(telephone);
            if (userDTO.getOpenId().startsWith(UserMemberSysConstants.SMALL_VENUE_USER_PREFIX)) {
                updateUser.setOpenId(UserMemberSysConstants.SMALL_VENUE_USER_PREFIX + telephone);
                updateUser.setUnionId(UserMemberSysConstants.SMALL_VENUE_USER_PREFIX + telephone);
            }
            userRepository.insertOrUpdateUser(updateUser);
        } catch (Exception e) {
            log.error("修改手机号码失败,userId:{},telephone:{}", userId, telephone);
            log.error(e.getMessage(), e);
        }
    }

    /**
     * B端用户列表查询判断是否有查询条件
     * @param merchantUserQueryDTO
     * @return
     */
    private boolean isNoQueryConditions(MerchantUserQueryDTO merchantUserQueryDTO) {
        return StringUtils.isBlank(merchantUserQueryDTO.getKeyword())
                && CollectionUtil.isEmpty(merchantUserQueryDTO.getGroupTagIdList())
                && CollectionUtil.isEmpty(merchantUserQueryDTO.getEquipmentTypeTagIdList())
                && CollectionUtil.isEmpty(merchantUserQueryDTO.getOtherTagIdList())
                && CollectionUtil.isEmpty(merchantUserQueryDTO.getSexTagIdList())
                && Objects.isNull(merchantUserQueryDTO.getTotalConsumeMoneyMin())
                && Objects.isNull(merchantUserQueryDTO.getTotalConsumeMoneyMax())
                && Objects.isNull(merchantUserQueryDTO.getTotalBalanceMin())
                && Objects.isNull(merchantUserQueryDTO.getTotalBalanceMax())
                && Objects.isNull(merchantUserQueryDTO.getTotalCoinMin())
                && Objects.isNull(merchantUserQueryDTO.getTotalCoinMax());
    }
}
