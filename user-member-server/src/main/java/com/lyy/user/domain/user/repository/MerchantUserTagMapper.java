package com.lyy.user.domain.user.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lyy.user.account.infrastructure.user.dto.userInfo.TagInfoDTO;
import com.lyy.user.domain.user.entity.MerchantUserTag;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface MerchantUserTagMapper extends BaseMapper<MerchantUserTag> {


    void deleteByTagId(@Param("tagId")  Long tagId,@Param("merchantId") Long merchantId,@Param("notHandleUserIds") List<Long> notHandleUserIds);

    void deleteByTagIdAndUserIds(@Param("tagId")  Long tagId,@Param("merchantId") Long merchantId,@Param("userIds") List<Long> userIds);

    /**
     *
     * @param tagId
     * @param tagType
     * @param merchantId
     * @param active
     * @return
     */
    Integer countUserMember(@Param("tagId") Long[] tagId, @Param("merchantId") Long merchantId,
                                      @Param("tagType") Integer tagType, @Param("active") Boolean active);

    List<MerchantUserTag> selectListByBusinessUserId(@Param("tagType") Integer tagType, @Param("merchantId") Long merchantId,@Param("userIds") List<Long> userIds);

    MerchantUserTag getByBusinessUserId(@Param("merchantId") Long merchantId, @Param("businessUserId") Long businessUserId);

    /**
     * 查询商户用户标签名称
     * @param tagType
     * @param merchantId
     * @param businessUserId
     * @return
     */
    List<TagInfoDTO> selectTagListByUserId(@Param("tagType") Integer tagType, @Param("merchantId") Long merchantId, @Param("businessUserId") Long businessUserId);
}