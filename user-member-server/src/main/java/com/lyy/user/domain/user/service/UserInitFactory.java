package com.lyy.user.domain.user.service;

import static java.util.Optional.ofNullable;

import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.user.account.infrastructure.constant.UserSourceEnum;
import com.lyy.user.infrastructure.execption.BusinessException;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName: UserInitFactory
 * @description: 用户初始化工厂
 * @author: pengkun
 * @date: 2021/04/06
 **/
@Service
public class UserInitFactory {

    private final Map<String, UserInitHandler> handlerMap = new HashMap<>();

    @Autowired
    public UserInitFactory(Map<String, UserInitHandler> handlerMap) {
        this.handlerMap.clear();
        handlerMap.forEach(this.handlerMap::put);
    }

    public UserInitHandler getHandler(UserSourceEnum userSourceEnum) {
        return ofNullable(handlerMap.get(userSourceEnum.getClassName())).orElseThrow(() -> new BusinessException(UserErrorCode.USER_INIT_NO_CLASS_HANDLER_ERROR.getCode(), String.format("用户初始化beanName:%s, 不存在", userSourceEnum.getClassName())));
    }

}
