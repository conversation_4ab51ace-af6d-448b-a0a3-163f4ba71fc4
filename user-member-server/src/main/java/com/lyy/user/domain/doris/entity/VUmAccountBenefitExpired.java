package com.lyy.user.domain.doris.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "v_um_account_benefit_expired")
public class VUmAccountBenefitExpired {

  @TableField(value = "id")
  private Long id;
  @TableField(value = "merchant_id")
  private Long merchantId;
  @TableField(value = "user_id")
  private Long userId;
  @TableField(value = "status")
  private Long status;
  @TableField(value = "classify")
  private Long classify;

}
