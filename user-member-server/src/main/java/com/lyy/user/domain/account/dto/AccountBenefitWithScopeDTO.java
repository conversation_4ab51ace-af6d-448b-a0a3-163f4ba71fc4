package com.lyy.user.domain.account.dto;

import com.lyy.user.domain.benefit.entity.BenefitScope;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.util.CollectionUtils;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/6 11:12
 */
@Getter
@Setter
@ToString
public class AccountBenefitWithScopeDTO {

    /**
     * 账户权益id
     */
    private Long accountBenefitId;

    /**
     * 权益id
     */
    private Long benefitId;

    /**
     * 总数
     */
    private BigDecimal total;

    /**
     * 权益数量
     */
    private BigDecimal amount;

    /**
     * 权益类型
     */
    private Integer classify;

    /**
     * 有效期类型:0、无限期,1、日期区间可用,2、单日时间区间可用
     */
    private Integer expiryDateCategory;

    /**
     * 生效时间
     */
    private String upTime;

    /**
     * 失效时间
     */
    private String downTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 消耗范围，key为类型，value为关联的id集合
     */

    private List<BenefitScope> scope;

    /**
     * 记录状态
     */
    private Integer status;

    /**
     * 获取分组后的消耗范围map
     * @return
     */
    public Map<Integer, Set<Long>> getScopeMap() {
        Map<Integer, Set<Long>> map = new HashMap<>(16);
        if (scope != null && !CollectionUtils.isEmpty(scope)) {
            map = scope.stream().collect(Collectors.groupingBy(BenefitScope::getApplicable,
                    Collectors.mapping(BenefitScope::getAssociatedId, Collectors.toSet())));
        }
        return map;
    }
}
