package com.lyy.user.domain.correction.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户标签
 * @TableName um_tag_user_bak
 */
@Data
public class UmTagUserBak implements Serializable {

    private static final long serialVersionUID = 7170189325579841016L;
    /**
     * 
     */
    private Long id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签编码
     */
    private String code;

    /**
     * 商户ID或平台ID
     */
    private Long merchantId;

    /**
     * 标签类型，1:自动, 2:手动
     */
    private Integer category;

    /**
     * 备注
     */
    private String description;

    /**
     * 是否可用，true:是，false：否
     */
    @TableField(value = "is_active")
    private Boolean active;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

    /**
     * 
     */
    private Long createdby;

    /**
     * 
     */
    private Long updatedby;

    /**
     * 0平台用户标签 ,1商户用户标签
     */
    private Integer tagType;

    /**
     * 0普通标签，1场地名称，2设备类型，3支付方式标签，4性别标签，5会员标签
     */
    private Integer businessType;

    /**
     * 1删除 ，正常0
     */
    private Integer state;


}