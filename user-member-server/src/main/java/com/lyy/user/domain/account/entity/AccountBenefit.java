package com.lyy.user.domain.account.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@TableName(value = "um_account_benefit")
public class AccountBenefit {
    /**
     * 标识
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 账户ID
     */
    @TableField(value = "account_id")
    private Long accountId;

    /**
     * 	商户ID
     */
    @TableField(value = "merchant_id")
    private Long merchantId;


    @TableField(value = "user_id")
    private Long userId;

    @TableField(value = "merchant_user_id")
    private Long merchantUserId;

    /**
     * 	权益ID
     */
    @TableField(value = "benefit_id")
    private Long benefitId;

    /**
     * 	总权益
     */
    @TableField(value = "total")
    private BigDecimal total;

    /**
     * 	剩余权益
     */
    @TableField(value = "balance")
    private BigDecimal balance;

    /**
     * 	权益类型
     */
    @TableField(value = "classify")
    private Integer classify;

    /**
     * 	状态：1=正常，2=过期
     */
    @TableField(value = "status")
    private Integer status;

    /**
     *  有效期类型:0、无限期,1、日期区间可用,2、单日时间区间可用
     */
    @TableField(value = "expiry_date_category")
    private Integer expiryDateCategory;

    /**
     * 	上线时间
     */
    @TableField(value = "up_time")
    private String upTime;

    /**
     * 	下线时间
     */
    @TableField(value = "down_time")
    private String downTime;

    /**
     * 	创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "create_by")
    private Long createBy;

    /**
     * 	更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(value = "update_by")
    private Long updateBy;

    @TableField(value = "resource")
    private Integer resource;

    @TableField(value = "resource_id")
    private Long resourceId;

    /**
     * 商家自定义权益id
     */
    @TableField(value = "merchant_benefit_classify_id")
    private Long merchantBenefitClassifyId;

    /**
     * 使用规则id
     */
    @TableField(value = "use_rule_id")
    private Long useRuleId;

    /**
     * 所属门店id
     */
    @TableField(value = "store_id")
    private Long storeId;

    /**
     * 权益值类型
     */
    @TableField(value = "value_type")
    private Integer valueType;

}