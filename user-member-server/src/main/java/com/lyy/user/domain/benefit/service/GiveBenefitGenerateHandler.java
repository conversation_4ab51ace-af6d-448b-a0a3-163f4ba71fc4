package com.lyy.user.domain.benefit.service;

import com.lyy.user.account.infrastructure.benefit.dto.GenerateAccountDTO;
import com.lyy.user.domain.benefit.repository.BenefitMapper;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 赠与方式产生的权益处理
 *      此类暂不使用，保留代码
 *
 * <AUTHOR>
 * @create 2021/4/6 14:52
 */
@Slf4j
@Component(value = "GIVE")
public class GiveBenefitGenerateHandler implements BenefitGenerateHandler {

    @Resource
    private BenefitMapper benefitMapper;


    @Override
    public void doGenerate(GenerateAccountDTO generateAccountDTO) {

        // 1 查出相关权益
//        LambdaQueryWrapper<Benefit> queryWrapper=  new QueryWrapper<Benefit>().lambda().in(Benefit::getId,generateAccountDTO.getBenefitIdList()).eq(Benefit::getActive,Boolean.TRUE);
//        List<Benefit> benefitList =  benefitMapper.selectList(queryWrapper);
//        Map<Integer,List<Benefit>> benefitMap = benefitList.stream().collect(Collectors.groupingBy(Benefit::getClassify));
//
//        // 2 创建权益关联账户
//        List<AccountCreateDTO> accountCreateList = new ArrayList<>();
//        for(Map.Entry<Integer,List<Benefit>> entry: benefitMap.entrySet()){
//            AccountCreateDTO accountCreateDTO = new AccountCreateDTO();
//            accountCreateDTO.setUserId(generateAccountDTO.getUserId());
//            accountCreateDTO.setMerchantId(generateAccountDTO.getMerchantId());
//            accountCreateDTO.setMerchantUserId(generateAccountDTO.getMerchantUserId());
//            accountCreateDTO.setStoreId(generateAccountDTO.getStoreId());
//            accountCreateDTO.setClassify(entry.getKey());
//            List<AccountBenefitCreateDTO> accountBenefitCreateList = entry.getValue().stream().map(benefit -> {
//                AccountBenefitCreateDTO accountBenefitCreateDTO = new AccountBenefitCreateDTO();
//                accountBenefitCreateDTO.setBenefitId(benefit.getId());
//                accountBenefitCreateDTO.setClassify(benefit.getClassify());
//                accountBenefitCreateDTO.setTotal(new BigDecimal(benefit.getBenefitCount()));
//                return accountBenefitCreateDTO;
//            }).collect(Collectors.toList());
//            accountCreateDTO.setAccountBenefit(accountBenefitCreateList);
//            accountCreateList.add(accountCreateDTO);
//        }
//
//        accountService.increaseAccountBenefit(accountCreateList);
    }
}
