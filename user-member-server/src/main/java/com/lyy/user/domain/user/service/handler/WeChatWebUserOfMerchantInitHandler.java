package com.lyy.user.domain.user.service.handler;

import static java.util.Optional.ofNullable;

import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.app.interfaces.facade.dto.UserVO;
import com.lyy.user.domain.user.service.AbstractUserInitService;
import com.lyy.user.domain.user.service.UserInitHandler;
import com.lyy.user.infrastructure.execption.BusinessException;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @ClassName: WeChatWebUserOfMerchantInitHandler
 * @description: B端商户--C端授权用户处理
 * @author: pengkun
 * @date: 2021/12/20
 **/
@Slf4j
@Service("weChatWebUserOfMerchantInit")
public class WeChatWebUserOfMerchantInitHandler extends AbstractUserInitService implements UserInitHandler {

    /**
     * 用户初始化处理方法
     *
     * @param dto
     * @return
     */
    @Override
    public UserInfoDTO handle(UserCreateDTO dto) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        if (dto.getLyyUserId() == null) {
            throw new BusinessException(UserErrorCode.PLATFORM_USER_QUERY_PARAM_ERROR.getCode(), UserErrorCode.PLATFORM_USER_QUERY_PARAM_ERROR.getMessage());
        }
        UserVO userDTO = getPlatformUserByUserId(dto.getLyyUserId());
        boolean flag = false;
        if (userDTO != null) {
            //更新平台用户信息的openId和unionId
            if (!userDTO.getOpenId().equals(dto.getOpenid())
                    || (StringUtils.isNotBlank(dto.getUnionid()) && !userDTO.getUnionId().equals(dto.getUnionid()))) {
                if(log.isDebugEnabled()) {
                    log.debug("更新平台用户信息:{}", dto);
                }
                flag = true;
                updateUserWx(dto, userDTO);
            }
            //初始化用户App,公众号关联信息
            initUserApp(dto.getAppId(), dto.getOpenid(), dto.getLyyUserId());
            userInfoDTO.setLyyUserId(dto.getLyyUserId());
            userInfoDTO.setIsactive(userDTO.getIsActive());
        } else {
            log.error("根据userId获取平台用户失败,userId:{}", dto.getLyyUserId());
            throw new BusinessException(UserErrorCode.QUERY_PLATFORM_USER_ERROR.getCode(), UserErrorCode.QUERY_PLATFORM_USER_ERROR.getMessage());
        }

        if (Objects.nonNull(dto.getMerchantUserCreateDTO())
                && dto.getMerchantUserCreateDTO().getMerchantId() != null) {
            //初始化商户用户
            dto.getMerchantUserCreateDTO().setUserId(ofNullable(dto.getMerchantUserCreateDTO().getUserId())
                    .orElse(userInfoDTO.getLyyUserId()));
            userInfoDTO.setMerchantId(dto.getMerchantUserCreateDTO().getMerchantId());
            Long merchantUserId = initPlatformMerchantUser(dto, flag);
            userInfoDTO.setMerchantUserId(merchantUserId);
        }
        return userInfoDTO;
    }

}
