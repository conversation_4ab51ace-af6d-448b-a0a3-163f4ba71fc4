package com.lyy.user.domain.user.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MerchantBenefitIncrementDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantIntegralUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueMobileUserListSelectDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueUserDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueUserListSelectDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueUserMobileVO;
import com.lyy.user.account.infrastructure.user.dto.UpdateMerchantUserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserInfoAndAccountDTO;
import com.lyy.user.domain.user.entity.MerchantUser;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 商户用户
 */
public interface
MerchantUserMapper extends BaseMapper<MerchantUser> {

    /**
     * 根据id更新商户用户信息
     * @param merchantUser
     * @return
     */
    int updateMerchantUserByIdAndMerchantId(MerchantUser merchantUser);

    /**
     * 根据用户Id和商户Id查询商户用户
     * @param userId
     * @param merchantId
     * @return
     */
    MerchantUser selectByUserIdAndMerchantId(@Param("userId") Long userId,@Param("merchantId") Long merchantId);

    /**
     * 根据id获取商户用户系信息
     * @param merchantUserId
     * @param merchantId
     * @return
     */
    MerchantUser selectByIdAndMerchantId(@Param("merchantUserId") Long merchantUserId,@Param("merchantId") Long merchantId);

    /**
     * 查询手机号在商户下的所有商户用户
     * @param telephone 手机号
     * @param merchantId    商户id
     * @return
     */
    List<MerchantUser> selectAllByTelephoneAndMerchantId(@Param("telephone") String telephone, @Param("merchantId") Long merchantId);

    /**
     * 查询用户总数 ，与selectUserListByMerchantOrder 配合使用
     * @param numericKeyword
     * @param strKeyWord
     * @param merchantId
     * @param tagIdList 标签
     * @param tagTypeNum 标签类型总数
     * @return
     */
    Long countUserListByMerchantOrder(@Param("numericKeyword") String numericKeyword,
                                      @Param("strKeyWord") String strKeyWord,
                                      @Param("merchantId") Long merchantId,
                                      @Param("tagIdList") List<Long> tagIdList,
                                      @Param("tagTypeNum") Integer tagTypeNum,
                                      @Param("totalConsumeMoneyMin") Integer totalConsumeMoneyMin,
                                      @Param("totalConsumeMoneyMax") Integer totalConsumeMoneyMax,
                                      @Param("totalBalanceMin") Integer totalBalanceMin,
                                      @Param("totalBalanceMax") Integer totalBalanceMax,
                                      @Param("totalCoinMin") Integer totalCoinMin,
                                      @Param("totalCoinMax") Integer totalCoinMax);

    Long countUserList(@Param("numericKeyword") String numericKeyword,
                                      @Param("strKeyWord") String strKeyWord,
                                      @Param("merchantId") Long merchantId,
                                      @Param("groupTagIds") Long[] groupTagIds,
                                      @Param("equipmentTypeTagIds") Long[] equipmentTypeTagIds,
                                      @Param("otherTagIds") Long[] otherTagIds,
                                      @Param("sexTagIds") Long[] sexTagIds,
                                      @Param("hasTag") Boolean hasTag,
                                      @Param("totalConsumeMoneyMin") Integer totalConsumeMoneyMin,
                                      @Param("totalConsumeMoneyMax") Integer totalConsumeMoneyMax,
                                      @Param("totalBalanceMin") Integer totalBalanceMin,
                                      @Param("totalBalanceMax") Integer totalBalanceMax,
                                      @Param("totalCoinMin") Integer totalCoinMin,
                                      @Param("totalCoinMax") Integer totalCoinMax);


    /**
     * 分页查询用户列表
     * @param numericKeyword
     * @param strKeyWord
     * @param merchantId
     * @param tagIdList
     * @param orderItemList
     * @param start
     * @param pageSize
     * @return
     */
    List<MerchantUserListDTO> selectUserListByMerchantOrder(@Param("numericKeyword") String numericKeyword,
                                                            @Param("strKeyWord") String strKeyWord,
                                                            @Param("merchantId") Long merchantId,
                                                            @Param("tagIdList") List<Long> tagIdList,
                                                            @Param("tagTypeNum") Integer tagTypeNum,
                                                            @Param("totalConsumeMoneyMin") Integer totalConsumeMoneyMin,
                                                            @Param("totalConsumeMoneyMax") Integer totalConsumeMoneyMax,
                                                            @Param("totalBalanceMin") Integer totalBalanceMin,
                                                            @Param("totalBalanceMax") Integer totalBalanceMax,
                                                            @Param("totalCoinMin") Integer totalCoinMin,
                                                            @Param("totalCoinMax") Integer totalCoinMax,
                                                            @Param("orderItemList") List<OrderItem> orderItemList,
                                                            @Param("start") Long start,
                                                            @Param("pageSize") Long pageSize);

    IPage<MerchantUserListDTO> selectUserList(IPage page,
                                             @Param("numericKeyword") Long numericKeyword,
                                             @Param("strKeyWord") String strKeyWord,
                                             @Param("merchantId") Long merchantId,
                                             @Param("groupTagIds") Long[] groupTagIds,
                                             @Param("equipmentTypeTagIds") Long[] equipmentTypeTagIds,
                                             @Param("otherTagIds") Long[] otherTagIds,
                                             @Param("sexTagIds") Long[] sexTagIds,
                                             @Param("hasTag") Boolean hasTag,
                                             @Param("totalConsumeMoneyMin") Integer totalConsumeMoneyMin,
                                             @Param("totalConsumeMoneyMax") Integer totalConsumeMoneyMax,
                                             @Param("totalBalanceMin") Integer totalBalanceMin,
                                             @Param("totalBalanceMax") Integer totalBalanceMax,
                                             @Param("totalCoinMin") Integer totalCoinMin,
                                             @Param("totalCoinMax") Integer totalCoinMax,
                                             @Param("orderItemList") List<OrderItem> orderItemList,
                                             @Param("start") Long start,
                                             @Param("pageSize") Long pageSize,
                                             @Param("keyword") String keyword,
                                             @Param("keywordType") Integer keywordType);

    /**
     * B端没有条件的列表查询
     * @param page
     * @param merchantId    商户id
     * @param orderItemList 排序
     * @param start
     * @param pageSize
     * @return
     */
    IPage<MerchantUserListDTO> selectNoQueryConditionsList(IPage page,
                                                           @Param("merchantId") Long merchantId,
                                                           @Param("orderItemList") List<OrderItem> orderItemList,
                                                           @Param("start") Long start,
                                                           @Param("pageSize") Long pageSize);

    List<MerchantUser> selectByIds(@Param("ids") List<Long> ids, @Param("merchantId") Long merchantId);

    List<Long> selectAllMerchantUserId( @Param("merchantId") Long merchantId);

    /**
     * 获取用户最大的创建人
     *
     * @param merchantId 商户id
     * @param userId     用户id
     * @return
     */
    Long getMax(@Param("merchantId")Long merchantId,
                @Param("userId") Long userId);

    /**
     * 关键字查询用户会员信息
     * 若 keyWordsType = 1, 限定 keywords 为 userId
     *
     * @param merchantId
     * @param keywords
     * @return
     */
    List<MerchantUserInfoAndAccountDTO> searchByKeywords(@Param("merchantId") Long merchantId,
                                                         @Param("keywords") String keywords,
                                                         @Param("keyWordsType") Integer keyWordsType,
                                                         @Param("userId") Long userId);

    /**
     * 查询会员密码
     * @param merchantId
     * @param userId
     * @return
     */
    String selectMerchantUserPassword(@Param("merchantId") Long merchantId, @Param("userId") Long userId);

    /**
     * 更新会员密码
     * @param merchantId
     * @param userId
     * @param newPassWord
     * @return
     */
    Integer updateMerchantUserPassword(@Param("merchantId") Long merchantId, @Param("userId") Long userId,
                                       @Param("newPassWord") String newPassWord);

    /**
     * 小场地会员列表查询
     *
     * @param page
     * @param selectDTO
     * @return
     */
    IPage<SmallVenueUserDTO> selectSmallVenueUserList(@Param("page") IPage page,
                                                      @Param("selectDTO") SmallVenueUserListSelectDTO selectDTO,
                                                      @Param("tagIds") Long[] tagIds);

    Long countSmallVenueUserList(
            @Param("selectDTO") SmallVenueUserListSelectDTO selectDTO,
            @Param("tagIds") Long[] tagIds);


    /**
     * 小场地会员列表查询-B端
     * @param page
     * @param selectDTO
     * @param hasTag
     * @param groupTagIds
     * @param equipmentTypeTagIds
     * @param otherTagIds
     * @param sexTagIds
     * @param orderItemList
     * @return
     */
    IPage<SmallVenueUserMobileVO> smallVenueMobileUserList(@Param("page") IPage page,
                                                           @Param("keyWordType") Integer keyWordType,
                                                           @Param("selectDTO") SmallVenueMobileUserListSelectDTO selectDTO,
                                                           @Param("hasTag") boolean hasTag,
                                                           @Param("groupTagIds") Long[] groupTagIds,
                                                           @Param("equipmentTypeTagIds") Long[] equipmentTypeTagIds,
                                                           @Param("otherTagIds") Long[] otherTagIds,
                                                           @Param("sexTagIds") Long[] sexTagIds,
                                                           @Param("orderItemList") List<OrderItem> orderItemList,
                                                           @Param("merchantUserIdList") List<Long> merchantUserIdList
    );

    /**
     * 小场地会员列表查询-B端
     * @param selectDTO
     * @return
     */
    List<SmallVenueUserMobileVO> smallVenueMobileMemberUserList(@Param("selectDTO") SmallVenueMobileUserListSelectDTO selectDTO,
                                                                @Param("merchantUserIdList") List<Long> merchantUserIdList);

    /**
     * 小场地会员列表查询-B端-统计数量
     * @param selectDTO
     * @param hasTag
     * @param groupTagIds
     * @param equipmentTypeTagIds
     * @param otherTagIds
     * @param sexTagIds
     * @return
     */
    Long countSmallVenueMobileUserList(
            @Param("keyWordType") Integer keyWordType,
            @Param("selectDTO") SmallVenueMobileUserListSelectDTO selectDTO,
            @Param("hasTag") boolean hasTag,
            @Param("groupTagIds") Long[] groupTagIds,
            @Param("equipmentTypeTagIds") Long[] equipmentTypeTagIds,
            @Param("otherTagIds") Long[] otherTagIds,
            @Param("sexTagIds") Long[] sexTagIds,
            @Param("merchantUserIdList") List<Long> merchantUserIdList);


    /**
     * 更新会员信息
     *
     * @param updateDTO
     * @return
     */
    Integer updateMerchantUserInfo(@Param("updateDTO") UpdateMerchantUserInfoDTO updateDTO);


    /**
     * 更新会员手机号码
     * @param merchantId
     * @param userId
     * @param newTelephone
     * @param oldTelephone
     * @return
     */
    Integer updateMerchantUserTelephone(@Param("merchantId") Long merchantId, @Param("userId") Long userId,
                                        @Param("newTelephone") String newTelephone,
                                        @Param("oldTelephone") String oldTelephone);

    /**
     * 根据条件查询商户用户信息
     * @param dto
     * @return
     */
    MerchantUserDTO getByQuery(@Param("dto") MerchantIntegralUserQueryDTO dto);


    /**
     * 手机号查询用户会员信息
     *
     * @param merchantId
     * @param phone
     * @return
     */
    List<MerchantUserInfoAndAccountDTO> searchByPhone(@Param("merchantId") Long merchantId,
                                                      @Param("phone") String phone);

    /**
     * 移动端会员注销
     * @param merchantId
     * @param merchantUserId
     * @return
     */
    Integer mobileTerminalMerchantUserCancellation(@Param("merchantId") Long merchantId,
                                                   @Param("merchantUserId") Long merchantUserId,
                                                   @Param("operatorId") Long operatorId);

    /**
     * 根据列表查询条件获取多金宝会员的用户ids
     * @param selectDTO
     * @param hasTag
     * @param groupTagIds
     * @param equipmentTypeTagIds
     * @param otherTagIds
     * @param sexTagIds
     * @param hasMemberLevel
     * @param hasMerchantBenefitClassifyId
     * @param numericKeyword
     * @param strKeyWord
     * @return
     */
    List<Long> findSmallVenueMobileUserIds(@Param("selectDTO") MerchantBenefitIncrementDTO selectDTO,
                                           @Param("hasTag") boolean hasTag,
                                           @Param("groupTagIds") Long[] groupTagIds,
                                           @Param("equipmentTypeTagIds") Long[] equipmentTypeTagIds,
                                           @Param("otherTagIds") Long[] otherTagIds,
                                           @Param("sexTagIds") Long[] sexTagIds,
                                           @Param("hasMemberLevel") boolean hasMemberLevel,
                                           @Param("hasMerchantBenefitClassifyId") boolean hasMerchantBenefitClassifyId,
                                           @Param("numericKeyword") String numericKeyword,
                                           @Param("strKeyWord") String strKeyWord);

    /**
     * 根据商户id获取商户的用户数
     * @param merchantId    商户id
     * @return
     */
    Long countByMerchantId(@Param("merchantId") Long merchantId);


}