package com.lyy.user.domain.user.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * 商户用户
 * <AUTHOR>
 */
@ToString
@Getter
@Setter
@TableName(value = "um_merchant_user")
public class MerchantUser {
    /**
     * 商户用户ID
     */
    @TableId
    private Long id;

    /**
     * 平台用户ID
     */
    private Long userId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 电话
     */
    private String telephone;

    private String description;
    /**
     * 姓名
     */
    private String name;
    /**
     * 性别
     */
    private String gender;
    /**
     * 头像链接
     */
    private String headImg;
    /**
     * 生日
     */
    private String birthday;
    /**
     * 用户类型
     * W 微信
     * A 支付宝
     * J 京东
     * U 云闪付
     * O 其他渠道
     */
    private String userType;
    /**
     * 城市Id
     */
    private Long cityId;
    /**
     * 城市
     */
    private String provinceCity;
    /**
     * 是否可用，true:是，false：否
     */
    @TableField(value = "is_active")
    private Boolean active;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    private Long createdby;

    private Long updatedby;

    /**
     * 省份Id
     */
    private Long provinceId;
    /**
     * 区域Id
     */
    private Long regionId;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 密码
     */
    @TableField(value = "pass_word")
    private String passWord;
}