package com.lyy.user.domain.user.service;

import cn.lyy.cache.annotation.CacheEvict;
import com.lyy.user.application.user.IRemoveCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RemoveCacheServiceImpl implements IRemoveCacheService {


    @Override
    @CacheEvict(value = "multilevel:cache:member:user", key = "#userId  + '-' + #merchantId ")
    public void removeUserMerchantCache(long userId, long merchantId) {
        log.warn("清除用户缓存: key={}, lyyUserId={}, merchantId={}", "multilevel:cache:member:user", userId, merchantId);
    }

    @Override
    @CacheEvict(value = "multilevel:cache:member:merchantUser", key = "#merchantId  + '-' + #merchantUserId ")
    public void removeMerchantUserCache(long merchantUserId, long merchantId) {
        log.warn("清除用户缓存:key= {}, merchantUserId={}, merchantId={}", "multilevel:cache:member:merchantUser", merchantUserId, merchantId);
    }
}
