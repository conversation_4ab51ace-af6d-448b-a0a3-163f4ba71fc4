package com.lyy.user.domain.member.dto;

import com.lyy.user.domain.member.entity.MemberLifting;
import com.lyy.user.domain.member.entity.MemberLiftingRule;
import com.lyy.user.domain.member.entity.MemberLiftingRuleRecord;
import java.util.List;
import lombok.Data;


/**
 * <AUTHOR>
 * @className: MemberLiftingStrategyDTO
 * @date 2021/4/7
 */
@Data
public class MemberLiftingStrategyDTO {
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 商户id
     */
    private Long merchantId;
    /**
     * 当前升级已经生效的次数
     */
    private Integer currentEffectiveNumber;
    private MemberLifting memberLifting;
    private List<MemberLiftingRuleRecord> memberLiftingRuleRecordList;
    private List<MemberLiftingRule> memberLiftingRuleList;

}
