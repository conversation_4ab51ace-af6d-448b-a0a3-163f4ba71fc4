package com.lyy.user.domain.statistics.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 日表统计
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName(value = "um_statistics_daily")
public class StatisticsDaily {
    /**
     * 商家用户id
     */
    @TableField(value = "merchant_user_id")
    private Long merchantUserId;

    /**
     * 商家id
     */
    @TableField(value = "merchant_id")
    private Long merchantId;

    /**
     * 平台用户id
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 统计日期
     */
    @TableField(value = "statistics_date")
    private Date statisticsDate;

    /**
     * 启动次数
     */
    @TableField(value = "start_times")
    private Integer startTimes;

    /**
     * 支付次数
     */
    @TableField(value = "pay_times")
    private Integer payTimes;

    /**
     * 支付金额
     */
    @TableField(value = "pay_amount")
    private BigDecimal payAmount;

    /**
     * 支付购买次数
     */
    @TableField(value = "pay_for_service_times")
    private Integer payForServiceTimes;

    /**
     * 支付购买金额
     */
    @TableField(value = "pay_for_service_amount")
    private BigDecimal payForServiceAmount;

    /**
     * 消耗币数（累计投币）
     */
    @TableField(value = "coins_consumption")
    private BigDecimal coinsConsumption;

    /**
     * 消耗金额（累计消耗金额）
     */
    @TableField(value = "amount_consumption")
    private BigDecimal amountConsumption;

    /**
     * 记录创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 记录更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

}