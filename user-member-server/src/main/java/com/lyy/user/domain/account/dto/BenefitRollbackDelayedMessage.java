package com.lyy.user.domain.account.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lyy.user.account.infrastructure.account.dto.BenefitRollbackDTO;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * @description: 权益回退延时处理对象
 * @author: qgw
 * @date on 2021/4/15.
 * @Version: 1.0
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class BenefitRollbackDelayedMessage {
    /**
     * 回退权益DTO
     */
    private BenefitRollbackDTO benefitRollbackDTO;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 失败次数
     */
    private Integer failCount;
}
