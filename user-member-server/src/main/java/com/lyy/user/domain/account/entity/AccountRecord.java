package com.lyy.user.domain.account.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "um_account_record")
public class AccountRecord {
    /**
     * 	流水号
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 	账户ID
     */
    @TableField(value = "account_id")
    private Long accountId;

    /**
     * 	平台用户ID
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 	商户用户ID
     */
    @TableField(value = "merchant_user_id")
    private Long merchantUserId;

    /**
     * 	商户
     */
    @TableField(value = "merchant_id")
    private Long merchantId;

    /**
     * 	店铺
     */
    @TableField(value = "store_id")
    private Long storeId;

    /**
     * 	设备
     */
    @TableField(value = "equipment_id")
    private Long equipmentId;

    /**
     * 	权益类型
     */
    @TableField(value = "benefit_classify")
    private Integer benefitClassify;

    /**
     * 	权益ID
     */
    @TableField(value = "benefit_id")
    private Long benefitId;

    /**
     * 	期初权益
     */
    @TableField(value = "initial_benefit")
    private BigDecimal initialBenefit;

    /**
     * 	权益
     */
    @TableField(value = "original_benefit")
    private BigDecimal originalBenefit;

    /**
     * 	实际权益
     */
    @TableField(value = "actual_benefit")
    private BigDecimal actualBenefit;

    /**
     * 	交易单号
     */
    @TableField(value = "out_trade_no")
    private String outTradeNo;

    /**
     * 	业务单号
     */
    @TableField(value = "order_no")
    private String orderNo;
    /**
     * 	业务子单号
     */
    @TableField(value = "sub_order_no")
    private String subOrderNo;

    /**
     * 	方式1=加，2=减
     */
    @TableField(value = "mode")
    private Integer mode;

    /**
     * 	时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 	来源
     */
    @TableField(value = "resource")
    private String resource;

    /**
     * 	备注
     */
    @TableField(value = "description")
    private String description;

    /**
     * 交易类型
     */
    @TableField(value = "trade_type")
    private Integer tradeType;

    /**
     * 交易金额
     */
    @TableField(value = "trade_amount")
    private BigDecimal tradeAmount;

    /**
     * 商品名称
     */
    @TableField(value = "commodity_name")
    private String commodityName;

    /**
     * 设备类型名称
     */
    @TableField(value = "equipment_type_name")
    private String equipmentTypeName;

    /**
     * 设备编号
     */
    @TableField(value = "equipment_value")
    private String equipmentValue;

    /**
     * 机台号
     */
    @TableField(value = "group_number")
    private Integer groupNumber;

    /**
     * 店铺名称
     */
    @TableField(value = "store_name")
    private String storeName;

    /**
     * 设备类型id
     */
    @TableField(value = "equipment_type_id")
    private Long equipmentTypeId;

    /**
     * 记录类型
     * @see com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum
     */
    @TableField(value = "record_type")
    private Integer recordType;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 账号明细id
     */
    @TableField(value = "account_benefit_id")
    private Long accountBenefitId;

    /**
     * 操作人id
     */
    @TableField(value = "createdby")
    private Long createdby;
    /**
     * 操作人名称
     */
    @TableField(value = "create_name")
    private String createName;
    /**
     * 卡号
     */
    @TableField(value = "card_no")
    private String cardNo;
    /**
     * 退单号
     */
    @TableField(value = "refund_no")
    private String refundNo;
    /**
     * 商品id
     */
    @TableField(value = "goods_id")
    private Long goodsId;
    /**
     * 商品类型
     */
    @TableField(value = "goods_type")
    private Integer goodsType;
    /**
     * 商品所属分类
     */
    @TableField(value = "goods_classify")
    private Long goodsClassify;
    /**
     * 商品所属分离名称
     */
    @TableField(value = "goods_classify_name")
    private String goodsClassifyName;
    /**
     * 商品数量
     */
    @TableField(value = "goods_num")
    private BigDecimal goodsNum;
    /**
     * 优惠金额
     */
    @TableField(value = "discounts_amount")
    private BigDecimal discountsAmount;
    /**
     * 操作设备名称
     */
    @TableField(value = "operation_equipment_name")
    private String operationEquipmentName;
    /**
     * 设备名称
     */
    @TableField(value = "equipment_name")
    private String equipmentName;
    /**
     * 终端名称
     */
    @TableField(value = "terminal_name")
    private String terminalName;
    /**
     * 商户自定义权益id
     */
    @TableField(value = "merchant_benefit_classify_id")
    private Long merchantBenefitClassifyId;
    /**
     * 商户自定义权益名称
     */
    @TableField(value = "merchant_benefit_classify_name")
    private String merchantBenefitClassifyName;
    /**
     * 记录类型（1.商品购买记录 2.商品兑换记录；3.商品回收记录；4.储值变更记录（储值变更记录包括设备消费记录））
     */
    @TableField(value = "operation_type")
    private Integer operationType;
    /**
     * 操作渠道（1.微信小程序；2.桌面收银台；3.移动收银台）
     */
    @TableField(value = "operation_channel")
    private Integer operationChannel;
    /**
     * 消费方式 （1.刷卡消费 2.微信小程序；3.桌面收银台；4.移动收银台）
     */
    @TableField(value = "consume_type")
    private Integer consumeType;

    /**
     * 权益实际价值
     */
    @TableField(value = "actual_value")
    private BigDecimal actualValue;

    /**
     * 商品单价
     */
    @TableField(value = "goods_price")
    private BigDecimal goodsPrice;


    /**
     * 商品类型名称
     */
    @TableField(value = "goods_type_name")
    private String goodsTypeName;

    /**
     * 账号下权益初始总数（总次数）
     */
    @TableField(value = "account_initial_balance")
    private BigDecimal accountInitialBalance;

    /**
     * 账号下频次卡权益初始总张数
     */
    @TableField(value = "account_initial_num")
    private BigDecimal accountInitialNum;

    /** 
     * 商品
     */
    @TableField(value = "goods_type_id")
    private Long goodsTypeId;
}