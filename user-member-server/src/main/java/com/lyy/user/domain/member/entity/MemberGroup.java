package com.lyy.user.domain.member.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 会员组表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("um_member_group")
public class MemberGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 商户ID
     */
    @TableField("merchant_id")
    private Long merchantId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 开始日期
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 结束日期
     */
    @TableField("end_date")
    private Date endDate;

    /**
     * 是否可用
     */
    @TableField("is_active")
    private Boolean active;

    /**
     * 开通方式,0 自动;1 手动
     */
    @TableField("open_method")
    private Short openMethod;

    /**
     * 升降策略(0 无策略,1 升级策略,2 降级策略,3 有升有降策略)
     * @see com.lyy.user.infrastructure.constants.MemberGroupLiftingStrategyEnum
     */
    @TableField("lifting_strategy")
    private Short liftingStrategy;

    /**
     * 规则策略(0、叠加,1、覆盖)
     */
    @TableField("rule_strategy")
    private Short ruleStrategy;

    /**
     * 有效期为多少天，若为-1,则为永久有效，若为-2则按固定时间生效
     */
    @TableField("member_effective_time")
    private Integer memberEffectiveTime;

    /**
     * 会员有效开始时间,member_effective_time为-2时生效
     */
    @TableField("member_start_time")
    private Date memberStartTime;

    /**
     * 会员有效结束时间,member_effective_time为-2时生效
     */
    @TableField("member_end_time")
    private Date memberEndTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建者
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    @TableField("update_by")
    private Long updateBy;
    /**
     * 是否已删除，true:是，false：否
     */
    @TableField("is_del")
    private Boolean del;

}
