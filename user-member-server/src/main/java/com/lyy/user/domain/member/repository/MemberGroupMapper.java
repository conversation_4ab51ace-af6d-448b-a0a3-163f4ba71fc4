package com.lyy.user.domain.member.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupRangCheckDTO;
import com.lyy.user.domain.member.entity.MemberGroup;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 会员组表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
public interface MemberGroupMapper extends BaseMapper<MemberGroup> {

    MemberGroup selectByIdForUpdate(Long id);

    /**
     * 根据范围查找到对应的会员组列表
     * @param memberGroupRangCheckDTO
     * @return
     */
    List<MemberGroup> getMemberGroupOfRange(MemberGroupRangCheckDTO memberGroupRangCheckDTO);
    /**
     * 根据会员组统计对应的会员信息
     * @param merchantId
     * @param memberGroupId
     * @param isEffective 是否有效，若为true，则表示会员未删除，并且处于有效时间内容
     * @return
     */
    Integer countMemberByGroup(@Param("merchantId") Long merchantId,@Param("memberGroupId") Long memberGroupId,@Param("isEffective") boolean isEffective);

    /**
     * 根据id获取会员组信息
     *
     * @param merchantId 商户id
     * @param id
     * @return
     */
    MemberGroup getMemberGroupById(@Param("merchantId") Long merchantId, @Param("id") Long id);

    /**
     * 获取商户有效的会员数
     * @param merchantId    商户id
     * @param memberGroupId 会员组id
     * @return
     */
    Integer countEffectiveMemberByGroup(@Param("merchantId") Long merchantId,@Param("memberGroupId") Long memberGroupId);

    Integer existMemberGroup(Long merchantId);
}
