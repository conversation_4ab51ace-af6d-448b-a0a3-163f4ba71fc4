package com.lyy.user.domain.account.dto;

import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * 类描述：权益消耗dto
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/6 11:11
 */
@Getter
@Setter
@ToString
@Slf4j
public class AccountBenefitConsumeDTO {

    /**
     * 权益类型
     */
    private Integer benefitClassify;

    /**
     * 这种权益类型的权重
     */

    private Integer weight;

    /**
     * 优先级类型
     */
    private Integer expirePriority;

    /**
     * 权益详细信息
     */
    private List<AccountBenefitWithScopeDTO> benefitWithScope;

    /**
     * 获取总权益数值
     * @return
     */
    public BigDecimal getTotal() {
        BigDecimal total = BigDecimal.ZERO;
        if (benefitWithScope != null && benefitWithScope.size() > 0) {
            total = benefitWithScope.stream()
                    .map(AccountBenefitWithScopeDTO::getAmount)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        }
        return total;
    }

    /**
     * 优先级排序
     */
    public void sort() {
        if (benefitWithScope != null && benefitWithScope.size() > 0) {
            switch(this.expirePriority) {
                case 1:
                    benefitWithScope.sort(Comparator.comparing(AccountBenefitWithScopeDTO::getCreateTime));
                    break;
                case 2:
                    benefitWithScope.sort((first, next) -> {
                        try {
                            if (!Objects.equals(first.getExpiryDateCategory(), next.getExpiryDateCategory())) {
                                return next.getExpiryDateCategory().compareTo(first.getExpiryDateCategory());
                            }
                            if (Objects.equals(first.getExpiryDateCategory(), ExpiryDateCategoryEnum.TIME_INTERVAL.getValue())) {
                                if(StringUtils.isNotBlank(first.getDownTime()) && StringUtils.isNotBlank(next.getDownTime())){
                                    LocalDateTime firstDate = LocalDateTime.parse(first.getDownTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                                    LocalDateTime nextDate = LocalDateTime.parse(next.getDownTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                                    return firstDate.compareTo(nextDate);
                                }
                            }
                            if (Objects.equals(first.getExpiryDateCategory(), ExpiryDateCategoryEnum.ONE_DAY_TIME_INTERVAL.getValue())) {
                                if(StringUtils.isNotBlank(first.getDownTime()) && StringUtils.isNotBlank(next.getDownTime())){
                                    LocalTime firstDate = LocalTime.parse(first.getDownTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));
                                    LocalTime nextDate = LocalTime.parse(next.getDownTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));
                                    return firstDate.compareTo(nextDate);
                                }
                            }
                        } catch (Exception e) {
                            log.error("排序出现异常");
                            log.error(e.getMessage(), e);
                        }
                        return 0;
                    });
                    break;
                default:
                    break;
            }
        }
    }
}
