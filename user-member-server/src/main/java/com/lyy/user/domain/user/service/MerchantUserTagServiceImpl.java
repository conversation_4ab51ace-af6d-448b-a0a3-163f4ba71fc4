package com.lyy.user.domain.user.service;

import static java.util.Optional.ofNullable;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserInfoQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagExternalSystemQuery;
import com.lyy.user.account.infrastructure.user.dto.tag.TagOfExternalSystemDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagOfExternalSystemVO;
import com.lyy.user.app.infrastructure.constant.ExternalSystemEnum;
import com.lyy.user.application.user.IMerchantUserService;
import com.lyy.user.application.user.IMerchantUserTagService;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.domain.user.entity.MerchantUserTag;
import com.lyy.user.domain.user.entity.TagUser;
import com.lyy.user.domain.user.repository.MerchantUserTagMapper;
import com.lyy.user.infrastructure.repository.tag.TagRepository;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@Slf4j
@Service
public class MerchantUserTagServiceImpl extends ServiceImpl<MerchantUserTagMapper, MerchantUserTag> implements IMerchantUserTagService {

    @Autowired
    private TagRepository tagRepository;

    @Autowired
    private IMerchantUserService merchantUserService;

    @Override
    public Page<MerchantUserInfoDTO> listByTagIds(MerchantUserInfoQueryDTO queryDTO) {
        return new Page<>();
    }


    @Override
    public TagOfExternalSystemVO getTagOfExternalSystem(TagOfExternalSystemDTO dto) {
        Long merchantId = dto.getMerchantId();
        Long userId = dto.getUserId();
        Long merchantUserId = dto.getMerchantUserId();
        Integer externalSystem = dto.getExternalSystem();
        TagOfExternalSystemVO result = new TagOfExternalSystemVO();
        result.setMerchantId(merchantId);
        result.setMerchantUserId(merchantUserId);
        result.setExternalSystem(externalSystem);
        result.setExternalUser(false);
        result.setExternalBalanceNull(false);

        if (merchantId != null && merchantUserId == null && userId != null) {
            MerchantUser merchantUser = merchantUserService.findByUserIdAndMerchantId(userId, merchantId);
            if (merchantUser != null && merchantUser.getId() != null) {
                merchantUserId = merchantUser.getId();
                result.setMerchantUserId(merchantUserId);
            } else {
                return result;
            }
        }

        if (merchantId == null || merchantUserId == null || externalSystem == null) {
            return result;
        }
        ExternalSystemEnum externalSystemEnum = ExternalSystemEnum.getType(externalSystem).orElse(null);
        if (externalSystemEnum == null) {
            return result;
        }
        TagRepository.TagQuery query = new TagRepository.TagQuery();
        query.setMerchantId(merchantId);
        query.setNames(Lists.newArrayList(externalSystemEnum.getCode(), externalSystemEnum.getName()));
        query.setBusinessTypes(Lists.newArrayList(TagBusinessTypeEnum.USER_SOURCE.getStatus(), TagBusinessTypeEnum.INNER.getStatus()));
        List<TagUser> tags = tagRepository.listTag(query);
        if (!CollectionUtils.isEmpty(tags)) {
            tags.forEach(tagUser -> {
                if (StringUtils.equals(tagUser.getName(), externalSystemEnum.getName())) {
                    result.setExternalUserTagId(tagUser.getId());
                } else if (StringUtils.equals(tagUser.getName(), externalSystemEnum.getCode())) {
                    result.setExternalBalanceNullTagId(tagUser.getId());
                }
            });
        }
        MerchantUserTag merchantUserTag = tagRepository.getUserTag(merchantId, merchantUserId);
        if (merchantUserTag != null && merchantUserTag.getUserTags() != null && merchantUserTag.getUserTags().length > 0) {
            Arrays.stream(merchantUserTag.getUserTags()).forEach(userTageId -> {
                if (userTageId == null) {
                    return;
                }
                if (userTageId.equals(result.getExternalUserTagId())) {
                    result.setExternalUser(true);
                } else if (userTageId.equals(result.getExternalBalanceNullTagId())) {
                    result.setExternalBalanceNull(true);
                }
            });
        }
        return result;
    }

    @Override
        public List<TagOfExternalSystemVO> listTagOfExternalSystem(TagExternalSystemQuery query) {
        Long merchantId = query.getMerchantId();
        Long userId = query.getUserId();
        Long merchantUserId = query.getMerchantUserId();

        if (merchantId != null && merchantUserId == null && userId != null) {
            MerchantUser merchantUser = merchantUserService.findByUserIdAndMerchantId(userId, merchantId);
            if (merchantUser != null && merchantUser.getId() != null) {
                merchantUserId = merchantUser.getId();
                query.setMerchantUserId(merchantUserId);
            } else {
                return Lists.newArrayList();
            }
        }

        if (merchantId == null || merchantUserId == null) {
            return Lists.newArrayList();
        }

        ExternalSystemEnum externalSystem = ExternalSystemEnum.getByCode(query.getExternalSystem()).orElse(null);
        Map<Long, TagOfExternalSystemVO> systemTagMappingMap = new HashMap<>(16);
        TagRepository.TagQuery tagQuery = new TagRepository.TagQuery();
        tagQuery.setMerchantId(merchantId);
        List<String> tagNames;
        if (Objects.nonNull(externalSystem)) {
            tagNames = Lists.newArrayList(externalSystem.getCode(), externalSystem.getName());
        } else {
            tagNames = Arrays.stream(ExternalSystemEnum.values())
                    .flatMap(e -> Lists.newArrayList(e.getCode(), e.getName()).stream())
                    .collect(Collectors.toList());
        }
        tagQuery.setNames(tagNames);
        tagQuery.setBusinessTypes(Lists.newArrayList(TagBusinessTypeEnum.USER_SOURCE.getStatus(), TagBusinessTypeEnum.INNER.getStatus()));
        List<TagUser> tags = tagRepository.listTag(tagQuery);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(tags)) {
            log.debug("[第三方系统标签查询]tags: {}", tags);
            Map<String, ExternalSystemEnum> externalSystemTahMap = new HashMap<>(16);
            Arrays.stream(ExternalSystemEnum.values())
                    .forEach(e -> {
                        externalSystemTahMap.put(e.getName(), e);
                        externalSystemTahMap.put(e.getCode(), e);
                    });
            log.debug("[第三方系统标签查询]externalSystemTahMap: {}", externalSystemTahMap);
            Map<ExternalSystemEnum, TagOfExternalSystemVO> enumExternalSystemTagMappingMap = new HashMap<>(16);
            tags.forEach(tag -> {
                ExternalSystemEnum es = externalSystemTahMap.get(tag.getName());
                TagOfExternalSystemVO value = enumExternalSystemTagMappingMap.get(es);
                if (Objects.nonNull(value)) {
                    if (org.apache.commons.lang3.StringUtils.equals(tag.getName(), es.getName())) {
                        value.setExternalUserTagId(tag.getId());
                    } else if (org.apache.commons.lang3.StringUtils.equals(tag.getName(), es.getCode())) {
                        value.setExternalBalanceNullTagId(tag.getId());
                    }
                } else {
                    value = new TagOfExternalSystemVO();
                    value.setExternalBalanceNull(false);
                    value.setExternalUser(false);
                    value.setMerchantUserId(query.getMerchantUserId());
                    value.setExternalSystem(es.getType());
                    value.setMerchantId(merchantId);
                    value.setUserId(userId);
                    if (org.apache.commons.lang3.StringUtils.equals(tag.getName(), es.getName())) {
                        value.setExternalUserTagId(tag.getId());
                    } else if (org.apache.commons.lang3.StringUtils.equals(tag.getName(), es.getCode())) {
                        value.setExternalBalanceNullTagId(tag.getId());
                    }
                    enumExternalSystemTagMappingMap.put(es, value);
                }
                systemTagMappingMap.put(tag.getId(), value);
            });
            MerchantUserTag userTag = tagRepository.getUserTag(merchantId, merchantUserId);
            ofNullable(userTag)
                    .map(MerchantUserTag::getUserTags)
                    .filter(e -> e.length > 0)
                    .map(Arrays::asList)
                    .orElse(Lists.newArrayList())
                    .forEach(tagId -> {
                        TagOfExternalSystemVO externalSystemTagMapping = systemTagMappingMap.get(tagId);
                        if (Objects.nonNull(externalSystemTagMapping)) {
                            if (externalSystemTagMapping.getExternalUserTagId().equals(tagId)) {
                                externalSystemTagMapping.setExternalUser(true);
                            } else if (externalSystemTagMapping.getExternalBalanceNullTagId().equals(tagId)) {
                                externalSystemTagMapping.setExternalBalanceNull(true);
                            }
                        }
                    });
            log.debug("[第三方系统标签查询]systemTagMappingMap: {}", systemTagMappingMap);
        }
        return systemTagMappingMap.values().stream().distinct().collect(Collectors.toList());
    }

}
