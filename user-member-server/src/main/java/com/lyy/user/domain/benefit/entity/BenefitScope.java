package com.lyy.user.domain.benefit.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *
 * 权益范围表
 *
 * <AUTHOR>
 * @create 2021/3/30 17:14
 */
@ToString
@Getter
@Setter
@TableName(value = "um_benefit_scope")
public class BenefitScope {


    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 权益ID
     */
    @TableField(value="benefit_id")
    private Long benefitId;

    /**
     *  适用类型
     */
    @TableField(value="applicable")
    private Integer applicable;

    /**
     * 关联第三方ID
     */
    @TableField(value="associated_id")
    private Long associatedId;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *  商户ID
     */
    @TableField(value = "merchant_id")
    private Long merchantId;
}
