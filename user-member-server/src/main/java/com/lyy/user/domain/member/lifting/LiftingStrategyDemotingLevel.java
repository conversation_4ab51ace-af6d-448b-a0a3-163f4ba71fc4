package com.lyy.user.domain.member.lifting;

import com.lyy.user.application.member.IMemberLevelService;
import com.lyy.user.application.member.IMemberRuleService;
import com.lyy.user.application.member.IMemberService;
import com.lyy.user.domain.member.dto.MemberLiftingRuleRecordJsonDTO;
import com.lyy.user.domain.member.dto.MemberLiftingStrategyResultDTO;
import com.lyy.user.domain.member.entity.Member;
import com.lyy.user.domain.member.entity.MemberLevel;
import com.lyy.user.domain.member.entity.MemberLifting;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 降级策略,降等级策略，会自动降一个等级
 * <AUTHOR>
 * @className: LiftingStrategyDemoting
 * @date 2021/4/6
 */
@Component("liftingStrategy:3")
@Slf4j
public class LiftingStrategyDemotingLevel extends AbstractLiftingStrategy{
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IMemberRuleService memberRuleService;
    @Autowired
    private IMemberLevelService memberLevelService;



    /**
     * 处理成功的记录
     *
     * @param memberLiftingStrategyResultDTO
     * @return
     */
    @Override
    public int handlerSuccess(MemberLiftingStrategyResultDTO memberLiftingStrategyResultDTO) {
        Member member = memberLiftingStrategyResultDTO.getMember();
        MemberLifting memberLifting = memberLiftingStrategyResultDTO.getMemberLifting();
        List<MemberLiftingRuleRecordJsonDTO> recordJsonList = MemberLiftingRuleRecordJsonDTO.exchangeToList(memberLiftingStrategyResultDTO.getMemberLiftingRuleMap());
        log.info("需要对 {} 用户的 {} 会员组进行降等级处理,记录数据为 {}", memberLiftingStrategyResultDTO.getMember().getUserId(),
                memberLifting.getMemberGroupId(), recordJsonList);
        String resources = MemberLiftingRuleRecordJsonDTO.toMapString(recordJsonList);


        boolean result = memberDemotingLevel(member, memberLifting, resources, 10);
        if (!result) {
            log.warn("{} 会员的等级 {} 更新失败", member.getId(), member.getMemberLevelId());
            return -1;
        }
        //处理降级功能
        return super.handlerSuccess(memberLiftingStrategyResultDTO);
    }

    /**
     * 会员降级
     * @param member
     * @param memberLifting
     * @param resources
     * @param tryNum 尝试次数
     * @return 会员降级过程中变化的成长值
     */
    private boolean memberDemotingLevel(Member member, MemberLifting memberLifting, String resources, int tryNum) {
        if (tryNum <= 0) {
            log.warn("会员尝试降级超过次数,不再处理-->{}", member);
            return false;
        }
        List<MemberLevel> mlList = memberLevelService.findNextLevel(member.getMerchantId(), member.getMemberLevelId(), 1, false);
        MemberLevel ml = Optional.ofNullable(mlList)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.get(0))
                //否则获取当前等级
                .orElse(memberLevelService.getById(member.getMerchantId(), member.getMemberLevelId()));
        if (Objects.isNull(ml)) {
            log.warn("{} 会员的等级信息错误,当前等级为 {}，上级等级为 {}", member.getId(), member.getMemberLevelId(), mlList);
            return false;
        }

        boolean result = memberService.updateMemberLevel(member, ml);
        if (!result) {
            //更新失败，递归调用
            return memberDemotingLevel(memberService.getById(member.getMerchantId(), member.getId()), memberLifting, resources, tryNum - 1);
        }

        long growValue = (member.getGrowValue() - ml.getGrowValue());
        log.info("{} 商户的 {} 会员等级由 {} 降到 {} ,变化的成长值为 {}", member.getMerchantId(), member.getId(), member.getMemberLevelId(), ml.getId(), growValue);
        //添加会员成长值变化记录
        memberService.addMemberGrowRecord(member, memberLifting, resources, growValue, memberLifting.getName(), false);
        //降权益
        if (!ml.getId().equals(member.getMemberLevelId())) {
            //等级变化，需要降权益
            //降级时，应该扣钱之前就的等级相关的权益
            List<MemberLevel> levelList = Collections.singletonList(memberLevelService.getById(member.getMerchantId(), member.getMemberLevelId()));
            memberRuleService.updateBenefitOfLevel(member, levelList, false);
        }
        return true;
    }

}
