package com.lyy.user.domain.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 平台用户手机号码记录
 */
@ToString
@Getter
@Setter
@TableName(value = "um_platform_user_phone")
public class PlatformUserPhone {

    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 平台用户ID
     */
    private Long userId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 是否可用，true:是，false：否
     */
    @TableField(value = "is_active")
    private Boolean active;

    private Date createTime;

    private Date updateTime;

    private Long createdby;

    private Long updatedby;

    /**
     * 商户用户Id
     */
    private Long merchantUserId;

    /**
     * 系统标识
     */
    private Integer systemFlag;
}