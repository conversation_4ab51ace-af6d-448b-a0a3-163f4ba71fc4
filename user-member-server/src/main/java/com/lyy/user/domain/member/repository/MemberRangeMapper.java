package com.lyy.user.domain.member.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lyy.user.account.infrastructure.member.dto.MemberRangeAssociatedDTO;
import com.lyy.user.domain.member.entity.MemberRange;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 会员组范围 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
public interface MemberRangeMapper extends BaseMapper<MemberRange> {

    MemberRange selectByIdForUpdate(Long id);

    /**
     * 获取关系列表id
     * @param memberGroupId
     * @return
     */
    List<MemberRangeAssociatedDTO> findAssociatedList(@Param("memberGroupId") Long memberGroupId);


    /**
     * 删除不在范围内的内容数据
     *
     * @param merchantId
     * @param memberGroupId
     * @param memberRangeAssociatedList
     * @return
     */
    int deleteNotInAssociated(@Param("merchantId") Long merchantId, @Param("memberGroupId") Long memberGroupId, @Param("memberRangeAssociatedList") List<MemberRangeAssociatedDTO> memberRangeAssociatedList);


    /**
     * 新增范围内的内容数据,改方法有问题，先不处理
     * @param memberGroupId
     * @param memberRangeAssociatedList
     * @return
     */
    @Deprecated
    int addAssociated(@Param("memberGroupId") Long memberGroupId,@Param("memberRangeAssociatedList") List<MemberRangeAssociatedDTO> memberRangeAssociatedList);

}
