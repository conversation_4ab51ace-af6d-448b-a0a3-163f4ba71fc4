package com.lyy.user.domain.member.lifting;

import com.lyy.user.domain.member.dto.MemberLiftingStrategyDTO;
import com.lyy.user.domain.member.dto.MemberLiftingStrategyResultDTO;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 升级策略工厂
 *
 * <AUTHOR>
 * @className: LiftingFactory
 * @date 2021/4/6
 */
@Slf4j
@Component
public class LiftingFactory {
    private static final String LIFTING_STRATEGY_PREFIX = "liftingStrategy:";
    private Map<String, LiftingStrategyInterface> liftingStrategyMap;
    private static LiftingFactory factory;

    @Autowired
    public LiftingFactory(Map<String, LiftingStrategyInterface> liftingStrategyMap) {
        factory = this;
        factory.liftingStrategyMap = liftingStrategyMap;
    }

    public static boolean handlerStrategy(MemberLiftingStrategyDTO memberLiftingStrategyDTO) {
        LiftingStrategyInterface liftingStrategyInterface = factory.liftingStrategyMap.get(LIFTING_STRATEGY_PREFIX + memberLiftingStrategyDTO.getMemberLifting().getLifting());
        MemberLiftingStrategyResultDTO resultDTO = liftingStrategyInterface.handlerResult(memberLiftingStrategyDTO);
        log.debug(" {} 的升级策略信息产生的结果为结果-->{}", memberLiftingStrategyDTO, resultDTO);
        if (resultDTO == null) {
            return false;
        }
        if (resultDTO.getFailureRecord() != null && !resultDTO.getFailureRecord().isEmpty()) {
            int failureSize = liftingStrategyInterface.handlerFailure(resultDTO);
            log.info(" {} 用户的 {} 会员升级策略处理 {} 条失效记录", resultDTO.getMember().getUserId(), resultDTO.getMemberLifting().getId(), failureSize);
        }
        if (resultDTO.getMemberLiftingRuleMap() != null && !resultDTO.getMemberLiftingRuleMap().isEmpty()) {
            int successSize = liftingStrategyInterface.handlerSuccess(resultDTO);
            log.info(" {} 用户的 {} 会员升级策略处理 {} 条升级记录", resultDTO.getMember().getUserId(), resultDTO.getMemberLifting().getId(), successSize);
        }


        return true;
    }

    public static boolean handlerByResult(MemberLiftingStrategyResultDTO memberLiftingStrategyResultDTO) {
        LiftingStrategyInterface liftingStrategyInterface = factory.liftingStrategyMap.get(LIFTING_STRATEGY_PREFIX + memberLiftingStrategyResultDTO.getMemberLifting().getLifting());
        if (memberLiftingStrategyResultDTO.getFailureRecord() != null && !memberLiftingStrategyResultDTO.getFailureRecord().isEmpty()) {
            int failureSize = liftingStrategyInterface.handlerFailure(memberLiftingStrategyResultDTO);
            log.info(" {} 用户的 {} 会员升级策略处理 {} 条失效记录", memberLiftingStrategyResultDTO.getMember().getUserId(), memberLiftingStrategyResultDTO.getMemberLifting().getId(), failureSize);
        }
        if (memberLiftingStrategyResultDTO.getMemberLiftingRuleMap() != null && !memberLiftingStrategyResultDTO.getMemberLiftingRuleMap().isEmpty()) {
            int successSize = liftingStrategyInterface.handlerSuccess(memberLiftingStrategyResultDTO);
            log.info(" {} 用户的 {} 会员升级策略处理 {} 条升级记录", memberLiftingStrategyResultDTO.getMember().getUserId(), memberLiftingStrategyResultDTO.getMemberLifting().getId(), successSize);
        }
        return true;
    }
}
