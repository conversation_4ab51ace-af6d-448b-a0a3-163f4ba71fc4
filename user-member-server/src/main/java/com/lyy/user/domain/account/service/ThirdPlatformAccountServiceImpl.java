package com.lyy.user.domain.account.service;

import static java.util.Optional.ofNullable;

import com.lyy.user.account.infrastructure.account.dto.request.ThirdPlatformAccountRecordSaveDTO;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.application.account.ThirdPlatformAccountService;
import com.lyy.user.domain.user.entity.ThirdPlatformAccountRecord;
import com.lyy.user.domain.user.repository.ThirdPlatformAccountRecordMapper;
import com.lyy.user.interfaces.assembler.ThirdPlatformAccountAssembler;
import java.util.Date;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/4/23
 */
@Slf4j
@Service
public class ThirdPlatformAccountServiceImpl implements ThirdPlatformAccountService {

    @Resource
    private ThirdPlatformAccountRecordMapper thirdPlatformAccountRecordMapper;

    @Override
    public Boolean addThirdPlatformAccountRecord(ThirdPlatformAccountRecordSaveDTO dto) {
        ThirdPlatformAccountRecord thirdPlatformAccountRecord = ThirdPlatformAccountAssembler.INSTANCE
            .fromThirdPlatformAccountRecordSaveDTO(dto);
        thirdPlatformAccountRecord.setCreateBy(ofNullable(thirdPlatformAccountRecord.getCreateBy())
                .orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
        Date now = new Date();
        thirdPlatformAccountRecord.setCreateTime(now);
        thirdPlatformAccountRecord.setUpdateTime(now);
        thirdPlatformAccountRecord.setUpdateBy(thirdPlatformAccountRecord.getCreateBy());
        return thirdPlatformAccountRecordMapper.insert(thirdPlatformAccountRecord) > 0;
    }
}
