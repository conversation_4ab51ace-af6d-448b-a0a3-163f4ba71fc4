package com.lyy.user.domain.user.service.handler;

import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.app.interfaces.facade.dto.UserVO;
import com.lyy.user.domain.user.service.AbstractUserInitService;
import com.lyy.user.domain.user.service.UserInitHandler;
import com.lyy.user.infrastructure.execption.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @ClassName: xiaoleWechatThirdAppInit
 * @description: 乐仔生活微信第三方初始化
 * @author: jiangyk
 * @date: 2025/05/27
 **/
@Slf4j
@Service("xiaoleWechatThirdAppInit")
public class XiaoleWechatThirdAppInitHandler extends AbstractUserInitService implements UserInitHandler {

    private static final String PREFIX = "第三方APP微信登录: ";

    @Override
    public UserInfoDTO handle(UserCreateDTO dto) {
        log.debug(PREFIX + "微信小程序用户初始化");
        if (StringUtils.isBlank(dto.getUnionid())) {
            throw new BusinessException(UserErrorCode.USER_UNION_ID_EMPTY_ERROR.getCode(), UserErrorCode.USER_UNION_ID_EMPTY_ERROR.getMessage());
        }
        String telephone = "";
        UserVO userDTO = super.getByUnionId(dto.getUnionid());
        Long userId;
        String isActive = null;
        if (userDTO == null) {
            userId = super.miniProgramPlatformUserInit(dto);
        } else {
            telephone = userDTO.getTelephone();
            isActive = userDTO.getIsActive();
            userId = userDTO.getId();
            //初始化userApp
            super.initUserApp(dto.getAppId(), dto.getOpenid(), userDTO.getId());
        }

        return getUserInfoDTO(dto, userId, telephone, isActive);
    }
}
