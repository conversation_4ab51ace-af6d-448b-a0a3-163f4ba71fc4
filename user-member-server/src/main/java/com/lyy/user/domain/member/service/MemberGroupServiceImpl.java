package com.lyy.user.domain.member.service;

import cn.hutool.core.util.BooleanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.error.member.infrastructure.MemberErrorCode;
import com.lyy.user.account.infrastructure.constant.MemberGroupStatusEnum;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupInfoSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupRangCheckDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberLevelSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberLiftingRuleDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberLiftingSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberRangeAssociatedDTO;
import com.lyy.user.application.member.IMemberGroupService;
import com.lyy.user.application.member.IMemberLevelService;
import com.lyy.user.application.member.IMemberLiftingRuleService;
import com.lyy.user.application.member.IMemberLiftingService;
import com.lyy.user.application.member.IMemberRangeService;
import com.lyy.user.application.member.IMemberService;
import com.lyy.user.domain.member.entity.Member;
import com.lyy.user.domain.member.entity.MemberGroup;
import com.lyy.user.domain.member.entity.MemberLevel;
import com.lyy.user.domain.member.entity.MemberLifting;
import com.lyy.user.domain.member.entity.MemberLiftingRule;
import com.lyy.user.domain.member.helper.MemberGroupRedisHelper;
import com.lyy.user.domain.member.repository.MemberGroupMapper;
import com.lyy.user.domain.member.repository.MemberLevelMapper;
import com.lyy.user.infrastructure.base.MerchantBaseServiceImpl;
import com.lyy.user.infrastructure.constants.MemberGroupLiftingStrategyEnum;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.util.CacheGrayUtil;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 会员组表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
@Slf4j
@Service

public class MemberGroupServiceImpl extends MerchantBaseServiceImpl<MemberGroupMapper, MemberGroup> implements IMemberGroupService {

    @Autowired
    private IMemberRangeService memberRangeService;

    @Autowired
    private IMemberService memberService;

    @Autowired
    private IMemberLiftingService memberLiftingService;

    @Autowired
    private IMemberLiftingRuleService memberLiftingRuleService;

    @Autowired
    private IMemberLevelService memberLevelService;

    @Autowired
    private MemberLevelMapper memberLevelMapper;

    @Autowired
    private MemberGroupRedisHelper memberGroupRedisHelper;

    /**
     * 保存或更新会员组信息
     *
     * @param memberGroupSaveDTO
     * @return
     */
    @Override
    public Long saveOrUpdate(MemberGroupSaveDTO memberGroupSaveDTO) {
        MemberGroup memberGroup = new MemberGroup();
        BeanUtils.copyProperties(memberGroupSaveDTO, memberGroup, "memberRangeAssociatedList");
        memberGroup.setUpdateTime(new Date());
        if (CacheGrayUtil.memberGroupCacheGray(memberGroup.getMerchantId())) {
            saveOrUpdateWithFlag(memberGroupSaveDTO, memberGroup);
        } else {
            if (memberGroup.getId() == null) {
                memberGroup.setCreateTime(new Date());
                memberGroup.setCreateBy(memberGroupSaveDTO.getUpdateBy());
                //新增会员组默认启用
                memberGroup.setActive(true);
            } else {
                memberGroup.setCreateTime(null);
                memberGroup.setCreateBy(null);
            }
            super.saveOrUpdate(memberGroup);
        }
        if (memberGroupSaveDTO.getMemberRangeAssociatedList() != null && !memberGroupSaveDTO.getMemberRangeAssociatedList().isEmpty()) {
            memberRangeService.updateRangeAssociated(memberGroup.getMerchantId(), memberGroup.getId(), memberGroupSaveDTO.getMemberRangeAssociatedList());
        }
        return memberGroup.getId();
    }

    private void saveOrUpdateWithFlag(MemberGroupSaveDTO memberGroupSaveDTO,MemberGroup memberGroup) {
        if (memberGroup.getId() == null) {
            memberGroup.setCreateTime(new Date());
            memberGroup.setCreateBy(memberGroupSaveDTO.getUpdateBy());
            //新增会员组默认启用
            memberGroup.setActive(true);

            // 先设置Redis标记，确保查询时能感知到数据存在
            boolean redisSuccess = memberGroupRedisHelper.setMemberGroupFlag(memberGroup.getMerchantId(), true);
            if (!redisSuccess) {
                throw new BusinessException(GlobalErrorCode.INTERNAL_SERVER_ERROR.getCode(), "会员组存在标记设置失败");
            }
            save(memberGroup);
        } else {
            memberGroup.setCreateTime(null);
            memberGroup.setCreateBy(null);
            UpdateWrapper<MemberGroup> wrapper = new UpdateWrapper<MemberGroup>()
                    .eq(getShardingFieldKey(), memberGroup.getMerchantId())
                    .eq("id",memberGroup.getId());
            update(memberGroup, wrapper);
        }
    }

    /**
     * 查找会员组数据
     *
     * @param page
     * @param memberGroupDTO
     * @param memberGroupStatus
     * @return
     */
    @Override
    public Page<MemberGroup> findMemberGroupOfPage(Page page, MemberGroupDTO memberGroupDTO, Integer memberGroupStatus) {
        page.setSearchCount(false);
        QueryWrapper<MemberGroup> queryWrapper = createWrapper(memberGroupDTO);
        if(memberGroupStatus != null){
            if(memberGroupStatus.equals(MemberGroupStatusEnum.PUTTING.getStatus())){
                //投放中
                LocalDateTime localDateTime = LocalDateTime.now();
                queryWrapper.lt("start_date",localDateTime)
                        .gt("end_date",localDateTime)
                        .eq("is_active",true)
                        .eq("is_del",false);
                //创建时间倒序
                queryWrapper.orderByDesc("create_time");
            }else if(memberGroupStatus.equals(MemberGroupStatusEnum.STOPPED.getStatus())){
                //已停用
                queryWrapper.eq("is_active",false)
                        .eq("is_del",false);
                //创建时间倒序
                queryWrapper.orderByDesc("create_time");
            }else if(memberGroupStatus.equals(MemberGroupStatusEnum.EXPIRED.getStatus())){
                //已过期
                LocalDateTime localDateTime = LocalDateTime.now();
                queryWrapper.lt("end_date",localDateTime)
                        .eq("is_del",false);
                //创建时间倒序
                queryWrapper.orderByDesc("create_time");
            }else if(memberGroupStatus.equals(MemberGroupStatusEnum.REMOVED.getStatus())){
                //已删除
                queryWrapper.eq("is_del",true);
                //创建时间倒序
                queryWrapper.orderByDesc("create_time");
            }else if(memberGroupStatus.equals(MemberGroupStatusEnum.PENDING.getStatus())){
                //待生效
                LocalDateTime localDateTime = LocalDateTime.now();
                queryWrapper.gt("start_date",localDateTime)
                        .eq("is_active",true)
                        .eq("is_del",false);
                //创建时间倒序
                queryWrapper.orderByDesc("create_time");
            }else{
                queryWrapper.eq("is_del",false);
                //排序sql，已删除不用考虑
                String sortSql = "order by \n" +
                        //已停用
                        "case when is_active = false then 9 \n" +
                        //已过期
                        "else case when end_date IS not null and end_date < now() then 10 \n" +
                        //待生效
                        "else case when start_date IS not null and start_date > now() then 8\n" +
                        //生效中
                        "else 1 end  end \n" +
                        "end asc,\n" +
                        "create_time desc ";
                queryWrapper.last(sortSql);
            }
        }
        return super.page(page,queryWrapper);
    }

    /**
     * 根据id获取详情信息
     *
     *
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    @Override
    public MemberGroupSaveDTO getInfoById(Long merchantId, Long memberGroupId) {
        MemberGroup memberGroup = getById(merchantId,memberGroupId);
        MemberGroupSaveDTO memberGroupSaveDTO = new MemberGroupSaveDTO();
        if(memberGroup == null){
            return memberGroupSaveDTO;
        }
        BeanUtils.copyProperties(memberGroup,memberGroupSaveDTO);
        List<MemberRangeAssociatedDTO> memberRangeAssociatedList = memberRangeService.getRangeAssociatedList(merchantId, memberGroupId);
        memberGroupSaveDTO.setMemberRangeAssociatedList(memberRangeAssociatedList);
        return memberGroupSaveDTO;
    }

    /**
     * 删除会员组信息，并删除范围信息
     *
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    @Override
    public boolean removeByMemberGroup(Long merchantId, Long memberGroupId) {
        //删除会员组信息
        UpdateWrapper wrapper = new UpdateWrapper<MemberGroup>()
                .eq(getShardingFieldKey(),merchantId)
                .eq("id",memberGroupId)
                .set("is_del",true)
                .set("update_time",LocalDateTime.now())
                .set("update_by",0);
        update(wrapper);

        //删除会员信息
        UpdateWrapper memberWrapper = new UpdateWrapper<Member>()
                .eq(getShardingFieldKey(),merchantId)
                .eq("member_group_id",memberGroupId)
                .set("is_del",true)
                .set("update_time",LocalDateTime.now())
                .set("update_by",0);
        int del = memberService.getBaseMapper().update(null,memberWrapper);
        log.debug("删除 {} 会员组 {} 个会员数据",memberGroupId,del);
        return true;
    }

    /**
     * 根据会员组统计对应的会员信息
     * @param merchantId
     * @param memberGroupId
     * @param isEffective 是否有效，若为true，则表示会员未删除，并且处于有效时间内容
     * @return
     */
    @Override
    public Integer countMemberByGroup(Long merchantId, Long memberGroupId,boolean isEffective) {

        return getBaseMapper().countMemberByGroup(merchantId,memberGroupId,isEffective);
    }

    /**
     * 根据会员组统计各个等级对应的会员信息
     *
     *
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    @Override
    public Map<Long, Integer> countLevelMemberByGroup(Long merchantId, Long memberGroupId) {
        QueryWrapper<Member> queryWrapper = new QueryWrapper<Member>()
                .select("member_level_id", "count(1) as member_level_count")
                .eq(getShardingFieldKey(), merchantId)
                .groupBy("member_group_id", "member_level_id")
                .having("member_group_id = " + memberGroupId);
        List<Map<String, Object>> list = memberService.listMaps(queryWrapper);
        //转换为map数据
        Map<Long, Integer> countMap = new HashMap<>(list.size() * 2);
        for (Map<String, Object> map : list) {
            Object key = map.get("member_level_id");
            Object value = map.get("member_level_count");
            if (key instanceof Number && value instanceof Number) {
                countMap.put(((Number) key).longValue(), ((Number) value).intValue());
            }
        }
        return countMap;
    }

    @Override
    public MemberGroup getSmallVenueMemberGroup(Long merchantId) {
        List<MemberGroup> memberGroupList =  this.baseMapper.selectList(new QueryWrapper<MemberGroup>().eq("merchant_id",merchantId).orderByAsc("create_time"));
        if(memberGroupList == null || memberGroupList.size()==0){
            MemberGroup newMemberGroup = new MemberGroup();
            newMemberGroup.setMerchantId(merchantId);
            newMemberGroup.setName("小场地默认会员组");
            newMemberGroup.setActive(true);
            newMemberGroup.setCreateTime(new Date());
            newMemberGroup.setUpdateTime(new Date());
            newMemberGroup.setOpenMethod((short) 0);
            newMemberGroup.setLiftingStrategy(MemberGroupLiftingStrategyEnum.LIFTING_STRATEGY_UPGRADE.getValue());
            newMemberGroup.setRuleStrategy((short)1);
            newMemberGroup.setMemberEffectiveTime(-1);
            int resNum = this.baseMapper.insert(newMemberGroup);
            if(resNum != 1 ){
                throw new BusinessException(MemberErrorCode.MEMBER_GROUP_NOT_FOUND);
            }
            return newMemberGroup;
        }else {
            // 存在多个会员组，取最先创建的第一条
            return memberGroupList.get(0);
        }
    }

    /**
     * 根据范围查找到对应的会员组列表
     *
     * @param memberGroupRangCheckDTO
     * @return
     */
    @Override
    public List<MemberGroup> getMemberGroupListOfRange(MemberGroupRangCheckDTO memberGroupRangCheckDTO) {
        checkRange(memberGroupRangCheckDTO);

        if (!CacheGrayUtil.memberGroupCacheGray(memberGroupRangCheckDTO.getMerchantId())) {
            return getBaseMapper().getMemberGroupOfRange(memberGroupRangCheckDTO);
        }
        //需要返回null,没命中缓存继续往下走
        Boolean memberGroupFlag = memberGroupRedisHelper.getMemberGroupFlag(memberGroupRangCheckDTO.getMerchantId());
        // 不存在会员组
        if (BooleanUtil.isFalse(memberGroupFlag)) {
            return Collections.emptyList();
        }
        log.info("查询会员组，merchantId: {}", memberGroupRangCheckDTO.getMerchantId());

        // 查询会员组
        List<MemberGroup> memberGroups = getBaseMapper().getMemberGroupOfRange(memberGroupRangCheckDTO);
        boolean hasMemberGroups = CollectionUtils.isNotEmpty(memberGroups);

        // 缓存未命中，更新标记
        if (memberGroupFlag == null) {
            if (hasMemberGroups) {
                memberGroupRedisHelper.setMemberGroupFlag(memberGroupRangCheckDTO.getMerchantId(), true);
            } else {
                // 重新查询会员组确定是否存在
                hasMemberGroups = getBaseMapper().existMemberGroup(memberGroupRangCheckDTO.getMerchantId()) != null;
                memberGroupRedisHelper.setMemberGroupFlag(memberGroupRangCheckDTO.getMerchantId(), hasMemberGroups);
            }
            log.info("会员组标记更新，merchantId: {}, hasMemberGroups: {}", memberGroupRangCheckDTO.getMerchantId(), hasMemberGroups);
        }
        return memberGroups;
    }

    /**
     * 获取默认会员组信息
     *
     * @return
     */
    @Override
    public MemberGroupInfoSaveDTO getDefaultMemberGroup() {
        MemberGroupInfoSaveDTO memberGroupInfoSaveDTO = new MemberGroupInfoSaveDTO();
        MemberGroupSaveDTO memberGroupSaveDTO = new MemberGroupSaveDTO();
        memberGroupSaveDTO.setName("默认会员类型");
        memberGroupSaveDTO.setStartDate(new Date());
        memberGroupSaveDTO.setEndDate(DateUtils.addYears(new Date(),1));
        memberGroupSaveDTO.setActive(false);
        memberGroupSaveDTO.setOpenMethod((short)0);
        memberGroupSaveDTO.setLiftingStrategy(MemberGroupLiftingStrategyEnum.LIFTING_STRATEGY_UPGRADE.getValue());
        memberGroupSaveDTO.setRuleStrategy((short)1);
        memberGroupSaveDTO.setMemberRangeAssociatedList(Collections.emptyList());
        memberGroupInfoSaveDTO.setMemberGroupSaveDTO(memberGroupSaveDTO);
        memberGroupInfoSaveDTO.setMemberLevelList(getDefaultMemberLevel());
        memberGroupInfoSaveDTO.setMemberLiftingList(Collections.emptyList());
        return memberGroupInfoSaveDTO;
    }

    /**
     * 更新会员组状态
     *
     * @param memberGroupDTO
     * @return
     */
    @Override
    public Boolean updateStatus(MemberGroupDTO memberGroupDTO) {
        UpdateWrapper<MemberGroup> wrapper = new UpdateWrapper<MemberGroup>()
                .set("update_time",LocalDateTime.now())
                .set(memberGroupDTO.getUpdateBy()!= null,"update_by",memberGroupDTO.getUpdateBy())
                .set("is_active",memberGroupDTO.getActive())
                .eq(getShardingFieldKey(), memberGroupDTO.getMerchantId())
                .eq("id",memberGroupDTO.getId());
        return update(wrapper);
    }

    /**
     * 统计某个名称的会员组数量
     * @param merchantId 商户id
     * @param name  会员组名称
     * @param notIdList 排除的id
     * @return
     */
    @Override
    public int countMemberGroupOfName(Long merchantId, String name, List<Long> notIdList) {
        QueryWrapper<MemberGroup> wrapper = new QueryWrapper<MemberGroup>()
                .eq("merchant_id",merchantId)
                .eq("name",name)
                .notIn(CollectionUtils.isNotEmpty(notIdList),"id",notIdList);
        return (int)count(wrapper);
    }

    @Override
    public MemberGroupInfoSaveDTO getSmallVenueDefaultMemberGroup(Long merchantId) {
        // 查询默认会员组
        MemberGroup memberGroup =  this.getSmallVenueMemberGroup(merchantId);
        MemberGroupInfoSaveDTO memberGroupInfoSaveDTO = new MemberGroupInfoSaveDTO();
        MemberGroupSaveDTO memberGroupSaveDTO = new MemberGroupSaveDTO();
        memberGroupSaveDTO.setId(memberGroup.getId());
        memberGroupSaveDTO.setName(memberGroup.getName());
        memberGroupSaveDTO.setActive(false);
        memberGroupSaveDTO.setOpenMethod(memberGroup.getOpenMethod());
        memberGroupSaveDTO.setLiftingStrategy(memberGroup.getLiftingStrategy());
        memberGroupSaveDTO.setRuleStrategy(memberGroup.getRuleStrategy());
        memberGroupSaveDTO.setMemberRangeAssociatedList(Collections.emptyList());
        memberGroupInfoSaveDTO.setMemberGroupSaveDTO(memberGroupSaveDTO);
        initSmallVenueMemberLevel(memberGroup.getId(),merchantId);
        memberGroupInfoSaveDTO.setMemberLiftingList(Arrays.asList(getDefaultMemberLifting(merchantId,memberGroup.getId())));
        return memberGroupInfoSaveDTO;
    }

    @Override
    public Integer countEffectiveMemberByGroup(Long merchantId, Long memberGroupId) {
        return getBaseMapper().countEffectiveMemberByGroup(merchantId, memberGroupId);
    }

    /**
     * 初始化小场地 会员组等级
     * @param memberGroupId
     * @param merchantId
     */
    private void  initSmallVenueMemberLevel(Long memberGroupId,Long merchantId){
        Long count = memberLevelMapper.selectCount(new QueryWrapper<MemberLevel>().eq("merchant_id",merchantId).eq("member_group_id",memberGroupId));
        if(count == null || count.intValue() == 0){
            log.debug("创建默认会员等级，merchantId:{}",merchantId);
            // 创建默认的会员级别
            MemberLevel defaultMemberLevel = new MemberLevel();
            defaultMemberLevel.setMemberGroupId(memberGroupId);
            defaultMemberLevel.setMerchantId(merchantId);
            defaultMemberLevel.setName("vip1");
            defaultMemberLevel.setGrowValue(0L);
            defaultMemberLevel.setCreateTime(new Date());
            defaultMemberLevel.setUpdateTime(new Date());
            memberLevelMapper.insert(defaultMemberLevel);
        }
    }


    /**
     * 默认会员等级信息
     * @return
     */
    private List<MemberLevelSaveDTO> getDefaultMemberLevel() {
        List<MemberLevelSaveDTO> list = new ArrayList<>(6);
        MemberLevelSaveDTO level1 = new MemberLevelSaveDTO();

        list.add(level1);
        level1.setGrowValue(100L);
        level1.setName("一级");
        level1.setMemberRuleList(Collections.emptyList());

        MemberLevelSaveDTO level2 = new MemberLevelSaveDTO();
        list.add(level2);
        level2.setGrowValue(250L);
        level2.setName("二级");
        level2.setMemberRuleList(Collections.emptyList());

        MemberLevelSaveDTO level3 = new MemberLevelSaveDTO();
        list.add(level3);
        level3.setGrowValue(500L);
        level3.setName("三级");
        level3.setMemberRuleList(Collections.emptyList());

        MemberLevelSaveDTO level4 = new MemberLevelSaveDTO();
        list.add(level4);
        level4.setGrowValue(1000L);
        level4.setName("四级");
        level4.setMemberRuleList(Collections.emptyList());

        MemberLevelSaveDTO level5 = new MemberLevelSaveDTO();
        list.add(level5);
        level5.setGrowValue(2000L);
        level5.setName("五级");
        level5.setMemberRuleList(Collections.emptyList());

        return list;
    }


    private MemberLiftingSaveDTO getDefaultMemberLifting(Long merchantId, Long memberGroupId) {
        MemberLiftingSaveDTO memberLiftingSaveDTO = new MemberLiftingSaveDTO();
        MemberLifting memberLifting = memberLiftingService.getSmallVenueLiftingInfoByGroup(merchantId, memberGroupId);
        BeanUtils.copyProperties(memberLifting, memberLiftingSaveDTO);

        List<MemberLiftingRuleDTO> memberLiftingRuleDTOList = new ArrayList<>();
        List<MemberLiftingRule> memberLiftingRuleList = memberLiftingRuleService.findByMemberLifting(merchantId, memberLifting.getId());
        if (CollectionUtils.isEmpty(memberLiftingRuleList)) {
            //创建一条默认规则
            MemberLiftingRuleDTO memberLiftingRuleDTO = new MemberLiftingRuleDTO();
            memberLiftingRuleDTO.setMemberLiftingId(memberLifting.getId());
            memberLiftingRuleDTO.setMerchantId(merchantId);
            MemberLiftingRule memberLiftingRule = memberLiftingRuleService.saveDefaultMemberLiftingRule(memberLiftingRuleDTO);
            MemberLiftingRuleDTO memberLiftingRuleDTO1 = new MemberLiftingRuleDTO();
            BeanUtils.copyProperties(memberLiftingRule, memberLiftingRuleDTO1);
            memberLiftingRuleDTOList.add(memberLiftingRuleDTO1);
            memberLiftingSaveDTO.setRuleList(memberLiftingRuleDTOList);
        } else {
            memberLiftingRuleDTOList = memberLiftingRuleList.stream().map(memberLiftingRule -> {
                MemberLiftingRuleDTO memberLiftingRuleDTO = new MemberLiftingRuleDTO();
                BeanUtils.copyProperties(memberLiftingRule, memberLiftingRuleDTO);
                return memberLiftingRuleDTO;
            }).collect(Collectors.toList());
            memberLiftingSaveDTO.setRuleList(memberLiftingRuleDTOList);
        }
        return memberLiftingSaveDTO;
    }

    /**
     * 检查范围参数是否合法
     * @param memberGroupRangCheckDTO
     */
    private void checkRange(MemberGroupRangCheckDTO memberGroupRangCheckDTO) {
        if (memberGroupRangCheckDTO.getMerchantId() == null) {
            throw new BusinessException(GlobalErrorCode.BAD_REQUEST.getCode(), "商户ID不能为空");
        }
    }

    /**
     * 创建查询的包装
     * @param memberGroupDTO
     * @return
     */
    private QueryWrapper<MemberGroup> createWrapper(MemberGroupDTO memberGroupDTO) {
        QueryWrapper<MemberGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq( "merchant_id", memberGroupDTO.getMerchantId())
                .eq(memberGroupDTO.getOpenMethod()!=null,"open_method",memberGroupDTO.getOpenMethod())
                .eq(memberGroupDTO.getLiftingStrategy()!=null,"lifting_strategy",memberGroupDTO.getLiftingStrategy())
                .eq(memberGroupDTO.getRuleStrategy()!=null,"rule_strategy",memberGroupDTO.getRuleStrategy())
                .like(StringUtils.isNotBlank(memberGroupDTO.getName()),"name","%" + memberGroupDTO.getName()+"%");
        return queryWrapper;
    }


}
