package com.lyy.user.domain.user.service;

import static java.util.Optional.ofNullable;

import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneDTO;
import com.lyy.user.app.interfaces.facade.dto.UserAppDTO;
import com.lyy.user.app.interfaces.facade.dto.UserDTO;
import com.lyy.user.app.interfaces.facade.dto.UserVO;
import com.lyy.user.application.member.IMemberService;
import com.lyy.user.application.statistics.StatisticsService;
import com.lyy.user.application.user.IMerchantUserService;
import com.lyy.user.application.user.IPlatformUserPhoneService;
import com.lyy.user.domain.account.dto.AccountInitDTO;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.domain.user.entity.PlatformUserPhone;
import com.lyy.user.domain.user.repository.PlatformUserPhoneMapper;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.repository.account.SmallVenueAccountRepository;
import com.lyy.user.infrastructure.repository.user.MerchantUserRepository;
import com.lyy.user.infrastructure.repository.user.UserRepository;
import com.lyy.user.interfaces.assembler.UserAssembler;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName: AbstractUserInitService
 * @description: 用户初始化抽象类
 * @author: pengkun
 * @date: 2021/04/06
 **/
@Slf4j
public abstract class AbstractUserInitService {

    /**
     * unionId授权模式
     */
    public static final String UNION_AUTH_MODEL = "1";

    public static final int BETWEEN_DAY = 182;

    @Autowired
    protected IMerchantUserService merchantUserService;

    @Autowired
    private IPlatformUserPhoneService platformUserPhoneService;

    @Autowired
    private StatisticsService statisticsService;

    @Resource(name = "smallVenueAccountRepository")
    private SmallVenueAccountRepository smallVenueAccountRepository;
    
    @Autowired
    private IMemberService iMemberService;

    @Resource
    protected UserRepository userRepository;
    @Resource
    private MerchantUserRepository merchantUserRepository;

    @Resource
    private PlatformUserPhoneMapper platformUserPhoneMapper;

    /**
     * 根据userId获取平台用户信息
     *
     * @param userId 用户id
     * @return
     */
    protected UserVO getPlatformUserByUserId(Long userId) {
        return userRepository.getByUserId(userId);
    }

    protected UserVO getByOpenId(String openId) {
        return userRepository.getByOpenIdV2(openId);
    }

    protected UserVO getByUnionId(String unionId) {
        return userRepository.getByUnionIdV2(unionId);
    }

    protected UserVO getByAppIdAndOpenId(String appId, String openId) {
        return userRepository.getByAppIdAndOpenIdV2(appId, openId);
    }

    /**
     * 更新用户的微信信息
     *
     * @param dto
     * @param unionUser
     * @param userDTO
     */
    protected void updateUserWx(UserCreateDTO dto, UserVO unionUser, UserVO userDTO) {
        userDTO.setId(unionUser.getId());
        updateUserWx(dto, userDTO);
    }

    /**
     * 更新用户的微信信息
     *
     * @param dto
     * @param userDTO
     */
    protected void updateUserWx(UserCreateDTO dto, UserVO userDTO) {
        UserVO updateDTO =  UserAssembler.INSTANCE.toUserVO(dto);
        updateDTO.setId(userDTO.getId());
        updatePlatformUser(updateDTO);
    }

    /**
     * 更新平台用户信息
     *
     * @param userVO
     */
    protected void updatePlatformUser(UserVO userVO) {
        UserDTO userDTO = UserAssembler.INSTANCE.toUserDTO(userVO);
        log.debug("更新平台用户信息,userVO:{}", userDTO);
        userDTO.setUpdated(new Date());
        try {
            userRepository.insertOrUpdateUser(userDTO);
        } catch (Exception e) {
            log.error("更新平台用户信息失败:{}", userDTO);
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 初始化用户App,公众号关联信息
     *
     * @param appId  appId
     * @param openId openId
     * @param userId userId
     */
    protected void initUserApp(String appId, String openId, Long userId) {
        log.debug("初始化用户App,公众号关联信息,appId:{},openId:{},userId:{}", appId, openId, userId);
        if (StringUtils.isBlank(appId)) {
            return;
        }
        UserAppDTO userAppDTO = new UserAppDTO();
        userAppDTO.setAppId(appId);
        userAppDTO.setOpenId(openId);
        userAppDTO.setUnFollow("Y");
        userAppDTO.setUserId(userId);
        UserAppDTO userApp = userRepository.insertOrUpdateUserApp(userId, openId, appId);
        if (Objects.isNull(userApp)) {
            log.error("USER_APP 初始化失败 userId {}, openId {} , appId {}", userId, openId, appId);
            throw new BusinessException(UserErrorCode.USER_APP_INIT_ERROR);
        }
    }

    /**
     * 初始化平台用户
     *
     * @param dto
     * @return
     */
    protected Long initPlatformUser(UserCreateDTO dto) {
        return initPlatformUser(dto, false);
    }

    /**
     * 初始化平台用户信息
     *
     * @param dto
     * @return
     */
    protected Long initPlatformUser(UserCreateDTO dto, boolean flag) {
        if (log.isDebugEnabled()) {
            log.debug("初始化平台用户信息,dto:{}", dto);
        }
        UserDTO userDTO = UserAssembler.INSTANCE.fromUserCreateDTO(dto);
        UserDTO user = userRepository.insertOrUpdateUser(userDTO);
        if (Objects.isNull(user)) {
            log.error("平台用户初始化失败 request {}", userDTO);
            throw new BusinessException(UserErrorCode.PLATFORM_USER_SAVE_ERROR);
        }
        Long userId = user.getId();

        if (StringUtils.isNotBlank(dto.getTelephone()) && (dto.getMerchantUserCreateDTO() == null || flag)) {
            //保存平台用户手机号码记录
            PlatformUserPhoneDTO userPhoneDTO = new PlatformUserPhoneDTO();
            userPhoneDTO.setUserId(userId);
            userPhoneDTO.setMerchantId(UserMemberSysConstants.PLATFORM_MERCHANT_ID);
            userPhoneDTO.setTelephone(dto.getTelephone());
            userPhoneDTO.setOperatorId(ofNullable(dto.getOperationUserId()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
            if (flag) {
                userPhoneDTO.setSystemFlag(UserMemberSysConstants.SMALL_VENUE_SYSTEM_FLAG);
            }
            platformUserPhoneService.saveOrUpdatePlatformUserPhone(userPhoneDTO);
        }
        return userId;
    }

    /**
     * 用户禁用
     *
     * @param oldUser 旧用户id
     * @param newUser 新用户id
     * @return
     */
    protected void updateUserAndIgnore(Long oldUser, Long newUser) {
        userRepository.updateUserAndIgnore(oldUser, newUser);
        log.info("用户禁用, oldUser:{}, newUser:{} ", oldUser, newUser);
    }

    /**
     * 初始户商户用户
     *
     * @param dto
     * @return
     */
    protected Long initMerchantUser(UserCreateDTO dto) {
        UserCreateDTO.MerchantUserCreateDTO merchantUserCreateDTO = dto.getMerchantUserCreateDTO();
        MerchantUser merchantUser = merchantUserService.findByUserIdAndMerchantId(merchantUserCreateDTO.getUserId(), merchantUserCreateDTO.getMerchantId());
        if (merchantUser != null) {
            return merchantUser.getId();
        }
        MerchantUserDTO merchantUserDTO = assembleMerchantUserDTO(dto, merchantUserCreateDTO);
        Long merchantUserId = merchantUserService.saveOrUpdateMerchantUser(merchantUserDTO);
        //初始化商户用户统计
        initMerchantUserStatistics(merchantUserDTO.getUserId(), merchantUserId, merchantUserDTO.getMerchantId());
        return merchantUserId;
    }

    protected Long initSmallVenueMerchantUser(UserCreateDTO dto) {
        log.debug("初始户小场地商户用户");
        UserCreateDTO.MerchantUserCreateDTO merchantUserCreateDTO = dto.getMerchantUserCreateDTO();
        MerchantUser merchantUser = merchantUserService.findByUserIdAndMerchantId(merchantUserCreateDTO.getUserId(), merchantUserCreateDTO.getMerchantId());
        if (merchantUser != null) {
            log.warn("商户用户已初始化,dto:{}", dto);
            if (StringUtils.isNotBlank(merchantUserCreateDTO.getTelephone())) {
                if (!Objects.equals(merchantUser.getTelephone(), merchantUserCreateDTO.getTelephone())) {
                    //如果手机号不相同
                    MerchantUser updateMerchantUser = new MerchantUser();
                    updateMerchantUser.setId(merchantUser.getId());
                    updateMerchantUser.setMerchantId(merchantUser.getMerchantId());
                    updateMerchantUser.setTelephone(merchantUserCreateDTO.getTelephone());
                    merchantUserService.updateByIdAndMerchantId(updateMerchantUser);
                }
                UserVO userDTO = getPlatformUserByTelephone(merchantUserCreateDTO.getTelephone(), merchantUserCreateDTO.getMerchantId());
                if (userDTO == null) {
                    log.info("补充多金宝用户手机信息,{},{},{}", merchantUserCreateDTO.getMerchantId(), merchantUserCreateDTO.getUserId(),
                            merchantUserCreateDTO.getTelephone());
                    PlatformUserPhoneDTO userPhoneDTO = new PlatformUserPhoneDTO();
                    userPhoneDTO.setUserId(merchantUserCreateDTO.getUserId());
                    userPhoneDTO.setMerchantId(merchantUserCreateDTO.getMerchantId());
                    userPhoneDTO.setTelephone(merchantUserCreateDTO.getTelephone());
                    userPhoneDTO.setMerchantUserId(merchantUser.getId());
                    userPhoneDTO.setOperatorId(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID);
                    userPhoneDTO.setSystemFlag(UserMemberSysConstants.SMALL_VENUE_SYSTEM_FLAG);
                    platformUserPhoneService.saveOrUpdatePlatformUserPhone(userPhoneDTO);
                }
            }
            return merchantUser.getId();
        }
        MerchantUserDTO merchantUserDTO = assembleMerchantUserDTO(dto, merchantUserCreateDTO);
        Long merchantUserId = merchantUserService.saveOrUpdateMerchantUser(merchantUserDTO, dto.getPassword(), UserMemberSysConstants.SMALL_VENUE_SYSTEM_FLAG);
        //初始化商户用户统计
        initMerchantUserStatistics(merchantUserDTO.getUserId(), merchantUserId, merchantUserDTO.getMerchantId());
        return merchantUserId;
    }

    /**
     * 小程序初始化平台用户
     *
     * @param dto
     * @return
     */
    protected Long miniProgramPlatformUserInit(UserCreateDTO dto) {
        UserDTO userDTO = UserAssembler.INSTANCE.fromUserCreateDTO(dto);
        Long userId = userRepository.signUp(userDTO);
        if (userId == null) {
            log.error("小程序初始化平台用户失败 {}", dto);
            throw new BusinessException(UserErrorCode.PLATFORM_USER_SAVE_ERROR);
        }
        //初始化平台用户统计
        initPlatformUserStatistics(userId);
        if (StringUtils.isNotBlank(dto.getTelephone()) && dto.getMerchantUserCreateDTO() == null) {
            //保存平台用户手机号码记录
            PlatformUserPhoneDTO userPhoneDTO = new PlatformUserPhoneDTO();
            userPhoneDTO.setUserId(userId);
            userPhoneDTO.setMerchantId(UserMemberSysConstants.PLATFORM_MERCHANT_ID);
            userPhoneDTO.setTelephone(dto.getTelephone());
            userPhoneDTO.setOperatorId(ofNullable(dto.getOperationUserId()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
            platformUserPhoneService.saveOrUpdatePlatformUserPhone(userPhoneDTO);
        }
        return userId;
    }

    /**
     * 平台用户统计初始化(考虑存在的情况)
     *
     * @param userId 平台用户Id
     */
    protected void initPlatformUserStatistics(Long userId) {
//        log.debug("平台用户统计初始化,userId:{}",userId);
        //TODO
    }

    /**
     * 商户用户统计初始化(考虑存在的情况)
     *
     * @param userId         平台用户Id
     * @param merchantUserId 商户用户Id
     * @param merchantId     商户Id
     */
    protected void initMerchantUserStatistics(Long userId, Long merchantUserId, Long merchantId) {
        statisticsService.initStatistics(merchantId, userId, merchantUserId);
    }

    /**
     * 初始户商户用户
     * @param dto
     * @param flag
     * @return
     */
    protected Long initPlatformMerchantUser(UserCreateDTO dto, boolean flag) {
        if (log.isDebugEnabled()) {
            log.debug("初始户平台的商户用户,dto:{},flag:{}", dto, flag);
        }
        UserCreateDTO.MerchantUserCreateDTO merchantUserCreateDTO = dto.getMerchantUserCreateDTO();
        MerchantUser merchantUser = merchantUserService.findByUserIdAndMerchantId(merchantUserCreateDTO.getUserId(), merchantUserCreateDTO.getMerchantId());
        if (merchantUser != null) {
            if (!flag) {
                return merchantUser.getId();
            }
        }
        MerchantUserDTO merchantUserDTO = assembleMerchantUserDTO(dto, merchantUserCreateDTO);
        Long merchantUserId = merchantUserService.saveOrUpdateMerchantUser(merchantUserDTO);
        //初始化商户用户统计
        initMerchantUserStatistics(merchantUserDTO.getUserId(), merchantUserId, merchantUserDTO.getMerchantId());
        return merchantUserId;
    }

    /**
     * 获取商户用户信息
     *
     * @param dto
     * @return
     * @throws BusinessException
     */
    protected MerchantUser getMerchantUser(UserCreateDTO dto) throws BusinessException {
        MerchantUser merchantUser = null;
        if (StringUtils.isNotBlank(dto.getTelephone()) && dto.getMerchantUserCreateDTO() != null
                && dto.getMerchantUserCreateDTO().getMerchantId() != null) {
            //根据手机号码和商户id获取多金宝商户用户信息
            PlatformUserPhone platformUserPhone = platformUserPhoneMapper.selectVenue(dto.getMerchantUserCreateDTO().getMerchantId(), dto.getTelephone());
            if (platformUserPhone != null) {
                merchantUser = merchantUserRepository.getByUserIdAndMerchantId(platformUserPhone.getUserId(), dto.getMerchantUserCreateDTO().getMerchantId());
            }
        }
        return merchantUser;
    }

    /**
     * 初始化账户
     *
     * @param dto            用户创建参数
     * @param userId         用户id
     * @param merchantUserId 商户用户id
     */
    protected void initSmallVenAccount(UserCreateDTO dto, Long userId, Long merchantUserId) {
        UserCreateDTO.MerchantUserCreateDTO merchantUserCreateDTO = dto.getMerchantUserCreateDTO();
        if (StringUtils.isBlank(dto.getCardNo()) || merchantUserCreateDTO == null) {
            return;
        }
        //初始化账户
        smallVenueAccountRepository.initAccount(assembleAccount(merchantUserCreateDTO.getMerchantId(),
                userId, merchantUserId, merchantUserCreateDTO.getStoreId(), true, dto));

    }

    /**
     * 根据手机号和商户id获取平台用户id
     *
     * @param telephone  手机号
     * @param merchantId 商户id
     * @return
     */
    protected UserVO getPlatformUserByTelephone(String telephone, Long merchantId) {
        if (StringUtils.isBlank(telephone) || merchantId == null) {
            return null;
        }
        //查询多金宝商户
        PlatformUserPhone platformUserPhone = platformUserPhoneMapper.selectVenue(merchantId, telephone);
        UserVO userDTO = null;
        if (platformUserPhone != null) {
            userDTO = new UserVO();
            userDTO.setId(platformUserPhone.getUserId());
        }
        return userDTO;
    }

    /**
     * 初始化会员等级
     *
     * @param userId         会员id
     * @param merchantId     商户id
     */
    protected void initUserMember(Long userId, Long merchantId) {
        try {
            log.info("初始化多金宝会员等级,userId:{},merchantId:{}", userId, merchantId);
            iMemberService.initSmallVenueUserMemberLevel(merchantId, userId);
        } catch (Exception e) {
            log.error("初始化会员等级信息失败,userId:{},merchantId:{}", userId, merchantId);
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 更新商户用户信息
     *
     * @param merchantUser 商户用户信息
     */
    protected void updateMerchantUserInfo(MerchantUser merchantUser) {
        merchantUserService.updateByIdAndMerchantId(merchantUser);
    }

    /**
     * 更新平台用户手机号记录状态
     *
     * @param userId     平台用户id
     * @param merchantId 商户id
     * @param active     状态
     * @param operatorId 操作人
     */
    protected void updatePlatformUserTelephoneActive(Long userId, Long merchantId, boolean active, Long operatorId) {
        platformUserPhoneMapper.updateByMerchantIdAndUserId(userId, merchantId, active,
                ofNullable(operatorId).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
    }


    private AccountInitDTO assembleAccount(Long merchantId, Long userId, Long merchantUserId,
                                           Long storeId, Boolean defaultFlag, UserCreateDTO dto) {
        AccountInitDTO initDTO = new AccountInitDTO();
        initDTO.setCardNo(dto.getCardNo());
        initDTO.setUserId(userId);
        initDTO.setMerchantId(merchantId);
        initDTO.setOperatorId(dto.getOperationUserId());
        initDTO.setDeposit(dto.getDeposit());
        initDTO.setDefaultFlag(defaultFlag);
        initDTO.setTotal(BigDecimal.ZERO);
        initDTO.setBalance(BigDecimal.ZERO);
        initDTO.setStoreId(storeId);
        initDTO.setClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        initDTO.setMerchantUserId(merchantUserId);
        initDTO.setDownTime(dto.getDownTime());
        return initDTO;
    }

    protected MerchantUserDTO assembleMerchantUserDTO(UserCreateDTO dto, UserCreateDTO.MerchantUserCreateDTO merchantUserCreateDTO) {
        MerchantUserDTO merchantUserDTO = new MerchantUserDTO();
        BeanUtils.copyProperties(merchantUserCreateDTO, merchantUserDTO);
        merchantUserDTO.setProvince(dto.getProvince());
        merchantUserDTO.setCityName(dto.getCityName());
        merchantUserDTO.setName(dto.getNickname());
        merchantUserDTO.setUserType(dto.getUserSourceEnum().getUserType());
        merchantUserDTO.setGender(dto.getGender());
        merchantUserDTO.setBirthday(dto.getBirthday());
        merchantUserDTO.setHeadImg(dto.getHeadImg());
        merchantUserDTO.setStoreName(StringUtils.isNotBlank(merchantUserCreateDTO.getStoreName()) ? merchantUserCreateDTO.getStoreName() : null);
        merchantUserDTO.setProvinceId(dto.getProvinceId());
        merchantUserDTO.setCityId(dto.getCityId());
        merchantUserDTO.setRegionId(dto.getRegionId());
        merchantUserDTO.setAddress(StringUtils.isNotBlank(dto.getAddress()) ? dto.getAddress() : null);
        return merchantUserDTO;
    }

    protected UserVO getByUnionIdOrOpenId(String unionId, String openId) {
        return userRepository.getByUnionIdOrOpenIdV2(unionId, openId);
    }

    protected UserInfoDTO getUserInfoDTO(UserCreateDTO dto, Long userId, String telephone, String isActive) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setLyyUserId(userId);
        userInfoDTO.setIsactive(isActive);
        if(Objects.nonNull(dto.getMerchantUserCreateDTO())
                && dto.getMerchantUserCreateDTO().getMerchantId() != null
                && dto.getMerchantUserCreateDTO().getMerchantId() > 0){
            //创建商户用户
            dto.getMerchantUserCreateDTO().setUserId(ofNullable(dto.getMerchantUserCreateDTO().getUserId()).orElse(userInfoDTO.getLyyUserId()));
            if(StringUtils.isBlank(dto.getMerchantUserCreateDTO().getTelephone())){
                if(StringUtils.isNotBlank(telephone)){
                    dto.getMerchantUserCreateDTO().setTelephone(telephone);
                }else {
                    dto.getMerchantUserCreateDTO().setTelephone(dto.getTelephone());
                }
            }
            userInfoDTO.setMerchantId(dto.getMerchantUserCreateDTO().getMerchantId());
            Long merchantUserId = initMerchantUser(dto);
            userInfoDTO.setMerchantUserId(merchantUserId);
        }
        return userInfoDTO;
    }
}
