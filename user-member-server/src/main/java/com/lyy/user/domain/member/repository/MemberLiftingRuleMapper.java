package com.lyy.user.domain.member.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lyy.user.domain.member.dto.MemberLiftingRuleTouchCheckDTO;
import com.lyy.user.domain.member.dto.MemberLiftingRuleTouchResultDTO;
import com.lyy.user.domain.member.entity.MemberLiftingRule;
import java.util.List;

/**
 * <p>
 * 会员升级策略规则 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
public interface MemberLiftingRuleMapper extends BaseMapper<MemberLiftingRule> {

    MemberLiftingRule selectByIdForUpdate(Long id);
    /**
     * 触发的规则
     * @param memberLiftingRuleTouchCheckDTO
     * @return
     */
    List<MemberLiftingRuleTouchResultDTO> touchLiftingRule(MemberLiftingRuleTouchCheckDTO memberLiftingRuleTouchCheckDTO);
}
