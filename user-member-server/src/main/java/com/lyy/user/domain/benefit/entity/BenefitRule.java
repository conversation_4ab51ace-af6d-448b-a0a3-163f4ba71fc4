package com.lyy.user.domain.benefit.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 *  权益规则表
 *
 * <AUTHOR>
 * @create 2021/3/30 17:27
 */
@ToString
@Getter
@Setter
@TableName(value = "um_benefit_rule")
public class BenefitRule {


    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 权益ID
     */
    @TableField(value="benefit_id")
    private Long benefitId;

    /**
     * 生成方式
     */
    @TableField(value="generate_type")
    private Integer generateType;


    /**
     * 权益分类（类型）
     */
    @TableField(value="benefit_classify")
    private Integer benefitClassify;

    /**
     * 权益值
     */
    @TableField(value="rule_value")
    private Integer ruleValue;

    /**
     * 权益单位
     */
    @TableField(value="rule_unit")
    private String ruleUnit;

    /**
     * 商户ID 用于分表
     */
    @TableField(value = "merchant_id")
    private Long merchantId;

    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    private LocalDateTime updateTime;

}
