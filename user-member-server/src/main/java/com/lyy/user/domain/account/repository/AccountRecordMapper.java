package com.lyy.user.domain.account.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lyy.user.account.infrastructure.account.dto.AccountAdjustRecordDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordCountDTO;
import com.lyy.user.account.infrastructure.account.dto.request.AccountAdjustRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitDetailSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalRecordSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueRecordListDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueRecordSelectDTO;
import com.lyy.user.domain.account.entity.AccountRecord;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AccountRecordMapper extends BaseMapper<AccountRecord> {

    Integer insertBatch(List<AccountRecord> list);


    /**
     * 根据条件获取消费记录
     *
     * @param orderNo        订单号
     * @param merchantId     商户id
     * @param userId         用户id
     * @param merchantUserId 商户用户id
     * @param classify       消耗的权益类型
     *
    /**
     * 开始时间
     */
    List<AccountRecord> findAllByOrderNo(@Param("orderNo") String orderNo,
                                         @Param("merchantId") Long merchantId,
                                         @Param("userId") Long userId,
                                         @Param("merchantUserId") Long merchantUserId,
                                         @Param("classify") List<Integer> classify,
                                         @Param("mode") Integer mode,
                                         @Param("startTime") Date startTime,
                                         @Param("endTime") Date endTime
                                         );

    /**
     * 获取权益明细使用记录
     * @param userId    用户id
     * @param merchantId    商户id
     * @param accountBenefitIds 权益明细id
     * @param recordTypes   流水类型
     * @return
     */
    List<AccountRecord> findByAccountBenefit(@Param("userId") Long userId,
                                             @Param("merchantId") Long merchantId,
                                             @Param("accountBenefitIds") List<Long> accountBenefitIds,
                                             @Param("recordTypes") List<Integer> recordTypes,
                                             @Param("merchantUserId") Long merchantUserId);

    /**
     * 商品购买记录、商品销售记录、兑换记录、商品回收记录、设备消费记录、储值变更记录查询
     * @param page
     * @param smallVenueRecordSelectDTO
     * @return
     */
    IPage<SmallVenueRecordListDTO> smallVenueRecord(IPage page, @Param("selectDTO") SmallVenueRecordSelectDTO smallVenueRecordSelectDTO);


    /**
     * 查询本日已使用数量
     *
     * @param benefitDetailSelectDTO
     * @return
     */
    BigDecimal accountBenefitTodayUseNum(@Param("selectDTO") BenefitDetailSelectDTO benefitDetailSelectDTO);

    /**
     * 流水数量统计
     * @param accountRecordCountDTO
     * @return
     */
    Long countRecord(@Param("accountRecordCountDTO") AccountRecordCountDTO accountRecordCountDTO);

    /**
     * 移动端账户流水列表
     * @param page
     * @param mobileTerminalRecordSelectDTO
     * @return
     */
    IPage<SmallVenueRecordListDTO> mobileTerminalRecord(IPage page, @Param("selectDTO") MobileTerminalRecordSelectDTO mobileTerminalRecordSelectDTO);

    IPage<AccountAdjustRecordDTO> listGrantCoinsRecord(IPage page,@Param("param") AccountAdjustRecordQueryDTO param,@Param("numericKeyword") String numericKeyword,@Param("stringKeyword") String stringKeyword);

    Long countGrantCoinsRecord(IPage page,@Param("param") AccountAdjustRecordQueryDTO param,@Param("numericKeyword") String numericKeyword,@Param("stringKeyword") String stringKeyword);

}