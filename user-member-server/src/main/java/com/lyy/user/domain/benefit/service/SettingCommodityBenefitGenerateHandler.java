package com.lyy.user.domain.benefit.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lyy.user.account.infrastructure.benefit.dto.GenerateAccountDTO;
import com.lyy.user.account.infrastructure.constant.ApplicableEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.BenefitGenerateTypeEnum;
import com.lyy.user.domain.benefit.entity.Benefit;
import com.lyy.user.domain.benefit.entity.BenefitRule;
import com.lyy.user.domain.benefit.entity.BenefitScope;
import com.lyy.user.domain.benefit.repository.BenefitMapper;
import com.lyy.user.domain.benefit.repository.BenefitRuleMapper;
import com.lyy.user.domain.benefit.repository.BenefitScopeMapper;
import com.lyy.user.infrastructure.constants.CommodityCategoryEnum;
import com.lyy.user.infrastructure.constants.CommodityClassifyCodeEnum;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * 设置商品权益生成
 * <AUTHOR>
 * @create 2021/4/21 16:32
 */
@Slf4j
@Component(value = "settingCommodity")
public class SettingCommodityBenefitGenerateHandler implements BenefitGenerateHandler {

    @Resource
    private BenefitMapper benefitMapper;

    @Resource
    private BenefitScopeMapper benefitScopeMapper;

    @Resource
    private BenefitRuleMapper benefitRuleMapper;

    @Override
    public void doGenerate(GenerateAccountDTO generateAccountDTO) {

        Assert.notNull(generateAccountDTO.getCommodityCategoryCode(),"商品类型不能为空");
        // 查询商户下的权益是否存在，不存在则生成
        LambdaQueryWrapper lambdaQueryWrapper = new QueryWrapper<Benefit>().lambda().eq(Benefit::getMerchantId,generateAccountDTO.getMerchantId());
        List<Benefit> merchantBenefitList =  benefitMapper.selectList(lambdaQueryWrapper);
        Set<Integer> benefitClassifySet = merchantBenefitList.stream().map(benefit -> {
            return benefit.getClassify();
        }).collect(Collectors.toSet());

        // 储值类商品
        if(CommodityCategoryEnum.SET_MEAL.getCode().equals(generateAccountDTO.getCommodityCategoryCode())){
            if(!benefitClassifySet.contains(BenefitClassifyEnum.USER_RECHARGE_BALANCE.getCode())){
                addMerchantBenefit(generateAccountDTO,BenefitGenerateTypeEnum.SELL,BenefitClassifyEnum.USER_RECHARGE_BALANCE);
            }else{
                log.debug("商户的【用户充值余额】权益已存在");
            }

            if(!benefitClassifySet.contains(BenefitClassifyEnum.USER_RECHARGE_COIN.getCode())){
                addMerchantBenefit(generateAccountDTO,BenefitGenerateTypeEnum.SELL,BenefitClassifyEnum.USER_RECHARGE_COIN);
            }else{
                log.debug("商户的【用户充值余币】权益已存在");
            }

            if(!benefitClassifySet.contains(BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE.getCode())){
                 addMerchantBenefit(generateAccountDTO,BenefitGenerateTypeEnum.GIVE,BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE);
            }else{
                log.debug("商户的【商户派送余额】权益已存在");
            }

            if(!benefitClassifySet.contains(BenefitClassifyEnum.MERCHANT_PAYOUT_COIN.getCode())){
                addMerchantBenefit(generateAccountDTO,BenefitGenerateTypeEnum.GIVE,BenefitClassifyEnum.MERCHANT_PAYOUT_COIN);
            }else{
                log.debug("商户的【商户派送余币】权益已存在");
            }
        }else if(CommodityCategoryEnum.DEVICE_SERVICE.getCode().equals(generateAccountDTO.getCommodityCategoryCode())){
            // 设备服务类商品 （即服务套餐）
            Assert.notNull(generateAccountDTO.getCommodityClassifyCode(),"商品分类不能为空");
            if(CommodityClassifyCodeEnum.TIME.equals(generateAccountDTO.getCommodityClassifyCode())){
                addMerchantBenefit(generateAccountDTO,BenefitGenerateTypeEnum.SELL,BenefitClassifyEnum.DEVICE_USE_TIME);
            }else{
                log.debug("商户的【项目启动次数(带时间)】权益已存在");
            }

            if(CommodityClassifyCodeEnum.ELEC.equals(generateAccountDTO.getCommodityCategoryCode())){
                addMerchantBenefit(generateAccountDTO,BenefitGenerateTypeEnum.SELL,BenefitClassifyEnum.ELECTRICITY);
            }else{
                log.debug("商户的【电量】权益已存在");
            }

        }


        if(!benefitClassifySet.contains(BenefitClassifyEnum.GROW_VALUE.getCode())){
            addMerchantGrowValueBenefit(generateAccountDTO,BenefitGenerateTypeEnum.SELL,BenefitClassifyEnum.GROW_VALUE,ApplicableEnum.DEVICE);
        }else{
            log.debug("商户的【成长值】权益已存在");
        }


    }

    /**
     *  新增商户权益操作
     * @param generateAccountDTO
     * @param benefitGenerateTypeEnum 权益生成方式
     *
     */
    private void addMerchantBenefit(GenerateAccountDTO generateAccountDTO,BenefitGenerateTypeEnum benefitGenerateTypeEnum,BenefitClassifyEnum benefitClassifyEnum) {

        Benefit benefit = new Benefit();
        benefit.setTitle(benefitClassifyEnum.getDesc());
        benefit.setClassify(benefitClassifyEnum.getCode());
        benefit.setMerchantId(generateAccountDTO.getMerchantId());
        benefit.setCreateTime(new Date());
        benefit.setActive(Boolean.TRUE);
        benefitMapper.insert(benefit);

        // 保存规则
        BenefitRule benefitRule = new BenefitRule();
        benefitRule.setBenefitId(benefit.getId());
        benefitRule.setGenerateType(benefitGenerateTypeEnum.getType());
        benefitRule.setBenefitClassify(benefit.getClassify());
        benefitRule.setCreateTime(LocalDateTime.now());
        benefitRuleMapper.insert(benefitRule);

        // 保存范围
        BenefitScope benefitScope = new BenefitScope();
        benefitScope.setBenefitId(benefit.getId());
        // 储值服务绑定到设备
        benefitScope.setApplicable(ApplicableEnum.DEVICE.getValue());
        benefitScope.setCreateTime(new Date());
        benefitScopeMapper.insert(benefitScope);
    }

    /**
     * 新增商户成长值权益
     * @param generateAccountDTO
     * @param benefitGenerateTypeEnum
     * @param benefitClassifyEnum
     * @param applicableEnum
     */
    private void addMerchantGrowValueBenefit(GenerateAccountDTO generateAccountDTO,BenefitGenerateTypeEnum benefitGenerateTypeEnum,BenefitClassifyEnum benefitClassifyEnum,ApplicableEnum applicableEnum) {

        Benefit benefit = new Benefit();
        benefit.setTitle(benefitClassifyEnum.getDesc());
        benefit.setClassify(benefitClassifyEnum.getCode());
        benefit.setMerchantId(generateAccountDTO.getMerchantId());
        benefit.setCreateTime(new Date());
        benefit.setActive(Boolean.TRUE);
        benefitMapper.insert(benefit);

        // 保存规则
        BenefitRule benefitRule = new BenefitRule();
        benefitRule.setBenefitId(benefit.getId());
        // 成长值的生成方式 目前只绑定到售卖即购买商品过程
        benefitRule.setGenerateType(benefitGenerateTypeEnum.getType());
        benefitRule.setBenefitClassify(benefit.getClassify());
        benefitRule.setCreateTime(LocalDateTime.now());
        benefitRuleMapper.insert(benefitRule);

        // 保存范围
        BenefitScope benefitScope = new BenefitScope();
        benefitScope.setBenefitId(benefit.getId());
        // 成长值绑定到设备  后期需绑定到其他地方
        benefitScope.setApplicable(applicableEnum.getValue());
        benefitScope.setCreateTime(new Date());
        benefitScopeMapper.insert(benefitScope);
    }
}
