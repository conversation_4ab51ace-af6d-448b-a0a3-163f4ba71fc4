package com.lyy.user.domain.benefit.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitScopeSelectDTO;
import com.lyy.user.domain.benefit.entity.BenefitScope;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


/**
 * <AUTHOR>
 * @create 2021/3/30 18:04
 */
@Repository
public interface BenefitScopeMapper extends BaseMapper<BenefitScope> {


    Integer insertBatch(List<BenefitScope> list);

    List<Long> findAllScopeId(@Param("merchantId") Long merchantId,
                              @Param("benefitIds") List<Long> benefitIds,
                              @Param("applicable") Integer applicable);

    /**
     * 删除权益的使用范围
     *
     * @param merchantId 商户id
     * @param benefitIds 权益id
     * @return
     */
    int deleteByBenefitIdsAndMerchantId(@Param("merchantId") Long merchantId,
                                        @Param("benefitIds") List<Long> benefitIds);

    /**
     * 根据associatedIdList查询BenefitScope集合
     *
     * @param benefitScopeSelectDTO
     * @return
     */
    List<Long> findBenefitIdsByAssociatedIds(@Param("selectDTO") BenefitScopeSelectDTO benefitScopeSelectDTO);
}
