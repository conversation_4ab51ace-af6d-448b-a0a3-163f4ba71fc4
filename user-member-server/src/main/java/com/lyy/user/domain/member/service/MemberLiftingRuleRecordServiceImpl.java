package com.lyy.user.domain.member.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.lyy.user.application.member.IMemberLiftingRuleRecordService;
import com.lyy.user.domain.member.dto.MemberLiftingRuleRecordUserDTO;
import com.lyy.user.domain.member.entity.MemberLiftingRuleRecord;
import com.lyy.user.domain.member.repository.MemberLiftingRuleRecordMapper;
import com.lyy.user.infrastructure.base.MerchantBaseServiceImpl;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 会员升级策略规则记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-06
 */
@Slf4j
@Service
public class MemberLiftingRuleRecordServiceImpl extends MerchantBaseServiceImpl<MemberLiftingRuleRecordMapper, MemberLiftingRuleRecord> implements IMemberLiftingRuleRecordService {

    /**
     * 根据记录id更新状态信息
     *
     *
     * @param merchantId
     * @param recordIds
     * @param oldStatus
     * @param newStatus
     * @return
     */
    @Override
    public int updateStatusOfIds(Long merchantId, List<Long> recordIds, short oldStatus, short newStatus) {
        UpdateWrapper<MemberLiftingRuleRecord> wrapper = new UpdateWrapper<MemberLiftingRuleRecord>()
                .set("status", newStatus)
                .setSql("update_time = now()")
                .eq(getShardingFieldKey(), merchantId)
                .in("id", recordIds)
                .eq("status", oldStatus);
        return getBaseMapper().update(null, wrapper);
    }

    /**
     * 根据状态与截止时间查询用户的升级记录信息
     * @param merchantId
     * @param userId
     * @param memberLiftingRuleId 可以为空
     * @param status
     * @param endTime 可以为空
     * @return
     */
    @Override
    public List<MemberLiftingRuleRecord> findByStatusAndEndTime(Long merchantId, Long userId, Long memberLiftingRuleId, Short status, LocalDateTime endTime) {
        QueryWrapper<MemberLiftingRuleRecord> wrapper = new QueryWrapper<MemberLiftingRuleRecord>()
                .eq(getShardingFieldKey(), merchantId)
                .eq("user_id", userId)
                .eq(memberLiftingRuleId != null && memberLiftingRuleId > 0, "member_lifting_rule_id", memberLiftingRuleId)
                .eq(status != null && status > 0, "status", status)
                .ge(endTime != null, "end_time", endTime);
        return list(wrapper);
    }

    /**
     * 根据升级策略规则获取用户的升降级记录
     *
     * @param merchantId
     * @param memberLiftingRuleIdList
     * @param memberIds
     * @param status
     * @param isCheckEndTime
     */
    @Override
    public List<MemberLiftingRuleRecordUserDTO> countUserRecordByRule(Long merchantId, List<Long> memberLiftingRuleIdList, List<Long> memberIds, Short status, boolean isCheckEndTime) {
        QueryWrapper<MemberLiftingRuleRecord> wrapper = new QueryWrapper<MemberLiftingRuleRecord>()
                .select("member_id,member_lifting_rule_id,count(*) as count_num,sum(coalesce(range_value,0)) as sum_range_value ")
                .eq(getShardingFieldKey(), merchantId)
                .in("member_id", memberIds)
                .in("member_lifting_rule_id", memberLiftingRuleIdList)
                .eq("status", status)
                .ge(isCheckEndTime, "end_time", LocalDateTime.now())
                .groupBy("member_id", "member_lifting_rule_id");
        List<Map<String, Object>> mapList = listMaps(wrapper);
        log.debug("countUserRecordByRule#mapList -->{}", mapList);
        List<MemberLiftingRuleRecordUserDTO> list = mapList.stream()
                .filter(Objects::nonNull)
                .map(map -> {
                    log.debug("map-->{}", map);
                    MemberLiftingRuleRecordUserDTO memberLiftingRuleRecordUserDTO = new MemberLiftingRuleRecordUserDTO();
                    memberLiftingRuleRecordUserDTO.setMemberId(((Number) map.get("member_id")).longValue());
                    memberLiftingRuleRecordUserDTO.setMemberLiftingRuleId(((Number) map.get("member_lifting_rule_id")).longValue());
                    memberLiftingRuleRecordUserDTO.setCountNum(((Number) map.get("count_num")).intValue());
                    Object sumRangeValue = map.get("sum_range_value");
                    if (sumRangeValue instanceof BigDecimal) {
                        memberLiftingRuleRecordUserDTO.setSumRangeValue((BigDecimal) sumRangeValue);
                    } else {
                        memberLiftingRuleRecordUserDTO.setSumRangeValue(new BigDecimal(sumRangeValue.toString()));
                    }
                    return memberLiftingRuleRecordUserDTO;
                }).collect(Collectors.toList());
        log.debug("countUserRecordByRule#list -->{}", list);
        return list;
    }
}
