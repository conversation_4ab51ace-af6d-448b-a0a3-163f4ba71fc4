package com.lyy.user.domain.user.service.handler;

import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.app.interfaces.facade.dto.UserVO;
import com.lyy.user.domain.user.service.AbstractUserInitService;
import com.lyy.user.domain.user.service.UserInitHandler;
import com.lyy.user.infrastructure.execption.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @ClassName: WeChatOtherMiniProgramUserInitHandler
 * @description: 微信小程序用户初始化（未在开放平台绑定的）
 * @author: pengkun
 * @date: 2021/04/06
 **/
@Slf4j
@Service("weChatOtherMiniProgramUserInit")
public class WeChatOtherMiniProgramUserInitHandler extends AbstractUserInitService implements UserInitHandler {

    /**
     * 用户初始化处理方法
     * @param dto
     * @return
     */
    @Override
    public UserInfoDTO handle(UserCreateDTO dto) {
        log.debug("微信小程序未在开放平台绑定的用户初始化");
        if(StringUtils.isBlank(dto.getOpenid())){
            throw new BusinessException(UserErrorCode.USER_UNION_ID_EMPTY_ERROR.getCode(),UserErrorCode.USER_UNION_ID_EMPTY_ERROR.getMessage());
        }
        String telephone = "";
        String isActive = null;
        UserVO userDTO = super.getByOpenId(dto.getOpenid());
        Long userId;
        if(userDTO == null){
            userId = super.miniProgramPlatformUserInit(dto);
        }else {
            telephone = userDTO.getTelephone();
            isActive = userDTO.getIsActive();
            userId = userDTO.getId();
            //初始化userApp
            super.initUserApp(dto.getAppId(),dto.getOpenid(), userDTO.getId());
        }
        return getUserInfoDTO(dto, userId, telephone, isActive);
    }
}
