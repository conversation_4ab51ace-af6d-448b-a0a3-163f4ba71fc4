package com.lyy.user.domain.account.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.pagehelper.Page;
import com.lyy.user.account.infrastructure.account.dto.AccountStatusDTO;
import com.lyy.user.account.infrastructure.account.dto.SmallVenueAccountInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.SmallVenueAllCardDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountDetailsDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountListDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalCardCountInfo;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalSupplementaryCardDTO;
import com.lyy.user.domain.account.dto.SmallVenueCountCardNoDTO;
import com.lyy.user.domain.account.entity.Account;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 账户表Mapper
 * <AUTHOR>
 */
@Repository
public interface AccountMapper extends BaseMapper<Account> {

    /**
     * 增加账户表余额和总数
     * @param id
     * @param merchantId
     * @param operator
     * @param total
     * @param balance
     */
    void increaseBalanceAndTotal(@Param("id") Long id,
                                 @Param("merchantId") Long merchantId,
                                 @Param("operator") Long operator,
                                 @Param("total") BigDecimal total,
                                 @Param("balance") BigDecimal balance);

    /**
     * 获取某个权益的数据
     * @param merchantId 商户id只作为确定数据库的作用，不用于搜索
     * @param classify
     * @return
     */
    Page<Account> findAccount(@Param("merchantId") Long merchantId,  @Param("classify") Integer classify);

    /**
     * 根据商户和商户用户ids获取账户信息
     *
     * @param merchantId    商户id
     * @param merchantUserIds   商户用户ids
     * @param classifies    权益类型
     * @return
     */
    List<Account> findAccountByMerchantUserIds(@Param("merchantId") Long merchantId,
                                               @Param("merchantUserIds") List<Long> merchantUserIds,
                                               @Param("classifies") List<Integer> classifies);

    /**
     * 查询会员卡的状态
     *
     * @param merchantId
     * @param cardNo
     * @return
     */
    AccountStatusDTO findCardStatus(@Param("merchantId") Long merchantId, @Param("cardNo") String cardNo);


    /**
     * 根据会员卡/会员id/手机号查询账户
     * @param merchantId
     * @param keyword
     * @return
     */
    SmallVenueAccountInfoDTO getAccountInfo(@Param("merchantId") Long merchantId, @Param("keyword") String keyword);

    /**
     * 查询用户主卡列表
     * @param merchantId
     * @param userId
     * @param hasSupplementaryCard
     * @return
     */
    List<SmallVenueAllCardDTO> selectUserAllCard(@Param("merchantId") Long merchantId,
                                                 @Param("userId") Long userId,
                                                 @Param("hasSupplementaryCard") Boolean hasSupplementaryCard);

    /**
     * 卡数量
     * @param merchantId
     * @param merchantUserIds
     * @param cardStatus 卡状态
     * @param hasSupplementaryCard 是否包括副属卡
     * @return
     */
    List<SmallVenueCountCardNoDTO> selectCountCardNumber(@Param("merchantId") Long merchantId,
                                                         @Param("merchantUserIds") List<Long> merchantUserIds,
                                                         @Param("cardStatus") List<Integer> cardStatus,
                                                         @Param("hasSupplementaryCard") Boolean hasSupplementaryCard);

    /**
     * 查询主卡的状态
     *
     * @param merchantId
     * @param userId
     * @param accountId
     * @return
     */
    Integer selectParentAccountStatus(@Param("merchantId") Long merchantId, @Param("userId") Long userId, @Param("accountId") Long accountId);

    /**
     * 查询主卡的附属卡数量
     *
     * @param merchantId
     * @param userId
     * @param accountId
     * @return
     */
    Integer selectSupplementaryCardCount(@Param("merchantId") Long merchantId, @Param("userId") Long userId, @Param("accountId") Long accountId);

    /**
     * 更新会员卡状态
     *
     * @param status
     * @param merchantId
     * @param cardNo
     * @return
     */
    Integer updateCardStatus(@Param("status") Integer status, @Param("merchantId") Long merchantId, @Param("cardNo") String cardNo, @Param("updateBy") Long updateBy);

    /**
     * 根据卡号查询会员卡信息
     * @param merchantId
     * @param cardNo
     * @return
     */
    Account selectAccountByCardNo(@Param("merchantId") Long merchantId, @Param("cardNo") String cardNo);

    List<Account> selectAccountByCardNos(@Param("merchantId") Long merchantId, @Param("cardNos") List<String> cardNos);

    /**
     * 更新卡号
     * @param merchantId
     * @param oldCardNo
     * @param newCardNo
     * @return
     */
    Integer updateAccountCardNo(@Param("merchantId") Long merchantId, @Param("oldCardNo") String oldCardNo, @Param("newCardNo") String newCardNo, @Param("updateBy") Long updateBy);

    /**
     * 更新用户账户默认状态
     *
     * @param merchantId     商户id
     * @param merchantUserId 商户用户id
     * @param updateBy       更新人
     */
    void updateAccountDefaultFlag(@Param("merchantId") Long merchantId,
                                  @Param("merchantUserId") Long merchantUserId,
                                  @Param("updateBy") Long updateBy);

    /**
     *会员卡续期
     * @param merchantId
     * @param cardNo
     * @param renewalTime
     * @param updateBy
     * @return
     */
    Integer renewalCard(@Param("merchantId") Long merchantId,
                        @Param("cardNo") String cardNo,
                        @Param("renewalTime") Date renewalTime,
                        @Param("updateBy") Long updateBy);

    /**
     * 查询会员卡对应额账户，以及默认卡
     *
     */
    List<Account> listAccountWithDefault(@Param("merchantId") Long merchantId,
                                         @Param("userId") Long userId,
                                         @Param("cardNo") String cardNo);

    /**
     * 将最近的一张正常状态的会员卡作为默认卡 <br/>
     * 不会将已有的默认卡的 defaultFlag 置为 false
     */
    Integer updateRecentlyAccountAsDefaultFlag(@Param("merchantId") Long merchantId,
                                               @Param("userId") Long userId,
                                               @Param("updateBy")  Long updateBy);

    /**
     * 更新账户的id信息
     * @param merchantId
     * @param userId
     * @param cardNo
     * @param merchantUserId
     * @return
     */
    Integer updateAccountIdInfo(@Param("merchantId") Long merchantId,
                                @Param("userId") Long userId,
                                @Param("cardNo") String cardNo,
                                @Param("merchantUserId") Long merchantUserId,
                                @Param("defaultFlag") Boolean defaultFlag);


    Integer updateStatusAfterReissue(@Param("merchantId") Long merchantId, @Param("cardNo") String cardNo, @Param("updateBy") Long updateBy);

    /**
     * 根据卡号查询会员信息
     *
     * @param merchantId
     * @param keyword
     * @return
     */
    SmallVenueAccountInfoDTO getAccountInfoByCardNo(@Param("merchantId") Long merchantId, @Param("keyword") String keyword);

    /**
     * 根据手机号码查询会员信息
     * @param merchantId
     * @param keyword
     * @return
     */
    SmallVenueAccountInfoDTO getAccountInfoByTelephone(@Param("merchantId") Long merchantId, @Param("keyword") String keyword);

    /**
     * 根据会员id会员信息
     * @param merchantId
     * @param keyword
     * @return
     */
    SmallVenueAccountInfoDTO getAccountInfoByMerchantUserId(@Param("merchantId") Long merchantId, @Param("keyword") String keyword);

    /**
     * 移动端会员卡列表
     * @param page
     * @param mobileTerminalAccountSelectDTO
     * @return
     */
    IPage<MobileTerminalAccountListDTO> mobileTerminalAccountList(IPage page, @Param("selectDTO") MobileTerminalAccountSelectDTO mobileTerminalAccountSelectDTO);

    /**
     * 查询主卡的附属卡张数
     * @param merchantId
     * @param accountIds
     * @return
     */
    List<MobileTerminalCardCountInfo> batchSelectSupplementaryCardCount(@Param("merchantId") Long merchantId,
                                                                        @Param("accountIds") List<Long> accountIds);

    /**
     * 会员卡详情
     * @param merchantId
     * @param accountId
     * @return
     */
    MobileTerminalAccountDetailsDTO mobileTerminalAccountDetails(@Param("merchantId") Long merchantId, @Param("accountId") Long accountId);

    /**
     * 查询主卡的附属卡列表
     * @param merchantId
     * @param accountId
     * @return
     */
    List<MobileTerminalSupplementaryCardDTO> findAccountSupplementaryCardList(@Param("merchantId") Long merchantId, @Param("accountId") Long accountId);

    /**
     * 获取用户默认的主卡
     * @param merchantId
     * @param userId
     * @return
     */
    Account getDefaultAccount(@Param("merchantId") Long merchantId,
                              @Param("userId") Long userId);

    /**
     * 更新多金宝账户
     * @param account
     * @return
     */
    Integer updateVenueAccountById(Account account);

    /**
     * 根据卡号模糊查询小场地会员id-根据会员id去重
     *
     * @param merchantId
     * @param keyword
     * @return
     */
    IPage<Long> selectDistinctMerchantUserIdByMerchantIdAndCardNo(@Param("page") IPage page, @Param("merchantId") Long merchantId, @Param("keyword") String keyword);

    /**
     * 小场地会员列表查询-统计数量(根据会员id去重)
     * @param merchantId
     * @param keyword
     * @return
     */
    Long countSmallVenueUserMemberList(@Param("merchantId") Long merchantId, @Param("keyword") String keyword);

    List<SmallVenueAccountInfoDTO> getAccountInfoByCardNos (@Param("merchantId") Long merchantId,@Param("cardNos") List<String> cardNos);
}