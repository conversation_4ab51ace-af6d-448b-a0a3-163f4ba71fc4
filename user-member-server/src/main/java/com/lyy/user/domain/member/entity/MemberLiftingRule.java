package com.lyy.user.domain.member.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 会员升级策略规则
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("um_member_lifting_rule")
public class MemberLiftingRule implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 商户ID
     */
    @TableField("merchant_id")
    private Long merchantId;

    /**
     * 升降级策略
     */
    @TableField("member_lifting_id")
    private Long memberLiftingId;

    /**
     * 超过日期范围,就是说多少天之内消耗触发多少次该规则
     */
    @TableField("range_date")
    private Short rangeDate;

    /**
     * 策略(1 登录次数,2 支付笔数,3 消费金额,4完善信息，5绑定手机，6关注公众号)
     * @see com.lyy.user.account.infrastructure.constant.MemberLiftingRuleCategoryEnum
     */
    @TableField("category")
    private Short category;

    /**
     * 范围值,若为消费金额时，单位为元
     */
    @TableField("range_value")
    private BigDecimal rangeValue;

    /**
     * 判断条件，true：为符合该条件，主要用于升级判断，false：为不符合该条件，主要用于降级判断
     */
    @TableField("judge_condition")
    private Boolean judgeCondition;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建者
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新者
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 是否有效 true 有效,false 无效
     */
    @TableField("active")
    private Boolean active;
}
