package com.lyy.user.domain.account.service;


import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.ADVERT_COIN;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.ADVERT_RED_PACKET;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.DEDUCTION_AMOUNT;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.IDLE_TIME_COIN;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.MERCHANT_PAYOUT_COIN;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.PLATFORM_PAYOUT_BALANCE;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.PLATFORM_PAYOUT_COIN;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.RED_BALANCE;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.RED_COIN;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.THIRD_PLATFORM_AMOUNT;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.THIRD_PLATFORM_COINS;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.USER_RECHARGE_BALANCE;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.USER_RECHARGE_COIN;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.account.dto.AccountAdjustRecordDTO;
import com.lyy.user.account.infrastructure.account.dto.request.AccountAdjustRecordQueryDTO;
import com.lyy.user.application.account.AccountRecordService;
import com.lyy.user.domain.account.entity.AccountRecord;
import com.lyy.user.domain.account.repository.AccountRecordMapper;
import com.lyy.user.infrastructure.base.MerchantBaseServiceImpl;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class AccountRecordServiceImpl extends MerchantBaseServiceImpl<AccountRecordMapper, AccountRecord> implements AccountRecordService {

    @Resource
    private AccountRecordMapper accountRecordMapper;

    @Override
    public Page<AccountAdjustRecordDTO> listGrantCoinsRecord(AccountAdjustRecordQueryDTO param) {
        Page<AccountAdjustRecordDTO> page = new Page<>(param.getPageIndex(), param.getPageSize());
        String numericKeyword = null;
        String strKeyWord = null;
        if(StringUtils.isNumeric(param.getKeyword())){
            numericKeyword = param.getKeyword();
        }else{
            strKeyWord = param.getKeyword();
        }
        //查询总数
        IPage<AccountAdjustRecordDTO> pageObj = accountRecordMapper.listGrantCoinsRecord(page,param,numericKeyword,strKeyWord);
        if(Objects.nonNull(pageObj) && CollectionUtils.isNotEmpty(pageObj.getRecords())){
            /**
             * 属于币的权益
             */
            List<Integer> coinList =  Arrays.asList(USER_RECHARGE_COIN.getCode(),
                    MERCHANT_PAYOUT_COIN.getCode(),
                    PLATFORM_PAYOUT_COIN.getCode(),
                    ADVERT_COIN.getCode(),
                    RED_COIN.getCode(),
                    IDLE_TIME_COIN.getCode(),
                    RED_COIN.getCode(),
                    THIRD_PLATFORM_COINS.getCode());

            List<Integer> balanceList = Arrays.asList(USER_RECHARGE_BALANCE.getCode(),
                            MERCHANT_PAYOUT_BALANCE.getCode(),
                            PLATFORM_PAYOUT_BALANCE.getCode(),
                            RED_BALANCE.getCode(),
                            ADVERT_RED_PACKET.getCode(),
                            THIRD_PLATFORM_AMOUNT.getCode(),
                            DEDUCTION_AMOUNT.getCode());

            pageObj.getRecords().forEach(t->{
                Integer benefitClassify = t.getBenefitClassify();
                if(coinList.contains(benefitClassify)){
                    t.setBenefitClassify(2);
                }else if(balanceList.contains(benefitClassify)){
                    t.setBenefitClassify(1);
                }
            });
        }
        Page pg = new Page(param.getPageIndex(), param.getPageSize(), pageObj.getTotal());
        pg.setRecords(pageObj.getRecords());
        return pg;
    }

}
