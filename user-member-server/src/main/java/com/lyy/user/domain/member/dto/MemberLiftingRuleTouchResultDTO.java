package com.lyy.user.domain.member.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @className: MemberLiftingRuleTouchResultDTO
 * @date 2021/4/6
 */
@Data
public class MemberLiftingRuleTouchResultDTO {

    private Long id;

    /**
     * 升降级策略
     */
    private Long memberLiftingId;

    /**
     * 超过日期范围,就是说多少天之内消耗触发多少次该规则
     */
    private Short rangeDate;

    /**
     * 策略(1 登录次数,2 支付笔数,3 消费金额
     */
    private Short category;

    /**
     * 范围值,若为消费金额时，单位为元
     */
    private BigDecimal rangeValue;

    /**
     * 所属会员组id
     */
    private Long memberGroupId;
}
