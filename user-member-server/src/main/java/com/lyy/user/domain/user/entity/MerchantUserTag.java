package com.lyy.user.domain.user.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lyy.user.infrastructure.typehandler.ArrayTypeHandlerPg;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.ibatis.type.JdbcType;

/**
 * 商户用户标签
 * <AUTHOR>
@ToString
@Getter
@Setter
@TableName(value = "um_merchant_user_tag")
public class MerchantUserTag {
    @TableId
    private Long id;

    /**
     * 商户用户ID或平台用户Id
     */
    @TableField("business_user_id")
    private Long businessUserId;

    /**
     * 商户ID或平台Id
     */
    private Long merchantId;


    /**
     * 标签ID
     */
    @TableField(value = "user_tags", typeHandler = ArrayTypeHandlerPg.class, jdbcType = JdbcType.ARRAY)
    private Long[] userTags;

    /**
     * 是否可用，true:是，false：否
     */
    @TableField(value = "is_active")
    private Boolean active;


    /**
     * 0 平台用户标签
     * 1 商户用户标签
     *
     */
    private Integer tagType;


    private Date createTime;

    private Date updateTime;

    private Long createdby;

    private Long updatedby;
}