package com.lyy.user.domain.report.repository;

import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberCardReportDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberCardReportQueryDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberReportDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberReportQueryDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberStoreValueRecordDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberStoreValueRecordQueryDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueStoreValueSimpleDto;
import java.math.BigDecimal;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date : 2022-12-21 14:12
 **/
@Repository
public interface SmallVenueMemberReportMapper {

    List<String> queryTagName(@Param("tagIdList")List<Long> tagIdList,@Param("merchantId")Long merchantId);

    /**
     * 查询会员总数
     * @param smallVenueMemberReportQueryDto
     * @return
     */
    Long queryUserMemberReportTotalMemberNum(@Param("smallVenueMemberReportQueryDto") SmallVenueMemberReportQueryDto smallVenueMemberReportQueryDto,@Param("userTagList") Object[] userTagList);

    /**
     * 分页查询会员数据
     * @param smallVenueMemberReportQueryDto
     * @param startIndex
     * @return
     */
    List<SmallVenueMemberReportDto> queryUserMemberReport(@Param("smallVenueMemberReportQueryDto") SmallVenueMemberReportQueryDto smallVenueMemberReportQueryDto,@Param("userTagList") Object[] userTagList, @Param("startIndex") Integer startIndex);

    /**
     * 根据会员id查询会员储值
     *
     * @param userIdList
     * @param merchantId
     * @param storeIds
     * @return
     */
    List<SmallVenueStoreValueSimpleDto> queryUserMemberStoreValueReport(@Param("userIdList") List<Long> userIdList, @Param("merchantId") Long merchantId, @Param("storeIds") List<Long> storeIds);

    /**
     * 查询商户下可用剩余储值集合
     * @param smallVenueMemberReportQueryDto
     * @return
     */
    List<SmallVenueStoreValueSimpleDto> totalQueryMemberStoreValueReport(@Param("smallVenueMemberReportQueryDto") SmallVenueMemberReportQueryDto smallVenueMemberReportQueryDto,@Param("userTagList") Object[] userTagList);

    /**
     * 查询会员卡总数
     * @param smallVenueMemberCardReportQueryDto
     * @return
     */
    Long queryUserMemberReportTotalCardNum(@Param("smallVenueMemberCardReportQueryDto") SmallVenueMemberCardReportQueryDto smallVenueMemberCardReportQueryDto,@Param("userTagList") Object[] userTagList);

    /**
     * 分页查询会员卡总数
     * @param smallVenueMemberCardReportQueryDto
     * @return
     */
    List<SmallVenueMemberCardReportDto> queryUserMemberCardReport(@Param("smallVenueMemberCardReportQueryDto") SmallVenueMemberCardReportQueryDto smallVenueMemberCardReportQueryDto,@Param("userTagList") Object[] userTagList,@Param("startIndex") Integer startIndex);

    /**
     * 会员卡储值查询
     * @param accountIdList
     * @return
     */
    List<SmallVenueStoreValueSimpleDto> queryUserMemberCardStoreValueReport(@Param("accountIdList") List<Long> accountIdList, @Param("merchantId") Long merchantId,@Param("storeIds") List<Long> storeIds);

    /**
     * 会员卡储值合计查询
     * @param smallVenueMemberCardReportQueryDto
     * @return
     */
    List<SmallVenueStoreValueSimpleDto> totalQueryMemberCardStoreValueReport(@Param("smallVenueMemberCardReportQueryDto") SmallVenueMemberCardReportQueryDto smallVenueMemberCardReportQueryDto,@Param("userTagList") Object[] userTagList);

    /**
     * 会员记录报表连表查询
     * @param smallVenueMemberStoreValueRecordQueryDto
     * @param toArray
     * @return
     */
    Long totalQueryMemberStoreValueRecordTotalReport(@Param("smallVenueMemberStoreValueRecordQueryDto") SmallVenueMemberStoreValueRecordQueryDto smallVenueMemberStoreValueRecordQueryDto, @Param("userTagList") Long[] toArray);

    /**
     * 会员记录报表连表查询
     * @param smallVenueMemberStoreValueRecordQueryDto
     * @param toArray
     * @return
     */
    List<SmallVenueMemberStoreValueRecordDto> totalQueryMemberStoreValueRecordReport(@Param("smallVenueMemberStoreValueRecordQueryDto")SmallVenueMemberStoreValueRecordQueryDto smallVenueMemberStoreValueRecordQueryDto, @Param("userTagList")Long[] toArray,@Param("startIndex") Integer startIndex);


    /**
     * 会员记录报表计算合计
     * @param smallVenueMemberStoreValueRecordQueryDto
     * @param toArray
     * @return
     */
    BigDecimal totalQueryMemberStoreValueRecordChangeReport(@Param("smallVenueMemberStoreValueRecordQueryDto")SmallVenueMemberStoreValueRecordQueryDto smallVenueMemberStoreValueRecordQueryDto, @Param("userTagList")Long[] toArray, @Param("mode")Integer mode);

    /**
     * 分页查询会员数据
     * @param smallVenueMemberReportQueryDto
     * @param startIndex
     * @return
     */
    List<SmallVenueMemberReportDto> queryUserMemberReportV2(@Param("smallVenueMemberReportQueryDto") SmallVenueMemberReportQueryDto smallVenueMemberReportQueryDto,@Param("userTagList") Object[] userTagList, @Param("startIndex") Integer startIndex);

}
