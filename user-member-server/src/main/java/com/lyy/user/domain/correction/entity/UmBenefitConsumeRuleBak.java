package com.lyy.user.domain.correction.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 权益消耗规则表
 * @TableName um_benefit_consume_rule_bak
 */
@Data
public class UmBenefitConsumeRuleBak implements Serializable {

    private static final long serialVersionUID = 8206279150308657934L;
    /**
     * 
     */
    private Long id;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 权益类型
     */
    private Integer benefitClassify;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 优先级
     */
    private Integer expirePriority;

    /**
     * 是否是默认的
     */
    private Boolean isDefault;

    /**
     * 
     */
    private Boolean isActive;

    /**
     * 
     */
    private Long createBy;

    /**
     * 
     */
    private Long updateBy;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

}