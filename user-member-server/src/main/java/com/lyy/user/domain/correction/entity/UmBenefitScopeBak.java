package com.lyy.user.domain.correction.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 权益范围表
 * @TableName um_benefit_scope_bak
 */
@Data
public class UmBenefitScopeBak implements Serializable {

    private static final long serialVersionUID = -2337272627486432660L;
    /**
     * 
     */
    private Long id;

    /**
     * 权益ID

     */
    private Long benefitId;

    /**
     * 适用类型 1 品类 2 场地 3 设备 4 商品
     */
    private Integer applicable;

    /**
     * 关联ID
     */
    private Long associatedId;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

    /**
     * 商户Id
     */
    private Long merchantId;


}