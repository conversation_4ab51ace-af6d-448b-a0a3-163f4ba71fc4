package com.lyy.user.domain.benefit.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.account.dto.BenefitExcludeClassifyDTO;
import com.lyy.user.account.infrastructure.account.dto.BenefitIncludeClassifyDTO;
import com.lyy.user.domain.benefit.dto.BenefitRecordCountDTO;
import com.lyy.user.domain.benefit.entity.Benefit;
import com.lyy.user.domain.benefit.entity.BenefitScope;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


/**
 * <AUTHOR>
 * @create 2021/3/30 18:04
 */
@Repository
public interface BenefitMapper extends BaseMapper<Benefit> {

    Integer insertBatch(List<Benefit> list);

    List<Benefit> queryBenefitList(@Param("lyyUserId") Long lyyUserId, @Param("merchantId") Long merchantId);

    /**
     * 根据权益生成类型 查询商户下的所有权益
     * @param merchantId
     * @param generateType
     * @return
     */
    List<Benefit> queryBenefitListByMerchant(@Param("merchantId") Long merchantId, @Param("generateType") Integer generateType);

    /**
     * 查询权益，通过关联权益范围
     * @param merchantId
     * @param classify
     * @param applicable
     * @param associatedId
     * @return
     */
    List<Benefit> queryBenefitListByScope(@Param("merchantId") Long merchantId, @Param("classify")  Integer classify,
                                          @Param("applicable") Integer applicable, @Param("associatedId") Long associatedId,
                                          @Param("expiryDateCategory") Integer expiryDateCategory, @Param("upTime") String upTime,
                                          @Param("downTime") String downTime);

    /**
     * 查询权益，通过关联集合 权益范围
     * @param merchantId
     * @param classify
     * @param applicable
     * @param associatedIdList
     * @return
     */
    List<Benefit> queryBenefitListByGroupIdList(@Param("merchantId") Long merchantId, @Param("classify")  Integer classify,
                                                @Param("applicable") Integer applicable, @Param("associatedIdList") List<Long> associatedIdList);


    /**
     * 查询商户 场地关联的权益
     * @param merchantId
     * @param benefitClassifyCode
     * @param applicable
     * @param associatedId
     * @return
     */
    Long selectCountBenefitScope(@Param("merchantId") Long merchantId, @Param("benefitClassifyCode") Integer benefitClassifyCode,
                                 @Param("applicable") Integer applicable,@Param("associatedId") Long associatedId);

    List<BenefitScope> selectBenefitScopeList(@Param("merchantId") Long merchantId, @Param("benefitClassifyCode") Integer benefitClassifyCode,
                                               @Param("applicable") Integer applicable, @Param("associatedId") Long associatedId);
    /**
     * 根据账号记录获取对应的权益信息,主要通过支付单号(outTradeNo)或业务单号(orderNo)来查找
     * @param accountBenefitAdjust
     * @param mode 订单的模式 1:增加，2：扣减
     * @param classifyList 检查的类型列表
     * @return
     */

    List<BenefitRecordCountDTO> getBenefitFromAccountRecord(@Param("accountBenefitAdjust") AccountBenefitAdjustDTO accountBenefitAdjust, @Param("mode") int mode,@Param("classifyList") List<Integer> classifyList);

    /**
     * 根据商户、场地和权益获取benefitId（权益范围只是单场地的）
     *
     * @param merchantId          商户id
     * @param benefitClassifyCode 权益类型
     * @param applicable          使用范围
     * @param associatedId        关联id
     * @return 权益id
     */
    Long getBenefitId(@Param("merchantId") Long merchantId, @Param("benefitClassifyCode") Integer benefitClassifyCode,
                      @Param("applicable") Integer applicable, @Param("associatedId") Long associatedId);

    /**
     * 更新商户权益的创建时间
     *
     * @param benefitId  权益id
     * @param merchantId 商户id
     * @return
     */
    int updateBenefit(@Param("benefitId") Long benefitId, @Param("merchantId") Long merchantId);

    /**
     * 更新权益集合的更新时间
     *
     * @param merchantId
     * @param benefitIdList
     * @return
     */
    Integer updateBenefitByIds(@Param("merchantId") Long merchantId, @Param("benefitIdList") List<Long> benefitIdList);

    /**
     * 根据分类排除查询权益id
     * @param benefitExcludeClassifyDTO
     * @return
     */
    List<Long> findBenefitIdByExcludeClassify(@Param("dto") BenefitExcludeClassifyDTO benefitExcludeClassifyDTO);


    List<Long> findBenefitIdByIncludeClassify(@Param("dto") BenefitIncludeClassifyDTO benefitIncludeClassifyDTO);

    List<Benefit> findMerchantExpireNoScopeBenefit(@Param("merchantId") Long merchantId, @Param("limit") Integer limit,
            @Param("date") String date, @Param("minId") Long minId);

    List<Benefit> findShardExpireNoScopeBenefit(@Param("merchantId") Long merchantId, @Param("limit") Integer limit,
            @Param("date") String date, @Param("minId") Long minId);
}
