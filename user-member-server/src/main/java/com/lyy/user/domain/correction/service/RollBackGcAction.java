package com.lyy.user.domain.correction.service;

import static java.util.Optional.ofNullable;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.lyy.user.domain.correction.dto.GcConditionDTO;
import com.lyy.user.domain.correction.repository.DataGCParser;
import com.lyy.user.infrastructure.constants.ShardingGCEnum;
import com.lyy.user.infrastructure.util.GcNodeUtils;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import javax.sql.DataSource;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shardingsphere.shardingjdbc.jdbc.core.datasource.ShardingDataSource;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.jdbc.core.namedparam.SqlParameterSourceUtils;
import org.springframework.util.StringUtils;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/**
 * <AUTHOR>
 * @date 2023/10/10
 */
@Slf4j
//@Component
public class RollBackGcAction implements InitializingBean, AutoCloseable {

    @Setter(onMethod_ = @Autowired)
    private DataSource shardingDataSource;
    private final AtomicInteger retryCloseResource = new AtomicInteger(0);
    private Map<String, DataSource> dataSourceMap;

    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    Connection connection;
    Statement statement;
    DataSource dataSource;
    DataGCParser dataGcParser;
    private static final Integer BATCH_SIZE = 500;


    public Boolean doAction(GcConditionDTO gcConditionDTO) {
        Integer tableNo = gcConditionDTO.getCode();
        Long pageSize = gcConditionDTO.getPageSize();
        Integer dsNo = gcConditionDTO.getDsNo();
        ShardingGCEnum data = ShardingGCEnum.getData(tableNo);
        String tableName = data.getTableName();
        String bakTableName = data.getBakTableName();
        Class umClazz = data.getUmClazz();
        if (StringUtils.isEmpty(bakTableName)) {
            return false;
        }

        dataGcParser = DataGCParser.builder().pageSize(pageSize).tableName(bakTableName).build();
        List identityByPage = new ArrayList<>(Math.toIntExact(pageSize));
        do {
            try {
                Tuple2<List, Long> tuple = findIdentityByPage();
                identityByPage = tuple.getT1();
                dataGcParser.setId(tuple.getT2());
                if (!identityByPage.isEmpty()) {
                    // 还原原表
                    backUp(identityByPage, tableName, umClazz);
                    afterHandler(bakTableName, identityByPage);
                }
            } catch (Exception e) {
                log.error("DS[{}] 执行报错， 表: {}, err: {}", dsNo, dataGcParser.getTableName(), e.getLocalizedMessage());
            }
        } while (!identityByPage.isEmpty());
        dataGcParser = null;
        return true;
    }

    public Tuple2<List, Long> findIdentityByPage() {
        String sql = dataGcParser.pageParserWithoutTime(ShardingGCEnum::getBakSourceClass);
        List result = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        long lastId = 0;
        ArrayNode arrNode = GcNodeUtils.createArrNode();
        try {
            ResultSet resultSet = statement.executeQuery(sql);
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            Class className = ShardingGCEnum.getBakSourceClass(dataGcParser.getTableName());
            while (resultSet.next()) {
                ObjectNode node = GcNodeUtils.createNode();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object obj = resultSet.getObject(i);
                    if (Objects.nonNull(obj)) {
                        GcNodeUtils.putNode(node, columnName, String.valueOf(obj));
                        if (columnName.equals(DataGCParser.COLUMN_ID)) {
                            ids.add(resultSet.getLong(i));
                        }
                    }
                }

                arrNode.add(node);
            }
            result = GcNodeUtils.convertNode(arrNode, className);
            Optional<Long> lastIdOption = ofNullable(ids).filter(CollectionUtils::isNotEmpty).map(Collections::max);
            if (lastIdOption.isPresent()) {
                lastId = lastIdOption.get();
            }
            resultSet.close();
        } catch (SQLException e) {
            log.error("CommonGCActionImpl query fail, err reason", e);
        }
        return Tuples.of(result, lastId);
    }

    public void backUp(List backUpEntity, String tableName, Class clazz) {
        DataGCParser dataGcParser = DataGCParser.builder().tableName(tableName).build();
        String insertSql = dataGcParser.insertParser(clazz);
        SqlParameterSource[] batch = SqlParameterSourceUtils.createBatch(backUpEntity.toArray());
        namedParameterJdbcTemplate.batchUpdate(insertSql, batch);
    }

    public void afterHandler(String bakTableName, List param) throws SQLException {
        List<Long> ids = GcNodeUtils.jsonToIds(param);
        DataGCParser dataGcParser = DataGCParser.builder().tableName(bakTableName).build();
        String delSql = dataGcParser.deleteParser(ids);
        statement.executeUpdate(delSql);
    }

    public RollBackGcAction chooseDS(Integer dsNo) {
        String dsName = "ds" + dsNo;
        dataSource = dataSourceMap.get(dsName);
        if (Objects.isNull(namedParameterJdbcTemplate)) {
            synchronized (RollBackGcAction.class) {
                if (Objects.isNull(namedParameterJdbcTemplate)) {
                    namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSource);
                }
                try {
                    connection = dataSource.getConnection();
                    statement = connection.createStatement();
                } catch (SQLException e) {
                    throw new RuntimeException("初始化连接失败");
                }
            }
        }
        return this;
    }
    @Override
    public void close() {
        releaseDs();
    }
    private void releaseDs() {
        try {
            synchronized (RollBackGcAction.class) {
                retryCloseResource.getAndIncrement();
                namedParameterJdbcTemplate = null;
                connection.close();
                statement.close();
            }
        } catch (SQLException e) {
            if (retryCloseResource.get() < 3) {
                log.info("CommonGCActionImpl 关闭资源失败, 再次尝试");
                releaseDs();
            }
            log.error("CommonGCActionImpl 三次尝试关闭资源失败", e);
        }
        retryCloseResource.set(0);
    }

    @Override
    public void afterPropertiesSet() {
        ShardingDataSource shardingDataSource = (ShardingDataSource) this.shardingDataSource;
        dataSourceMap = shardingDataSource.getDataSourceMap();
    }
}
