package com.lyy.user.domain.user.service.handler;

import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.app.interfaces.facade.dto.UserVO;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.domain.user.service.AbstractUserInitService;
import com.lyy.user.domain.user.service.UserInitHandler;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.interfaces.assembler.UserAssembler;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @ClassName: WeChatWebUserInitHandler
 * @description: 微信网页用户初始化
 * @author: pengkun
 * @date: 2021/04/06
 **/
@Slf4j
@Service("weChatWebUserInit")
public class WeChatWebUserInitHandler extends AbstractUserInitService implements UserInitHandler {

    /**
     * 用户初始化处理方法
     */
    @Override
    public UserInfoDTO handle(UserCreateDTO dto) {
        log.debug("微信网页用户初始化");
        UserVO userDTO = getByAppIdAndOpenId(dto.getAppId(), dto.getOpenid());
        if (userDTO == null) {
            userDTO = super.getByOpenId(dto.getOpenid());
        }
        String telephone = "";
        Long userId;
        String isActive = null;
        if(Objects.isNull(userDTO)){
            log.debug("当前用户信息不存在处理逻辑");
            UserVO unionUser = null;
            if(StringUtils.isNotBlank(dto.getUnionid())){
                unionUser = super.getByUnionId(dto.getUnionid());
            }
            if (Objects.nonNull(unionUser)) {
                userId = unionUser.getId();
                isActive = unionUser.getIsActive();
            } else {
                userId = -1L;
            }
            if(userId <= 0){
                //初始化平台用户信息
                userId = super.initPlatformUser(dto);
            }else {
                log.info("账号合并当前用户不存在处理逻辑:appId={}, openid={}, unionId={}, unionUser={}, dto={}", dto.getAppId(),
                    dto.getOpenid(), dto.getUnionid(), unionUser, dto);
                //更新平台用户信息
                UserVO updateUserDTO =  UserAssembler.INSTANCE.toUserVO(dto);
                updateUserDTO.setId(userId);
                log.info("[微信网页授权]更新平台用户信息: {}", updateUserDTO);
                super.updatePlatformUser(updateUserDTO);
            }
            //初始化用户App,公众号关联信息
            super.initUserApp(dto.getAppId(), dto.getOpenid(), userId);
        }else {
            log.debug("当前用户存在");
            telephone = userDTO.getTelephone();
            isActive = userDTO.getIsActive();
            if(StringUtils.isNotBlank(userDTO.getUnionId())){
                log.info("当前用户unionId非空处理逻辑:{}", userDTO);
                boolean updateUser = StringUtils.isBlank(userDTO.getGender())
                        || Objects.isNull(userDTO.getCityId());
                if(updateUser){
                    super.updateUserWx(dto,userDTO);
                }
            }else {
                log.debug("当前用户unionId为空处理逻辑:{}", userDTO);
                //初始化用户App,公众号关联信息
                super.initUserApp(dto.getAppId(),dto.getOpenid(), userDTO.getId());
                if(UNION_AUTH_MODEL.equals(dto.getSilent())){
                    // 判断unionId
                    if(StringUtils.isBlank(dto.getUnionid())){
                        throw new BusinessException(UserErrorCode.USER_UNION_ID_EMPTY_ERROR);
                    }
                    AtomicBoolean updateUnion = new AtomicBoolean(true);
                    // unionId 授权模式
                    UserVO unionUser = super.getByUnionId(dto.getUnionid());
                    if(unionUser != null){
                        log.info("账号合并当前用户unionId为空处理逻辑:unionUser={},openUser={}", unionUser, userDTO);
                        super.updateUserAndIgnore(userDTO.getId(), unionUser.getId());
                        //更新用户信息
                        super.updateUserWx(dto, unionUser, userDTO);
                        updateUnion.set(false);
                    }
                    // 更新unionId
                    if (updateUnion.get()) {
                        log.info("更新用户 unionId, openid={}, userid={}, unionId={}", dto.getOpenid(), userDTO.getId(), dto.getUnionid());
                        UserVO updateDTO =  UserAssembler.INSTANCE.toUserVO(dto);
                        updateDTO.setId(userDTO.getId());
                        super.updatePlatformUser(updateDTO);
                    }
                }
            }
            userId = userDTO.getId();
        }
        return getUserInfoDTO(dto, userId, telephone, isActive);
    }

    @Override
    protected Long initMerchantUser(UserCreateDTO dto) {
        UserCreateDTO.MerchantUserCreateDTO merchantUserCreateDTO = dto.getMerchantUserCreateDTO();
        MerchantUser merchantUser = merchantUserService.findByUserIdAndMerchantId(merchantUserCreateDTO.getUserId(), merchantUserCreateDTO.getMerchantId());
        if (merchantUser != null) {
            if (org.apache.commons.lang3.StringUtils.isAnyBlank(merchantUser.getName(), merchantUser.getHeadImg(), merchantUser.getGender())) {
                MerchantUserDTO merchantUserDTO = super.assembleMerchantUserDTO(dto, merchantUserCreateDTO);
                log.debug("更新商户用户信息, user: {}", merchantUserDTO);
                merchantUserService.saveOrUpdateMerchantUser(merchantUserDTO);
            }
            return merchantUser.getId();
        }
        MerchantUserDTO merchantUserDTO = super.assembleMerchantUserDTO(dto, merchantUserCreateDTO);
        Long merchantUserId = merchantUserService.saveOrUpdateMerchantUser(merchantUserDTO);
        //初始化商户用户统计
        initMerchantUserStatistics(merchantUserDTO.getUserId(), merchantUserId, merchantUserDTO.getMerchantId());
        return merchantUserId;
    }
}
