package com.lyy.user.domain.member.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lyy.user.account.infrastructure.member.dto.MemberLevelInfoNameDTO;
import com.lyy.user.domain.member.entity.MemberLevel;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 会员等级 Mapper 接口
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
@Repository
public interface MemberLevelMapper extends BaseMapper<MemberLevel> {

    MemberLevel selectByIdForUpdate(Long id);


    MemberLevel selectByIdAndMerchant(@Param("merchantId") Long merchantId,@Param("id") Long id);
    /**
     * 根据会员的成长值获取需要变动的等级
     *
     * @param merchantId
     * @param memberId
     * @param isUpgrade 是否为升级功能，true为升级，false为降级
     */
    List<MemberLevel> findLevelOfMemberGrowValue(@Param("merchantId") Long merchantId, @Param("memberId") Long memberId, @Param("upgrade") Boolean isUpgrade);


    /**
     * 根据会员的成长值获取需要变动的等级,降级
     *
     * @param memberId
     */
    List<MemberLevel> findLevelOfMemberGrowValueDemoting(@Param("memberId") Long memberId);


    /**
     * 获取后面的会员等级信息
     * @param merchantId
     * @param memberLevelId
     * @param size 获取等级的数据
     * @param isNext 是否为下一个等级，若true为下个等级，若false为上个等级
     * @return
     */
    List<MemberLevel> findNextLevel(@Param("merchantId") Long merchantId, @Param("memberLevelId") Long memberLevelId,@Param("size") int size,@Param("isNext") boolean isNext);

    /**
     * 查询会员等级名称
     * @param idList
     * @return
     */
    List<MemberLevelInfoNameDTO> batchSelectMemberLevelName(@Param("idList") Set<Long> idList);


}
