package com.lyy.user.domain.member.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.constant.MemberLiftingConditionEnum;
import com.lyy.user.account.infrastructure.constant.MemberLiftingRuleCategoryEnum;
import com.lyy.user.account.infrastructure.member.dto.MemberLiftingRuleDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberLiftingSaveDTO;
import com.lyy.user.application.member.IMemberLiftingRuleService;
import com.lyy.user.application.member.IMemberLiftingService;
import com.lyy.user.domain.member.entity.MemberLifting;
import com.lyy.user.domain.member.entity.MemberLiftingRule;
import com.lyy.user.domain.member.repository.MemberLiftingMapper;
import com.lyy.user.infrastructure.base.MerchantBaseServiceImpl;
import com.lyy.user.infrastructure.constants.MemberLiftingStrategyEnum;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 会员升级策略 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
@Service
@Slf4j
public class MemberLiftingServiceImpl extends MerchantBaseServiceImpl<MemberLiftingMapper, MemberLifting> implements IMemberLiftingService {

    @Autowired
    private IMemberLiftingRuleService memberLiftingRuleService;

    /**
     * 保存或更新升级策略信息
     *
     * @param memberLiftingSaveDTO
     * @return
     */
    @Override
    public Long saveOrUpdate(MemberLiftingSaveDTO memberLiftingSaveDTO) {
        MemberLifting memberLifting = new MemberLifting();
        BeanUtils.copyProperties(memberLiftingSaveDTO, memberLifting, "ruleList");
        memberLifting.setUpdateTime(new Date());
        if (memberLifting.getId() == null) {
            memberLifting.setCreateTime(new Date());
        }
        memberLifting.setActive(true);
        super.saveOrUpdate(memberLifting);
        List<MemberLiftingRuleDTO> ruleList = memberLiftingSaveDTO.getRuleList();
        memberLiftingRuleService.saveOfMemberLifting(memberLifting.getMerchantId(), memberLifting.getId(), ruleList);

        return memberLifting.getId();
    }

//    private  List<MemberLiftingRuleDTO> addDefaultRule(){
//        MemberLiftingRuleDTO memberLiftingRuleDTO = new MemberLiftingRuleDTO();
//        memberLiftingRuleDTO.setRangeDate((short)1);
//        memberLiftingRuleDTO.setCategory(MemberLiftingRuleCategoryEnum.CATEGORY_CONSUMPTION_MONEY.getValue());
//        memberLiftingRuleDTO.setRangeValue(BigDecimal.ONE);
//        memberLiftingRuleDTO.setJudgeCondition(true);
//        List<MemberLiftingRuleDTO> list = new ArrayList<>();
//        list.add(memberLiftingRuleDTO);
//        return list;
//
//    }

    /**
     * 根据会员组获取对应的会员策略
     *
     *
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    @Override
    public List<MemberLifting> findByMemberGroup(Long merchantId, Long memberGroupId) {
        QueryWrapper<MemberLifting> queryWrapper = new QueryWrapper<MemberLifting>()
                .eq(getShardingFieldKey(), merchantId)
                .eq("member_group_id",memberGroupId)
                .and(wrapper -> wrapper.isNull("active").or().eq("active", true));
        return list(queryWrapper);
    }

    /**
     * 根据id获取详情信息
     *
     *
     * @param merchantId
     * @param memberLiftingId
     * @return
     */
    @Override
    public MemberLiftingSaveDTO getInfoById(Long merchantId, Long memberLiftingId) {
        MemberLifting memberLifting = getById(merchantId,memberLiftingId);
        if(memberLifting == null){
            return null;
        }
        MemberLiftingSaveDTO memberLiftingSaveDTO = new MemberLiftingSaveDTO();
        BeanUtils.copyProperties(memberLifting,memberLiftingSaveDTO);
        //查找规则列表信息
        List<MemberLiftingRule> liftingRules =  memberLiftingRuleService.findByMemberLifting(merchantId, memberLiftingId);
        if(liftingRules.isEmpty()){
            memberLiftingSaveDTO.setRuleList(Collections.EMPTY_LIST);
        }else{
            List<MemberLiftingRuleDTO> ruleList = liftingRules.stream().map(liftingRule->{
                MemberLiftingRuleDTO memberLiftingRuleDTO = new MemberLiftingRuleDTO();
                BeanUtils.copyProperties(liftingRule,memberLiftingRuleDTO);
                return memberLiftingRuleDTO;
            }).collect(Collectors.toList());
            memberLiftingSaveDTO.setRuleList(ruleList);
        }
        return memberLiftingSaveDTO;
    }

    /**
     * 根据会员组信息，获取对应的升级策略
     *
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    @Override
    public List<MemberLiftingSaveDTO> findInfoByMemberGroup(Long merchantId, Long memberGroupId) {
        //获取所有的升级策略
        List<MemberLifting> memberLiftingList = findByMemberGroup(merchantId, memberGroupId);
        if (CollectionUtils.isEmpty(memberLiftingList)) {
            return Collections.EMPTY_LIST;
        }
        //获取升级策略规则数据
        List<Long> memberLiftingIds = memberLiftingList.stream()
                .map(MemberLifting::getId)
                .collect(Collectors.toList());
        List<MemberLiftingRule> liftingRules = memberLiftingRuleService.findByMemberLifting(merchantId, memberLiftingIds);
        Map<Long, List<MemberLiftingRule>> ruleMap;
        if (CollectionUtils.isEmpty(liftingRules)) {
            ruleMap = new HashMap<>(16);
        } else {
            ruleMap = liftingRules.stream()
                    .collect(Collectors.groupingBy(MemberLiftingRule::getMemberLiftingId));
        }
        //组装及转换为dto数据
        return memberLiftingList.stream()
                .map(memberLifting -> {
                    MemberLiftingSaveDTO memberLiftingSaveDTO = new MemberLiftingSaveDTO();
                    BeanUtils.copyProperties(memberLifting, memberLiftingSaveDTO);
                    List<MemberLiftingRule> memberLiftingRuleList = ruleMap.get(memberLiftingSaveDTO.getId());
                    if (CollectionUtils.isEmpty(memberLiftingRuleList)) {
                        memberLiftingSaveDTO.setRuleList(Collections.EMPTY_LIST);
                    } else {
                        List<MemberLiftingRuleDTO> ruleList = memberLiftingRuleList.stream().map(rule -> {
                            MemberLiftingRuleDTO memberLiftingRuleDTO = new MemberLiftingRuleDTO();
                            BeanUtils.copyProperties(rule, memberLiftingRuleDTO);
                            return memberLiftingRuleDTO;
                        }).collect(Collectors.toList());
                        memberLiftingSaveDTO.setRuleList(ruleList);
                    }
                    return memberLiftingSaveDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据策略id删除对应的数据
     *
     *
     * @param merchantId
     * @param memberLiftingIds
     * @return
     */
    @Override
    public int removeByLiftingIds(Long merchantId, Collection<Long> memberLiftingIds) {
        if(memberLiftingIds == null || memberLiftingIds.isEmpty()){
            return 0;
        }
        int del = memberLiftingRuleService.removeByMemberLifting(merchantId,memberLiftingIds);
        log.debug("删除 {} 升级策略 {} 个规则数据",memberLiftingIds,del);
        UpdateWrapper<MemberLifting> wrapper = new UpdateWrapper<MemberLifting>()
                .eq(getShardingFieldKey(), merchantId)
                .in("id", memberLiftingIds);
        return getBaseMapper().delete(wrapper);
    }

    /**
     * 分页获取存在不满足条件的升级规则内容
     *
     * @param page
     * @param merchantId
     * @return
     */
    @Override
    public Page<MemberLifting> findOfJudgeFalse(Page page, Long merchantId) {
        QueryWrapper<MemberLifting> queryWrapper = new QueryWrapper<MemberLifting>()
                .orderByAsc("id")
                .exists("select mlr.*\n" +
                        "from um_member_lifting_rule mlr\n" +
                        "where um_member_lifting.id = mlr.member_lifting_id\n" +
                        "and judge_condition = false ")
                .and(wrapper -> wrapper.eq("merchant_id", merchantId).or().exists("select 1"))
                .eq("merchant_id % 128", merchantId);

        return page(page, queryWrapper);
    }

    /**
     * 批量更新会员组的升级策略信息
     *
     * @param merchantId
     * @param memberGroupId
     * @param memberLiftingList
     * @return
     */
    @Override
    public int saveOrUpdateBatchMemberLifting(Long merchantId, Long memberGroupId, List<MemberLiftingSaveDTO> memberLiftingList) {
        //目前已经保存的升降级策略
        List<MemberLiftingSaveDTO> oldLiftingList = findSaveInfoByGroup(merchantId, memberGroupId);
        List<MemberLiftingSaveDTO> defaultList = oldLiftingList.stream()
                //过滤出默认升级策略
                .filter(this::checkLiftingDefault)
                .collect(Collectors.toList());
        //补充默认列表
        memberLiftingList.addAll(defaultList);
        //先清理旧的等级信息
        List<Long> notInList = memberLiftingList.stream()
                .map(MemberLiftingSaveDTO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        int del = removeByMemberGroup(merchantId, memberGroupId, notInList);
        log.info("清除 {} 商户下的 {} 会员组 {} 条升级策略信息", merchantId, memberGroupId, del);

        if (CollectionUtils.isEmpty(defaultList) && !checkLiftingDefault(memberLiftingList)) {
            //若没有默认的升级任务，则自动添加默认任务
            log.info(" {} 商户下的 {} 会员组没有默认的升级策略(按消费金额升级)，自动添加默认升级策略", merchantId, memberGroupId);
            memberLiftingList.add(createDefaultLifting());
        }
        memberLiftingList.stream()
//                .filter(memberLiftingSaveDTO->defaultList.contains(memberLiftingSaveDTO))
                .forEach(memberLiftingSaveDTO -> {
                    memberLiftingSaveDTO.setMemberGroupId(memberGroupId);
                    memberLiftingSaveDTO.setMerchantId(merchantId);
                    //若没有输入成长值，则默认为0成长值，主要是处理降级策略，因为改为直接降等级
                    memberLiftingSaveDTO.setGrowValue(Optional.ofNullable(memberLiftingSaveDTO.getGrowValue()).orElse(0L));
                    //先不处理批量保存的问题，先遍历
                    saveOrUpdate(memberLiftingSaveDTO);
                });
        return memberLiftingList.size();
    }
    /**
     * 检查是否为默认的升级任务
     * @param memberLifting
     * @return
     */
    @Override
    public boolean checkLiftingDefault(MemberLiftingSaveDTO memberLifting) {
        if (memberLifting.getLifting().equals(MemberLiftingStrategyEnum.LIFTING_STRATEGY_UPGRADE.getValue())){
            //升级策略判断
            //过滤没有规则的
            if(CollectionUtils.isEmpty(memberLifting.getRuleList())){
                return false;
            }
            long ruleCount = memberLifting.getRuleList().stream()
                    //过滤有消费金额的
                    .filter(rule->rule.getCategory().equals(MemberLiftingRuleCategoryEnum.CATEGORY_CONSUMPTION_MONEY.getValue()))
                    .count();
            return ruleCount > 0;
        }
        return false;
    }

    /**
     * 获取会员组对应的保存策略信息
     *
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    @Override
    public List<MemberLiftingSaveDTO> findSaveInfoByGroup(Long merchantId, Long memberGroupId) {

        //处理升级策略信息
        List<MemberLifting> liftingList = findByMemberGroup(merchantId,memberGroupId);
        if(!liftingList.isEmpty()) {
            //处理升级策略规则数据
            List<Long> liftingIds = liftingList.stream()
                    .map(MemberLifting::getId)
                    .collect(Collectors.toList());
            List<MemberLiftingRule> memberLiftingRuleList = memberLiftingRuleService.findByMemberLifting(merchantId, liftingIds);
            Map<Long, List<MemberLiftingRuleDTO>> ruleMap = memberLiftingRuleList.stream()
                    .map(memberLiftingRule -> {
                        MemberLiftingRuleDTO memberLiftingRuleDTO = new MemberLiftingRuleDTO();
                        BeanUtils.copyProperties(memberLiftingRule, memberLiftingRuleDTO);
                        return memberLiftingRuleDTO;
                    })
                    .collect(Collectors.groupingBy(MemberLiftingRuleDTO::getMemberLiftingId));

            //转换升级策略数据
            return liftingList.stream()
                    .map(memberLifting -> {
                        MemberLiftingSaveDTO memberLiftingSaveDTO = new MemberLiftingSaveDTO();
                        BeanUtils.copyProperties(memberLifting, memberLiftingSaveDTO);
                        List<MemberLiftingRuleDTO> memberLiftingRuleDTOList = Optional.ofNullable(ruleMap.get(memberLifting.getId()))
                                .orElse(Collections.emptyList());
                        memberLiftingSaveDTO.setRuleList(memberLiftingRuleDTOList);
                        return memberLiftingSaveDTO;
                    })
                    //过滤默认的升降级策略
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 分页获取降级策略
     *
     * @param page
     * @param merchantId
     * @return
     */
    @Override
    public Page<MemberLifting> findLiftingDemoting(Page page, Long merchantId) {
        QueryWrapper queryWrapper = new QueryWrapper<MemberLifting>()
                .eq("lifting", MemberLiftingStrategyEnum.LIFTING_STRATEGY_DEMOTING_LEVEL.getValue())
                .orderByAsc("merchant_id", "member_group_id", "id")
                .and(wrapper -> wrapper.eq("merchant_id", merchantId).or().exists("select 1"))
                .eq("merchant_id % 128", merchantId);
        return page(page, queryWrapper);
    }

    @Override
    public MemberLifting getSmallVenueLiftingInfoByGroup(Long merchantId, Long memberGroupId) {
        MemberLifting memberLifting = this.baseMapper.selectOne(new QueryWrapper<MemberLifting>().eq("merchant_id", merchantId).eq("member_group_id", memberGroupId).and(wrapper -> wrapper.isNull("active").or().eq("active", true)));
        if (memberLifting == null) {
            // 创建默认的升降级策略
            MemberLifting newMemberLifting = new MemberLifting();
            newMemberLifting.setLifting(MemberLiftingStrategyEnum.LIFTING_STRATEGY_UPGRADE.getValue());
            newMemberLifting.setMemberGroupId(memberGroupId);
            newMemberLifting.setMerchantId(merchantId);
            newMemberLifting.setGrowValue(1L);
            newMemberLifting.setCondition((short) 0);
            newMemberLifting.setEffectiveNumber((short) -1);
            newMemberLifting.setCreateTime(new Date());
            newMemberLifting.setUpdateTime(new Date());
            this.baseMapper.insert(newMemberLifting);
            return newMemberLifting;
        }
        return memberLifting;
    }

    /**
     * 检查是否有默认的升级任务
     * @param memberLiftingList
     * @return
     */
    private boolean checkLiftingDefault(List<MemberLiftingSaveDTO> memberLiftingList) {
        if(memberLiftingList.isEmpty()){
            return false;
        }
        long count = memberLiftingList.stream()
                //过滤升级的策略
                .filter(memberLifting ->checkLiftingDefault(memberLifting))
                .count();
        return count > 0;
    }

    /**
     * 创建默认的升级策略，默认为按照消费金额1元1成长值的方式
     * @return
     */
    private MemberLiftingSaveDTO createDefaultLifting() {
        MemberLiftingSaveDTO memberLiftingSaveDTO = new MemberLiftingSaveDTO();
        memberLiftingSaveDTO.setLifting(MemberLiftingStrategyEnum.LIFTING_STRATEGY_UPGRADE.getValue());
        memberLiftingSaveDTO.setGrowValue(1L);
        memberLiftingSaveDTO.setCondition(MemberLiftingConditionEnum.OR_ONE.getValue());
        memberLiftingSaveDTO.setEffectiveNumber((short) -1);
        memberLiftingSaveDTO.setName(MemberLiftingRuleCategoryEnum.CATEGORY_CONSUMPTION_MONEY.getDescription());
        //升级规则
        MemberLiftingRuleDTO memberLiftingRuleDTO = new MemberLiftingRuleDTO();
        //默认为0天，即表示只有一次起作用
        memberLiftingRuleDTO.setRangeDate((short)0);
        memberLiftingRuleDTO.setCategory(MemberLiftingRuleCategoryEnum.CATEGORY_CONSUMPTION_MONEY.getValue());
        memberLiftingRuleDTO.setRangeValue(BigDecimal.ONE);
        memberLiftingRuleDTO.setJudgeCondition(true);
        List<MemberLiftingRuleDTO> ruleList = new ArrayList<>(1);
        ruleList.add(memberLiftingRuleDTO);
        memberLiftingSaveDTO.setRuleList(ruleList);
        return memberLiftingSaveDTO;
    }

    private int removeByMemberGroup(Long merchantId, Long memberGroupId, List<Long> notInList) {
        UpdateWrapper<MemberLifting> wrapper = new UpdateWrapper<MemberLifting>()
                .eq(getShardingFieldKey(), merchantId)
                .eq("member_group_id", memberGroupId)
                .and(queryWrapper -> queryWrapper.isNull("active").or().eq("active", true))
                .set("active", false)
                .notIn(!notInList.isEmpty(), "id", notInList);
        return getBaseMapper().update(null, wrapper);
    }
}
