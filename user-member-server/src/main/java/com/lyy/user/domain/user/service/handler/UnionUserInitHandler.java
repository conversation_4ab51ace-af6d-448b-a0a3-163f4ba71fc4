package com.lyy.user.domain.user.service.handler;

import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.app.interfaces.facade.dto.UserVO;
import com.lyy.user.domain.user.service.AbstractUserInitService;
import com.lyy.user.domain.user.service.UserInitHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName: UnionUserInitHandler
 * @description: 云闪付用户初始化
 * @author: pengkun
 * @date: 2021/04/06
 **/
@Slf4j
@Service("unionUserInit")
public class UnionUserInitHandler extends AbstractUserInitService implements UserInitHandler {
    /**
     * 用户初始化处理方法
     * @param dto
     * @return
     */
    @Override
    public UserInfoDTO handle(UserCreateDTO dto) {
        log.debug("云闪付用户初始化");
        String telephone = "";
        String isActive = null;
        UserVO openUser = super.getByOpenId(dto.getOpenid());
        Long userId;
        if (openUser == null) {
            userId = super.initPlatformUser(dto);
        } else {
            telephone = openUser.getTelephone();
            userId = openUser.getId();
            isActive = openUser.getIsActive();
        }
        return getUserInfoDTO(dto, userId, telephone, isActive);
    }
}
