package com.lyy.user.domain.user.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: 自动打标签DTO
 * @author: qgw
 * @date on 2021-07-07.
 * @Version: 1.0
 */
@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TagUserParamDTO {

    private Integer tradeType;

    private String userType;

    /**
     * 平台用户ID
     */
    private Long userId;

    /**
     * 性别
     */
    private String gender;

    /**
     * 商家用户ID
     */
    private Long merchantUserId ;

    private Long merchantId ;

    /**
     * 场地/店铺名称
     */
    private String storeName;
    /**
     * 设备类型名称
     */
    private String equipmentTypeName;

}
