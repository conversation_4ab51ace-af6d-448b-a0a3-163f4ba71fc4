package com.lyy.user.domain.benefit.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *  权益消耗规则表
 *
 * <AUTHOR>
 * @create 2021/3/30 17:27
 */
@ToString
@Getter
@Setter
@TableName(value = "um_benefit_consume_rule")
public class BenefitConsumeRule {

    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    /**
     *  商户ID
     */
    @TableField(value="merchant_id")
    private Long merchantId;

    /**
     * 权益类型
     */
    @TableField(value="benefit_classify")
    private Integer benefitClassify;


    /**
     * 权重
     */
    @TableField(value="weight")
    private Integer weight;

    /**
     * 优先级
     */
    @TableField(value="expire_priority")
    private Integer expirePriority;

    /**
     * 是否是默认的
     */
    @TableField(value="is_default")
    private Boolean isDefault;


    @TableField(value = "is_active")
    private Boolean isActive;

    @TableField(value="create_by")
    private Long  createBy;

    @TableField(value="update_by")
    private Long  updateBy;

    @TableField(value="create_time")
    private Date  createTime;

    @TableField(value="update_time")
    private Date updateTime;

}
