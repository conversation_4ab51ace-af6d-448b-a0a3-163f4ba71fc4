package com.lyy.user.domain.member.dto;

import com.lyy.user.domain.member.entity.Member;
import com.lyy.user.domain.member.entity.MemberLifting;
import com.lyy.user.domain.member.entity.MemberLiftingRule;
import com.lyy.user.domain.member.entity.MemberLiftingRuleRecord;
import java.util.Collection;
import java.util.Map;
import lombok.Data;


/**
 * <AUTHOR>
 * @className: MemberLiftingStrategyResultDTO
 * @date 2021/4/7
 */
@Data
public class MemberLiftingStrategyResultDTO {

    /**
     * 会员
     */
    private Member member;

    /**
     * 会员升级策略
     */
    private MemberLifting memberLifting;

    /**
     * 符合规则的记录
     */
    private Map<MemberLiftingRule, Collection<MemberLiftingRuleRecord>> memberLiftingRuleMap;

    /**
     * 失效的记录
     */
    private Collection<MemberLiftingRuleRecord> failureRecord;

}
