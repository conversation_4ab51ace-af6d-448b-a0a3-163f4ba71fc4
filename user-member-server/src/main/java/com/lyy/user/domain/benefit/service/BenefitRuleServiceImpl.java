package com.lyy.user.domain.benefit.service;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lyy.user.application.benefit.BenefitRuleService;
import com.lyy.user.domain.benefit.entity.BenefitRule;
import com.lyy.user.domain.benefit.repository.BenefitRuleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/10/13
 */
@Service
@Slf4j
public class BenefitRuleServiceImpl extends ServiceImpl<BenefitRuleMapper, BenefitRule> implements BenefitRuleService {

}
