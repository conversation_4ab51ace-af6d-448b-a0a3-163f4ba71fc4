package com.lyy.user.domain.member.lifting;

import com.lyy.user.domain.member.dto.MemberLiftingStrategyDTO;
import com.lyy.user.domain.member.dto.MemberLiftingStrategyResultDTO;

/**
 * <AUTHOR>
 * @className: LiftingStrategyInterface
 * @date 2021/4/6
 */
public interface LiftingStrategyInterface {


    /**
     * 处理得到升级结果，但是还没有进行升级
     * @param memberLiftingStrategyDTO
     * @return
     */
    MemberLiftingStrategyResultDTO handlerResult(MemberLiftingStrategyDTO memberLiftingStrategyDTO);

    /**
     * 处理失效的记录
     * @param memberLiftingStrategyResultDTO
     * @return
     */
    int handlerFailure(MemberLiftingStrategyResultDTO memberLiftingStrategyResultDTO);


    /**
     * 处理成功的记录
     * @param memberLiftingStrategyResultDTO
     * @return
     */
    int handlerSuccess(MemberLiftingStrategyResultDTO memberLiftingStrategyResultDTO);
//    /**
//     * 检查会员组的的升级规则策略是否符合
//     * @param userId
//     * @param memberGroup
//     * @return
//     */
//    boolean checkRule(Long userId, MemberGroup memberGroup);
//
//
//    /**
//     * 检查会员组的的升级规则策略是否符合
//     * @param userId
//     * @param MemberLifting
//     * @return
//     */
//    boolean checkRule(Long userId, MemberLifting MemberLifting);
//
//    /**
//     * 查询升级规则成功的操作
//     * @param userId
//     * @param memberGroup
//     * @return
//     */
//    Long checkRuleSuccess(Long userId, MemberGroup memberGroup);
//
//    /**
//     * 查询升级规则失败的操作
//     * @param userId
//     * @param memberGroup
//     * @return
//     */
//    Long checkRuleFail(Long userId, MemberGroup memberGroup);
}
