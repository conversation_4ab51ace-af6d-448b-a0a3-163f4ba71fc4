package com.lyy.user.domain.user.service.handler;

import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.app.interfaces.facade.dto.UserVO;
import com.lyy.user.domain.user.service.AbstractUserInitService;
import com.lyy.user.domain.user.service.UserInitHandler;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName: JdUserInitHandler
 * @description: 京东用户初始化
 * @author: pengkun
 * @date: 2021/04/06
 **/
@Slf4j
@Service("jdUserInit")
public class JdUserInitHandler extends AbstractUserInitService implements UserInitHandler {
    /**
     * 用户初始化处理方法
     * @param dto
     * @return
     */
    @Override
    public UserInfoDTO handle(UserCreateDTO dto) {
        log.debug("京东用户初始化");
        String telephone = "";
        String isActive = null;
        Long userId;
        if(UNION_AUTH_MODEL.equalsIgnoreCase(dto.getSilent())){
            log.debug("unionId授权新建用户");
            UserVO unionUser = super.getByUnionId(dto.getUnionid());
            if(Objects.isNull(unionUser)){
                //初始化京东用户
                userId = initPlatformUser(dto);
            }else {
                userId = unionUser.getId();
                telephone = unionUser.getTelephone();
                isActive = unionUser.getIsActive();
            }
        }else {
            log.debug("openId授权新建用户");
            UserVO openUser = super.getByOpenId(dto.getOpenid());
            if(Objects.isNull(openUser)){
                //初始化京东用户
                userId = initPlatformUser(dto);
            }else {
                userId = openUser.getId();
                telephone = openUser.getTelephone();
                isActive = openUser.getIsActive();
            }
        }
        return getUserInfoDTO(dto, userId, telephone, isActive);
    }
}
