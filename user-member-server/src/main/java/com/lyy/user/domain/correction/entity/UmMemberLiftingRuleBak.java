package com.lyy.user.domain.correction.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 会员升级策略规则
 * @TableName um_member_lifting_rule_bak
 */
@Data
public class UmMemberLiftingRuleBak implements Serializable {

    private static final long serialVersionUID = 8147647379043669704L;
    /**
     * 
     */
    private Long id;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 升降级策略
     */
    private Long memberLiftingId;

    /**
     * 超过日期范围,就是说多少天之内消耗触发多少次该规则
     */
    private Integer rangeDate;

    /**
     * 策略(1 登录次数,2 支付笔数,3 消费金额,4完善信息，5绑定手机，6关注公众号)
     */
    private Integer category;

    /**
     * 范围值,若为消费金额时，单位为元
     */
    private BigDecimal rangeValue;

    /**
     * 
     */
    private Boolean judgeCondition;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 是否有效 true 有效,false 无效
     */
    private Boolean active;
}