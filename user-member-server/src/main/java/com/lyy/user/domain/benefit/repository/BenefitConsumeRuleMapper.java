package com.lyy.user.domain.benefit.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lyy.user.domain.benefit.entity.BenefitConsumeRule;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


/**
 * <AUTHOR>
 * @create 2021/3/30 18:04
 */
@Repository
public interface BenefitConsumeRuleMapper extends BaseMapper<BenefitConsumeRule> {

    /**
     * 根据商户和权益类型获取权益消耗规则
     *
     * @param merchantId 商户id
     * @param classify   权益消耗规则
     * @return
     */
    BenefitConsumeRule getByMerchantIdAndClassify(@Param("merchantId") Long merchantId, @Param("classify") Integer classify);

    /**
     *
     * @param record
     * @return
     */
    int updateBenefitConsumeRule(@Param("record") BenefitConsumeRule record);

    @Override
    int insert(BenefitConsumeRule entity);
}
