package com.lyy.user.domain.user.service;

import static com.lyy.user.account.infrastructure.constant.UserMemberSysConstants.TAG_MERCHANT_USER;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.idempotent.core.annotation.Idempotent;
import com.lyy.lock.redis.RedisLock;
import com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum;
import com.lyy.user.account.infrastructure.constant.TagCategoryEnum;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.account.infrastructure.user.dto.tag.TagBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserNumberDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserNumberParam;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagSimpleInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagSimpleInfoParam;
import com.lyy.user.account.infrastructure.user.dto.tag.TagStatusDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUnBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserDetailDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserSaveDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingMerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingMerchantUserLinkDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.UpdateSpecificBusinessTagsParam;
import com.lyy.user.application.user.IMerchantUserTagService;
import com.lyy.user.application.user.ITagUserService;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.domain.user.entity.MerchantUserTag;
import com.lyy.user.domain.user.entity.TagUser;
import com.lyy.user.domain.user.repository.MerchantUserMapper;
import com.lyy.user.domain.user.repository.MerchantUserTagMapper;
import com.lyy.user.domain.user.repository.TagUserMapper;
import com.lyy.user.infrastructure.constants.RedisKey;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.repository.tag.TagRepository;
import com.lyy.user.infrastructure.util.CommonConverterTools;
import com.lyy.user.interfaces.assembler.user.TagUserMapStruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@Service
@Slf4j
public class TagUserServiceImpl extends ServiceImpl<TagUserMapper, TagUser> implements ITagUserService {

    @Resource
    private TagUserMapper tagUserMapper;
    @Resource
    private MerchantUserTagMapper merchantUserTagMapper;

    @Autowired
    private IMerchantUserTagService tagMerchantUserService;

    @Resource
    private MerchantUserMapper merchantUserMapper;

    @Resource
    private TagRepository tagRepository;

    @Autowired
    @Lazy
    private AsyncProcessHandler asyncProcessHandler;

    @Resource
    private RedisLock redisLock;

    /**
     * 锁有效时间为 5分钟
     */
    @Value("${member.user.task.binding.time:300000}")
    private long taskBindingTime;
    /**
     * 锁有效时间为 5分钟
     */
    @Value("${member.user.task.unbinding.time:300000}")
    private long taskUnBindingTime;

    @Override
    public Page<TagUserListDTO> list(TagUserQueryDTO param) {
        Page<TagUser> page = new Page<>(param.getPageIndex(), param.getPageSize());
        if (Boolean.FALSE.equals(param.getCountSql())) {
            page.setSearchCount(false);
        }
        QueryWrapper<TagUser> queryWrapper = buildTagQueryWrapper(param);
        Page<TagUser> list = tagUserMapper.selectPage(page, queryWrapper);
        List<TagUser> records = list.getRecords();
        List<TagUserListDTO> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(records)) {
            boolean flag = Objects.nonNull(param.getQueryUserNumber()) && param.getQueryUserNumber();
            records.forEach(e -> {
                log.debug("用户标签:{}", e);
                TagUserListDTO dto = new TagUserListDTO();
                BeanUtils.copyProperties(e, dto);
                if (flag) {
                    Long[] tags = new Long[]{e.getId()};
                    dto.setUserNumber(merchantUserTagMapper.countUserMember(tags, e.getMerchantId(), param.getTagType(),
                            UserMemberSysConstants.ENABLE));
                }
                dto.setBusinessTypeName(Optional.ofNullable(TagBusinessTypeEnum.of(e.getBusinessType())).map(TagBusinessTypeEnum::getDesc).orElse(null));
                result.add(dto);
            });
        }
        Page<TagUserListDTO> pageResult = new Page<>(page.getCurrent(), page.getSize(), page.getTotal(), page.isSearchCount());
        pageResult.setRecords(result);
        return pageResult;

    }

    @Override
    public List<TagUserListDTO> listAllTag(TagUserQueryDTO param) {
        QueryWrapper<TagUser> queryWrapper = buildTagQueryWrapper(param);
        List<TagUser> tagList = list(queryWrapper);
        return tagList.stream()
                .filter(Objects::nonNull)
                .map(TagUserMapStruct.INSTANCE::toTagUserListDTO)
                .collect(Collectors.toList());
    }

    /**
     * 根据用户查所属标签
     *
     * @param dto
     * @return
     */
    @Override
    public Page<TagUserListDTO> listTagByUser(TagUserQueryDTO dto) {
        if (dto.getMerchantUserId() == null) {
            throw new BusinessException(UserErrorCode.PLATFORM_USER_QUERY_PARAM_ERROR);
        }
        Page<TagUserListDTO> page = new Page<>(dto.getPageIndex(), dto.getPageSize());

        List<TagUserListDTO> list = Lists.newArrayList();
        Long count = 0L;
        if (Boolean.FALSE.equals(dto.getCountSql())) {
            list = tagUserMapper.listTagByUser(dto, page.offset(), page.getSize());
        } else {
            count = Optional.ofNullable(tagUserMapper.countListTagByUser(dto)).orElse(0L);
            if (count > 0) {
                list = tagUserMapper.listTagByUser(dto, page.offset(), page.getSize());
            }
        }
        page.setTotal(count);
        page.setRecords(list);
        return page;

    }


    /**
     * 根据标签查所属用户
     *
     * @param dto
     * @return
     */
    @Override
    public TagUserDetailDTO findByTagId(TagUserQueryDTO dto) {
        QueryWrapper<TagUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", dto.getId());
        Long merchantId = dto.getMerchantId();
        queryWrapper.eq("merchant_id", merchantId);
        TagUser tagUser = tagUserMapper.selectOne(queryWrapper);
        if (tagUser == null) {
            throw new BusinessException(UserErrorCode.TAG_USER_NOT_EXIST_ERROR);
        }
        boolean queryIdFlag = checkSearchNameIsNumeric(dto.getName());
        TagUserDetailDTO result = new TagUserDetailDTO();
        BeanUtils.copyProperties(tagUser, result);
        Page<TagUserInfoDTO> page = new Page<>(dto.getPageIndex(), dto.getPageSize());
        List<Long> tagIds = new ArrayList<>();
        List<TagUserInfoDTO> list = new ArrayList<>();
        Long count = 0L;
        if (dto.getId() != null) {
            tagIds.add(dto.getId());
        }
        if (Boolean.FALSE.equals(dto.getCountSql())) {
            list = tagUserMapper.listTagUserInfoByTagIds(dto, queryIdFlag, tagIds.toArray(new Long[0]), page.offset(), page.getSize());
        } else {
            count = Optional.ofNullable(tagUserMapper.countListTagUserInfoByTagIds(dto, queryIdFlag, tagIds.toArray(new Long[0]))).orElse(0L);
            if (count > 0) {
                list = tagUserMapper.listTagUserInfoByTagIds(dto, queryIdFlag, tagIds.toArray(new Long[0]), page.offset(), page.getSize());
            }
        }
        page.setTotal(count);
        result.setUserInfos(list);
        result.setUserNumber(count);
        return result;

    }

    private boolean checkSearchNameIsNumeric(String searchName) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(searchName)) {
            if (org.apache.commons.lang3.StringUtils.isNumeric(searchName)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Long countFindByTagId(TagCountUserQueryDTO dto) {
        TagUserQueryDTO param = new TagUserQueryDTO();
        BeanUtils.copyProperties(dto, param);
        boolean queryIdFlag = checkSearchNameIsNumeric(dto.getName());
        List<Long> tagIds = new ArrayList<>();
        if (dto.getId() != null) {
            tagIds.add(dto.getId());
        }
        Long aLong = tagUserMapper.countListTagUserInfoByTagIds(param, queryIdFlag, tagIds.toArray(new Long[0]));
        if (aLong == null) {
            return 0L;
        }
        if (!CollectionUtils.isEmpty(dto.getNotHandleUserIds())) {
            int size = dto.getNotHandleUserIds().size();
            if (aLong < size) {
                return aLong;
            }
            return aLong - size;
        }
        return aLong;
    }

    @Override
    public List<TagCountUserNumberDTO> findMemberCountByTagIds(TagCountUserNumberParam dto) {
        List<TagCountUserNumberDTO> result = new ArrayList<>();
        TagUserQueryDTO param = new TagUserQueryDTO();
        param.setMerchantId(dto.getMerchantId());
        for (Long tagId : dto.getTagIds()) {
            List<Long> tagIds = new ArrayList<>();
            tagIds.add(tagId);
            Long aLong = tagUserMapper.countListTagUserInfoByTagIds(param, null, tagIds.toArray(new Long[0]));
            TagCountUserNumberDTO countUserNumberDTO = new TagCountUserNumberDTO();
            countUserNumberDTO.setTagId(tagId);
            countUserNumberDTO.setMemberCount(Optional.ofNullable(aLong).map(Long::intValue).orElse(0));
            result.add(countUserNumberDTO);
        }
        return result;
    }

    private QueryWrapper<TagUser> buildTagQueryWrapper(TagUserQueryDTO param) {
        QueryWrapper<TagUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("merchant_id", param.getMerchantId());
        if (param.getId() != null) {
            queryWrapper.eq("id", param.getId());
        }
        if (StringUtils.isNotBlank(param.getName())) {
            queryWrapper.like("name", param.getName().trim());
        }
        if (param.getActive() != null) {
            queryWrapper.eq("is_active", param.getActive());
        }
        if (param.getTagType() != null) {
            queryWrapper.eq("tag_type", param.getTagType());
        }
        if (param.getBusinessType() != null) {
            queryWrapper.eq("business_type", param.getBusinessType());
        }
        if (param.getCategory() != null) {
            queryWrapper.eq("category", param.getCategory());
        }
        if (param.getState() != null) {
            queryWrapper.eq("state", param.getState());
        } else {
            queryWrapper.eq("state", UserMemberSysConstants.RECORD_STATE_NORMAL);
        }
        queryWrapper.orderByDesc("create_time");
        return queryWrapper;
    }


    @Override
    public Long saveOrUpdateTagUser(TagUserSaveDTO saveDTO) {
        saveDTO.setName(saveDTO.getName().trim());
        checkTagName(saveDTO);
        if (saveDTO.getId() == null) {
            QueryWrapper<TagUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("merchant_id", saveDTO.getMerchantId());
            queryWrapper.eq("name", saveDTO.getName());
            queryWrapper.eq("tag_type", saveDTO.getTagType());
            queryWrapper.eq("business_type", saveDTO.getBusinessType());
            queryWrapper.eq("state", UserMemberSysConstants.RECORD_STATE_NORMAL);
            queryWrapper.eq("is_active", Optional.ofNullable(saveDTO.getActive()).orElse(UserMemberSysConstants.ENABLE));
            TagUser tagUser;
            // 根据用户-商户加锁，重试时间3000ms，锁超时5000ms
            String lockKey = RedisKey.MEMBER_TAG_CREATE_LOCK
                    + saveDTO.getMerchantId()
                    + ":" + saveDTO.getName();
            if (redisLock.lock(lockKey, UserMemberSysConstants.YES, 5000, 1) == null) {
                log.error("获取锁失败 -> {}", lockKey);
                throw new BusinessException(UserErrorCode.TAG_USER_SAVE_ERROR);
            }
            try {
                tagUser = tagUserMapper.selectOne(queryWrapper);
                if (tagUser == null) {
                    return createTagUser(saveDTO);
                }
                saveDTO.setId(tagUser.getId());
            } finally {
                redisLock.unlock(lockKey, UserMemberSysConstants.YES);
            }
        }
        //更新
        return updateTagUser(saveDTO);
    }

    private Long updateTagUser(TagUserSaveDTO saveDTO) {
        Long id = saveDTO.getId();
        QueryWrapper<TagUser> query = new QueryWrapper<>();
        query.eq("id", id);
        query.eq("merchant_id", saveDTO.getMerchantId());
        TagUser tagUser = tagUserMapper.selectOne(query);
        if (tagUser == null) {
            throw new BusinessException(UserErrorCode.TAG_USER_NOT_EXIST_ERROR);
        }
        if (saveDTO.getActive() != null) {
            tagUser.setActive(saveDTO.getActive());
        }
        tagUser.setName(saveDTO.getName().trim());
        if (!StringUtils.isEmpty(saveDTO.getCode())) {
            tagUser.setCode(saveDTO.getCode());
        }
        if (saveDTO.getCategory() != null) {
            tagUser.setCategory(saveDTO.getCategory());
        }
        //若已删除,则变更为正常
        tagUser.setState(UserMemberSysConstants.RECORD_STATE_NORMAL);
        tagUser.setDescription(saveDTO.getDescription());
        Long operatorId = saveDTO.getOperatorId();
        Integer tagType = tagUser.getTagType();
        tagUser.setUpdatedby(operatorId);
        tagUser.setUpdateTime(new Date());

        int updateById = tagUserMapper.updateTagUser(tagUser);
        if (updateById > 0) {
            //更改性别
            cleanSexTag(saveDTO);
            List<Long> tagIds = new ArrayList<>(1);
            tagIds.add(id);
            bindingMerchantUser(saveDTO.getUserIds(), tagIds, operatorId, tagType, saveDTO.getMerchantId());
        } else {
            log.error("标签ID: {} 更新失败:", id);
            throw new BusinessException(UserErrorCode.TAG_USER_SAVE_ERROR);
        }
        return saveDTO.getId();
    }

    private void bindingMerchantUser(List<Long> userIdList, List<Long> tagIds, Long operatorId, Integer tagType, Long merchantId) {
        bindingMerchantUser(userIdList, tagIds, operatorId, tagType, merchantId, false);
    }

    private void bindingMerchantUser(List<Long> userIdList, List<Long> tagIds, Long operatorId, Integer tagType, Long merchantId, boolean retry) {
        if (CollectionUtils.isEmpty(userIdList) || CollectionUtils.isEmpty(tagIds)) {
            return;
        }

        Long[] userTags = tagIds.toArray(new Long[0]);
        Date now = new Date();
        List<MerchantUserTag> list = merchantUserTagMapper.selectListByBusinessUserId(tagType, merchantId, userIdList);
        log.debug("标签list:{},当前线程:{}", list, Thread.currentThread().getName());

        Map<Long, MerchantUserTag> map = list.stream().collect(Collectors.toMap(MerchantUserTag::getBusinessUserId, Function.identity()));
        List<MerchantUserTag> newList = new ArrayList<>();
        userIdList.forEach(userId -> {
            MerchantUserTag old = map.get(userId);
            if (old == null) {
                MerchantUserTag userTag = new MerchantUserTag();
                userTag.setBusinessUserId(userId);
                userTag.setMerchantId(merchantId);
                userTag.setUserTags(userTags);
                userTag.setActive(UserMemberSysConstants.ENABLE);
                userTag.setTagType(tagType);
                userTag.setCreateTime(now);
                userTag.setUpdateTime(now);
                userTag.setCreatedby(operatorId);
                userTag.setUpdatedby(operatorId);
                newList.add(userTag);
            } else {
                Set<Long> tags = Arrays.stream(old.getUserTags()).collect(Collectors.toSet());
                if (!tags.containsAll(new HashSet<>(tagIds))) {
                tags.addAll(tagIds);
                UpdateWrapper updateWrapper = new UpdateWrapper<MerchantUserTag>()
                        .eq("merchant_id", merchantId)
                        .eq("id", old.getId());
                old.setUserTags(tags.toArray(new Long[0]));
                old.setUpdateTime(new Date());
                old.setUpdatedby(operatorId);
                merchantUserTagMapper.update(old, updateWrapper);
                tagRepository.clearMerchantUserTagCache(merchantId, userId);
                }
            }

        });
        if (!CollectionUtils.isEmpty(newList)) {
            try {
                tagMerchantUserService.saveBatch(newList);
            } catch (DuplicateKeyException e) {
                log.warn("用户标签唯一索引重复：{}", e.getMessage());
                if (retry) {
                    // 唯一索引重复，只执行一次
                    return;
                }
                bindingMerchantUser(userIdList, tagIds, operatorId, tagType, merchantId, true);
            }
        }
    }


    private Long createTagUser(TagUserSaveDTO saveDTO) {
            List<Long> userIds = saveDTO.getUserIds();
            TagUser tagUser = CommonConverterTools.convert(TagUser.class, saveDTO);
            if (tagUser.getActive() == null) {
                tagUser.setActive(UserMemberSysConstants.ENABLE);
            }
            tagUser.setBusinessType(saveDTO.getBusinessType());
            tagUser.setCreatedby(saveDTO.getOperatorId());
            tagUser.setCreateTime(new Date());
            int insert = tagUserMapper.insert(tagUser);
            log.debug("保存标签insert: {} , 新增标签: {}", insert, tagUser.getId());
            if (insert > 0 && !CollectionUtils.isEmpty(userIds)) {
                //更改性别
                cleanSexTag(saveDTO);
                List<Long> tagIds = new ArrayList<>(1);
                tagIds.add(tagUser.getId());
                bindingMerchantUser(userIds, tagIds, saveDTO.getOperatorId(), saveDTO.getTagType(), saveDTO.getMerchantId());
            }
            return tagUser.getId();

    }

    /**
     * 修改性别标签，清除其他数据
     *
     * @param saveDTO
     */
    private void cleanSexTag(TagUserSaveDTO saveDTO) {
        if (!TagBusinessTypeEnum.SEX.getStatus().equals(saveDTO.getBusinessType())) {
            return;
        }
        if (CollectionUtils.isEmpty(saveDTO.getUserIds())) {
            return;
        }
        QueryWrapper<TagUser> query = new QueryWrapper<>();
        query.eq("merchant_id", saveDTO.getMerchantId());
        query.eq("tag_type", saveDTO.getTagType());
        query.eq("business_type", TagBusinessTypeEnum.SEX.getStatus());
        query.ne("name", saveDTO.getName().trim());
        List<TagUser> tagUser = tagUserMapper.selectList(query);
        if (CollectionUtils.isEmpty(tagUser)) {
            return;
        }
        List<Long> userIds = new ArrayList<>(saveDTO.getUserIds());
        tagUser.forEach(tu -> tagRepository.removeUserTag(saveDTO.getMerchantId(), userIds, tu.getId()));

    }

    private void checkTagName(TagUserSaveDTO saveDTO) {
        String name = saveDTO.getName().trim();
        QueryWrapper<TagUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("merchant_id", saveDTO.getMerchantId());
        queryWrapper.eq("name", name);
        if (saveDTO.getId() != null) {
            queryWrapper.ne("id", saveDTO.getId());
        }
        TagUser tagUser = tagUserMapper.selectOne(queryWrapper);
        if (tagUser != null) {
            if (UserMemberSysConstants.RECORD_STATE_DELETE.equals(tagUser.getState())) {
                Long tagId = tagUser.getId();
                log.debug("清除逻辑删除的标签及标签用户关系,tagId:{}", tagId);
                merchantUserTagMapper.deleteByTagId(tagId, tagUser.getMerchantId(), null);
                tagUserMapper.deleteByTagId(tagId, tagUser.getMerchantId());
            } else {
                throw new BusinessException(UserErrorCode.TAG_USER_NAME_EXIST_ERROR);
            }
        }
    }

    @Override
    public Boolean changeTagStatus(TagStatusDTO dto) {
        dto.getTagInfos().forEach(e -> {
            List<Long> ids = new ArrayList<>(e.getTagIds());
            tagUserMapper.changeTagStatus(dto.getActive(), dto.getState(), ids, e.getMerchantId(), dto.getOperatorId());
        });
        return true;
    }

    @Override
    public Boolean bindUser(TagBindUserDTO dto) {

        if (dto.getChooseAll() != null && dto.getChooseAll()) {
            //
            String lockKey = RedisKey.TAG_USER_All_BINDING_LOCK + "merchantId:" + dto.getMerchantId();
            if (redisLock.lock(lockKey, UserMemberSysConstants.YES, taskBindingTime, 3000) == null) {
                log.warn("获取锁失败 -> {}", lockKey);
                throw new BusinessException(UserErrorCode.TAG_USER_BINDING_WORKING);
            }
            asyncProcessHandler.bindingUser(dto);
            return true;
        }
        if (!CollectionUtils.isEmpty(dto.getTagIds())) {
            List<Long> tagIds = dto.getTagIds();
            List<TagUser> tagUsers = tagUserMapper.selectBatchIds(tagIds, dto.getMerchantId());
            if (CollectionUtils.isEmpty(tagUsers)) {
                throw new BusinessException(UserErrorCode.TAG_USER_NOT_EXIST_ERROR);
            }
            List<Long> tagIdResults = tagUsers.stream().map(TagUser::getId).distinct().collect(Collectors.toList());
            //tagUsers.get(0).getTagType() 标签类型应该为同一种
            bindingMerchantUser(dto.getUserIds(), tagIdResults, dto.getOperatorId(), tagUsers.get(0).getTagType(), dto.getMerchantId());
            return true;
        }
        return false;
    }

    @Override
    public Boolean unBindUser(TagUnBindUserDTO dto) {
        Long merchantId = dto.getMerchantId();
        if (dto.getChooseAll() != null && dto.getChooseAll()) {
            //
            String lockKey = RedisKey.TAG_USER_ALL_UNBINDING_LOCK + "merchantId:" + merchantId;
            if (redisLock.lock(lockKey, UserMemberSysConstants.YES, taskUnBindingTime, 3000) == null) {
                log.debug("获取锁失败 -> {}", lockKey);
                throw new BusinessException(UserErrorCode.TAG_USER_UNBINDING_WORKING);
            }
            asyncProcessHandler.unBindingUser(dto);
            return true;
        }
        if (!CollectionUtils.isEmpty(dto.getTagIds())) {
            List<Long> userIds = dto.getUserIds();
            if (CollectionUtils.isEmpty(userIds)) {
                return false;
            }
            dto.getTagIds().forEach(tagId -> {
                tagRepository.removeUserTag(merchantId, userIds, tagId);
            });
        }
        return true;
    }

    @Override
    public Long updateTagName(TagDTO dto) {
        QueryWrapper<TagUser> newTagNameWrapper = new QueryWrapper<>();
        newTagNameWrapper.eq("merchant_id", dto.getMerchantId());
        newTagNameWrapper.eq("name", dto.getNewName().trim());

        Long exist = tagUserMapper.selectCount(newTagNameWrapper);
        if (exist != null && exist.intValue() > 0) {
            throw new BusinessException(UserErrorCode.TAG_USER_NAME_EXIST_ERROR);
        }

        QueryWrapper<TagUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("merchant_id", dto.getMerchantId());
        if (StringUtils.isNotBlank(dto.getOldName())) {
            queryWrapper.eq("name", dto.getOldName().trim());
        }
        if (dto.getTagId() != null) {
            queryWrapper.eq("id", dto.getTagId());
        }
        TagUser oldTagUser = tagUserMapper.selectOne(queryWrapper);
        if (oldTagUser == null) {
            throw new BusinessException(UserErrorCode.TAG_USER_NOT_EXIST_ERROR);
        }
        oldTagUser.setName(dto.getNewName());
        oldTagUser.setUpdateTime(new Date());
        oldTagUser.setUpdatedby(dto.getOperatorId());
        int updateById = tagUserMapper.updateTagUser(oldTagUser);
        return updateById > 0 ? oldTagUser.getId() : null;
    }

    @Override
    public void taggingUser(TaggingMerchantUserDTO dto) {
        dto.setName(dto.getName().trim());
        String name = dto.getName();
        if (StringUtils.length(dto.getName()) > 18) {
            log.warn("标签长度超长:{}", name);
            return;
        }
        log.debug("打标签内容,商家ID:{} ,标签名:{} ,businessType:{}", dto.getMerchantId(), name, dto.getBusinessType());
        QueryWrapper<TagUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("merchant_id", dto.getMerchantId());
        queryWrapper.eq("name", name);
        queryWrapper.eq("tag_type", dto.getTagType());
        queryWrapper.eq("business_type", dto.getBusinessType());
        queryWrapper.eq("state", UserMemberSysConstants.RECORD_STATE_NORMAL);
        queryWrapper.eq("is_active", UserMemberSysConstants.ENABLE);

        TagUserSaveDTO saveDTO = new TagUserSaveDTO();
        BeanUtils.copyProperties(dto, saveDTO);
        saveDTO.setName(name);
        saveDTO.setCategory(TagCategoryEnum.AUTO.getStatus());
        saveDTO.setActive(UserMemberSysConstants.ENABLE);
        saveDTO.setUserIds(Collections.singletonList(dto.getUserId()));
        TagUser tagUser;


        tagUser = tagUserMapper.selectOne(queryWrapper);
        if(tagUser == null){
            // 根据用户-商户加锁，重试时间3000ms，锁超时5000ms
            String lockKey = RedisKey.MEMBER_TAG_CREATE_LOCK
                    + saveDTO.getMerchantId()
                    + ":" + saveDTO.getName();
            if (redisLock.lock(lockKey, UserMemberSysConstants.YES, 5000, 1) == null) {
                log.warn("获取锁失败 -> {},", lockKey);
                return ;
            }
            try {
                // 创建标签加锁操作
                createTagUser(saveDTO);
                return ;
            }finally {
                redisLock.unlock(lockKey, UserMemberSysConstants.YES);
            }
        }
        //更改性别
        cleanSexTag(saveDTO);
        saveTagUserLink(dto, tagUser.getId());

    }

    @Override
    @Idempotent(keys = {"#keyNo"})
    public void taggingUserWithIdempotent(String keyNo, TaggingMerchantUserDTO dto) {
        taggingUser(dto);
    }

    private void saveTagUserLink(TaggingMerchantUserDTO dto, Long tagId) {
        log.debug("用户关联标签ID:{} ,userId:{} ,merchantId:{}", tagId, dto.getUserId(), dto.getMerchantId());
        List<Long> userIdList = new ArrayList<>(1);
        userIdList.add(dto.getUserId());
        List<Long> tagIds = new ArrayList<>();
        tagIds.add(tagId);
        bindingMerchantUser(userIdList, tagIds, dto.getOperatorId(), dto.getTagType(), dto.getMerchantId());
    }


    @Override
    public Boolean taggingMerchant(List<TaggingMerchantUserLinkDTO> list) {
        log.debug("商家标签:{}", list);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        for (TaggingMerchantUserLinkDTO dto : list) {
            taggingUser(buildMerchantTag(dto));
        }

        return true;
    }


    private TaggingMerchantUserDTO buildMerchantTag(TaggingMerchantUserLinkDTO dto) {
        TaggingMerchantUserDTO merchantUserDTO = new TaggingMerchantUserDTO();
        merchantUserDTO.setName(dto.getName());
        merchantUserDTO.setUserId(dto.getUserId());
        merchantUserDTO.setBusinessType(dto.getBusinessType());
        merchantUserDTO.setMerchantId(dto.getMerchantId());
        merchantUserDTO.setTagType(TAG_MERCHANT_USER);
        merchantUserDTO.setOperatorId(dto.getOperatorId());
        return merchantUserDTO;
    }

    @Override
    public void syncMerchantTag(List<Long> merchantIds, List<Long> tagIds) {
        List<MerchantUserTag> list = new ArrayList<>();
        merchantIds.forEach(merchantId -> {
            tagIds.forEach(tagId -> {
                QueryWrapper<MerchantUser> query = new QueryWrapper<>();
                query.eq("merchant_id", merchantId);
                List<MerchantUser> merchantUsers = merchantUserMapper.selectList(query);

                QueryWrapper<TagUser> tagquery = new QueryWrapper<>();
                tagquery.eq("merchant_id", merchantId);
                tagquery.eq("id", tagId);
                TagUser tagUser = tagUserMapper.selectOne(tagquery);
                if (tagUser == null) {
                    log.error("标签信息不存在,标签ID:{}", tagId);
                    return;
                }
                merchantUsers.forEach(e -> {
                    MerchantUserTag save = new MerchantUserTag();
                    save.setBusinessUserId(e.getId());
                    save.setMerchantId(merchantId);
                    save.setUserTags(new Long[]{tagId});
                    save.setActive(UserMemberSysConstants.ENABLE);
                    save.setTagType(tagUser.getTagType());
                    save.setCreateTime(new Date());
                    save.setCreatedby(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID);
                    list.add(save);
                });
            });
        });

        tagMerchantUserService.saveBatch(list);

    }

    @Override
    public Boolean batchUpdateSpecificBusinessTagsByUserId(UpdateSpecificBusinessTagsParam dto) {
        MerchantUserTag userTag = merchantUserTagMapper.getByBusinessUserId(dto.getMerchantId(), dto.getMerchantUserId());
        Date now = new Date();
        if (userTag == null) {
            userTag = new MerchantUserTag();
            userTag.setBusinessUserId(dto.getMerchantUserId());
            userTag.setMerchantId(dto.getMerchantId());
            userTag.setUserTags(dto.getTagIds().toArray(new Long[0]));
            userTag.setActive(Boolean.TRUE);
            userTag.setTagType(TAG_MERCHANT_USER);
            userTag.setCreateTime(now);
            userTag.setUpdateTime(now);
            userTag.setCreatedby(Optional.ofNullable(dto.getOperatorId()).orElse(0L));
            userTag.setUpdatedby(Optional.ofNullable(dto.getOperatorId()).orElse(0L));
            return merchantUserTagMapper.insert(userTag) > 0;
        }
        Long[] existsTags = userTag.getUserTags();
        Set<Long> tags = new HashSet<>();
        if (existsTags != null && existsTags.length > 0) {
            List<Long> tagIds = Arrays.asList(existsTags);
            List<TagUser> tagUsers = tagUserMapper.selectBatchIds(tagIds, dto.getMerchantId());
            //剔除要被替换的标签类型
            List<Long> collect = tagUsers.stream().filter(tagUser -> !tagUser.getBusinessType().equals(dto.getBusinessType().getStatus()))
                    .map(TagUser::getId).distinct().collect(Collectors.toList());
            dto.getTagIds().addAll(collect);
            tags = new HashSet<>(dto.getTagIds());
        }
        UpdateWrapper updateWrapper = new UpdateWrapper<MerchantUserTag>()
                .eq("merchant_id", userTag.getMerchantId())
                .eq("id", userTag.getId());
        userTag.setUserTags(tags.toArray(new Long[0]));
        userTag.setUpdateTime(now);
        userTag.setUpdatedby(Optional.ofNullable(dto.getOperatorId()).orElse(0L));
        return merchantUserTagMapper.update(userTag, updateWrapper) > 0;
    }

    @Override
    public List<TagSimpleInfoDTO> tagSimpleInfoList(TagSimpleInfoParam param) {
        QueryWrapper<TagUser> queryWrapper = buildTagInfoListQueryWrapper(param);
        List<TagUser> records = tagUserMapper.selectList(queryWrapper);
        List<TagSimpleInfoDTO> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(records)) {
            records.forEach(e -> {
                log.debug("用户标签info:{}", e);
                TagSimpleInfoDTO dto = new TagSimpleInfoDTO();
                dto.setTagId(e.getId());
                dto.setName(e.getName());
                dto.setMerchantId(e.getMerchantId());
                dto.setCategory(e.getCategory());
                dto.setBusinessType(e.getBusinessType());
                dto.setBusinessTypeName(Optional.ofNullable(TagBusinessTypeEnum.of(e.getBusinessType())).map(TagBusinessTypeEnum::getDesc).orElse(null));
                result.add(dto);
            });
        }
        return result;
    }

    private QueryWrapper<TagUser> buildTagInfoListQueryWrapper(TagSimpleInfoParam param) {
        QueryWrapper<TagUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("merchant_id", param.getMerchantId());
        queryWrapper.eq("tag_type", 1);

        if (!CollectionUtils.isEmpty(param.getTagIds())) {
            queryWrapper.in("id", param.getTagIds());
        }
        if (!CollectionUtils.isEmpty(param.getNames())) {
            List<String> names = param.getNames().stream().map(String::trim).collect(Collectors.toList());
            queryWrapper.in("name", names);
        }
        if (StringUtils.isNotBlank(param.getSearchTagName())) {
            queryWrapper.like("name", param.getSearchTagName().trim());
        }
        if (param.getCategory() != null) {
            queryWrapper.eq("category", param.getCategory());
        }
        if (!CollectionUtils.isEmpty(param.getBusinessTypes())) {
            queryWrapper.in("business_type", param.getBusinessTypes());
        }
        if (param.getActive() != null) {
            queryWrapper.eq("is_active", param.getActive());
        }
        if (param.getState() != null) {
            queryWrapper.eq("state", param.getState());
        }
        queryWrapper.orderByDesc("create_time");
        return queryWrapper;
    }
}
