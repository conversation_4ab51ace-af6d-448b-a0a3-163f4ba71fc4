package com.lyy.user.domain.statistics.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserStatisticsListDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsUpdateDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserConsumeStatisticsDTO;
import com.lyy.user.domain.statistics.entity.Statistics;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StatisticsMapper extends BaseMapper<Statistics> {

    void updateStatistics(UserStatisticsUpdateDTO param);

    void updateCleanBanlace(Statistics statistics);

    List<MerchantUserStatisticsListDTO> queryStatisticsUserList(IPage ipage, @Param("merchantId") Long merchantId,  @Param("userId") Long userId, @Param("telephone") String telephone,
                                                                @Param("orderField") String orderField, @Param("orderValue") String orderValue);

    MerchantUserConsumeStatisticsDTO selectStatisticsByUserId(@Param("merchantId") Long merchantId, @Param("userId") Long userId);

    List<MerchantUserConsumeStatisticsDTO> querySmallVenueStatisticsList(@Param("merchantId") Long merchantId, @Param("merchantUserIdList") List<Long> merchantUserIdList);

    IPage<MerchantUserConsumeStatisticsDTO> queryPageSmallVenueStatisticsList(@Param("page") IPage page, @Param("merchantId") Long merchantId);

    Long countSmallVenueStatisticsList(@Param("merchantId") Long merchantId);

    void upsertStatistics(UserStatisticsUpdateDTO param);
}