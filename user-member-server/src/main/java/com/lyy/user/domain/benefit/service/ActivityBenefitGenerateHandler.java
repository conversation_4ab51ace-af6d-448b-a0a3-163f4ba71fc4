package com.lyy.user.domain.benefit.service;

import com.lyy.user.account.infrastructure.benefit.dto.GenerateAccountDTO;
import org.springframework.stereotype.Component;

/**
 * 活动生成权益 处理
 * <AUTHOR>
 * @create 2021/4/9 10:42
 */
@Component(value = "ACTIVITY")
public class ActivityBenefitGenerateHandler implements BenefitGenerateHandler {
    @Override
    public void doGenerate(GenerateAccountDTO generateAccountDTO) {

    }
}
