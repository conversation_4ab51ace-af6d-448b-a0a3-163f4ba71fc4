package com.lyy.user.domain.doris.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/3 - 19:58
 */
@Data
@TableName("ads_user_member_merchant_coupons_number")
public class MemberCouponNumber {

    @TableField("lyy_distributor_id")
    private Long merchantId;
    @TableField("lyy_user_id")
    private Long userId;
    @TableField("lyy_marketing_coupons_num")
    private Integer marketingNum=0;
    @TableField("lyy_user_coupons_num")
    private Integer accountNum = 0;
}
