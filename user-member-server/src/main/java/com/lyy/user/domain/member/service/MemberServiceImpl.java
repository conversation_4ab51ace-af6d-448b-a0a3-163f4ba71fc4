package com.lyy.user.domain.member.service;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import com.lyy.error.member.infrastructure.MemberErrorCode;
import com.lyy.idempotent.core.annotation.Idempotent;
import com.lyy.klock.annotation.Klock;
import com.lyy.user.account.infrastructure.constant.MemberGrowRecordModeEnum;
import com.lyy.user.account.infrastructure.constant.MemberLiftingConditionEnum;
import com.lyy.user.account.infrastructure.constant.MemberLiftingRuleCategoryEnum;
import com.lyy.user.account.infrastructure.constant.MemberLiftingRuleRecordStatusEnum;
import com.lyy.user.account.infrastructure.member.dto.MemberDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupInfoSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberInfoDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberTouchRuleDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberUserInfoDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberUserPageDTO;
import com.lyy.user.account.infrastructure.member.dto.UpdateUserMemberLevelDTO;
import com.lyy.user.application.member.IMemberGroupService;
import com.lyy.user.application.member.IMemberGrowRecordService;
import com.lyy.user.application.member.IMemberLevelService;
import com.lyy.user.application.member.IMemberLiftingRuleRecordService;
import com.lyy.user.application.member.IMemberLiftingRuleService;
import com.lyy.user.application.member.IMemberLiftingService;
import com.lyy.user.application.member.IMemberRuleService;
import com.lyy.user.application.member.IMemberService;
import com.lyy.user.application.user.IMerchantUserService;
import com.lyy.user.domain.member.dto.MemberLiftingRuleRecordJsonDTO;
import com.lyy.user.domain.member.dto.MemberLiftingRuleTouchCheckDTO;
import com.lyy.user.domain.member.dto.MemberLiftingRuleTouchResultDTO;
import com.lyy.user.domain.member.dto.MemberLiftingStrategyDTO;
import com.lyy.user.domain.member.entity.Member;
import com.lyy.user.domain.member.entity.MemberGroup;
import com.lyy.user.domain.member.entity.MemberGrowRecord;
import com.lyy.user.domain.member.entity.MemberLevel;
import com.lyy.user.domain.member.entity.MemberLifting;
import com.lyy.user.domain.member.entity.MemberLiftingRule;
import com.lyy.user.domain.member.entity.MemberLiftingRuleRecord;
import com.lyy.user.domain.member.lifting.LiftingFactory;
import com.lyy.user.domain.member.repository.MemberLevelMapper;
import com.lyy.user.domain.member.repository.MemberMapper;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.infrastructure.base.MerchantBaseServiceImpl;
import com.lyy.user.infrastructure.constants.MemberLiftingStrategyEnum;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.repository.user.MerchantUserRepository;
import com.lyy.user.interfaces.assembler.AssemblerFactory;
import com.lyy.user.interfaces.assembler.MemberUserInfoAssembler;
import io.jsonwebtoken.lang.Assert;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 会员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
@Service
@Slf4j
public class MemberServiceImpl extends MerchantBaseServiceImpl<MemberMapper, Member> implements IMemberService {

    @Autowired
    private MemberLevelMapper memberLevelMapper;
    @Autowired
    private IMemberGroupService memberGroupService;
    @Autowired
    private IMemberLevelService memberLevelService;
    @Autowired
    private IMemberLiftingService memberLiftingService;
    @Autowired
    private IMemberLiftingRuleService memberLiftingRuleService;
    @Autowired
    private IMemberLiftingRuleRecordService memberLiftingRuleRecordService;
    @Autowired
    private IMerchantUserService merchantUserService;
    @Autowired
    private IMemberGrowRecordService memberGrowRecordService;
    @Autowired
    private IMemberRuleService memberRuleService;
    @Autowired
    private MemberUserInfoAssembler memberUserInfoAssembler;
    @Resource
    private MerchantUserRepository merchantUserRepository;

    @Resource
    private MemberMapper memberMapper;

    private final AssemblerFactory factory = new AssemblerFactory();

    @Override
    public Long saveOrUpdate(MemberDTO memberDTO) {
        Member member = new Member();
        BeanUtils.copyProperties(memberDTO,member);
        member.setUpdateTime(new Date());
        if(member.getId() == null){
            member.setCreateTime(new Date());
        }
        saveOrUpdate(member);
        return member.getId();
    }

    /**
     * 根据用户id与会员组id获取对应的会员信息，若没有会员信息，则初始化
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Member getMemberOrInit(Long merchantId, Long userId, Long memberGroupId) {
        Member member = getMember(merchantId, userId, memberGroupId);
        if (member != null) {
            if (Boolean.TRUE.equals(member.getDel())) {
                log.error(" {} 商户的 {} 会员组中 {} 的会员信息已经删除，id为 {}", member.getMerchantId(), memberGroupId, userId, member.getId());
                throw new BusinessException(MemberErrorCode.MEMBER_DELETE);
            }
            if (member.getMemberEndTime() != null) {
                MemberGroup memberGroup = memberGroupService.getById(merchantId, memberGroupId);
                if (memberGroup.getMemberEffectiveTime() > 0) {
                    member.setMemberEndTime(DateUtils.addDays(new Date(), memberGroup.getMemberEffectiveTime()));
                } else if (memberGroup.getMemberEffectiveTime() == -2) {
                    member.setMemberEndTime(memberGroup.getMemberEndTime());
                }
                //更新会员的有效期
                if (log.isDebugEnabled()) {
                    log.debug("更新会员的有效期:{}", member);
                }
                memberMapper.updateMemberEndTimeById(merchantId, member.getId(), member.getMemberEndTime());
            }
            return member;
        }
        return initMember(merchantId, userId, memberGroupId);
    }

    /**
     * 根据用户id与会员组id获取对应的会员信息
     */
    @Override
    public Member getMember(Long merchantId, Long userId, Long memberGroupId) {
        QueryWrapper<Member> queryWrapper = new QueryWrapper<Member>()
                .eq(getShardingFieldKey(), merchantId)
                .eq("user_id",userId)
                .eq("member_group_id",memberGroupId)
                .last("limit 1");
        return getOne(queryWrapper);
    }

    /**
     * 根据规则来更新会员对应的信息
     */
    @Override
    public int updateMemberOfRule(MemberTouchRuleDTO memberTouchRuleDTO) {
        Long merchantId = memberTouchRuleDTO.getMerchantId();
        memberTouchRuleDTO.getMemberGroupRangCheckDTO().setMerchantId(merchantId);
        List<MemberGroup> memberGroupList = memberGroupService.getMemberGroupListOfRange(memberTouchRuleDTO.getMemberGroupRangCheckDTO());
        if (memberGroupList == null || memberGroupList.isEmpty()) {
            log.info("{} 用户无法找到对应 {} 商户对应对会员组信息", memberTouchRuleDTO.getUserId(), merchantId);
            return 0;
        }
        //获取触发的规则数据
        List<Long> mgIdList = memberGroupList.stream()
                .map(MemberGroup::getId)
                .collect(Collectors.toList());
        MemberLiftingRuleTouchCheckDTO memberLiftingRuleTouchCheckDTO = new MemberLiftingRuleTouchCheckDTO();
        memberLiftingRuleTouchCheckDTO.setMemberGroupIdList(mgIdList);
        memberLiftingRuleTouchCheckDTO.setCategory(memberTouchRuleDTO.getCategory());
        memberLiftingRuleTouchCheckDTO.setMerchantId(memberTouchRuleDTO.getMerchantId());
        List<MemberLiftingRuleTouchResultDTO> memberLiftingRuleList = memberLiftingRuleService.touchLiftingRule(memberLiftingRuleTouchCheckDTO);
        log.debug("{} 用户触发 {} 商户的规则-->{}", memberTouchRuleDTO.getUserId(), merchantId, memberLiftingRuleList);
        if (memberLiftingRuleList == null || memberLiftingRuleList.isEmpty()) {
            log.warn("{}用户,{}商户的规则为空", memberTouchRuleDTO.getUserId(), merchantId);
            return 0;
        }
        //记录触发规则数据
        List<MemberLiftingRuleRecord> memberLiftingRuleRecordList = memberLiftingRuleList.stream()
                .map(memberLiftingRuleTouchResultDTO -> {
                    //得到会员信息
                    Member member;
                    try {
                        member = getMemberOrInit(merchantId, memberTouchRuleDTO.getUserId(), memberLiftingRuleTouchResultDTO.getMemberGroupId());
                        if (member.getMemberEndTime() != null && member.getMemberEndTime().getTime() < System.currentTimeMillis()) {
                            log.info("获取会员信息已经过期：{}", member);
                            return null;
                        }
                    } catch (BusinessException e) {
                        log.error("获取会员信息失败：userId:{},merchantId:{},memberGroupId:{},{}", memberTouchRuleDTO.getUserId(),merchantId,memberLiftingRuleTouchResultDTO.getMemberGroupId(),e.getMessage());
                        return null;
                    } catch (Exception e) {
                        log.error("获取会员信息失败：{} {} {} {}", memberTouchRuleDTO.getUserId(),merchantId,memberLiftingRuleTouchResultDTO.getMemberGroupId(), e.getMessage());
                        log.error(e.getMessage(), e);
                        return null;
                    }
                    //保存记录信息
                    MemberLiftingRuleRecord memberLiftingRuleRecord = new MemberLiftingRuleRecord();
                    memberLiftingRuleRecord.setUserId(member.getUserId());
                    memberLiftingRuleRecord.setMerchantId(merchantId);
                    memberLiftingRuleRecord.setMerchantUserId(member.getMerchantUserId());
                    memberLiftingRuleRecord.setMemberId(member.getId());
                    memberLiftingRuleRecord.setMemberLiftingRuleId(memberLiftingRuleTouchResultDTO.getId());
                    memberLiftingRuleRecord.setStatus(MemberLiftingRuleRecordStatusEnum.INIT.getValue());
                    memberLiftingRuleRecord.setOtherInfo(memberTouchRuleDTO.getOtherInfo());
                    memberLiftingRuleRecord.setRangeValue(memberTouchRuleDTO.getRangeValue());
                    memberLiftingRuleRecord.setCreateTime(new Date());
                    memberLiftingRuleRecord.setUpdateTime(new Date());
                    memberLiftingRuleRecord.setCreateBy(10000L);
                    memberLiftingRuleRecord.setUpdateBy(10000L);
                    memberLiftingRuleRecord.setEndTime(DateUtils.addDays(new Date(), memberLiftingRuleTouchResultDTO.getRangeDate()));
                    memberLiftingRuleRecord.setDescription(memberTouchRuleDTO.getDescription());
                    memberLiftingRuleRecordService.save(memberLiftingRuleRecord);
                    log.debug("保存 {} 商户 {} 用户 {} 规则的触发记录", merchantId, member.getUserId(), memberLiftingRuleTouchResultDTO.getId());
                    return memberLiftingRuleRecord;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        log.info("merchantId:{},userId:{},触发记录:{}", merchantId, memberTouchRuleDTO.getUserId(), memberLiftingRuleRecordList);
        //根据用户的会员组计算更新成长值
        int count = updateGrowValueOfLifting(merchantId, memberTouchRuleDTO.getUserId(), memberLiftingRuleRecordList);
        log.info("{} 用户更新了 {} 个升级信息", memberTouchRuleDTO.getUserId(), count);
        return memberLiftingRuleList.size();
    }

    @Override
    @Idempotent(keys = "#memberTouchRuleDTO.outTradeNo")
    public int updateMemberOfRuleWithIdempotent(MemberTouchRuleDTO memberTouchRuleDTO) {
        return updateMemberOfRule(memberTouchRuleDTO);
    }

    /**
     * 回退会员触发规则信息
     */
    @Override
    public boolean removeMemberOfRule(MemberTouchRuleDTO memberTouchRuleDTO) {
        //先更新记录，把还没生效的记录更新为失效
        UpdateWrapper<MemberLiftingRuleRecord> updateWrapper = new UpdateWrapper<MemberLiftingRuleRecord>()
                .set("status", MemberLiftingRuleRecordStatusEnum.FAILURE.getValue())
                .set("update_time", new Date())
                .set("update_by", 10000L)
                .eq("merchant_id", memberTouchRuleDTO.getMerchantId())
                .eq("user_id", memberTouchRuleDTO.getUserId())
                .eq("status", MemberLiftingRuleRecordStatusEnum.INIT.getValue())
                .eq("other_info", memberTouchRuleDTO.getOtherInfo());
        long failureNum = memberLiftingRuleRecordService.getBaseMapper().update(null, updateWrapper);

        //找出已经生效的记录
        QueryWrapper<MemberLiftingRuleRecord> wrapper = new QueryWrapper<MemberLiftingRuleRecord>()
                .eq("merchant_id", memberTouchRuleDTO.getMerchantId())
                .eq("user_id", memberTouchRuleDTO.getUserId())
                .eq("status", MemberLiftingRuleRecordStatusEnum.FINISH.getValue())
                .eq("other_info", memberTouchRuleDTO.getOtherInfo());
        List<MemberLiftingRuleRecord> liftingRuleRecordList = memberLiftingRuleRecordService.list(wrapper);
        if (!liftingRuleRecordList.isEmpty()) {
            List<Long> recordIds = liftingRuleRecordList.stream()
                    .map(liftingRuleRecord -> {
                        //回退成长值
                        returnGrowValue(liftingRuleRecord);
                        return liftingRuleRecord.getId();
                    }).collect(Collectors.toList());
            //把状态设置为失效的
            memberLiftingRuleRecordService.updateStatusOfIds(memberTouchRuleDTO.getMerchantId(), recordIds, MemberLiftingRuleRecordStatusEnum.FINISH.getValue(), MemberLiftingRuleRecordStatusEnum.FAILURE.getValue());
        }
        log.info("{} 商户的 {} 用户需要回退触发规则信息 {},共设置失效记录为 {}, 回退记录为 {}", memberTouchRuleDTO.getMerchantId(),
                memberTouchRuleDTO.getUserId(), memberTouchRuleDTO.getOtherInfo(), failureNum, liftingRuleRecordList.size());
        return true;
    }

    /**
     * 根据记录回退成长值
     */
    private Long returnGrowValue(MemberLiftingRuleRecord liftingRuleRecord) {
        MemberLiftingRule memberLiftingRule = memberLiftingRuleService.getById(liftingRuleRecord.getMerchantId(), liftingRuleRecord.getMemberLiftingRuleId());
        if (Objects.isNull(memberLiftingRule)) {
            log.warn("{} 商户 {} 用户在 {} 会员升级策略规则不存在", liftingRuleRecord.getMerchantId(), liftingRuleRecord.getUserId(), liftingRuleRecord.getMemberLiftingRuleId());
            return 0L;
        }
        MemberLifting memberLifting = memberLiftingService.getById(liftingRuleRecord.getMerchantId(), memberLiftingRule.getMemberLiftingId());
        Member member = getMember(liftingRuleRecord.getMerchantId(), liftingRuleRecord.getUserId(), memberLifting.getMemberGroupId());
        if (Objects.isNull(member)) {
            log.warn("{} 商户 {} 用户在 {} 会员组下不存在会员信息", liftingRuleRecord.getMerchantId(), liftingRuleRecord.getUserId(), memberLifting.getMemberGroupId());
            return 0L;
        }
        List<MemberLiftingRuleRecord> memberLiftingRuleRecords;
        if (MemberLiftingStrategyEnum.LIFTING_STRATEGY_UPGRADE.getValue().equals(memberLifting.getLifting())) {
            if (memberLifting.getCondition() != null && MemberLiftingConditionEnum.OR_ONE.getValue() != memberLifting.getCondition()) {
                //若同时满足多项的，需要回退记录
                // TODO: 2021/5/14  若同时满足多项的，需要回退记录
                log.warn("若同时满足多项的，需要回退记录");
                memberLiftingRuleRecords = Collections.singletonList(liftingRuleRecord);
            } else {
                //满足一项的，直接处理
                memberLiftingRuleRecords = Collections.singletonList(liftingRuleRecord);
            }
            Long growValue;
            MemberLiftingRuleCategoryEnum categoryEnum = MemberLiftingRuleCategoryEnum.getByValue(memberLiftingRule.getCategory());
            StringBuilder description = new StringBuilder();
            if (MemberLiftingRuleCategoryEnum.CATEGORY_CONSUMPTION_MONEY.equals(categoryEnum)) {
                //算累计金额的，根据对应的金额计算具体的成长值
                growValue = liftingRuleRecord.getRangeValue()
                        .divide(memberLiftingRule.getRangeValue(), 2, RoundingMode.FLOOR)
                        .multiply(new BigDecimal(memberLifting.getGrowValue())).longValue();
                description.append("退款扣成长值");
            } else {
                growValue = memberLifting.getGrowValue();
                description.append("撤销扣成长值");
            }

            //会员扣成长值
            log.info("{} 商户 {} 会员组的 {} 用户根据 {} 升级策略回退 {} 成长值", member.getMerchantId(), member.getMemberGroupId(),
                    member.getUserId(), memberLifting.getId(), growValue);
            //处理来源信息
            Map<MemberLiftingRule, Collection<MemberLiftingRuleRecord>> memberLiftingRuleMap = new HashMap<>(16);
            memberLiftingRuleMap.put(memberLiftingRule, memberLiftingRuleRecords);
            List<MemberLiftingRuleRecordJsonDTO> recordJsonList = MemberLiftingRuleRecordJsonDTO.exchangeToList(memberLiftingRuleMap);
            String resources = new Gson().toJson(recordJsonList);
            List<MemberLevel> memberLevelList = updateGrowValueAndLevel(member, memberLifting, resources, growValue, MemberLiftingStrategyEnum.LIFTING_STRATEGY_DEMOTING.getValue(), description.toString());
            if (!memberLevelList.isEmpty()) {
                log.info("{} 商户的 {} 用户回退成长值有等级变化-->{}", member.getMerchantId(), member.getUserId(), memberLevelList);
                memberRuleService.updateBenefitOfLevel(member, memberLevelList, false);
            }
        }
        return memberLifting.getGrowValue();
    }

    /**
     * 根据用户的升级策略更新对应会员的成长值
     *
     * @param merchantId
     * @param userId 用户id
     * @param newRecordList 新补充的记录列表
     * @return 更新的升级策略策略数量
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public int updateGrowValueOfLifting(Long merchantId, Long userId,List<MemberLiftingRuleRecord> newRecordList) {
        List<MemberLiftingRuleRecord> recordList = memberLiftingRuleRecordService.findByStatusAndEndTime(merchantId, userId, MemberLiftingRuleRecordStatusEnum.INIT.getValue(), LocalDateTime.now());
        if (CollectionUtils.isEmpty(recordList) && CollectionUtils.isEmpty(newRecordList)) {
            return 0;
        }
        //获取所有规则id
        Set<Long> memberLiftingRuleIdSet = recordList.stream()
                .map(MemberLiftingRuleRecord::getMemberLiftingRuleId)
                .collect(Collectors.toSet());
        //获取所有会员升级策略规则记录id
        Set<Long> memberLiftingRuleRecordIdSet = recordList.stream()
                .map(MemberLiftingRuleRecord::getId)
                .collect(Collectors.toSet());
        //补充 nowRecordList 中的记录到 recordList 中
        Optional.ofNullable(newRecordList)
                .ifPresent(newList -> newList.stream()
                        //过滤重复数据
                        .filter(n -> !memberLiftingRuleRecordIdSet.contains(n.getId()))
                        //遍历增加记录
                        .forEach(n -> {
                            log.debug("补充新记录-->{}", n);
                            memberLiftingRuleIdSet.add(n.getMemberLiftingRuleId());
                            recordList.add(n);
                        })
                );
        //获取所有的升级规则信息
        List<MemberLiftingRule> memberLiftingRuleList = memberLiftingRuleService.listByIds(merchantId, memberLiftingRuleIdSet);
        if (memberLiftingRuleList == null || memberLiftingRuleList.isEmpty()) {
            return 0;
        }
        //获取所有对应的升级信息
        Set<Long> memberLiftingIdSet = memberLiftingRuleList.stream()
                //只处理符合条件的，不符合条件的需要由定时器处理
                .filter(MemberLiftingRule::getJudgeCondition)
                .map(MemberLiftingRule::getMemberLiftingId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(memberLiftingIdSet)) {
            return 0;
        }
        List<MemberLifting> memberLiftingList = memberLiftingService.listByIds(merchantId, memberLiftingIdSet);
        if (memberLiftingList == null || memberLiftingList.isEmpty()) {
            return 0;
        }

        Map<Long, Integer> countLiftingMap = memberGrowRecordService.countRecordByMemberLifting(merchantId, userId, memberLiftingIdSet);
        //分组组装 MemberLiftingStrategyDTO 对象
        List<MemberLiftingStrategyDTO> memberLiftingStrategyList = memberLiftingList.stream()
                .map(memberLifting -> {
                    MemberLiftingStrategyDTO memberLiftingStrategyDTO = new MemberLiftingStrategyDTO();
                    memberLiftingStrategyDTO.setUserId(userId);
                    memberLiftingStrategyDTO.setMerchantId(merchantId);
                    memberLiftingStrategyDTO.setMemberLifting(memberLifting);
                    List<MemberLiftingRule> ruleList = memberLiftingRuleService.findByMemberLifting(merchantId, memberLifting.getId());
                    memberLiftingStrategyDTO.setMemberLiftingRuleList(ruleList);

                    //提取符合该规则的记录
                    Set<Long> ruleIdSet = ruleList.stream()
                            .map(MemberLiftingRule::getId)
                            .collect(Collectors.toSet());
                    List<MemberLiftingRuleRecord> filterRecordList = recordList.stream()
                            .filter(record -> ruleIdSet.contains(record.getMemberLiftingRuleId()))
                            .collect(Collectors.toList());
                    memberLiftingStrategyDTO.setMemberLiftingRuleRecordList(filterRecordList);

                    // 有效次数更新
                    Integer currentEffectiveNumber = Optional.ofNullable(countLiftingMap.get(memberLifting.getId()))
                            .orElse(0);
                    log.debug("{} 用户的 {} 升级策略触发的有效次数为 {}", userId, memberLifting.getId(), currentEffectiveNumber);
                    memberLiftingStrategyDTO.setCurrentEffectiveNumber(currentEffectiveNumber);

                    return memberLiftingStrategyDTO;
                }).collect(Collectors.toList());
        int count = 0;
        for (MemberLiftingStrategyDTO memberLiftingStrategy : memberLiftingStrategyList) {
            if (LiftingFactory.handlerStrategy(memberLiftingStrategy)) {
                count++;
            }
        }

        return count;
    }

    /**
     * 更新会员成长值的数值
     *
     *
     * @param merchantId
     * @param memberId  会员id
     * @param growValue 更新的成长值
     * @param isUpgrade 是否为升级功能，true为升级，false为降级
     * @return
     */
    @Override
    public boolean updateGrowValueOfId(Long merchantId, Long memberId, Long growValue, boolean isUpgrade) {
        //shardingsphere 中用case会报错，因此需要用这种方式
        UpdateWrapper<Member> wrapper = new UpdateWrapper<Member>()
                .eq(getShardingFieldKey(), merchantId)
                .eq("id",memberId)
                .setSql("update_time = now()")
                .setSql(isUpgrade,"grow_value = grow_value + " + growValue)
                //使用 greatest函数防止,成长值扣至小于0
                .setSql(!isUpgrade,"grow_value = greatest(grow_value - " + growValue +",0) ");
        return update(wrapper);
    }

    /**
     * 更新会员的等级
     *
     * @param merchantId 商户id
     * @param memberId 会员id
     * @param oldMemberLevelId 旧会员等级id
     * @param newMemberLevelId 新会员等级id
     * @return
     */
    @Override
    public boolean updateLevel(Long merchantId, Long memberId, Long oldMemberLevelId, Long newMemberLevelId) {
        UpdateWrapper<Member> wrapper = new UpdateWrapper<Member>()
                .eq(getShardingFieldKey(), merchantId)
                .eq("id",memberId)
                .eq("member_level_id",oldMemberLevelId)
                .set("member_level_id",newMemberLevelId)
                .setSql("update_time = now()");
        return update(wrapper);
    }
    /**
     * 更新会员的成长值,若可以升降级的同时升降级处理，并返回对应的等级信息
     * @param member
     * @param memberLifting
     * @param resources 来源记录数据
     * @param growValue 变动成长值
     * @return
     */

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public List<MemberLevel> updateGrowValueAndLevel(Member member, MemberLifting memberLifting, String resources, Long growValue,String description) {
        return updateGrowValueAndLevel(member,memberLifting,resources,growValue,memberLifting.getLifting(),description);
    }

    /**
     * 更新会员的成长值,若可以升降级的同时升降级处理，并返回对应的等级信息
     * @param member
     * @param memberLifting
     * @param resources 来源记录数据
     * @param growValue 变动成长值
     * @param lifting
     * @param description 描述
     * @return
     */
//    @Transactional(rollbackFor = Exception.class)
    public List<MemberLevel> updateGrowValueAndLevel(Member member, MemberLifting memberLifting, String resources,
                                                     Long growValue, Short lifting, String description) {
        if (log.isDebugEnabled()) {
            log.debug("更新会员的成长值和等级,member:{}, memberLifting:{}, resources:{}, growValue:{}, lifting:{}, description:{}", member,
                    memberLifting, resources, growValue, lifting, description);
        }
        boolean isUpgrade = MemberLiftingStrategyEnum.LIFTING_STRATEGY_UPGRADE.getValue().equals(lifting);
        updateGrowValueOfId(member.getMerchantId(), member.getId(), growValue, isUpgrade);
        addMemberGrowRecord(member, memberLifting, resources, growValue, description, isUpgrade);
        List<MemberLevel> memberLevelList = memberLevelService.findLevelOfMemberGrowValue(member.getMerchantId(), member.getId(), isUpgrade);

        if (!memberLevelList.isEmpty()) {
            MemberLevel memberLevelListLast = memberLevelList.get(memberLevelList.size() - 1);
            log.info(" {} 会员原等级id为 {}，等级变化到 {} ,发生等级更新 -->{}", member.getId(), member.getMemberLevelId(),
                    memberLevelListLast.getName(), memberLevelList);

            if (isUpgrade) {
                //升级的直接更新会员等级即可
                updateLevel(member.getMerchantId(), member.getId(), member.getMemberLevelId(), memberLevelListLast.getId());
            } else {
                //降级的需要取最后一个等级的更小的等级

                MemberLevel lastLevel = memberLevelService.findLastLevel(member.getMerchantId(), memberLevelListLast.getId());
                if (Objects.isNull(lastLevel)) {
                    //最后1个等级已经是最小了，只能降到那个等级
                    log.warn("{} 会员已经成长值已经跌破最低等级,只能使用变化的最后等级-->{}", member.getId(), memberLevelListLast);
                    if (memberLevelListLast.getId().equals(member.getMemberLevelId())) {
                        log.warn("{} 会员等级为 {}，已经最小不允许再降", member.getId(), memberLevelListLast.getName());
                        return Collections.EMPTY_LIST;
                    }
                    lastLevel = memberLevelListLast;
                    memberLevelList.remove(memberLevelList.size() - 1);
                }
                updateLevel(member.getMerchantId(), member.getId(), member.getMemberLevelId(), lastLevel.getId());
            }
        }
        return memberLevelList;
    }

    /**
     * 增加会员成长值变化记录
     */
    @Override
    public void addMemberGrowRecord(Member member, MemberLifting memberLifting, String resources, Long growValue, String description, boolean isUpgrade) {
        //添加记录
        MemberGrowRecordModeEnum memberGrowRecordModeEnum = isUpgrade ? MemberGrowRecordModeEnum.INCREMENT : MemberGrowRecordModeEnum.DECREMENT;
        MemberGrowRecord memberGrowRecord = new MemberGrowRecord();
        memberGrowRecord.setUserId(member.getUserId());
        memberGrowRecord.setMerchantUserId(member.getMerchantUserId());
        memberGrowRecord.setMerchantId(member.getMerchantId());
        memberGrowRecord.setGrowValue(growValue);
        memberGrowRecord.setMode(memberGrowRecordModeEnum.getMode());
        memberGrowRecord.setMemberLiftingId(memberLifting.getId());
        memberGrowRecord.setInitGrowValue(member.getGrowValue());
        memberGrowRecord.setResources(resources);
        memberGrowRecord.setCreatedby(0L);
        memberGrowRecord.setCreateTime(new Date());
        memberGrowRecord.setUpdatedby(0L);
        memberGrowRecord.setUpdateTime(new Date());
        memberGrowRecord.setMemberId(member.getId());
        memberGrowRecord.setDescription(description);
        memberGrowRecordService.save(memberGrowRecord);
    }

    @Override
    public MemberUserInfoDTO getSmallVenueUserMemberInfo(Long merchantId, Long userId) {

        MemberGroupInfoSaveDTO memberGroupInfoSaveDTO = memberGroupService.getSmallVenueDefaultMemberGroup(merchantId);

        MemberUserInfoDTO memberUserInfoDTO = null;
        Member member =  this.getBaseMapper().selectOne(new QueryWrapper<Member>().eq("merchant_id",merchantId).eq("member_group_id", memberGroupInfoSaveDTO.getMemberGroupSaveDTO().getId()).eq("user_id",userId));
        if(member == null){
            //没有会员信息
            return  null;
        }
        memberUserInfoDTO = factory.convert(memberUserInfoAssembler,member,MemberUserInfoDTO.class);

        MemberLevel memberLevel = memberLevelMapper.selectByIdAndMerchant(merchantId,member.getMemberLevelId());
        if(memberLevel != null){
            memberUserInfoDTO.setMemberLevelName(memberLevel.getName());
        }
        return memberUserInfoDTO;

    }

    @Override
    @Klock(keys = "'user-member:klock:initSmallVenueUserMemberLevel-'+#merchantId+'_'+#userId", waitTime = 5)
    public Boolean initSmallVenueUserMemberLevel(Long merchantId, Long userId) {
        MemberGroupInfoSaveDTO  memberGroupInfoSaveDTO = memberGroupService.getSmallVenueDefaultMemberGroup(merchantId);

        List<MemberLevel> memberLevels = memberLevelMapper.selectList(new QueryWrapper<MemberLevel>().eq("merchant_id",merchantId)
                .eq("member_group_id",memberGroupInfoSaveDTO.getMemberGroupSaveDTO().getId())
                .orderByAsc("grow_value")
        );
        Long defaultMemberLevelId;
        if(memberLevels.size() > 0){
            //取默认的会员等级
            defaultMemberLevelId = memberLevels.get(0).getId();
        }else{
            log.debug("创建默认会员等级，merchantId:{}",merchantId);
            // 创建默认的会员级别
            MemberLevel defaultMemberLevel = new MemberLevel();
            defaultMemberLevel.setMemberGroupId(memberGroupInfoSaveDTO.getMemberGroupSaveDTO().getId());
            defaultMemberLevel.setMerchantId(merchantId);
            defaultMemberLevel.setName("vip1");
            defaultMemberLevel.setGrowValue(0L);
            defaultMemberLevel.setCreateTime(new Date());
            defaultMemberLevel.setUpdateTime(new Date());
            memberLevelMapper.insert(defaultMemberLevel);
            defaultMemberLevelId = defaultMemberLevel.getId();
        }

        Long count = this.getBaseMapper().selectCount(new QueryWrapper<Member>().eq("member_group_id",memberGroupInfoSaveDTO.getMemberGroupSaveDTO().getId())
                .eq("merchant_id",merchantId)
                .eq("user_id",userId)
                .eq("member_level_id",defaultMemberLevelId)
        );

        if(count!=null && count.intValue() > 0){
            //已经初始化了
            log.warn("会员组已初始化,{},{},{}", merchantId, userId, defaultMemberLevelId);
            return true;
        }
        Member member = new Member();
        member.setUserId(userId);
        member.setMemberGroupId(memberGroupInfoSaveDTO.getMemberGroupSaveDTO().getId());
        member.setGrowValue(0L);
        member.setCreateTime(new Date());
        member.setUpdateTime(new Date());
        member.setMerchantId(merchantId);
        member.setMemberLevelId(defaultMemberLevelId);
        member.setMemberStartTime(new Date());

        MerchantUser merchantUser = merchantUserService.findByUserIdAndMerchantId(userId,merchantId);
        member.setMerchantUserId(merchantUser.getId());
        boolean resultFlag = this.save(member);
        if(!resultFlag){
            log.warn("用户会员初始化信息失败");
        }
        return resultFlag;

    }

    @Override
    public Boolean updateUserMemberLevel(UpdateUserMemberLevelDTO updateUserMemberLevelDTO) {

        MemberGroup defaultMemberGroup = memberGroupService.getSmallVenueMemberGroup(updateUserMemberLevelDTO.getMerchantId());
        Assert.notNull(defaultMemberGroup, "获取默认会员组失败");

        Member member = this.getBaseMapper().selectOne(new QueryWrapper<Member>().eq("merchant_user_id", updateUserMemberLevelDTO.getMerchantUserId())
                .eq("member_group_id", defaultMemberGroup.getId()).eq("merchant_id", updateUserMemberLevelDTO.getMerchantId()));
        if (member == null) {
            MerchantUser merchantUser = merchantUserRepository.getByIdAndMerchantId(updateUserMemberLevelDTO.getMerchantUserId(), updateUserMemberLevelDTO.getMerchantId());
            org.springframework.util.Assert.notNull(merchantUser, "无效的merchantUserId " + updateUserMemberLevelDTO.getMerchantUserId());

            member = new Member();
            member.setMerchantId(updateUserMemberLevelDTO.getMerchantId());
            member.setMemberLevelId(updateUserMemberLevelDTO.getMemberLevelId());
            member.setMerchantUserId(updateUserMemberLevelDTO.getMerchantUserId());
            member.setUserId(merchantUser.getUserId());
            member.setMemberGroupId(defaultMemberGroup.getId());
            member.setGrowValue(0L);
            member.setMemberStartTime(new Date());
            member.setCreateTime(new Date());
            member.setUpdateTime(new Date());
            member.setDel(false);
            this.getBaseMapper().insert(member);
            return true;
        }
        member.setMemberLevelId(updateUserMemberLevelDTO.getMemberLevelId());
        member.setUpdateTime(new Date());
        int resultNum = this.getBaseMapper().updateMemberByIdAndMerchantId(member, member.getId(), member.getMerchantId());
        return resultNum > 0;
    }

    /**
     * 获取商户指定等级的用户数
     *
     * @param merchantId 商户id
     * @param levelIds   会员等级id
     * @return
     */
    @Override
    public Boolean hasUserInMemberLevelIds(Long merchantId, List<Long> levelIds) {
        QueryWrapper<Member> queryWrapper = new QueryWrapper<Member>()
                .eq(getShardingFieldKey(), merchantId)
                .in("member_level_id", levelIds)
                .last("limit 1");
        return getOne(queryWrapper) != null;
    }

    /**
     * 根据用户组信息分页获取会员信息
     */
    @Override
    public Page<MemberInfoDTO> findMemberByMemberGroup(Page page, Long merchantId, Long memberGroupId, Long memberLevelId) {
        PageHelper.startPage((int) page.getCurrent(), (int) page.getSize(), false);
        com.github.pagehelper.Page<MemberInfoDTO> memberPage = getBaseMapper().findMemberByMemberGroup(merchantId, memberGroupId, memberLevelId);
        Page pg = new Page(memberPage.getPageNum(), memberPage.getPageSize(), memberPage.getTotal());
        pg.setRecords(memberPage);
        return pg;
    }

    /**
     * 获取相同会员组下的在某个时间段内没有数据的会员信息
     */
    @Override
    public List<Member> getNotRecordOfTime(MemberLifting memberLifting, LocalDateTime startTime, LocalDateTime endTime) {
        return getBaseMapper().getNotRecordOfTime(memberLifting, startTime, endTime, startTime);
    }

    /**
     * 分页获取用户的会员信息
     */
    @Override
    public Page<MemberUserPageDTO> findPageMemberUser(Page page, Long merchantId, Long userId) {
        // 先查询总数，如影响性能再关闭
        getBaseMapper().findValidMemberUser(page, merchantId, userId, new Date());
        return page;
    }

    /**
     * 更新会员为新的等级
     */
    @Override
    public boolean updateMemberLevel(Member member, MemberLevel memberLevel) {
        LambdaUpdateWrapper updateWrapper = new LambdaUpdateWrapper<Member>()
                .eq(Member::getMerchantId, member.getMerchantId())
                .eq(Member::getId, member.getId())
                //保持等级与成长值一直，防止这个过程中有修改
                .eq(Member::getMemberLevelId, member.getMemberLevelId())
                .eq(Member::getGrowValue, member.getGrowValue())
                .set(Member::getMemberLevelId, memberLevel.getId())
                .set(Member::getGrowValue, memberLevel.getGrowValue())
                .set(Member::getUpdateTime, new Date());
        return update(updateWrapper);
    }


    /**
     * 初始化会员信息
     */
    private Member initMember(Long merchantId, Long userId, Long memberGroupId) {
        MemberGroup memberGroup = memberGroupService.getById(merchantId,memberGroupId);
        if(memberGroup == null){
            log.error("无法为 {} 用户找到 {} 会员组的信息",userId,memberGroupId);
            throw new BusinessException(MemberErrorCode.MEMBER_GROUP_NOT_FOUND);
        }
        checkInitMemberOfGroup(memberGroup);
        MemberLevel memberLevel = memberLevelService.getFirstLevel(merchantId,memberGroupId,memberGroup.getLiftingStrategy());
        if(memberLevel == null){
            log.error("无法为 {} 用户找到 {} 会员组的初始等级信息",userId,memberGroupId);
            throw new BusinessException(MemberErrorCode.MEMBER_LEVEL_NOT_FOUND);
        }
        Member member = new Member();
        member.setUserId(userId);
        member.setMemberGroupId(memberGroupId);
        member.setGrowValue(memberLevel.getGrowValue());
        member.setCreateTime(new Date());
        member.setUpdateTime(new Date());
        member.setMerchantId(merchantId);
        member.setMemberLevelId(memberLevel.getId());
        member.setMemberStartTime(new Date());

        if(memberGroup.getMemberEffectiveTime() > 0){
            member.setMemberEndTime(DateUtils.addDays(new Date(),memberGroup.getMemberEffectiveTime()));
        }else if (memberGroup.getMemberEffectiveTime() == -2){
            member.setMemberEndTime(memberGroup.getMemberEndTime());
        }
        MerchantUser merchantUser = merchantUserService.findByUserIdAndMerchantId(userId,memberGroup.getMerchantId());
        if(merchantUser == null){
            log.warn("无法为 {} 用户找到 {} 会员组的商户会员信息记录",userId,memberGroupId);
            throw new BusinessException(MemberErrorCode.MEMBER_NOT_EXISTS);
        }
        member.setMerchantUserId(merchantUser.getId());
        save(member);
        boolean updateBenefitResult = memberRuleService.updateBenefitOfLevel(member, Collections.singletonList(memberLevel),true);
        log.debug("{} 商户 {} 用户在 {} 会员组创建新会员，派发初始等级 {} 的权益结果为 {}",merchantId,userId,memberGroupId,memberLevel.getName(),updateBenefitResult);
        return member;
    }

    /**
     * 检查当前的会员组是否可以创建会员信息
     */
    private void checkInitMemberOfGroup(MemberGroup memberGroup) {
        if(memberGroup.getDel()){
            log.error(" {} 商户的 {} 会员组已经删除，id为 {}",memberGroup.getMerchantId(),memberGroup.getName(),memberGroup.getId());
            throw new BusinessException(MemberErrorCode.MEMBER_GROUP_DELETE);
        }
        if(!memberGroup.getActive()){
            log.error(" {} 商户的 {} 会员组已经停用，id为 {}",memberGroup.getMerchantId(),memberGroup.getName(),memberGroup.getId());
            throw new BusinessException(MemberErrorCode.MEMBER_GROUP_DISABLE);
        }

        long currentTime =  System.currentTimeMillis();
        if(memberGroup.getStartDate() != null && memberGroup.getStartDate().getTime() > currentTime){
            log.error(" {} 商户的 {} 会员组还没开始使用，id为 {}",memberGroup.getMerchantId(),memberGroup.getName(),memberGroup.getId());
            throw new BusinessException(MemberErrorCode.MEMBER_GROUP_DISABLE);
        }
        if(memberGroup.getEndDate() != null &&  memberGroup.getEndDate().getTime() < currentTime){
            log.error(" {} 商户的 {} 会员组已经过期，id为 {}",memberGroup.getMerchantId(),memberGroup.getName(),memberGroup.getId());
            throw new BusinessException(MemberErrorCode.MEMBER_GROUP_DISABLE);
        }

    }


}
