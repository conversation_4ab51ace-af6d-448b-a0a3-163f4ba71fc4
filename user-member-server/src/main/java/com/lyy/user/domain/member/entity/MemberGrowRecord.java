package com.lyy.user.domain.member.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lyy.user.domain.member.dto.MemberLiftingRuleRecordJsonDTO;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 会员成长值记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("um_member_grow_record")
public class MemberGrowRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 平台用户ID
     */
    private Long userId;

    /**
     * 商户用户ID
     */
    private Long merchantUserId;

    /**
     * 商户ID
     */
    private Long merchantId;


    /**
     * 成长值规则ID
     */
    private Long ruleId;

    /**
     * 成长值
     */
    private Long growValue;

    /**
     * 交易单号
     */
    private String outTradeNo;


    /**
     * 方式: 1加 2减
     */
    private Integer mode;

    /**
     * 来源， 通过会员升级策略触发时，为规则记录json
     * 会员升级策略触发,则
     * @see MemberLiftingRuleRecordJsonDTO
     */
    private String resources;

    /**
     * 描述
     */
    private String description;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("createdby")
    private Long createdby;

    @TableField("updatedby")
    private Long updatedby;

    /**
     * 会员升级策略id
     */
    @TableField("member_lifting_id")
    private Long memberLiftingId;

    /**
     * 初始成长值
     */
    @TableField("init_grow_value")
    private Long initGrowValue;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 过期时间
     */
    @TableField(value = "down_time")
    private Date downTime;

}
