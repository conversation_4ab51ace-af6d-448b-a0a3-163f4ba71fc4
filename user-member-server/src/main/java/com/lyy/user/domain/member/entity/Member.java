package com.lyy.user.domain.member.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 会员表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("um_member")
public class Member implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 商户ID
     */
    @TableField("merchant_id")
    private Long merchantId;

    /**
     * 平台用户,对应lyy_user表的id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 平台用户
     */
    @TableField("merchant_user_id")
    private Long merchantUserId;

    /**
     * 会员组ID
     */
    @TableField("member_group_id")
    private Long memberGroupId;

    /**
     * 会员等级ID
     */
    @TableField("member_level_id")
    private Long memberLevelId;

    /**
     * 成长值
     */
    @TableField("grow_value")
    private Long growValue;

    /**
     * 会员有效开始时间
     */
    @TableField("member_start_time")
    private Date memberStartTime;

    /**
     * 会员有效结束时间,若为空，则表示没有结束时间
     */
    @TableField("member_end_time")
    private Date memberEndTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建者
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新者
     */
    @TableField("update_by")
    private Long updateBy;
    /**
     * 是否已删除，true:是，false：否
     */
    @TableField("is_del")
    private Boolean del;
}
