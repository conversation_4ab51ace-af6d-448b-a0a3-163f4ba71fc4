package com.lyy.user.domain.member.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.lyy.user.account.infrastructure.member.dto.MemberLevelDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberLevelSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberRuleDTO;
import com.lyy.user.account.infrastructure.member.dto.SmallVenueMemberLevelSaveDTO;
import com.lyy.user.application.member.IMemberGroupService;
import com.lyy.user.application.member.IMemberLevelService;
import com.lyy.user.application.member.IMemberRuleService;
import com.lyy.user.domain.member.entity.Member;
import com.lyy.user.domain.member.entity.MemberGroup;
import com.lyy.user.domain.member.entity.MemberLevel;
import com.lyy.user.domain.member.entity.MemberRule;
import com.lyy.user.domain.member.repository.MemberGroupMapper;
import com.lyy.user.domain.member.repository.MemberLevelMapper;
import com.lyy.user.domain.member.repository.MemberMapper;
import com.lyy.user.infrastructure.base.MerchantBaseServiceImpl;
import com.lyy.user.infrastructure.constants.MemberGroupLiftingStrategyEnum;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 会员等级 服务实现类
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
@Service
@Slf4j
public class MemberLevelServiceImpl extends MerchantBaseServiceImpl<MemberLevelMapper, MemberLevel> implements IMemberLevelService {

    @Autowired
    private IMemberRuleService memberRuleService;

    @Resource
    private MemberGroupMapper memberGroupMapper;

    @Resource
    private MemberMapper memberMapper;

    @Resource
    private IMemberGroupService memberGroupService;

    /**
     * 保存或更新会员的等级信息
     *
     * @param memberLevelSaveDTO
     * @return
     */
    @Override
    public Long saveOrUpdate(MemberLevelSaveDTO memberLevelSaveDTO) {
        MemberLevel memberLevel = new MemberLevel();
        BeanUtils.copyProperties(memberLevelSaveDTO, memberLevel, "memberRuleList", "createTime", "updateTime", "createBy");
        memberLevel.setUpdateTime(new Date());
        if (memberLevel.getId() == null) {
            memberLevel.setCreateTime(new Date());
            memberLevel.setCreateBy(memberLevelSaveDTO.getCreateBy());
        }
        memberLevel.setActive(true);
        super.saveOrUpdate(memberLevel);

        // 保存会员等级规则信息
        if (memberLevelSaveDTO.getMemberRuleList() != null && !memberLevelSaveDTO.getMemberRuleList().isEmpty()) {
            memberRuleService.saveOfMemberLevel(memberLevel.getMerchantId(), memberLevel.getId(),
                    memberLevelSaveDTO.getMemberRuleList());
        }

        return memberLevel.getId();
    }

    /**
     * 根据会员组获取对应的会员等级
     *
     * @param merchantId
     * @param memberGroupId
     * @param isContainZero 是否包含0成长值等级的
     * @return
     */
    @Override
    public List<MemberLevel> findByMemberGroup(Long merchantId, Long memberGroupId, boolean isContainZero) {
        if (memberGroupId.equals(-1L)) {
            // 查询默认会员组
            List<MemberGroup> memberGroupList = memberGroupMapper.selectList(new QueryWrapper<MemberGroup>().eq("merchant_id", merchantId).orderByAsc("create_time"));
            if (memberGroupList != null && memberGroupList.size() > 0) {
                memberGroupId = memberGroupList.get(0).getId();
            }
        }
        QueryWrapper<MemberLevel> queryWrapper = new QueryWrapper<MemberLevel>()
                .eq(getShardingFieldKey(), merchantId)
                .eq("member_group_id", memberGroupId)
                .gt(!isContainZero, "grow_value", 0)
                .ge(isContainZero, "grow_value", 0)
                .and(wrapper -> wrapper.isNull("active").or().eq("active", true))
                //根据成长值排序
                .orderByAsc("grow_value");
        return super.list(queryWrapper);
    }

    @Override
    public List<MemberLevelDTO> findSmallVenueMemberLevelByMemberGroup(Long merchantId, Long memberGroupId, boolean isContainZero) {
        boolean isSmallVenueMerchant = false;
        if (memberGroupId.equals(-1L)) {
            isSmallVenueMerchant = true;
            // 查询默认会员组
            List<MemberGroup> memberGroupList = memberGroupMapper.selectList(new QueryWrapper<MemberGroup>().eq("merchant_id", merchantId).orderByAsc("create_time"));
            if (memberGroupList != null && memberGroupList.size() > 0) {
                memberGroupId = memberGroupList.get(0).getId();
            }
        }
        QueryWrapper<MemberLevel> queryWrapper = new QueryWrapper<MemberLevel>()
                .eq(getShardingFieldKey(), merchantId)
                .eq("member_group_id", memberGroupId)
                .gt(!isContainZero, "grow_value", 0)
                .ge(isContainZero, "grow_value", 0)
                .and(wrapper -> wrapper.isNull("active").or().eq("active", true))
                //根据成长值排序
                .orderByAsc("grow_value");
        List<MemberLevel> memberLevelList = super.list(queryWrapper);
        boolean finalIsSmallVenueMerchant = isSmallVenueMerchant;
        return memberLevelList.stream().map(memberLevel -> {
            MemberLevelDTO memberLevelDTO = new MemberLevelDTO();
            BeanUtils.copyProperties(memberLevel, memberLevelDTO);
            if (finalIsSmallVenueMerchant) {
                Long memberLevelCount = memberMapper.selectCount(new QueryWrapper<Member>().eq("merchant_id", memberLevel.getMerchantId()).eq("member_level_id", memberLevel.getId()));
               if(Objects.nonNull(memberLevelCount)){
                   memberLevelDTO.setMemberCount(memberLevelCount.intValue());
               }

            }
            return memberLevelDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据id获取详情信息
     *
     * @param merchantId
     * @param memberLevelId
     * @return
     */
    @Override
    public MemberLevelSaveDTO getInfoById(Long merchantId, Long memberLevelId) {
        MemberLevel memberLevel = super.getById(merchantId, memberLevelId);
        MemberLevelSaveDTO memberLevelSaveDTO = new MemberLevelSaveDTO();
        if (memberLevel == null) {
            return memberLevelSaveDTO;
        }
        BeanUtils.copyProperties(memberLevel, memberLevelSaveDTO);

        //获取规则信息
        List<MemberRule> ruleList = memberRuleService.findByMemberLevel(merchantId, memberLevelId);
        if (ruleList == null || ruleList.isEmpty()) {
            memberLevelSaveDTO.setMemberRuleList(Collections.EMPTY_LIST);
        } else {
            List<MemberRuleDTO> memberRuleList = ruleList.stream().map(rule -> {
                MemberRuleDTO memberRuleDTO = new MemberRuleDTO();
                BeanUtils.copyProperties(rule, memberRuleDTO);
                return memberRuleDTO;
            }).collect(Collectors.toList());
            memberLevelSaveDTO.setMemberRuleList(memberRuleList);
        }
        return memberLevelSaveDTO;
    }

    /**
     * 删除会员等级信息，并删除对应的会员规则信息
     *
     * @param merchantId
     * @param memberLevelId
     * @return
     */
    @Override
    public int removeByMemberLevel(Long merchantId, Long memberLevelId) {
        int del = memberRuleService.removeByMemberLevel(merchantId, memberLevelId);
        log.debug("删除 {} 会员等级 {} 个会员规则", memberLevelId, del);
        return super.removeById(merchantId, memberLevelId);
    }

    /**
     * 获取第一个会员等级
     *
     * @param merchantId
     * @param memberGroupId
     * @param liftingStrategy
     * @return
     */
    @Override
    public MemberLevel getFirstLevel(Long merchantId, Long memberGroupId, Short liftingStrategy) {
        boolean isUpgrade = !MemberGroupLiftingStrategyEnum.LIFTING_STRATEGY_DEMOTING.getValue().equals(liftingStrategy);
        QueryWrapper<MemberLevel> queryWrapper = new QueryWrapper<MemberLevel>()
                .eq(getShardingFieldKey(), merchantId)
                .eq("member_group_id", memberGroupId)
                //根据成长值排序,升级拿最小的，降级拿最大的
                .orderByAsc(isUpgrade, "grow_value")
                .orderByDesc(!isUpgrade, "grow_value")
                .last("limit 1");
        return super.getOne(queryWrapper);
    }

    /**
     * 根据会员的成长值获取需要变动的等级
     *
     * @param merchantId
     * @param memberId
     * @param isUpgrade  是否为升级功能，true为升级，false为降级
     */
    @Override
    public List<MemberLevel> findLevelOfMemberGrowValue(Long merchantId, Long memberId, boolean isUpgrade) {

        return getBaseMapper().findLevelOfMemberGrowValue(merchantId, memberId, isUpgrade);
//        if(isUpgrade){
//            return getBaseMapper().findLevelOfMemberGrowValueUpgrade(memberId);
//        }else{
//            return getBaseMapper().findLevelOfMemberGrowValueDemoting(memberId);
//        }
    }

    /**
     * 批量更新会员组等级信息
     *
     * @param merchantId
     * @param memberGroupId
     * @param memberLevelList
     * @return
     */
    @Override
    public int saveOrUpdateBatchMemberLevel(Long merchantId, Long memberGroupId, List<MemberLevelSaveDTO> memberLevelList) {
        //先清理旧的等级信息
        List<Long> notInList = memberLevelList.stream()
                .map(MemberLevelSaveDTO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        int del = removeByMemberGroup(merchantId, memberGroupId, notInList);
        log.info("清除 {} 商户下的 {} 会员组 {} 条等级信息", merchantId, memberGroupId, del);

        autoAddZeroLevel(merchantId, memberGroupId, memberLevelList);
        memberLevelList.forEach(memberLevelSaveDTO -> {
            memberLevelSaveDTO.setMemberGroupId(memberGroupId);
            memberLevelSaveDTO.setMerchantId(merchantId);
            //先不处理批量保存的问题，先遍历
            saveOrUpdate(memberLevelSaveDTO);
        });
        return memberLevelList.size();
    }


    /**
     * 自动添加0经验值的会员等级
     *
     * @param merchantId
     * @param memberGroupId
     * @param memberLevelList
     * @return
     */
    private boolean autoAddZeroLevel(Long merchantId, Long memberGroupId, List<MemberLevelSaveDTO> memberLevelList) {
        for (MemberLevelSaveDTO level : memberLevelList) {
            if (level.getGrowValue() <= 0) {
                //已经有0经验值的等级，不需要再处理
                return false;
            }
        }
        Long ZeroId = getZeroLevelId(merchantId, memberGroupId);
        if (Objects.nonNull(ZeroId)) {
            //已经有0经验值的等级，不需要再处理
            return false;
        }
        MemberLevelSaveDTO zeroLevel = new MemberLevelSaveDTO();
        zeroLevel.setMerchantId(merchantId);
        zeroLevel.setMemberGroupId(memberGroupId);
        zeroLevel.setName("初始等级");
        zeroLevel.setGrowValue(0L);
        zeroLevel.setMemberRuleList(Collections.emptyList());
        return saveOrUpdate(zeroLevel) > 0;
    }


    /**
     * 获取下一个等级的列表
     *
     * @param merchantId
     * @param memberLevelId
     * @param size          需要获取的最大个数
     * @param isNext        是否为下一个等级，若true为下个等级，若false为上个等级
     * @return
     */
    @Override
    public List<MemberLevel> findNextLevel(Long merchantId, Long memberLevelId, int size, boolean isNext) {
        return getBaseMapper().findNextLevel(merchantId, memberLevelId, size, isNext);
    }

    /**
     * 获取0成长值的等级id
     *
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    @Override
    public Long getZeroLevelId(Long merchantId, Long memberGroupId) {
        QueryWrapper<MemberLevel> queryWrapper = new QueryWrapper<MemberLevel>()
                .select("id")
                .eq(getShardingFieldKey(), merchantId)
                .eq("member_group_id", memberGroupId)
                //成长值为0的
                .eq("grow_value", 0)
                .last("limit 1");
        return Optional.ofNullable(getOne(queryWrapper))
                .map(MemberLevel::getId)
                .orElse(null);
    }

    @Override
    public void saveOrUpdateSmallVenueMemberLevel(SmallVenueMemberLevelSaveDTO smallVenueMemberLevelSaveDTO) {
        MemberLevel memberLevel = new MemberLevel();
        Long memberGroupId = smallVenueMemberLevelSaveDTO.getMemberGroupId();
        if (memberGroupId == null) {
            // 查询小场地的默认会员组，不存在则创建
            MemberGroup memberGroup = memberGroupService.getSmallVenueMemberGroup(smallVenueMemberLevelSaveDTO.getMerchantId());
            memberGroupId = memberGroup.getId();
        }
        BeanUtils.copyProperties(smallVenueMemberLevelSaveDTO, memberLevel, "memberRuleList", "createTime", "updateTime", "createBy");
        memberLevel.setMemberGroupId(memberGroupId);
        memberLevel.setUpdateTime(new Date());
        if (memberLevel.getId() == null) {
            memberLevel.setCreateTime(new Date());
            memberLevel.setCreateBy(smallVenueMemberLevelSaveDTO.getCreateBy());
            memberLevel.setActive(true);
        }
        super.saveOrUpdate(memberLevel);
    }

    private int removeByMemberGroup(Long merchantId, Long memberGroupId, List<Long> notInList) {
        UpdateWrapper<MemberLevel> updateWrapper = new UpdateWrapper<MemberLevel>()
                .eq(getShardingFieldKey(), merchantId)
                .eq("member_group_id", memberGroupId)
                .notIn(!notInList.isEmpty(), "id", notInList)
                //补充只删除成长值大于0的等级
                .gt("grow_value", 0);
        updateWrapper.set("active", false);
        updateWrapper.and(wrapper -> wrapper.isNull("active").or().eq("active", true));

        return getBaseMapper().update(null, updateWrapper);
    }
}
