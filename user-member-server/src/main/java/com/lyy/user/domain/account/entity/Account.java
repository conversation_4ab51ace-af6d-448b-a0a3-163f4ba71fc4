package com.lyy.user.domain.account.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "um_account")
public class Account {
    /**
     * 	账户ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 	用户ID
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 	商户ID
     */
    @TableField(value = "merchant_id")
    private Long merchantId;

    /**
     * 	商户用户ID
     */
    @TableField(value = "merchant_user_id")
    private Long merchantUserId;

    /**
     * 	总权益
     */
    @TableField(value = "total")
    private BigDecimal total;

    /**
     * 	剩余权益
     */
    @TableField(value = "balance")
    private BigDecimal balance;

    /**
     * 	权益类型
     */
    @TableField(value = "classify")
    private Integer classify;

    /**
     * 	状态：1=正常，2=禁用
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 	备注
     */
    @TableField(value = "description")
    private String description;

    /**
     * 	创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "create_by")
    private Long createBy;

    /**
     * 	更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 	更新人
     */
    @TableField(value = "update_by")
    private Long updateBy;

    /**
     *  卡号
     */
    @TableField(value = "card_no")
    private String cardNo;

    /**
     * 父账号id
     */
    @TableField(value = "parent_account_id")
    private Long parentAccountId;

    /**
     * 所属门店id
     */
    @TableField(value = "store_id")
    private Long storeId;

    /**
     * 卡有效期
     */
    @TableField(value = "down_time")
    private Date downTime;

    /**
     * 押金
     */
    private BigDecimal deposit;

    /**
     *
     */
    @TableField(value = "default_flag")
    private Boolean defaultFlag;
}