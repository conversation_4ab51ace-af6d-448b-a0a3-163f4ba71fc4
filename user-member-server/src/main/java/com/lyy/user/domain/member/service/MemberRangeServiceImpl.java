package com.lyy.user.domain.member.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lyy.user.account.infrastructure.member.dto.MemberRangeAssociatedDTO;
import com.lyy.user.application.member.IMemberRangeService;
import com.lyy.user.domain.member.entity.MemberRange;
import com.lyy.user.domain.member.repository.MemberRangeMapper;
import com.lyy.user.infrastructure.base.MerchantBaseServiceImpl;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 会员组范围 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
@Service
@Slf4j
public class MemberRangeServiceImpl extends MerchantBaseServiceImpl<MemberRangeMapper, MemberRange> implements IMemberRangeService {


    /**
     * 获取会员组的范围列表
     *
     *
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    @Override
    public List<MemberRangeAssociatedDTO> getRangeAssociatedList(Long merchantId, Long memberGroupId) {
        QueryWrapper<MemberRange> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("applicable", "associated_id")
                .eq(getShardingFieldKey(), merchantId)
                .eq("member_group_id", memberGroupId)
                .and(wrapper -> wrapper.isNull("active").or().eq("active", true))
                .orderByAsc("applicable", "associated_id");
        List<MemberRange> memberRangeList = getBaseMapper().selectList(queryWrapper);
        return memberRangeList.stream()
                //按适用类型进行分组
                .collect(Collectors.groupingBy(MemberRange::getApplicable))
                .entrySet().stream()
                //转换对象
                .map(entry -> {
                    MemberRangeAssociatedDTO memberRangeAssociatedDTO = new MemberRangeAssociatedDTO();
                    memberRangeAssociatedDTO.setApplicable(entry.getKey());
                    //汇总所有关系id列表
                    List<Long> associatedList = entry.getValue().stream()
                            .map(MemberRange::getAssociatedId)
                            .collect(Collectors.toList());
                    memberRangeAssociatedDTO.setAssociatedIdList(associatedList);
                    return memberRangeAssociatedDTO;
                }).collect(Collectors.toList());
    }

    /**
     * 更新会员组的范围
     *
     *
     * @param merchantId
     * @param memberGroupId
     * @param memberRangeAssociatedDTOList
     * @return
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public boolean updateRangeAssociated(Long merchantId, Long memberGroupId, List<MemberRangeAssociatedDTO> memberRangeAssociatedDTOList) {
        int del = getBaseMapper().deleteNotInAssociated(merchantId,memberGroupId, memberRangeAssociatedDTOList);
        log.info("memberGroupId：{}共删除{}条范围数据", memberGroupId, del);

        List<MemberRangeAssociatedDTO> existList = getRangeAssociatedList(merchantId, memberGroupId);

        //生成对应的需要新增的范围数据内容
        List<MemberRange> list = memberRangeAssociatedDTOList.stream()

                .flatMap(memberRangeAssociatedDTO -> {
                    //过滤已有的数据
                    List<Long> hasList = existList.stream()
                            .filter(exist -> memberRangeAssociatedDTO.getApplicable().equals(exist.getApplicable()))
                            .findFirst()
                            .map(MemberRangeAssociatedDTO::getAssociatedIdList)
                            .orElse(new ArrayList<>(0));

                    return memberRangeAssociatedDTO.getAssociatedIdList().stream()
                            //过滤已有的数据
                            .filter(id -> {
                                if (!hasList.isEmpty()) {
                                    return !hasList.contains(id);
                                }
                                return true;
                            })
                            //创建对应对象
                            .map(id -> {
                                MemberRange memberRange = new MemberRange();
                                memberRange.setMemberGroupId(memberGroupId);
                                memberRange.setApplicable(memberRangeAssociatedDTO.getApplicable());
                                memberRange.setMerchantId(merchantId);
                                memberRange.setAssociatedId(id);
                                memberRange.setActive(true);
                                return memberRange;
                            });
                }).collect(Collectors.toList());
        log.info("memberGroupId：{}共新增{}条范围数据", memberGroupId, list.size());
        log.debug("memberGroupId：{}的范围数据-->{}", memberGroupId, list);
        if (!list.isEmpty()) {
            MemberRangeServiceImpl proxy = (MemberRangeServiceImpl) AopContext.currentProxy();
            proxy.saveBatch(list);
        }
        return true;
    }

}
