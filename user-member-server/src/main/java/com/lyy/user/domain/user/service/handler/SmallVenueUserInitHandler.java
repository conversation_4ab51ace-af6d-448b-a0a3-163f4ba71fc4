package com.lyy.user.domain.user.service.handler;

import static java.util.Optional.ofNullable;

import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.app.interfaces.facade.dto.UserVO;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.domain.user.service.AbstractUserInitService;
import com.lyy.user.domain.user.service.UserInitHandler;
import com.lyy.user.infrastructure.execption.BusinessException;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @ClassName: SmallVenueUserInitHandler
 * @description: 小场地用户初始
 * @author: pengkun
 * @date: 2022/01/06
 **/
@Slf4j
@Service("smallVenueUserInit")
public class SmallVenueUserInitHandler extends AbstractUserInitService implements UserInitHandler {

    /**
     * 用户初始化处理方法
     *
     * @param dto
     * @return
     */
    @Override
    public UserInfoDTO handle(UserCreateDTO dto) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        //获取商户用户信息
        MerchantUser merchantUser = getMerchantUser(dto);
        if (merchantUser != null) {
            //手机已经被用户使用
            log.warn("手机已经被用户使用,{},{}", dto.getMerchantUserCreateDTO().getMerchantId(), dto.getTelephone());
            userInfoDTO.setMerchantId(merchantUser.getMerchantId());
            userInfoDTO.setLyyUserId(merchantUser.getUserId());
            userInfoDTO.setMerchantUserId(merchantUser.getId());
        } else {
            //是否需要初始化账户,不记名卡根据卡号初始化用户,需要初始账户
            boolean accountFlag = false;
            UserVO userDTO;
            //手机号码为空
            if (StringUtils.isBlank(dto.getTelephone())) {
                if (StringUtils.isNotBlank(dto.getCardNo())) {
                    //会员卡初始化用户
                    dto.setOpenid(UserMemberSysConstants.SMALL_VENUE_USER_PREFIX + dto.getCardNo());
                    dto.setUnionid(UserMemberSysConstants.SMALL_VENUE_USER_PREFIX + dto.getCardNo());
                    accountFlag = true;
                    userDTO = super.getByOpenId(dto.getOpenid());
                } else {
                    throw new BusinessException(UserErrorCode.USER_CARD_NO_EMPTY_ERROR);
                }
            } else {
                dto.setOpenid(UserMemberSysConstants.SMALL_VENUE_USER_PREFIX + dto.getTelephone());
                dto.setUnionid(UserMemberSysConstants.SMALL_VENUE_USER_PREFIX + dto.getTelephone());
                userDTO = super.getPlatformUserByTelephone(dto.getTelephone(), UserMemberSysConstants.PLATFORM_MERCHANT_ID);
                if (userDTO == null) {
                    userDTO = getByUnionIdOrOpenId(dto.getUnionid(), dto.getOpenid());
                }
            }

            Long userId;
            if (userDTO == null) {
                //初始化平台用户
                userId = super.initPlatformUser(dto, true);
            } else {
                userId = userDTO.getId();
            }
            userInfoDTO.setLyyUserId(userId);
            userInfoDTO.setIsactive(userInfoDTO.getIsactive());
            if (Objects.nonNull(dto.getMerchantUserCreateDTO())
                    && dto.getMerchantUserCreateDTO().getMerchantId() != null
                    && dto.getMerchantUserCreateDTO().getMerchantId() > 0) {
                //初始化商户用户
                dto.getMerchantUserCreateDTO().setUserId(ofNullable(dto.getMerchantUserCreateDTO().getUserId()).orElse(userId));
                if (StringUtils.isBlank(dto.getMerchantUserCreateDTO().getTelephone())) {
                    dto.getMerchantUserCreateDTO().setTelephone(dto.getTelephone());
                }
                Long merchantUserId = super.initSmallVenueMerchantUser(dto);
                //初始化用户会员等级
                super.initUserMember(userId, dto.getMerchantUserCreateDTO().getMerchantId());
                userInfoDTO.setMerchantId(dto.getMerchantUserCreateDTO().getMerchantId());
                userInfoDTO.setMerchantUserId(merchantUserId);
                if (accountFlag) {
                    //初始化卡账户
                    super.initSmallVenAccount(dto, userInfoDTO.getLyyUserId(), merchantUserId);
                }
            }
        }
        return userInfoDTO;
    }
}
