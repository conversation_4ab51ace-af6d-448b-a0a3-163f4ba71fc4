package com.lyy.user.domain.account.service;

import static com.lyy.error.member.infrastructure.AccountErrorCode.ACCOUNT_BENEFIT_NOT_ENOUGH;
import static com.lyy.error.member.infrastructure.AccountErrorCode.ACCOUNT_CANCELLATION_ERROR;
import static com.lyy.error.member.infrastructure.AccountErrorCode.ACCOUNT_CREATE_FAIL;
import static com.lyy.error.member.infrastructure.AccountErrorCode.ACCOUNT_DEFAULT_FLAG_ADJUST_ERROR;
import static com.lyy.error.member.infrastructure.AccountErrorCode.ACCOUNT_STATUS_ERROR;
import static com.lyy.error.member.infrastructure.AccountErrorCode.BENEFIT_NOT_FOUND;
import static com.lyy.error.member.infrastructure.AccountErrorCode.BENEFIT_PARAM_ERROR;
import static com.lyy.error.member.infrastructure.AccountErrorCode.TRANSFER_ERROR;
import static com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum.SV_DEDUCTION;
import static com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum.SV_EQUIPMENT_CONSUMPTION;
import static com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum.SV_EQUIPMENT_CONSUMPTION_REFUND;
import static com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum.SV_EXCHANGE;
import static com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum.SV_EXCHANGE_REFUND;
import static com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum.SV_GIFT_RECYCLING;
import static com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum.SV_GIFT_REFUND;
import static com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum.SV_PRESENTATION;
import static com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum.SV_PRESENTATION_REFUND;
import static com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum.SV_RECHARGE;
import static com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum.SV_REFUND;
import static com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum.SV_STORED_VALUE_DEDUCTION;
import static com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum.SV_STORED_VALUE_DEDUCTION_REFUND;
import static com.lyy.user.infrastructure.util.DateTimeUtils.parseLocalDateTimeOrElse;
import static com.lyy.user.infrastructure.util.DateTimeUtils.parseLocalTimeOrElse;
import static java.math.BigDecimal.ONE;
import static java.math.BigDecimal.ZERO;
import static java.util.Optional.ofNullable;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.lyy.error.member.infrastructure.AccountErrorCode;
import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.idempotent.core.exception.action.CompletedActionException;
import com.lyy.idempotent.core.support.IdempotentTemplate;
import com.lyy.idempotent.core.support.IdempotentTemplateBuilder;
import com.lyy.klock.annotation.Klock;
import com.lyy.klock.handler.KlockTimeoutException;
import com.lyy.klock.model.LockTimeoutStrategy;
import com.lyy.lock.redis.RedisLock;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.account.dto.SmallVenueMergeAccountInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.SmallVenueMergeAccountInfoDTO.MergeCardBenefit;
import com.lyy.user.account.infrastructure.account.dto.request.AccountBenefitUpdateDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.AccountCancellationDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.AccountRecordInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.AvailableBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitDecrementDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitDecrementDTO.BenefitDetailsDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitIncrementBatchDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitIncrementDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitIncrementItemDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitRefundQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitRefundResultDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.CardTransferDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MerchantBenefitIncrementDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueAccountBenefitTransferDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueAccountBenefitTransferQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueUserAvailableBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.UserAccountBenefit;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.UserAccountRecord;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.request.AccountDefaultStatusUpdateDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.request.BenefitConsumptionSummaryQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.response.AccountRecordBenefitSummaryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.response.BenefitConsumptionSummaryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.response.BenefitRechargeSummaryDTO;
import com.lyy.user.account.infrastructure.constant.AccountBenefitNumTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountBenefitStatusEnum;
import com.lyy.user.account.infrastructure.constant.AccountRecordOperationTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountStatusEnum;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.CardExpiredTypeEnum;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueMobileUserListSelectDTO;
import com.lyy.user.application.account.AccountService;
import com.lyy.user.application.benefit.SmallVenueAccountService;
import com.lyy.user.domain.account.dto.AccountInitDTO;
import com.lyy.user.domain.account.dto.AccountInitResultDTO;
import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.domain.account.entity.AccountBenefit;
import com.lyy.user.domain.account.entity.AccountRecord;
import com.lyy.user.domain.statistics.entity.SmallVenueStoredStatistics;
import com.lyy.user.domain.statistics.repository.SmallVenueStoredStatisticsMapper;
import com.lyy.user.domain.user.dto.TagUserParamDTO;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.domain.user.service.MerchantAutoTagAsyncHandler;
import com.lyy.user.infrastructure.constants.RedisKey;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.mapstruct.AccountMapStruct;
import com.lyy.user.infrastructure.repository.account.AccountRecordRepository;
import com.lyy.user.infrastructure.repository.account.SmallVenueAccountRepository;
import com.lyy.user.infrastructure.repository.user.MerchantUserRepository;
import com.lyy.user.infrastructure.support.specification.account.AccountCancellationSpecification;
import com.lyy.user.infrastructure.support.specification.account.AccountPredicateSpecification;
import com.lyy.user.infrastructure.support.specification.account.AccountValidSpecification;
import com.lyy.user.infrastructure.support.specification.account.DecrementChangingTotalConsumeSpecification;
import com.lyy.user.infrastructure.support.specification.account.FrequencyBenefitSpecification;
import com.lyy.user.infrastructure.support.specification.account.IncrementChangingTotalSpecification;
import com.lyy.user.infrastructure.support.specification.account.IncrementRollbackTotalConsumeSpecification;
import com.lyy.user.infrastructure.support.specification.account.MainAccountSpecification;
import com.lyy.user.infrastructure.support.specification.account.TransferSpecification;
import com.lyy.user.infrastructure.support.tuple.AccountTransferTuple;
import com.lyy.user.infrastructure.support.tuple.AccountTuple;
import com.lyy.user.infrastructure.support.tuple.MainAccountTuple;
import com.lyy.user.infrastructure.util.DateTimeUtils;
import com.lyy.user.infrastructure.util.LyyStringUtil;
import com.lyy.user.infrastructure.util.MathUtils;
import com.lyy.user.infrastructure.util.WxAuthUpdateUtils;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.BinaryOperator;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

/**
 * <AUTHOR>
 * @since 2022/1/8
 */
@Slf4j
@Service
public class SmallVenueAccountServiceImpl implements SmallVenueAccountService {

    @Resource(name = "smallVenueAccountRepository")
    private SmallVenueAccountRepository accountRepository;
    @Resource
    private MerchantUserRepository merchantUserRepository;
    @Resource
    private AccountRecordRepository accountRecordRepository;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private AccountMapStruct mapStruct;

    private SmallVenueAccountService self;

    @Resource
    private AccountService accountService;

    @Autowired
    private IdempotentTemplateBuilder idempotentTemplateBuilder;

    @PostConstruct
    public void init() {
        self = applicationContext.getBean(SmallVenueAccountService.class);
    }

    @Resource
    private RedisLock redisLock;

    @Resource
    private AsyncVenueAccountHandler asyncVenueAccountHandler;


    @Autowired
    private MerchantAutoTagAsyncHandler merchantAutoTagAsyncHandler;

    @Autowired
    private SmallVenueStoredStatisticsMapper smallVenueStoredStatisticsMapper;

    @Value("${smallVenue.benefit-perf-switch:false}")
    private Boolean svBenefitPerf;

    @Value("${smallVenue.order-record-time-offset:2}")
    private Integer orderRecordTimeOffset;

    private final static List<Integer> REFUND_WITH_ROLLBACK_BENEFIT_LIST = Lists.newArrayList(
            SV_EQUIPMENT_CONSUMPTION_REFUND.getCode(),
            SV_EXCHANGE_REFUND.getCode(),
            SV_STORED_VALUE_DEDUCTION_REFUND.getCode()
    );

    public static Map<Long, List<AccountBenefit>> sortedMapList(List<AccountBenefit> benefits) {
        return benefits
                .stream()
                .collect(Collectors.groupingBy(AccountBenefit::getMerchantBenefitClassifyId))
                .entrySet()
                .stream()
                .peek(entry -> entry.setValue(sortedGroupByCategory(entry)))
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue));
    }

    private static List<AccountBenefit> sortedGroupByCategory(Entry<Long, List<AccountBenefit>> entry) {
        return entry.getValue().stream()
                .collect(Collectors.groupingBy(ab -> ofNullable(ab.getExpiryDateCategory()).orElse(ExpiryDateCategoryEnum.NO_LIMIT.getValue())))
                .entrySet()
                .stream()
                .sorted(Entry.comparingByKey(Comparator.reverseOrder()))
                .flatMap(e -> e.getValue().stream()
                        .collect(Collectors.toCollection(getTreeSetSupplier(e.getKey())))
                        .stream()
                )
                .collect(Collectors.toList());
    }

    /**
     * 先过期的先转。过期时间一致的，按生效时间转
     * 如果是生效时间一致的，按数据库id顺序
     */
    private static Supplier<TreeSet<AccountBenefit>> getTreeSetSupplier(Integer category) {
        Supplier<TreeSet<AccountBenefit>> supplier;
        if (Objects.equals(category, ExpiryDateCategoryEnum.TIME_INTERVAL.getValue())) {
            supplier = () -> new TreeSet<>(
                    Comparator.comparing((AccountBenefit ab) -> parseLocalDateTimeOrElse(ab.getDownTime()))
                            .thenComparing((AccountBenefit ab) -> parseLocalDateTimeOrElse(ab.getUpTime()))
                            .thenComparing(AccountBenefit::getId)
            );
        } else if (Objects.equals(category, ExpiryDateCategoryEnum.ONE_DAY_TIME_INTERVAL.getValue())) {
            supplier = () -> new TreeSet<>(
                    Comparator.comparing((AccountBenefit ab) -> parseLocalTimeOrElse(ab.getDownTime()))
                    .thenComparing((AccountBenefit ab) -> parseLocalTimeOrElse(ab.getUpTime()))
                    .thenComparing(AccountBenefit::getId)
            );
        } else {
            supplier = () -> new TreeSet<>(Comparator.comparing(AccountBenefit::getId));
        }
        return supplier;
    }

    /**
     * 是否可以整合的无限期权益明细：无生效时间、无过期时间、过期类型为无限制，使用规则ID 参考 {@link SmallVenueAccountServiceImpl#getAggregationAccountBenefitMap}
     *
     * @param category  权益类型
     * @param valueType 权益值类型
     * @param uptime 生效时间
     * @return boolean
     */
    private static boolean couldAggregateBenefit(Integer category, Integer valueType, String uptime) {
        return Objects.equals(category, ExpiryDateCategoryEnum.NO_LIMIT.getValue())
                && Objects.equals(valueType, AccountBenefitNumTypeEnum.AMOUNT.getCode())
                && org.apache.commons.lang3.StringUtils.isBlank(uptime);
    }

    private static boolean couldAggregateBenefit(AccountBenefit accountBenefit) {
        return couldAggregateBenefit(accountBenefit.getExpiryDateCategory(), accountBenefit.getValueType(), accountBenefit.getUpTime());
    }

    public static Map<Long, BigDecimal> getBalanceSumGroupMap(List<AccountBenefit> benefits, Function<AccountBenefit, Long> mapper) {
        Map<Long, BigDecimal> map = benefits
                .stream()
                .collect(Collectors.groupingBy(mapper, Collectors.reducing(ZERO, AccountBenefit::getBalance, BigDecimal::add)));
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--当前会员卡账户总权益分组 map: {}", map);
        }
        return map;
    }

    static SmallVenueStoredStatistics generateUpdatedStatistics4Decrement(BenefitDecrementDTO.BenefitDetailsDTO detail,
                                                                          SmallVenueStoredStatistics statistics,
                                                                          AccountBenefit accountBenefit,
                                                                          Integer recordType) {
        BigDecimal detailNum = detail.getNum();
        SmallVenueStoredStatistics entity = new SmallVenueStoredStatistics();
        entity.setId(statistics.getId());
        BigDecimal balance = MathUtils.add(statistics.getBalance(), detailNum);
        entity.setBalance(balance);
        statistics.setBalance(balance);
        // 进行消费需要增加累计消费
        if (new DecrementChangingTotalConsumeSpecification().isSatisfiedBy(recordType)) {
            BigDecimal totalConsume = MathUtils.add(statistics.getTotalConsume(), detailNum.abs());
            entity.setTotalConsume(totalConsume);
            statistics.setTotalConsume(totalConsume);
            if (shouldChangeNum(detail, accountBenefit, detailNum)) {
                Integer totalNumConsume = MathUtils.add(statistics.getTotalNumConsume(), 1);
                Integer remainNum = MathUtils.subtract(statistics.getRemainNum(), 1);
                entity.setTotalNumConsume(totalNumConsume);
                entity.setRemainNum(remainNum);
                statistics.setTotalNumConsume(totalNumConsume);
                statistics.setRemainNum(remainNum);
            }
        }
        return entity;
    }

    /**
     * 需要处理张数统计
     */
    static boolean shouldChangeNum(BenefitDecrementDTO.BenefitDetailsDTO detail, AccountBenefit accountBenefit, BigDecimal detailNum) {
        return new FrequencyBenefitSpecification().isSatisfiedBy(detail.getNumType())
                && ObjectUtils.compare(ZERO, accountBenefit.getBalance()) == 0
                && !Objects.equals(0, ZERO.compareTo(detailNum));
    }

    static SmallVenueStoredStatistics generateUpdatedStatistics4Increment(BenefitIncrementItemDTO.BenefitDetailsDTO detail,
                                                                          Integer recordType,
                                                                          SmallVenueStoredStatistics accountStatistics) {
        SmallVenueStoredStatistics entity = new SmallVenueStoredStatistics();
        entity.setId(accountStatistics.getId());
        boolean isFrequencyBenefit = new FrequencyBenefitSpecification().isSatisfiedBy(detail.getNumType());

        BigDecimal balance = MathUtils.add(accountStatistics.getBalance(), detail.getNum());
        entity.setBalance(balance);
        accountStatistics.setBalance(balance);
        if (isFrequencyBenefit) {
            int remainNum = MathUtils.add(accountStatistics.getRemainNum(), 1);
            entity.setRemainNum(remainNum);
            accountStatistics.setRemainNum(remainNum);
        }

        if (new IncrementChangingTotalSpecification().isSatisfiedBy(recordType)) {
            BigDecimal total = MathUtils.add(accountStatistics.getTotal(), detail.getNum());
            entity.setTotal(total);
            accountStatistics.setTotal(total);
            if (isFrequencyBenefit) {
                int totalNum = MathUtils.add(accountStatistics.getTotalNum(), 1);
                entity.setTotalNum(totalNum);
                accountStatistics.setTotalNum(totalNum);
            }
        }

        if (new IncrementRollbackTotalConsumeSpecification().isSatisfiedBy(recordType)) {
            if (isFrequencyBenefit) {
                if (ObjectUtils.compare(accountStatistics.getTotalConsume(), accountStatistics.getTotal()) == 0) {
                    entity.setTotalNumConsume(accountStatistics.getTotalNumConsume() - 1);
                    accountStatistics.setTotalNumConsume(accountStatistics.getTotalNumConsume() - 1);
                }
            }
            BigDecimal totalConsume = MathUtils.subtract(accountStatistics.getTotalConsume(), detail.getNum());
            entity.setTotalConsume(totalConsume);
            accountStatistics.setTotalConsume(totalConsume);
        }
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--更新账户权益统计, accountStatistics: {}", entity);
        }
        return entity;
    }

    @Override
    public Optional<Account> get(Long merchantId, Long userId, String cardNo) {
        AccountTuple accountTuple = getAccountTuple(merchantId, userId, cardNo);
        if (accountTuple.getCardAccount().isPresent()) {
            return accountTuple.getCardAccount();
        } else if (StringUtils.isNotBlank(cardNo)) {
            return Optional.empty();
        }
        return accountTuple.getDefaultAccount();
    }

    @Override
    public Account initAccountIfAbsent(Long merchantId, Long userId, String cardNo, BigDecimal deposit, Supplier<AccountInitDTO> supplier) {
        AccountTuple accountTuple = getAccountTuple(merchantId, userId, cardNo);

        if (accountTuple.getCardAccount().isPresent()) {
            return accountTuple.getCardAccount().get();
        }

        if (StringUtils.isNotBlank(cardNo)) {
            if (accountTuple.getDefaultAccount().isPresent()) {
                Account account = accountTuple.getDefaultAccount().get();
                if (StringUtils.isBlank(account.getCardNo()) && StringUtils.isNotBlank(cardNo)) {
                    account.setCardNo(cardNo);
                    if ((account.getDeposit() == null || ZERO.compareTo(account.getDeposit()) == 0) && deposit != null) {
                        account.setDeposit(deposit);
                    }
                    if (log.isDebugEnabled()) {
                        log.debug("[小场地会员账户]--账户未绑定会员卡, account: {}, cardNo: {}", account, cardNo);
                    }
                    accountRepository.update(merchantId, account);
                    return account;
                }
            }
        } else if (accountTuple.getDefaultAccount().isPresent()) {
            return accountTuple.getDefaultAccount().get();
        }
        return this.initAccount(supplier);
    }

    @Override
    public Account initAccountIfAbsent(Long merchantId, Long userId, Long storeId, BenefitIncrementItemDTO item, Supplier<AccountInitDTO> supplier) {
        String cardNo = item.getCardNo();
        AccountTuple accountTuple = getAccountTuple(merchantId, userId, cardNo);

        if (accountTuple.getCardAccount().isPresent()) {
            return accountTuple.getCardAccount().get();
        }

        if (StringUtils.isNotBlank(cardNo)) {
            if (accountTuple.getDefaultAccount().isPresent()) {
                Account account = accountTuple.getDefaultAccount().get();
                if (StringUtils.isBlank(account.getCardNo()) && StringUtils.isNotBlank(cardNo)) {
                    account.setCardNo(cardNo);
                    BigDecimal deposit = item.getDeposit();
                    if ((account.getDeposit() == null || ZERO.compareTo(account.getDeposit()) == 0) && deposit != null) {
                        account.setDeposit(deposit);
                    }
                    setAccountDownTime(account::setDownTime, item.getExpiredType(), item.getDownTime());
                    if (Objects.nonNull(storeId)) {
                        account.setStoreId(storeId);
                    }
                    if (log.isDebugEnabled()) {
                        log.debug("[小场地会员账户]--账户未绑定会员卡, account: {}, cardNo: {}", account, cardNo);
                    }
                    accountRepository.update(merchantId, account);
                    return account;
                }
            }
        } else if (accountTuple.getDefaultAccount().isPresent()) {
            return accountTuple.getDefaultAccount().get();
        }
        return this.initAccount(supplier);
    }

    /**
     * 获取默认账户，以及当前卡号对应的账户
     */
    AccountTuple getAccountTuple(Long merchantId, Long userId, String cardNo) {
        List<Account> accounts = accountRepository.listAccountWithDefault(merchantId, userId, cardNo);
        Account cardAccount = null;
        Account defaultAccount = null;
        for (Account account : accounts) {
            if (StringUtils.isNotBlank(cardNo) && StringUtils.equals(cardNo, account.getCardNo())) {
                cardAccount = account;
            }
            if (Objects.nonNull(account.getDefaultFlag()) && account.getDefaultFlag()) {
                defaultAccount = account;
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--当前会员卡账户: {}", cardAccount);
            log.debug("[小场地会员账户]--默认卡账户: {}", defaultAccount);
        }
        return new AccountTuple(cardAccount, defaultAccount);
    }

    @Override
    public Account initAccount(Supplier<AccountInitDTO> supplier) {
        if (Objects.isNull(supplier)) {
            throw new BusinessException(ACCOUNT_CREATE_FAIL);
        }
        AccountInitDTO initDTO = supplier.get();
        checkParam(initDTO);

        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--初始化会员, account: {}", initDTO);
        }
        if (StringUtils.isNotBlank(initDTO.getCardNo())) {
            initDTO.setDeposit(Optional.ofNullable(initDTO.getDeposit()).orElse(BigDecimal.ZERO));
        }
        AccountInitResultDTO AccountInitResultDTO = accountRepository.initAccount(initDTO);
        return AccountInitResultDTO.getAccount();
    }

    AccountBenefit saveOrUpdateAccountBenefit4Increment(BenefitIncrementDTO dto,
                                                        Account account,
                                                        boolean isIncreaseByRefund,
                                                        Map<String, AccountBenefit> accountBenefitMap,
                                                        BenefitIncrementItemDTO.BenefitDetailsDTO detail, Date now) {
        AccountBenefit ab;
        Function<AccountBenefit, AccountBenefit> function = accountBenefit -> {
            AccountBenefit entity = generateUpdatedBenefit(dto, detail, accountBenefit, isIncreaseByRefund, now);
            accountRepository.updateAccountBenefit(dto.getMerchantId(), entity);
            log.info("[小场地会员账户]--更新权益明细, benefit: {}", entity);
            return accountBenefit;
        };
        if (isIncreaseByRefund) {
            Long accountBenefitId = detail.getAccountBenefitId();
            if (Objects.nonNull(accountBenefitId)){
                log.debug("[小场地会员账户]--增加权益--退单新增权益 key: {}", accountBenefitId);
                ab = ofNullable(accountBenefitMap.get(accountBenefitId.toString()))
                        .map(function)
                        .orElseThrow(() -> new BusinessException(BENEFIT_NOT_FOUND));
            }else {
                log.debug("[小场地会员账户]--增加权益--退单新增过期权益 key: {}", accountBenefitId);
                String key = compositeKey(detail.getMerchantBenefitClassifyId(), detail.getUseRuleId());
                ab = ofNullable(accountBenefitMap.get(key))
                        .map(function)
                        .orElseGet(() -> {
                            AccountBenefit benefit = initAccountBenefit(account, dto, detail, now);
                            log.info("[小场地会员账户]--退单增加过期权益明细, benefit: {}", benefit);
                            if (couldAggregateBenefit(benefit)) {
                                accountBenefitMap.put(key, benefit);
                            }
                            return benefit;
                        });
            }
        } else {
            String key = compositeKey(detail.getMerchantBenefitClassifyId(), detail.getUseRuleId());
            log.debug("[小场地会员账户]--增加权益--购买新增权益 key: {}", key);
            ab = ofNullable(accountBenefitMap.get(key))
                    .map(function)
                    .orElseGet(() -> {
                        AccountBenefit benefit = initAccountBenefit(account, dto, detail, now);
                        log.info("[小场地会员账户]--增加权益明细, benefit: {}", benefit);
                        if (couldAggregateBenefit(benefit)) {
                            accountBenefitMap.put(key, benefit);
                        }
                        return benefit;
                    });
        }
        return ab;
    }

    static String compositeKey(AccountBenefit accountBenefit) {
        return compositeKey(accountBenefit.getMerchantBenefitClassifyId(),
                Objects.isNull(accountBenefit.getUseRuleId()) ? accountBenefit.getId() : accountBenefit.getUseRuleId());
    }

    static String compositeKey(Long classifyId, Long useRuleId) {
        return classifyId + ofNullable(useRuleId).map(i -> ":" + i).orElse("");
    }

    /**
     * <p>获取无期限额度类型权益 Map 映射，key 根据 isIncreaseByRefund 进行条件生成 </p><br/>
     * <p>true   =>  key: 'accountBenefitId'</p>
     * <p>false   =>  key: 'merchantBenefitClassifyId:useRuleId'</p>
     *
     * @param isIncreaseByRefund 是否属于退单加权益，是则通过 AccountBenefitId 增加权益
     * @param abIds
     * @return map
     */
    Map<String, AccountBenefit> getAggregationAccountBenefitMap(BenefitIncrementItemDTO item,
                                                     Account account,
                                                     boolean isIncreaseByRefund,
                                                     List<Long> abIds) {
        List<BenefitIncrementItemDTO.BenefitDetailsDTO> unlimitedDetails;
        Map<String, AccountBenefit> accountBenefitMap;
        if (isIncreaseByRefund) {
            List<AccountBenefit> list = accountRepository.selectBenefit(account.getMerchantId(), abIds);
            accountBenefitMap = list.stream()
                    .collect(Collectors.toMap(ab -> ab.getId().toString(), Function.identity()));
        } else {
            unlimitedDetails = item.getBenefitDetails()
                    .stream()
                    .filter(d -> couldAggregateBenefit(d.getExpiryDateCategory(), d.getNumType(), d.getUpTime()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(unlimitedDetails)) {
                log.debug("[小场地会员账户]--增加权益--新增详情不存在无期限权益");
                return Maps.newHashMap();
            }
            List<Long> unlimitedClassifyIds = unlimitedDetails.stream()
                    .map(BenefitIncrementItemDTO.BenefitDetailsDTO::getMerchantBenefitClassifyId)
                    .collect(Collectors.toList());
            accountBenefitMap = accountRepository.selectUnlimitedBenefit(account.getMerchantId(), account.getId(), unlimitedClassifyIds)
                    .stream()
                    .collect(Collectors.toMap(SmallVenueAccountServiceImpl::compositeKey, Function.identity()));
        }
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--增加权益--获取权益, accountBenefitMap: {}", accountBenefitMap);
        }
        return accountBenefitMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Klock(keys = "'user-member:klock:smallvenue:benefitupdate:'+#dto.merchantId+':'+#dto.userId", waitTime = 0 ,leaseTime = 10, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public void increaseAccountBenefit(BenefitIncrementDTO dto) {
        accountBenefitIncrement(dto);
    }

    private void accountBenefitIncrement(BenefitIncrementDTO dto) {
        log.info("[小场地会员账户]--增加权益, dto: {}", dto);

        Long merchantId = dto.getMerchantId();
        Long userId = dto.getUserId();
        MerchantUser merchantUser = merchantUserRepository.selectBy(merchantId, userId)
                .orElseThrow(() -> new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR));

        Date now = new Date();
        List<AccountRecord> records = Lists.newArrayList();
        AccountValidSpecification accountValidSpecification = new AccountValidSpecification();
        MainAccountSpecification mainAccountSpecification = new MainAccountSpecification();
        for (BenefitIncrementItemDTO item : dto.getItems()) {
            Account mainAccount;
            Account account = initAccountIfAbsent(merchantId, userId, dto.getStoreId(), item,
                    () -> {
                        AccountInitDTO accountInitDTO = assembleAccount(dto, item);
                        accountInitDTO.setMerchantUserId(merchantUser.getId());
                        return accountInitDTO;
                    });
            mainAccount = getMainAccount(merchantId, accountValidSpecification, mainAccountSpecification, account);

            List<BenefitIncrementItemDTO.BenefitDetailsDTO> benefitDetails = item.getBenefitDetails();
            if (CollectionUtils.isEmpty(benefitDetails)) {
                continue;
            }
            List<Long> abIds = benefitDetails.stream()
                    .map(BenefitIncrementItemDTO.BenefitDetailsDTO::getAccountBenefitId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            Integer recordType = dto.getRecord().getRecordType();
            boolean isIncreaseByRefund = isIncreaseByRefund(recordType, item, abIds, dto.getContainExpireBenefit());

            Map<String, AccountBenefit> aggregationBenefitMap = getAggregationAccountBenefitMap(item, mainAccount, isIncreaseByRefund, abIds);
            // 账号级别权益统计
            List<Long> classifyIds = benefitDetails.stream().map(BenefitIncrementItemDTO.BenefitDetailsDTO::getMerchantBenefitClassifyId).collect(Collectors.toList());
            List<AccountBenefit> accountBenefits = accountRepository.selectBenefit(merchantId, userId, mainAccount.getId(), classifyIds);
            Map<Long, AccountStatistic> accountStatisticMap = getAccountStatisticMap(accountBenefits, dto.getStoreId(),
                item.getMerchantBenefitClassifyScopeMap());
            // 总权益统计
            Map<Long, SmallVenueStoredStatistics> statisticsMap = getStatisticsMap(item, merchantId, userId);

            for (BenefitIncrementItemDTO.BenefitDetailsDTO detail : benefitDetails) {
                if (log.isDebugEnabled()) {
                    log.debug("[小场地会员账户]--增加权益--当前新增明细 detail: {}", detail);
                }
                AccountBenefit ab = saveOrUpdateAccountBenefit4Increment(dto, mainAccount, isIncreaseByRefund, aggregationBenefitMap, detail, now);

                saveOrUpdateAccountStatistics4Increment(dto, mainAccount, statisticsMap, detail, now);

                AccountStatistic accountStatistic = getAccountStatistic(isIncreaseByRefund, accountStatisticMap, detail.getNum(), ab);

                records.add(assembleIncreaseRecord(account, ab, detail, item.getCardNo(), accountStatistic));
            }
        }
        records.forEach(record -> {
            mapStruct.assignAccountRecord(dto, record);
            record.setCreateTime(now);
            if (log.isDebugEnabled()) {
                log.debug("[小场地会员账户]--记录账户权益流水, record: {}", record);
            }
        });
        boolean result = batchSaveRecord(records);
        //打标签
        if (result) {
            taggingStoreNameOrEquipmentTypeName(records.get(0));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Klock(keys = "'user-member:klock:smallvenue:benefitupdate:'+#dto.merchantId+':'+#dto.userId", waitTime = 10 ,leaseTime = 10)
    public Boolean increaseAccountBenefitLockWait(BenefitIncrementDTO dto) {
        accountBenefitIncrement(dto);
        return true;
    }

    private Account getMainAccount(Long merchantId,
                                   AccountValidSpecification accountValidSpecification,
                                   MainAccountSpecification mainAccountSpecification,
                                   Account account) {
        Account mainAccount;
        if (!accountValidSpecification.isSatisfiedBy(account)) {
            log.warn("[小场地会员账户]--当前账户状态：{}", account.getStatus());
            throw new BusinessException(ACCOUNT_STATUS_ERROR);
        }
        if (mainAccountSpecification.isSatisfiedBy(account)) {
            mainAccount = account;
        } else {
            mainAccount = getOrElseThrow(accountRepository.get(merchantId, account.getParentAccountId()));
            if (!accountValidSpecification.isSatisfiedBy(mainAccount)) {
                log.warn("[小场地会员账户]--当前主账户状态：{}", account.getStatus());
                throw new BusinessException(ACCOUNT_STATUS_ERROR);
            }
        }
        return mainAccount;
    }

    @Override
    public void increaseAccountBenefitBatch(BenefitIncrementBatchDTO dto) {
        List<BenefitIncrementDTO> list = dto.getList();
        for (int index = 0; index < list.size(); index++) {
            BenefitIncrementDTO benefitIncrement = list.get(index);
            log.debug("[小场地会员账户]--批量用户增加权益-用户Id {}", benefitIncrement.getUserId());
            try {
                IdempotentTemplate idempotentTemplate = idempotentTemplateBuilder.build();
                idempotentTemplate
                    .key(benefitIncrement.getMerchantId())
                    .key(benefitIncrement.getUserId())
                    .key(ofNullable(benefitIncrement.getRefundNo()).orElse(benefitIncrement.getOrderNo()));
                if (CollectionUtils.isNotEmpty(benefitIncrement.getItems())
                    && Objects.nonNull(benefitIncrement.getItems().get(0).getCardNo())) {
                    idempotentTemplate.key(benefitIncrement.getItems().get(0).getCardNo());
                }
                idempotentTemplate
                    .strict(false)
                    .ignoreDBLock(true)
                    .scene("SmallVenueAccountService#increaseAccountBenefit");
                idempotentTemplate.execute(() -> self.increaseAccountBenefitLockWait(benefitIncrement));
            } catch (KlockTimeoutException e) {
                if (index != 0) {
                    log.error("批量用户增加权益部分获取锁超时,merchantId:{},userId:{}", benefitIncrement.getMerchantId(), benefitIncrement.getUserId());
                }
                throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_IS_UPDATING);
            } catch (CompletedActionException completedActionException) {
                log.warn("[{}]已经执行完成，忽略错误继续执行", completedActionException.getAction().getActionId());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Klock(keys = "'user-member:klock:smallvenue:benefitupdate:'+#dto.merchantId+':'+#dto.userId", waitTime = 0 ,leaseTime = 10, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST)
    public void decreaseAccountBenefit(BenefitDecrementDTO dto) {
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--减扣权益, dto: {}", dto);
        }
        List<BenefitDecrementDTO.BenefitDetailsDTO> benefitDetails = dto.getBenefitDetails();
        if (CollectionUtils.isEmpty(benefitDetails)) {
            return;
        }
        Long merchantId = dto.getMerchantId();

        MainAccountTuple accountTuple = getAndCheckAccountTuple(dto);

        List<AccountBenefit> accountBenefits = listDecrementBenefits(benefitDetails, merchantId);
        Map<Long, AccountBenefit> accountBenefitMap = getDecrementBenefitMap(accountBenefits);
        // 账号级别权益统计
        Map<Long, AccountStatistic> accountStatisticMap = getAccountStatisticMap4Decrement(dto, benefitDetails, merchantId,
            accountTuple);

        Map<Long, SmallVenueStoredStatistics> statisticsMap = getStatisticsMap(dto);

        Date now = new Date();
        List<AccountRecord> records = Lists.newArrayList();

        for (BenefitDecrementDTO.BenefitDetailsDTO detail : benefitDetails) {
            AccountBenefit accountBenefit = ofNullable(accountBenefitMap.get(detail.getAccountBenefitId()))
                    .orElseThrow(() -> new BusinessException(BENEFIT_NOT_FOUND));

            Long operatorId = dto.getOperatorId();
            BigDecimal detailNum = detail.getNum();

            if (!dto.isNegative() && accountBenefit.getBalance().compareTo(detailNum.abs()) < 0) {
                log.warn("[小场地会员账户]--权益减扣--当前权益明细余额：{}, 需减扣余额：{}", accountBenefit.getBalance(), detailNum);
                throw new BusinessException(ACCOUNT_BENEFIT_NOT_ENOUGH);
            } else {
                log.debug("[小场地会员账户]--权益减扣--允许扣减至负数--当前权益明细余额：{}, 需减扣余额：{}", accountBenefit.getBalance(), detailNum);
            }

            Boolean changeResult = accountRepository.changeBalance(merchantId, accountBenefit.getId(), detailNum, now, operatorId);
            if (!changeResult) {
                throw new BusinessException(AccountErrorCode.ACCOUNT_BENEFIT_IS_UPDATING);
            }

            BigDecimal balance = MathUtils.add(accountBenefit.getBalance(), detailNum);
            accountBenefit.setBalance(balance);

            SmallVenueStoredStatistics statistics = ofNullable(statisticsMap.get(detail.getMerchantBenefitClassifyId()))
                    .orElseThrow(() -> new BusinessException(BENEFIT_NOT_FOUND));
            updateDecrementStatistics(detail, statistics, accountBenefit, dto.getRecord().getRecordType(), operatorId, now);

            AccountRecord record = assembleDecreaseRecord(accountTuple.getAccount(), accountBenefit, dto, detail, now, accountStatisticMap);
            record.setAccountId(accountTuple.getMainAccount().getId());
            records.add(record);

            updateAccountStatisticMapValue(accountStatisticMap, detailNum, accountBenefit, AdjustTypeEnum.DECREMENT);
        }
        boolean result = batchSaveRecord(records);
        //打标签
        if (result) {
            taggingStoreNameOrEquipmentTypeName(records.get(0));
        }
    }

    private Map<Long, AccountStatistic> getAccountStatisticMap4Decrement(BenefitDecrementDTO dto,
        List<BenefitDetailsDTO> benefitDetails, Long merchantId, MainAccountTuple accountTuple) {
        List<Long> classifyIds = benefitDetails.stream().map(BenefitDetailsDTO::getMerchantBenefitClassifyId).distinct()
            .collect(Collectors.toList());
        log.debug("[权益减扣]-计算账号下每种权益总数, classifyIds: {}", classifyIds);
        List<AccountBenefit> asStatisticsMap = accountRepository.selectBenefit(merchantId, dto.getUserId(),
            accountTuple.getMainAccount().getId(), classifyIds);
        log.debug("[权益减扣]-计算账号下每种权益总数, 获取权益列表: {}", asStatisticsMap);
        return getAccountStatisticMap(asStatisticsMap, dto.getStoreId(), dto.getMerchantBenefitClassifyScopeMap());
    }

    MainAccountTuple getAndCheckAccountTuple(BenefitDecrementDTO dto) {
        Long merchantId = dto.getMerchantId();
        Account mainAccount;
        Account account = getOrElseThrow(get(merchantId, dto.getUserId(), dto.getCardNo()));
        AccountPredicateSpecification accountPredication = new AccountPredicateSpecification(dto.getPredicate());
        accountPredication(account, accountPredication);
        if (new MainAccountSpecification().isSatisfiedBy(account)) {
            mainAccount = account;
        } else {
            if (log.isDebugEnabled()) {
                log.debug("[小场地会员账户]--减扣权益--获取主账户 subAccount: {}, mainAccount: {}", account.getId(), account.getParentAccountId());
            }
            mainAccount = getOrElseThrow(accountRepository.get(merchantId, account.getParentAccountId()));
            accountPredication(account, accountPredication);
        }
        return new MainAccountTuple(account, mainAccount);
    }

    /**
     * 打门店标签及设备类型标签
     * @param record
     */
    private void taggingStoreNameOrEquipmentTypeName(AccountRecord record) {
        boolean flag = false;
        TagUserParamDTO tagUserParamDTO = new TagUserParamDTO();

        //设备类型标签
        if (org.apache.commons.lang.StringUtils.isNotBlank(record.getEquipmentTypeName())) {
            tagUserParamDTO.setEquipmentTypeName(record.getEquipmentTypeName().trim());
            flag = true;
        }
        //场地类型标签
        if (org.apache.commons.lang.StringUtils.isNotBlank(record.getStoreName())) {
            // 只取32位，避免过长保存报错
            String groupName = LyyStringUtil.substring(record.getStoreName().trim(), 32);
            tagUserParamDTO.setStoreName(groupName);
            flag = true;
        }
        if (flag) {
            tagUserParamDTO.setMerchantId(record.getMerchantId());
            tagUserParamDTO.setMerchantUserId(record.getMerchantUserId());
            merchantAutoTagAsyncHandler.autoCreateTag(tagUserParamDTO);
        }
    }

    private Account getOrElseThrow(Optional<Account> optional) {
        return optional.orElseThrow(() -> new BusinessException(AccountErrorCode.ACCOUNT_NOT_FOUND));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transfer(CardTransferDTO dto) {
        AccountTransferTuple accountTuple = getAccountTuple(dto);

        if (!new TransferSpecification().isSatisfiedBy(accountTuple)) {
            log.warn("[小场地会员账户]--转账--转入账户：{}, 转出账户：{}", accountTuple.getInAccount(), accountTuple.getOutAccount());
            throw new BusinessException(AccountErrorCode.TRANSFER_ERROR);
        }

        Account outAccount = accountTuple.getOutAccount();
        Account inAccount = accountTuple.getInAccount();
        Long merchantId = dto.getMerchantId();
        Long userId = dto.getUserId();
        Long operatorId = dto.getOperatorId();

        List<Long> classifyIds = dto.getBenefit().stream()
                .map(CardTransferDTO.BenefitDTO::getMerchantBenefitClassifyId)
                .collect(Collectors.toList());
        Map<Long, List<AccountBenefit>> outAccountBenefitMap = getOutAccountBenefitMap(merchantId, userId, outAccount, classifyIds);
        Map<Long, Map<String, AccountBenefit>> inAccountBenefitMap = getInAccountUnlimitedBenefitMap(merchantId, userId, inAccount, classifyIds);

        Date now = new Date();
        List<AccountRecord> records = Lists.newArrayList();
        Map<Long, String> classifyNameMap = dto.getBenefit()
                .stream()
                .collect(Collectors.toMap(CardTransferDTO.BenefitDTO::getMerchantBenefitClassifyId,
                        CardTransferDTO.BenefitDTO::getMerchantBenefitClassifyName, (v1, v2) -> v1));
        for (CardTransferDTO.BenefitDTO detail : dto.getBenefit()) {
            if (log.isDebugEnabled()) {
                log.debug("[小场地]--转账--待转账权益 detail: {}", detail);
            }
            Long benefitClassifyId = detail.getMerchantBenefitClassifyId();
            List<AccountBenefit> outAccountBenefits = getOutAccountBenefits(outAccountBenefitMap, benefitClassifyId);
            //noinspection OptionalGetWithoutIsPresent
            AccountBenefit firstOutAccountBenefit = outAccountBenefits.stream().findFirst().get();
            List<AccountRecord> list;
            if (couldAggregateBenefit(firstOutAccountBenefit)) {
                Map<String, AccountBenefit> map = inAccountBenefitMap.getOrDefault(benefitClassifyId, Maps.newHashMap());
                list = transferUnlimitedAmountTypeBenefit(outAccountBenefits, map, detail.getNum(), accountTuple, operatorId, now);
            } else {
                list = transferAnotherBenefit(outAccountBenefits, detail, accountTuple, operatorId, now);
            }
            assignRecordValue(dto, now, classifyNameMap, list);
            records.addAll(list);
        }
        batchSaveRecord(records);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancellation(AccountCancellationDTO dto) {
        Long merchantId = dto.getMerchantId();
        Long userId = dto.getUserId();
        Long operatorId = dto.getOperatorId();

        List<AccountRecord> records = Lists.newArrayList();
        Date now = new Date();
        AccountCancellationSpecification cancellationSpecification = new AccountCancellationSpecification(accountRepository);
        List<Account> accounts = accountRepository.selectAccount(merchantId, userId, dto.getCardNos(), null)
                .stream()
                .sorted(Comparator.comparing(Account::getParentAccountId, Comparator.nullsLast(Long::compareTo)))
                .collect(Collectors.toList());
        boolean chooseNewDefault = false;
        for (Account account : accounts) {
            if (!cancellationSpecification.isSatisfiedBy(account)) {
                throw new BusinessException(ACCOUNT_CANCELLATION_ERROR);
            }
            Long accountId = account.getId();
            if (new MainAccountSpecification().isSatisfiedBy(account)) {
                subtractStatisticsBalance(dto, account, now, records);
                accountRepository.deleteAccountBenefit(merchantId, accountId);
            }
            Boolean defaultFlag = null;
            if (Objects.nonNull(account.getDefaultFlag()) && account.getDefaultFlag()) {
                if (log.isInfoEnabled()) {
                    log.debug("[小场地]--注销会员卡--当前账户为默认卡 accountId :{}, cardNo: {}", accountId, account.getCardNo());
                }
                chooseNewDefault = true;
                defaultFlag = false;
            }
            accountRepository.updateAccountStatusAndDefaultFlag(merchantId, accountId, AccountStatusEnum.CANCELLATION,
                    defaultFlag, now, operatorId);
        }
        if (chooseNewDefault) {
            log.debug("[小场地]--注销会员卡--选择最近一张会员卡作为默认卡");
            accountRepository.updateRecentlyAccountAsDefaultFlag(merchantId, userId, operatorId);
        }
        batchSaveRecord(records);
    }

    /**
     * 更新用户的权益统计
     */
    private void subtractStatisticsBalance(AccountCancellationDTO dto, Account account, Date now, List<AccountRecord> records) {
        Long merchantId = account.getMerchantId();
        Long id = account.getId();
        List<AccountBenefit> benefits = accountRepository.selectBenefit(merchantId, id, AccountBenefitStatusEnum.NORMAL.getStatus());
        Map<Long, List<AccountBenefit>> benefitMap = benefits.stream()
                .collect(Collectors.groupingBy(AccountBenefit::getMerchantBenefitClassifyId));
        Map<Long, BigDecimal> sumGroupMap = getBalanceSumGroupMap(benefits, AccountBenefit::getMerchantBenefitClassifyId);

        List<Long> classifyIds = benefits.stream()
                .map(AccountBenefit::getMerchantBenefitClassifyId)
                .distinct()
                .collect(Collectors.toList());
        accountRepository.selectStatistics(merchantId, dto.getUserId(), classifyIds)
                .forEach(statistics -> {
                    SmallVenueStoredStatistics entity = generateAccountStatistics4Cancellation(benefitMap, sumGroupMap, statistics);
                    if (Objects.nonNull(entity)) {
                        entity.setUpdated(now);
                        entity.setUpdatedby(dto.getOperatorId());
                        accountRepository.updateAccountStatistics(merchantId, entity);
                    }
                });

        // 计算频次权益的批次数量作为张数
        Map<Long, Long> frequencyValueTypeCountMap = benefits
                .stream()
                .filter(b -> Objects.equals(b.getValueType(), AccountBenefitNumTypeEnum.FREQUENCY.getCode()))
                .collect(Collectors.groupingBy(AccountBenefit::getMerchantBenefitClassifyId, Collectors.counting()));

        sumGroupMap.entrySet().forEach(entry -> {
            AccountRecord record = assembleAccountCancellationRecord(dto, account, entry, frequencyValueTypeCountMap, now);
            records.add(record);
        });
    }

    private SmallVenueStoredStatistics generateAccountStatistics4Cancellation(Map<Long, List<AccountBenefit>> benefitMap,
                                                                              Map<Long, BigDecimal> sumGroupMap,
                                                                              SmallVenueStoredStatistics statistics) {

        List<AccountBenefit> accountBenefits = benefitMap.get(statistics.getMerchantBenefitClassifyId())
                .stream()
                .filter(e -> ObjectUtils.compare(e.getBalance(), ZERO) > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accountBenefits)) {
            return null;
        }
        SmallVenueStoredStatistics entity = new SmallVenueStoredStatistics();
        entity.setId(statistics.getId());
        Integer valueType = accountBenefits.get(0).getValueType();
        BigDecimal sum = sumGroupMap.get(statistics.getMerchantBenefitClassifyId());
        if (Objects.equals(valueType, AccountBenefitNumTypeEnum.FREQUENCY.getCode())) {
            entity.setRemainNum(statistics.getRemainNum() - accountBenefits.size());
            entity.setBalance(MathUtils.subtract(statistics.getBalance(), sum));
        } else {
            entity.setBalance(statistics.getBalance().subtract(sum));
        }
        return entity;
    }

    @Override
    public List<UserAccountBenefit> listBenefit(Long merchantId, String cardNo, Integer status) {
        Account account = getOrElseThrow(accountRepository.get(merchantId, cardNo));
        if (!new AccountValidSpecification().isSatisfiedBy(account)) {
            log.warn("[小场地会员账户]--当前账户状态：{}", account.getStatus());
            throw new BusinessException(ACCOUNT_STATUS_ERROR);
        }
        List<AccountBenefit> benefits = accountRepository.selectBenefit(merchantId, account.getId(), status);
        return mapStruct.AccountBenefit2UserAccountBenefit(benefits);
    }


    /**
     * 根据单号获取消费记录和消费记录中对应的权益明细
     *
     * @param queryDTO 查询参数
     */
    @Override
    public BenefitRefundResultDTO findAccountRecordsAndAccountBenefits(BenefitRefundQueryDTO queryDTO) {
        //获取订单的储值变更记录
        BenefitRefundResultDTO resultDTO = null;
        //默认查一个月
        Date startTime = Objects.nonNull(queryDTO.getOrderTime())
            ? DateTimeUtils.localDateTime2Date(DateTimeUtils.getDayStart(queryDTO.getOrderTime()))
            : WxAuthUpdateUtils.getLocalDateTimeModifyMonthsDate(LocalDateTime.now(), -1);
        Date endTime = Objects.isNull(queryDTO.getOrderTime()) ? null
            : DateTimeUtils.localDateTime2Date(DateTimeUtils.getDayStart(queryDTO.getOrderTime().plusDays(orderRecordTimeOffset + 1)));
        List<AccountRecord> list = accountRepository.findRecordByOrderNo(queryDTO.getUserId(), queryDTO.getMerchantId(),
                queryDTO.getOrderNo(), queryDTO.getSubOrderNo(),
                startTime, endTime);
        if (CollectionUtils.isNotEmpty(list)) {
            resultDTO = new BenefitRefundResultDTO();
            Set<Long> merchantBenefits = Sets.newHashSet();
            List<UserAccountRecord> accountRecords = list.stream()
                    .map(accountRecord -> {
                        UserAccountRecord userAccountRecord = mapStruct.AccountRecord2UserAccountRecord(accountRecord);
                        merchantBenefits.add(accountRecord.getMerchantBenefitClassifyId());
                        return userAccountRecord;
                    })
                    .collect(Collectors.toList());
            resultDTO.setAccountRecords(accountRecords);
            if (CollectionUtils.isNotEmpty(merchantBenefits)) {
                //获取对应消耗权益的权益明细
                AvailableBenefitQueryDTO benefitQueryDTO = new AvailableBenefitQueryDTO();
                benefitQueryDTO.setUserId(queryDTO.getUserId());
                benefitQueryDTO.setMerchantId(queryDTO.getMerchantId());
                benefitQueryDTO.setMerchantBenefitIds(new ArrayList<>(merchantBenefits));
                benefitQueryDTO.setClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
                List<AccountBenefit> accountBenefits = accountRepository.findUserAllBenefit(benefitQueryDTO);
                if (CollectionUtils.isNotEmpty(accountBenefits)) {
                    //把已过期的进行过滤
                    List<UserAccountBenefit> userAccountBenefits = accountBenefits.stream()
                            .filter(accountBenefit -> accountBenefit.getBalance().compareTo(BigDecimal.ZERO) > 0)
                            .filter(accountBenefit -> {
                                if (Objects.equals(accountBenefit.getExpiryDateCategory(), ExpiryDateCategoryEnum.TIME_INTERVAL.getValue())) {
                                    LocalDateTime end = LocalDateTime.parse(ofNullable(accountBenefit.getDownTime()).orElse("9999-01-01 00:00:00"),
                                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                                    return end.isAfter(LocalDateTime.now());
                                }
                                return true;
                            })
                            .map(accountBenefit -> mapStruct.AccountBenefit2UserAccountBenefit(accountBenefit))
                            .collect(Collectors.toList());
                    resultDTO.setAccountBenefits(userAccountBenefits);
                }
            }
        }
        return resultDTO;
    }

    @Override
    public AccountRecordBenefitSummaryDTO benefitsConsumptionSummary(BenefitConsumptionSummaryQueryDTO query) {
        String orderNo = query.getOrderNo();
        Long merchantId = query.getMerchantId();
        Long userId = query.getUserId();
        List<Long> presentationBenefitClassifyIds = query.getPresentationBenefitClassifyIds();
        boolean isRefund = query.isRefund();

        List<AccountRecord> records = accountRepository.listRecordWithBenefit(orderNo, merchantId, userId, isRefund, query.getDate());

        if (CollectionUtils.isEmpty(records)) {
            return new AccountRecordBenefitSummaryDTO();
        }

        List<BenefitRechargeSummaryDTO> rechargeSummary = calcRecharge(records, isRefund);
        List<BenefitConsumptionSummaryDTO> consumptionSummary = calcConsumption(records, presentationBenefitClassifyIds, isRefund);

        //填充最后消耗权益到期时间
        List<Long> lastConsumptionAccountBenefitIds = consumptionSummary.stream()
            .map(BenefitConsumptionSummaryDTO::getLastConsumptionAccountBenefitId)
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lastConsumptionAccountBenefitIds)) {
            Map<Long, String> accountBenefitDownTimeMap = accountRepository.selectBenefit(merchantId, lastConsumptionAccountBenefitIds)
                .stream()
                .filter(accountBenefit -> Objects.nonNull(accountBenefit.getDownTime()))
                .collect(Collectors.toMap(AccountBenefit::getId, AccountBenefit::getDownTime));
            consumptionSummary.stream()
                .filter(benefitConsumptionSummary -> Objects.nonNull(benefitConsumptionSummary.getLastConsumptionAccountBenefitId()))
                .forEach(benefitConsumptionSummary ->
                    ofNullable(accountBenefitDownTimeMap.get(benefitConsumptionSummary.getLastConsumptionAccountBenefitId()))
                        .ifPresent(benefitConsumptionSummary::setLastConsumptionAccountBenefitDownTime));
        }

        return new AccountRecordBenefitSummaryDTO(consumptionSummary, rechargeSummary);
    }

    @Override
    public void adjustDefaultAccount(AccountDefaultStatusUpdateDTO dto) {
        Long merchantId = dto.getMerchantId();
        Long userId = dto.getUserId();

        merchantUserRepository.selectBy(merchantId, userId)
                .orElseThrow(() -> new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR));

        AccountTuple accountTuple = getAccountTuple(merchantId, userId, dto.getCarNo());
        Account newDefaultAccount = getOrElseThrow(accountTuple.getCardAccount());
        Account oldDefaultAccount = getOrElseThrow(accountTuple.getDefaultAccount());

        if (Objects.equals(oldDefaultAccount.getId(), newDefaultAccount.getId())) {
            return;
        }

        if (!new MainAccountSpecification().isSatisfiedBy(newDefaultAccount)) {
            log.warn("[小场地]--子账号无法设置为默认卡账户, cardNo: {}, account: {}", dto.getCarNo(), newDefaultAccount);
            throw new BusinessException(ACCOUNT_DEFAULT_FLAG_ADJUST_ERROR);
        }

        Long updateBy = ofNullable(dto.getOperator()).orElse(userId);
        accountRepository.updateAccountDefaultFlag(merchantId, newDefaultAccount.getId(), updateBy, true);
        accountRepository.updateAccountDefaultFlag(merchantId, oldDefaultAccount.getId(), updateBy, false);

    }

    /**
     * 商家给用户批量增加权益
     *
     * @param dto
     */
    @Override
    public void merchantBatchIncrementAccountBenefit(MerchantBenefitIncrementDTO dto) {
        long expire = 10 * 60 * 1000;
        boolean allFlag = dto.getAllFlag() != null && dto.getAllFlag();
        if (!allFlag) {
            int num = dto.getUserIds().size();
            expire = num * 150;
        }
        //获取锁
        String lockKey = RedisKey.MERCHANT_PAYOUT_BENEFIT_LOCK + dto.getMerchantId();
        if (redisLock.lock(lockKey, "Y", expire , 30) == null) {
            log.warn("获取商户权益派发锁失败 -> {}", lockKey);
            throw new BusinessException(AccountErrorCode.MERCHANT_PAYOUT_BENEFIT_PROCESSING_ERROR);
        }
        if (allFlag) {
            dto.setUserIds(null);
            String numericKeyword = "";
            String strKeyWord = "";
            if (StringUtils.isBlank(dto.getKeyword())) {
                dto.setKeyword(null);
            } else {
                if (StringUtils.isNumeric(dto.getKeyword())) {
                    numericKeyword = dto.getKeyword();
                } else {
                    strKeyWord = dto.getKeyword();
                }
            }
            boolean hasTag = false;
            dto.setGroupTagIdList(Optional.ofNullable(dto.getGroupTagIdList()).orElse(Collections.emptyList()));
            dto.setEquipmentTypeTagIdList(Optional.ofNullable(dto.getEquipmentTypeTagIdList()).orElse(Collections.emptyList()));
            dto.setOtherTagIdList(Optional.ofNullable(dto.getOtherTagIdList()).orElse(Collections.emptyList()));
            dto.setSexTagIdList(Optional.ofNullable(dto.getSexTagIdList()).orElse(Collections.emptyList()));

            Long[] groupTagIds =  dto.getGroupTagIdList().toArray(new Long[dto.getGroupTagIdList().size()]);
            Long[] equipmentTypeTagIds = dto.getEquipmentTypeTagIdList().toArray(new Long[dto.getEquipmentTypeTagIdList().size()]);
            Long[] otherTagIds =  dto.getOtherTagIdList().toArray(new Long[dto.getOtherTagIdList().size()]);
            Long[] sexTagIds = dto.getSexTagIdList().toArray(new Long[dto.getSexTagIdList().size()]);
            if(groupTagIds.length > 0){
                hasTag = true;
            }else if(equipmentTypeTagIds.length > 0){
                hasTag = true;
            }else if(otherTagIds.length > 0){
                hasTag = true;
            }else if(sexTagIds.length > 0){
                hasTag = true;
            }

            boolean hasMemberLevel = CollectionUtils.isNotEmpty(ofNullable(dto.getSpecificFilterInfoDTO())
                    .map(SmallVenueMobileUserListSelectDTO.SpecificFilterInfoDTO::getMemberLevelIdList).orElse(Collections.emptyList()));
            boolean hasMerchantBenefitClassifyId = CollectionUtils.isNotEmpty(dto.getMerchantBenefitClassifyDTOList());
            List<Long> useIdList = merchantUserRepository.findSmallVenueMobileUserIds(dto, hasTag, groupTagIds,equipmentTypeTagIds,otherTagIds,sexTagIds, hasMemberLevel,
                    hasMerchantBenefitClassifyId, numericKeyword.trim(), strKeyWord.trim());
            if (CollectionUtils.isNotEmpty(useIdList)) {
                dto.setUserIds(useIdList);
            }
        }
        if (CollectionUtils.isEmpty(dto.getUserIds())) {
            log.warn("商户批量派发权益派发的用户数据为空,传参为:{}", dto);
            redisLock.unlock(RedisKey.MERCHANT_PAYOUT_BENEFIT_LOCK + dto.getMerchantId(), "Y");
        } else {
            asyncVenueAccountHandler.asyncMerchantPayoutBusiness(dto);
        }
    }


    /**
     * 初始化多金宝账户
     *
     * @param merchantId     商户id
     * @param storeId        场地id
     * @param userId         用户id
     * @param merchantUserId 商户用户id
     * @return
     */
    @Override
    public Long initVenueAccount(Long merchantId, Long userId, Long merchantUserId, Long storeId) {
        MerchantUser merchantUser;
        if (merchantUserId == null) {
            merchantUser = ofNullable(merchantUserRepository.getByUserIdAndMerchantId(userId, merchantId))
                    .orElseThrow(() -> new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR));
        } else {
            merchantUser = ofNullable(merchantUserRepository.getByIdAndMerchantId(merchantUserId, merchantId))
                    .orElseThrow(() -> new BusinessException(UserErrorCode.MERCHANT_USER_NOT_EXIST_ERROR));
        }

        //初始化默认账户
        AccountInitDTO accountInitDTO = new AccountInitDTO();
        accountInitDTO.setMerchantUserId(merchantUser.getId());
        accountInitDTO.setUserId(merchantUser.getUserId());
        accountInitDTO.setMerchantId(merchantId);
        accountInitDTO.setDefaultFlag(true);
        accountInitDTO.setOperatorId(merchantUser.getUserId());
        accountInitDTO.setClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        accountInitDTO.setStoreId(storeId);
        AccountInitResultDTO accountInitResultDTO = accountRepository.initAccount(accountInitDTO);
        return accountInitResultDTO.getAccount().getId();
    }

    @Override
    public void mergeUserMemberAccount(SmallVenueMergeAccountInfoDTO smallVenueMergeAccountInfoDTO) {

        log.info("多金宝合并会员账户 smallVenueMergeAccountInfoDTO: {}", smallVenueMergeAccountInfoDTO);
        AvailableBenefitQueryDTO queryDTO = new AvailableBenefitQueryDTO();
        queryDTO.setMerchantId(smallVenueMergeAccountInfoDTO.getMerchantId());
        queryDTO.setUserId(smallVenueMergeAccountInfoDTO.getMergedUserId());
        queryDTO.setClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        List<AccountBenefit> userAllBenefits = accountRepository.findUserAllBenefit(queryDTO);

        int row = 0;
        /**
         * A 没有手机号码的会员
         * B 有手机号码的会员
         * A -> B
         */
        if (StringUtils.isBlank(smallVenueMergeAccountInfoDTO.getMergedDefaultCardNo())) {
            // 有储值，没有会员卡
            if (smallVenueMergeAccountInfoDTO.getNewDefaultAccountId() == null) {
                // A 有储值、没有会员卡  B 没有储值和会员卡
                row = accountRepository.updateAccount(smallVenueMergeAccountInfoDTO.getMerchantId(),
                        smallVenueMergeAccountInfoDTO.getMergedUserId(),
                        smallVenueMergeAccountInfoDTO.getMergedMerchantUserId(),
                        smallVenueMergeAccountInfoDTO.getNewUserId(),
                        smallVenueMergeAccountInfoDTO.getNewMerchantUserId(),
                        null);

                row = accountRepository.updateAccountBebefitIdInfo(smallVenueMergeAccountInfoDTO.getMerchantId(), smallVenueMergeAccountInfoDTO.getMergedUserId(),
                        smallVenueMergeAccountInfoDTO.getMergedMerchantUserId(), smallVenueMergeAccountInfoDTO.getNewUserId(),
                        smallVenueMergeAccountInfoDTO.getNewMerchantUserId(), null);
            } else if (smallVenueMergeAccountInfoDTO.getNewDefaultAccountId() != null
                    && StringUtils.isBlank(smallVenueMergeAccountInfoDTO.getNewDefaultCardNo())) {
                // A 有储值、没有会员卡  B 有储值没有会员卡
                row = accountRepository.updateAccountBebefitIdInfo(smallVenueMergeAccountInfoDTO.getMerchantId(), smallVenueMergeAccountInfoDTO.getMergedUserId(),
                        smallVenueMergeAccountInfoDTO.getMergedMerchantUserId(), smallVenueMergeAccountInfoDTO.getNewUserId(),
                        smallVenueMergeAccountInfoDTO.getNewMerchantUserId(), smallVenueMergeAccountInfoDTO.getNewDefaultAccountId());
            } else if (smallVenueMergeAccountInfoDTO.getNewDefaultAccountId() != null
                    && StringUtils.isNotBlank(smallVenueMergeAccountInfoDTO.getNewDefaultCardNo())) {
                // A 有储值、没有会员卡  B 有储值和有会员卡
                row = accountRepository.updateAccountBebefitIdInfo(smallVenueMergeAccountInfoDTO.getMerchantId(), smallVenueMergeAccountInfoDTO.getMergedUserId(),
                        smallVenueMergeAccountInfoDTO.getMergedMerchantUserId(), smallVenueMergeAccountInfoDTO.getNewUserId(),
                        smallVenueMergeAccountInfoDTO.getNewMerchantUserId(), smallVenueMergeAccountInfoDTO.getNewDefaultAccountId());
            }
        } else {
            if (smallVenueMergeAccountInfoDTO.getNewDefaultAccountId() == null) {
                // A 有储值有会员卡  B 没有储值没有会员卡
                row = accountRepository.updateAccount(smallVenueMergeAccountInfoDTO.getMerchantId(),
                        smallVenueMergeAccountInfoDTO.getMergedUserId(),
                        smallVenueMergeAccountInfoDTO.getMergedMerchantUserId(),
                        smallVenueMergeAccountInfoDTO.getNewUserId(),
                        smallVenueMergeAccountInfoDTO.getNewMerchantUserId(),
                        null);

                row = accountRepository.updateAccountBebefitIdInfo(smallVenueMergeAccountInfoDTO.getMerchantId(), smallVenueMergeAccountInfoDTO.getMergedUserId(),
                        smallVenueMergeAccountInfoDTO.getMergedMerchantUserId(), smallVenueMergeAccountInfoDTO.getNewUserId(),
                        smallVenueMergeAccountInfoDTO.getNewMerchantUserId(), null);
            } else if (smallVenueMergeAccountInfoDTO.getNewDefaultAccountId() != null
                    && StringUtils.isBlank(smallVenueMergeAccountInfoDTO.getNewDefaultCardNo())) {
                // A 有储值有会员卡（多张会员卡）  B 有储值没有会员卡

                // 把 B account 删了，把 account_benefit 的 accountId 更新为 smallVenueMergeAccountInfoDTO.getMergedDefaultAccountId()
                row = accountRepository.updateAccount(smallVenueMergeAccountInfoDTO.getMerchantId(), smallVenueMergeAccountInfoDTO.getNewUserId(),
                        smallVenueMergeAccountInfoDTO.getNewMerchantUserId(), 0L, 0L, null);

                row = accountRepository.updateAccountBebefitIdInfo(smallVenueMergeAccountInfoDTO.getMerchantId(), smallVenueMergeAccountInfoDTO.getNewUserId(),
                        smallVenueMergeAccountInfoDTO.getNewMerchantUserId(), smallVenueMergeAccountInfoDTO.getNewUserId(),
                        smallVenueMergeAccountInfoDTO.getNewMerchantUserId(), smallVenueMergeAccountInfoDTO.getMergedDefaultAccountId());

                // 把 A 的所有 account 和 account_benefit 更新为 B
                row = accountRepository.updateAccount(smallVenueMergeAccountInfoDTO.getMerchantId(),
                        smallVenueMergeAccountInfoDTO.getMergedUserId(),
                        smallVenueMergeAccountInfoDTO.getMergedMerchantUserId(),
                        smallVenueMergeAccountInfoDTO.getNewUserId(),
                        smallVenueMergeAccountInfoDTO.getNewMerchantUserId(),
                        null);

                row = accountRepository.updateAccountBebefitIdInfo(smallVenueMergeAccountInfoDTO.getMerchantId(), smallVenueMergeAccountInfoDTO.getMergedUserId(),
                        smallVenueMergeAccountInfoDTO.getMergedMerchantUserId(), smallVenueMergeAccountInfoDTO.getNewUserId(),
                        smallVenueMergeAccountInfoDTO.getNewMerchantUserId(), null);
            } else if (smallVenueMergeAccountInfoDTO.getNewDefaultAccountId() != null
                    && StringUtils.isNotBlank(smallVenueMergeAccountInfoDTO.getNewDefaultCardNo())) {
                // A 有储值有会员卡  B 有储值有会员卡
                row = accountRepository.updateAccount(smallVenueMergeAccountInfoDTO.getMerchantId(),
                        smallVenueMergeAccountInfoDTO.getMergedUserId(),
                        smallVenueMergeAccountInfoDTO.getMergedMerchantUserId(),
                        smallVenueMergeAccountInfoDTO.getNewUserId(),
                        smallVenueMergeAccountInfoDTO.getNewMerchantUserId(),
                        null);

                row = accountRepository.updateAccountBebefitIdInfo(smallVenueMergeAccountInfoDTO.getMerchantId(), smallVenueMergeAccountInfoDTO.getMergedUserId(),
                        smallVenueMergeAccountInfoDTO.getMergedMerchantUserId(), smallVenueMergeAccountInfoDTO.getNewUserId(),
                        smallVenueMergeAccountInfoDTO.getNewMerchantUserId(), null);
            }
        }

        if (row <= 0) {
            log.warn("会员账户更新失败");
            throw new BusinessException(UserErrorCode.MERCHANT_USER_UPDATE_PARAM_ERROR);
        }

        log.info("[小场地会员账户]--账户合并，插入储值流水转入转出记录");
        mergeUserMemberBenefit(smallVenueMergeAccountInfoDTO, userAllBenefits);
        log.info("[小场地会员账户]--账户合并，合并统计的用户信息");
        mergeUserMemberStatistics(smallVenueMergeAccountInfoDTO.getMerchantId(), smallVenueMergeAccountInfoDTO.getNewMerchantUserId(),
                smallVenueMergeAccountInfoDTO.getNewUserId(), smallVenueMergeAccountInfoDTO.getMergedUserId(), smallVenueMergeAccountInfoDTO.getMergedMerchantUserId());
    }
    @Override
    public List<SmallVenueAccountBenefitTransferDTO> selectTotalTransferAccountBenefit(SmallVenueAccountBenefitTransferQueryDTO smallVenueAccountBenefitTransferQueryDTO) {
        return accountRepository.selectTotalTransferAccountBenefit(smallVenueAccountBenefitTransferQueryDTO);
    }

    @Override
    public void smallVenueClearBalanceAndCoin(SmallVenueAccountBenefitTransferQueryDTO smallVenueAccountBenefitTransferQueryDTO) {
        log.info("[小场地会员账户]--清除娱乐会员场地储值, smallVenueAccountBenefitTransferQueryDTO: {}", smallVenueAccountBenefitTransferQueryDTO);
        List<SmallVenueAccountBenefitTransferDTO> accountBenefits = accountRepository.selectTransferAccountBenefit(smallVenueAccountBenefitTransferQueryDTO);
        if (CollectionUtils.isNotEmpty(accountBenefits)) {
            List<AccountBenefitAdjustDTO> adjustDTOS = accountBenefits.stream()
                    .filter(ab -> ab.getBalance().compareTo(BigDecimal.ZERO) > 0)
                    .map(ab -> {
                        AccountBenefitAdjustDTO adjustDTO = new AccountBenefitAdjustDTO();
                        adjustDTO.setId(ab.getId());
                        adjustDTO.setMerchantId(smallVenueAccountBenefitTransferQueryDTO.getMerchantId());
                        adjustDTO.setMerchantUserId(smallVenueAccountBenefitTransferQueryDTO.getMerchantUserId());
                        adjustDTO.setClassify(ab.getClassify());
                        adjustDTO.setAmount(ab.getBalance());
                        adjustDTO.setDescription(smallVenueAccountBenefitTransferQueryDTO.getDesc());
                        adjustDTO.setAdjustType(AdjustTypeEnum.DECREMENT);
                        adjustDTO.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_All.getCode());
                        return adjustDTO;
                    }).collect(Collectors.toList());

            accountService.decreaseAccountBenefit(adjustDTOS, true, null);
        }
    }

    @Override
    public void updateVenueAccount(Account account) {
        accountRepository.updateVenueAccountById(account);
    }

    @Override
    public Boolean updateExpireBatch(List<AccountBenefitUpdateDTO> accountBenefitUpdateDTOList) {
        if(CollectionUtils.isEmpty(accountBenefitUpdateDTOList)) {
            return false;
        }
        Date now = new Date();
        for(AccountBenefitUpdateDTO accountBenefitUpdateDTO: accountBenefitUpdateDTOList) {
            if(Objects.isNull(accountBenefitUpdateDTO) || Objects.isNull(accountBenefitUpdateDTO.getMerchantId())
                    || Objects.isNull(accountBenefitUpdateDTO.getAccountBenefitId())) {
                log.warn("待更新的权益数据为Null，accountBenefitUpdateDTO={}", accountBenefitUpdateDTO);
                continue;
            }
            AccountBenefit accountBenefit = new AccountBenefit();
            accountBenefit.setId(accountBenefitUpdateDTO.getAccountBenefitId());
            accountBenefit.setUpdateTime(now);
            accountBenefit.setDownTime(accountBenefitUpdateDTO.getDownTime());
            accountRepository.updateAccountBenefit(accountBenefitUpdateDTO.getMerchantId(), accountBenefit);
        }
        return true;
    }

    /******                私有方法             ****/

    private void mergeUserMemberBenefit(SmallVenueMergeAccountInfoDTO smallVenueMergeAccountInfoDTO, List<AccountBenefit> userAllBenefits) {
        List<MergeCardBenefit> mergeCardNoList = smallVenueMergeAccountInfoDTO.getMergeCardNoList();
        if (CollectionUtils.isNotEmpty(mergeCardNoList)) {
            String desc = "账户合并。会员(" + smallVenueMergeAccountInfoDTO.getMergedUserId() + ")下的储值转出到会员(" + smallVenueMergeAccountInfoDTO.getNewUserId() + ")下";
            for (MergeCardBenefit mergeCardBenefit : mergeCardNoList) {
                AccountBenefit accountBenefit = new AccountBenefit();
                accountBenefit.setUserId(smallVenueMergeAccountInfoDTO.getMergedUserId());
                accountBenefit.setMerchantUserId(smallVenueMergeAccountInfoDTO.getMergedMerchantUserId());
                accountBenefit.setAccountId(mergeCardBenefit.getAccountId());
                accountBenefit.setMerchantBenefitClassifyId(mergeCardBenefit.getMerchantBenefitClassifyId());
                accountBenefit.setBalance(mergeCardBenefit.getBalance());
                AccountRecord outAccountRecord = assembleTransferRecord(accountBenefit, accountBenefit.getBalance(), null, AdjustTypeEnum.DECREMENT, AccountRecordTypeEnum.SV_TRANSFER_OUT);
                outAccountRecord.setUserId(smallVenueMergeAccountInfoDTO.getNewUserId());
                outAccountRecord.setMerchantId(smallVenueMergeAccountInfoDTO.getMerchantId());
                outAccountRecord.setCreateTime(new Date());
                outAccountRecord.setStoreId(smallVenueMergeAccountInfoDTO.getStoreId());
                outAccountRecord.setStoreName(smallVenueMergeAccountInfoDTO.getStoreName());
                outAccountRecord.setConsumeType(smallVenueMergeAccountInfoDTO.getConsumeType());
                outAccountRecord.setEquipmentValue(smallVenueMergeAccountInfoDTO.getEquipmentValue());
                outAccountRecord.setEquipmentName(smallVenueMergeAccountInfoDTO.getEquipmentName());
                outAccountRecord.setTerminalName(smallVenueMergeAccountInfoDTO.getEquipmentName());
                outAccountRecord.setCreatedby(smallVenueMergeAccountInfoDTO.getCreatedBy());
                outAccountRecord.setCreateName(smallVenueMergeAccountInfoDTO.getCreateName());
                outAccountRecord.setDescription(desc);
                outAccountRecord.setCardNo(mergeCardBenefit.getCardNo());
                outAccountRecord.setMerchantBenefitClassifyName(mergeCardBenefit.getMerchantBenefitClassifyName());
                accountRecordRepository.insert(outAccountRecord);

                accountBenefit.setUserId(smallVenueMergeAccountInfoDTO.getNewUserId());
                accountBenefit.setMerchantUserId(smallVenueMergeAccountInfoDTO.getNewMerchantUserId());
                AccountRecord inAccountRecord = assembleTransferRecord(accountBenefit, accountBenefit.getBalance(), ZERO, AdjustTypeEnum.INCREMENT, AccountRecordTypeEnum.SV_TRANSFER_IN);
                inAccountRecord.setUserId(smallVenueMergeAccountInfoDTO.getNewUserId());
                inAccountRecord.setMerchantId(smallVenueMergeAccountInfoDTO.getMerchantId());
                inAccountRecord.setCreateTime(new Date());
                inAccountRecord.setStoreId(smallVenueMergeAccountInfoDTO.getStoreId());
                inAccountRecord.setStoreName(smallVenueMergeAccountInfoDTO.getStoreName());
                inAccountRecord.setConsumeType(smallVenueMergeAccountInfoDTO.getConsumeType());
                inAccountRecord.setEquipmentValue(smallVenueMergeAccountInfoDTO.getEquipmentValue());
                inAccountRecord.setEquipmentName(smallVenueMergeAccountInfoDTO.getEquipmentName());
                inAccountRecord.setTerminalName(smallVenueMergeAccountInfoDTO.getEquipmentName());
                inAccountRecord.setCreatedby(smallVenueMergeAccountInfoDTO.getCreatedBy());
                inAccountRecord.setCreateName(smallVenueMergeAccountInfoDTO.getCreateName());
                inAccountRecord.setDescription(desc);
                if (StringUtils.isBlank(smallVenueMergeAccountInfoDTO.getMergedDefaultCardNo())) {
                    inAccountRecord.setCardNo(smallVenueMergeAccountInfoDTO.getNewDefaultCardNo());
                } else {
                    inAccountRecord.setCardNo(mergeCardBenefit.getCardNo());
                }
                inAccountRecord.setMerchantBenefitClassifyName(mergeCardBenefit.getMerchantBenefitClassifyName());
                accountRecordRepository.insert(inAccountRecord);
            }
        }
    }

    /**
     * 合并统计的用户信息
     * @param merchantId
     * @param merchantUserId
     * @param userId
     * @param oldUserId
     * @param oldMerchantUserId
     */
    private void mergeUserMemberStatistics(Long merchantId, Long merchantUserId, Long userId, Long oldUserId, Long oldMerchantUserId) {
        log.info("合并统计的用户信息:mergeUserMemberStatistics参数,merchantId:{},merchantUserId:{},userId:{},oldUserId:{},oldMerchantUserId:{}",
                merchantId, merchantUserId, userId, oldUserId, oldMerchantUserId);
        // 被合并会员已有的储值记录
        List<SmallVenueStoredStatistics> oldSmallVenueStoredStatistics = smallVenueStoredStatisticsMapper.selectStoredStatisticsByUserId(oldUserId, oldMerchantUserId, merchantId);
        if (CollUtil.isEmpty(oldSmallVenueStoredStatistics)) {
            // 被合并会员没有储值，直接返回
            return;
        }
        List<SmallVenueStoredStatistics> smallVenueStoredStatistics = smallVenueStoredStatisticsMapper.selectStoredStatisticsByUserId(userId, merchantUserId, merchantId);
        if (CollUtil.isEmpty(smallVenueStoredStatistics)) {
            // 会员没有储值记录，直接把被合并会员的储值记录更新到会员账户下
            smallVenueStoredStatisticsMapper.updateStatisticsIdInfo(userId, merchantUserId, merchantId, oldUserId, oldMerchantUserId);
        } else {
            // 会员已有的储值记录
            List<Long> classifyIdList = smallVenueStoredStatistics.stream().map(SmallVenueStoredStatistics::getMerchantBenefitClassifyId).collect(Collectors.toList());
            // 取交集, 被合并会员和会员都有的储值记录
            List<SmallVenueStoredStatistics> existedClassifyList = oldSmallVenueStoredStatistics.stream().filter(o -> classifyIdList.contains(o.getMerchantBenefitClassifyId())).collect(Collectors.toList());
            // 取差集，被合并会员独有的储值记录
            Set<Long> newClassifyIdList = oldSmallVenueStoredStatistics.stream().filter(o -> !classifyIdList.contains(o.getMerchantBenefitClassifyId()))
                    .map(SmallVenueStoredStatistics::getMerchantBenefitClassifyId).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(newClassifyIdList)) {
                log.info("更新被合并会员新增的储值记录:newClassifyIdList={}", newClassifyIdList);
                smallVenueStoredStatisticsMapper.updateStatisticsIdInfoByMerchantBenefitClassifyIds(userId, merchantUserId, merchantId, oldUserId, oldMerchantUserId, newClassifyIdList);
            }
            if (CollUtil.isNotEmpty(existedClassifyList)) {
                // 合并不记名卡的储值记录到会员的储值记录中
                log.info("更新会员已存在的储值记录:existedClassifyList={}", existedClassifyList);
                // 取负数
                existedClassifyList.forEach(statistics -> {
                    // 累计充值
//                    statistics.setTotal(ZERO);
                    if (statistics.getBalance() != null) {
                        // 累计消耗
                        statistics.setTotalConsume(statistics.getBalance());
                        // 累计剩余
                        statistics.setBalance(statistics.getBalance().negate());
                    }
                });
                existedClassifyList.forEach(o -> smallVenueStoredStatisticsMapper.updateStatisticsIdInfoByMerchantBenefitClassifyId(oldUserId, oldMerchantUserId, merchantId, o.getMerchantBenefitClassifyId(), o));
            }
        }
    }

    private boolean batchSaveRecord(List<AccountRecord> records) {
        return accountRecordRepository.insertBatch(records);
    }

    private Map<Long, AccountBenefit> getDecrementBenefitMap( List<AccountBenefit> accountBenefits) {
        return accountBenefits
                .stream()
                .collect(Collectors.toMap(AccountBenefit::getId, Function.identity()));
    }

    private List<AccountBenefit> listDecrementBenefits(List<BenefitDecrementDTO.BenefitDetailsDTO> benefitDetails, Long merchantId) {
        List<Long> abIds = benefitDetails.stream()
                .map(BenefitDecrementDTO.BenefitDetailsDTO::getAccountBenefitId)
                .collect(Collectors.toList());
        return accountRepository.selectBenefit(merchantId, abIds);
    }

    private Map<Long, SmallVenueStoredStatistics> getStatisticsMap(BenefitDecrementDTO dto) {
        List<Long> classifyIds = dto.getBenefitDetails().stream()
                .map(BenefitDecrementDTO.BenefitDetailsDTO::getMerchantBenefitClassifyId)
                .collect(Collectors.toList());
        return getClassifyStatisticsMap(classifyIds, dto.getMerchantId(), dto.getUserId());
    }

    private Map<Long, SmallVenueStoredStatistics> getStatisticsMap(BenefitIncrementItemDTO dto,
                                                                   Long merchantId,
                                                                   Long userId) {
        List<Long> classifyIds = dto.getBenefitDetails().stream()
                .map(BenefitIncrementItemDTO.BenefitDetailsDTO::getMerchantBenefitClassifyId)
                .collect(Collectors.toList());
        return getClassifyStatisticsMap(classifyIds, merchantId, userId);
    }

    private Map<Long, SmallVenueStoredStatistics> getClassifyStatisticsMap(List<Long> classifyIds, Long merchantId, Long userId) {
        return accountRepository.selectStatistics(merchantId, userId, classifyIds)
                .stream()
                .collect(Collectors.toMap(SmallVenueStoredStatistics::getMerchantBenefitClassifyId, Function.identity()));
    }

    public boolean isIncreaseByRefund(Integer recordType, BenefitIncrementItemDTO dto, List<Long> abIds,Boolean containExpireBenefit) {
        boolean isIncreaseByRefund = REFUND_WITH_ROLLBACK_BENEFIT_LIST.contains(recordType)
                && CollectionUtils.isNotEmpty(abIds);
        // 如果是退单增加，非过期储值则 detail 必须都有 accountBenefitId
        if (isIncreaseByRefund && abIds.size() != dto.getBenefitDetails().size() && !containExpireBenefit) {
            log.warn("[小场地会员账户]--退单增加权益失败，缺少参数 benefitIdSize: {} itemSize: {}", abIds.size(), dto.getBenefitDetails().size());
            throw new BusinessException(BENEFIT_PARAM_ERROR.getCode(), "账户权益ID不存在");
        }
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--增加权益  isIncreaseByRefund: {}", isIncreaseByRefund);
        }
        return isIncreaseByRefund;
    }

    public boolean isIncreaseByRefund(Integer recordType, BenefitIncrementItemDTO dto, List<Long> abIds) {
        return isIncreaseByRefund(recordType,dto,abIds,false);
    }

    private void accountPredication(Account account, AccountPredicateSpecification accountPredicateSpecification) {
        if (accountPredicateSpecification.isSatisfiedBy(account)) {
            throw new BusinessException(AccountErrorCode.ACCOUNT_STATUS_ERROR);
        }
    }

    List<AccountRecord> transferUnlimitedAmountTypeBenefit(List<AccountBenefit> outAccountBenefits,
                                                           Map<String, AccountBenefit> inAccountBenefitMap,
                                                           BigDecimal transferNum, AccountTransferTuple accountTuple,
                                                           Long operatorId, Date now) {
        if (log.isDebugEnabled()) {
            log.debug("[小场地]--转账--无无限期权益明细转账处理 转出明细列表: {}", outAccountBenefits);
            log.debug("[小场地]--转账--无限期权益明细转账处理 待转账额度 transferNum: {}", transferNum);
        }
        List<AccountRecord> records = Lists.newArrayList();
        for (AccountBenefit outAccountBenefit : outAccountBenefits) {
            if (outAccountBenefit.getBalance().compareTo(transferNum) > -1) {
                BigDecimal remainNum = outAccountBenefit.getBalance().subtract(transferNum);
                if (log.isDebugEnabled()) {
                    log.debug("[小场地]--转账--当前转出权益额度满足扣除，转出账户当前权益剩余额度：{}", remainNum);
                }
                List<AccountRecord> list = doTransferUnlimitedCategoryBenefit(transferNum, remainNum, outAccountBenefit, inAccountBenefitMap, accountTuple, operatorId, now);
                records.addAll(list);
                return records;
            } else {
                transferNum = transferNum.subtract(outAccountBenefit.getBalance());
                if (log.isDebugEnabled()) {
                    log.debug("[小场地]--转账--当前转出权益额度不足, 从下一条明细继续扣除, 剩余待转账额度: {}", transferNum);
                }
                List<AccountRecord> list = doTransferUnlimitedCategoryBenefit(outAccountBenefit.getBalance(), ZERO, outAccountBenefit, inAccountBenefitMap, accountTuple, operatorId, now);
                records.addAll(list);
            }
        }
        return records;
    }

    /**
     * @param transferNum 当前转账额度
     * @param remainNum   转出权益明细剩余额度
     */
    private List<AccountRecord> doTransferUnlimitedCategoryBenefit(BigDecimal transferNum,
                                                                   BigDecimal remainNum,
                                                                   AccountBenefit outAccountBenefit,
                                                                   Map<String, AccountBenefit> inAccountBenefitMap,
                                                                   AccountTransferTuple accountTuple,
                                                                   Long operatorId, Date now) {
        List<AccountRecord> records = Lists.newArrayList();
        updateOutAccountBenefit(remainNum, outAccountBenefit.getId(), outAccountBenefit.getMerchantId(), operatorId, now);
        AccountRecord outRecord = assembleTransferOutRecord(outAccountBenefit, transferNum);
        outRecord.setCardNo(accountTuple.getOutAccount().getCardNo());
        records.add(outRecord);

        AccountBenefit accountBenefit = inAccountBenefitMap.get(compositeKey(outAccountBenefit));
        AccountRecord inRecord;
        if (Objects.nonNull(accountBenefit)) {
            updateInAccountBenefit(transferNum, operatorId, now, outAccountBenefit.getMerchantId(), accountBenefit);
            inRecord = assembleTransferRecord(accountBenefit, transferNum, accountBenefit.getBalance(), AdjustTypeEnum.INCREMENT, AccountRecordTypeEnum.SV_TRANSFER_IN);
        } else {
            AccountBenefit inAccountBenefit = saveInAccountBenefit(outAccountBenefit, transferNum, accountTuple, operatorId, now);
            inRecord = assembleTransferRecord(inAccountBenefit, transferNum, ZERO, AdjustTypeEnum.INCREMENT, AccountRecordTypeEnum.SV_TRANSFER_IN);
        }
        inRecord.setCardNo(accountTuple.getInAccount().getCardNo());
        records.add(inRecord);
        return records;
    }

    List<AccountRecord> transferAnotherBenefit(List<AccountBenefit> outAccountBenefits,
                                               CardTransferDTO.BenefitDTO benefit,
                                               AccountTransferTuple accountTuple,
                                               Long operatorId, Date now) {
        BigDecimal transferNum = benefit.getNum();
        if (log.isDebugEnabled()) {
            log.debug("[小场地]--转账--非无限期权益明细转账处理 转出明细列表: {}", outAccountBenefits);
            log.debug("[小场地]--转账--非无限期权益明细转账处理 待转账额度:{} benefit: {}", transferNum, benefit);
        }
        List<AccountRecord> records = Lists.newArrayList();

        if (Objects.equals(benefit.getNumType(), AccountBenefitNumTypeEnum.FREQUENCY.getCode())) {
            log.debug("频次卡转账");
            AccountBenefit outAccountBenefit = outAccountBenefits
                    .stream()
                    .filter(ab -> Objects.equals(ab.getId(), benefit.getAccountBenefitId()))
                    .findFirst()
                    .orElseThrow(() -> {
                        log.warn("[小场地]--转账--转账失败, 权益明细未找到 accountBenefitId: {}", benefit.getAccountBenefitId());
                        return new BusinessException(BENEFIT_NOT_FOUND);
                    });

            if (ObjectUtils.compare(transferNum, outAccountBenefit.getBalance()) != 0) {
                log.warn("次卡转账异常, 转账次数不相等 out: {}, in: {}", outAccountBenefit.getBalance(), transferNum);
                throw new BusinessException(TRANSFER_ERROR);
            }
            List<AccountRecord> list = anotherBenefitTransfer(transferNum, ZERO, outAccountBenefit, accountTuple, operatorId, now);
            records.addAll(list);
            return records;
        } else {
            log.debug("额度卡转账");
            for (AccountBenefit outAccountBenefit : outAccountBenefits) {
                if (outAccountBenefit.getBalance().compareTo(transferNum) > -1) {
                    BigDecimal remainNum = outAccountBenefit.getBalance().subtract(transferNum);
                    if (log.isDebugEnabled()) {
                        log.debug("[小场地]--转账--当前转出权益额度满足扣除，转出账户当前权益剩余额度：{}", remainNum);
                    }
                    List<AccountRecord> list = anotherBenefitTransfer(transferNum, remainNum, outAccountBenefit, accountTuple, operatorId, now);
                    records.addAll(list);
                    return records;
                } else {
                    transferNum = transferNum.subtract(outAccountBenefit.getBalance());
                    if (log.isDebugEnabled()) {
                        log.debug("[小场地]--转账--当前转出权益额度不足, 从下一条明细继续扣除, 剩余待转账额度: {}", transferNum);
                    }
                    List<AccountRecord> list = anotherBenefitTransfer(outAccountBenefit.getBalance(), ZERO, outAccountBenefit, accountTuple, operatorId, now);
                    records.addAll(list);
                }
            }
        }
        return records;
    }

    /**
     * @param transferNum       转入额度
     * @param remainNum         转出账户权益剩余额度
     * @param outAccountBenefit 转出账户权益明细
     */
    private List<AccountRecord> anotherBenefitTransfer(BigDecimal transferNum, BigDecimal remainNum,
                                                       AccountBenefit outAccountBenefit,
                                                       AccountTransferTuple accountTuple,
                                                       Long operatorId, Date now) {
        List<AccountRecord> records = Lists.newArrayList();
        updateOutAccountBenefit(remainNum, outAccountBenefit.getId(), outAccountBenefit.getMerchantId(), operatorId, now);

        AccountRecord outRecord = assembleTransferOutRecord(outAccountBenefit, transferNum);
        outRecord.setCardNo(accountTuple.getOutAccount().getCardNo());
        records.add(outRecord);

        AccountBenefit inAccountBenefit = saveInAccountBenefit(outAccountBenefit, transferNum, accountTuple, operatorId, now);
        AccountRecord inRecord = assembleTransferRecord(inAccountBenefit, transferNum, ZERO,
                AdjustTypeEnum.INCREMENT, AccountRecordTypeEnum.SV_TRANSFER_IN);
        inRecord.setCardNo(accountTuple.getInAccount().getCardNo());
        records.add(inRecord);
        return records;
    }

    AccountTransferTuple getAccountTuple(CardTransferDTO dto) {
        String outCardNo = dto.getOutCardNo();
        String inCardNo = dto.getInCardNo();
        if (StringUtils.equals(outCardNo, inCardNo)) {
            log.warn("[小场地]--转账失败--转入转出账户相同, outCardNo: {}, inCardNo: {}", outCardNo, inCardNo);
            throw new BusinessException(AccountErrorCode.SAME_CARD_TRANSFER_ERROR);
        }
        List<String> cards = Lists.newArrayList(outCardNo, inCardNo);
        Map<String, Account> accountMap = accountRepository.selectAccount(dto.getMerchantId(), dto.getUserId(), cards)
                .stream()
                .collect(Collectors.toMap(Account::getCardNo, Function.identity()));
        Account out = getOrElseThrow(ofNullable(accountMap.get(outCardNo)));
        Account in = getOrElseThrow(ofNullable(accountMap.get(inCardNo)));
        if (log.isDebugEnabled()) {
            log.debug("[小场地]--转账--转出账户 out: {}", out);
            log.debug("[小场地]--转账--转入账户 in: {}", in);
        }
        return new AccountTransferTuple(out, in);
    }

    private AccountBenefit initAccountBenefit(Account account,
                                              BenefitIncrementDTO dto,
                                              BenefitIncrementItemDTO.BenefitDetailsDTO detail,
                                              Date now) {
        AccountBenefit entity = assembleAccountBenefit(account, dto, detail, now);
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--初始化账户权益, accountBenefit: {}", entity);
        }
        accountRepository.saveAccountBenefit(entity);
        return entity;
    }

    private SmallVenueStoredStatistics initAccountStatistics(Account account, BenefitIncrementDTO dto,
                                                             BenefitIncrementItemDTO.BenefitDetailsDTO detail, Date now) {
        SmallVenueStoredStatistics entity = assembleAccountStatistics(account, dto, detail, now);
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--初始化账户权益统计信息, accountBenefit: {}", entity);
        }
        accountRepository.saveAccountStatistics(entity);
        return entity;
    }

    private AccountInitDTO assembleAccount(BenefitIncrementDTO benefitIncrementDTO, BenefitIncrementItemDTO dto) {
        AccountInitDTO initDTO = new AccountInitDTO();
        initDTO.setUserId(benefitIncrementDTO.getUserId());
        initDTO.setMerchantId(benefitIncrementDTO.getMerchantId());
        initDTO.setClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        if (Objects.nonNull(benefitIncrementDTO.getRecord())) {
            initDTO.setDescription(benefitIncrementDTO.getRecord().getDescription());
        }
        initDTO.setOperatorId(benefitIncrementDTO.getOperatorId());
        initDTO.setCardNo(dto.getCardNo());
        initDTO.setStoreId(benefitIncrementDTO.getStoreId());
        initDTO.setDeposit(dto.getDeposit());
        initDTO.setDefaultFlag(dto.getDefaultFlag());
        initDTO.setTotal(ZERO);
        initDTO.setBalance(ZERO);
        setAccountDownTime(initDTO::setDownTime, dto.getExpiredType(), dto.getDownTime());
        return initDTO;
    }

    private void setAccountDownTime(Consumer<Date> consumer,Integer expiredType, Date downTime) {
        if (Objects.equals(expiredType, CardExpiredTypeEnum.DURATION_EXPIRES.getCode())) {
            if (Objects.isNull(downTime)) {
                throw new BusinessException(ACCOUNT_CREATE_FAIL.getCode(), "会员卡过期时间不能为空");
            }
            consumer.accept(downTime);
        }
    }

    private SmallVenueStoredStatistics assembleAccountStatistics(Account account,
                                                                 BenefitIncrementDTO dto,
                                                                 BenefitIncrementItemDTO.BenefitDetailsDTO detail,
                                                                 Date now) {
        SmallVenueStoredStatistics entity = new SmallVenueStoredStatistics();
        entity.setUserId(dto.getUserId());
        entity.setMerchantId(dto.getMerchantId());
        entity.setMerchantUserId(account.getMerchantUserId());
        entity.setMerchantBenefitClassifyId(detail.getMerchantBenefitClassifyId());
        entity.setCreated(now);
        entity.setUpdated(now);
        entity.setCreatedby(dto.getOperatorId());
        entity.setUpdatedby(dto.getOperatorId());
        if (Objects.equals(detail.getNumType(), AccountBenefitNumTypeEnum.FREQUENCY.getCode())) {
            entity.setTotal(detail.getNum());
            entity.setBalance(detail.getNum());
            entity.setRemainNum(1);
            entity.setTotalNum(1);
        } else {
            entity.setTotal(detail.getNum());
            entity.setBalance(detail.getNum());
        }
        return entity;
    }

    private AccountBenefit assembleAccountBenefit(Account account,
                                                  BenefitIncrementDTO dto,
                                                  BenefitIncrementItemDTO.BenefitDetailsDTO detail,
                                                  Date now) {
        AccountBenefit ab = new AccountBenefit();
        ab.setAccountId(account.getId());
        ab.setMerchantId(dto.getMerchantId());
        ab.setUserId(dto.getUserId());
        ab.setMerchantUserId(account.getMerchantUserId());
        ab.setTotal(detail.getNum());
        ab.setBalance(detail.getNum());
        ab.setClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        ab.setStatus(AccountBenefitStatusEnum.NORMAL.getStatus());
        ab.setExpiryDateCategory(detail.getExpiryDateCategory());
        ab.setUpTime(detail.getUpTime());
        ab.setDownTime(detail.getDownTime());
        ab.setCreateTime(now);
        ab.setUpdateTime(now);
        ab.setCreateBy(dto.getOperatorId());
        ab.setUpdateBy(dto.getOperatorId());
        ab.setMerchantBenefitClassifyId(detail.getMerchantBenefitClassifyId());
        ab.setUseRuleId(detail.getUseRuleId());
        ab.setStoreId(dto.getStoreId());
        ab.setBenefitId(DEFAULT_BENEFIT_ID);
        ab.setValueType(detail.getNumType());
        return ab;
    }

    private AccountBenefit generateUpdatedAccountBenefit(Long id, BigDecimal balance, BigDecimal total, Long operatorId, Date now) {
        AccountBenefit entity = new AccountBenefit();
        entity.setId(id);
        entity.setBalance(balance);
        if (Objects.nonNull(total)) {
            entity.setTotal(total);
        }
        entity.setUpdateTime(now);
        entity.setUpdateBy(operatorId);
        return entity;
    }

    /**
     * @param accountBenefit             更新后的对象
     */
    private AccountRecord assembleIncreaseRecord(Account account,
                                                 AccountBenefit accountBenefit,
                                                 BenefitIncrementItemDTO.BenefitDetailsDTO detail, String cardNo,
                                                 AccountStatistic accountStatistic) {
        AccountRecord entity = new AccountRecord();

        entity.setAccountId(account.getId());
        entity.setCardNo(cardNo);
        entity.setAccountBenefitId(accountBenefit.getId());
        entity.setMerchantUserId(account.getMerchantUserId());

        entity.setMerchantBenefitClassifyId(detail.getMerchantBenefitClassifyId());
        entity.setMerchantBenefitClassifyName(detail.getMerchantBenefitClassifyName());
        entity.setBenefitClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());

        // operationType  相当于一个大分类，recordType 属于 operationType 的子分类
        entity.setOperationType(AccountRecordOperationTypeEnum.BENEFIT_MODIFY.getCode());
        entity.setMode(AdjustTypeEnum.INCREMENT.getType());
        entity.setBenefitId(DEFAULT_BENEFIT_ID);

        entity.setInitialBenefit(calcInitialBenefit(accountBenefit.getBalance(), detail.getNum(), AdjustTypeEnum.INCREMENT.getType()));
        entity.setOriginalBenefit(detail.getNum());
        entity.setActualBenefit(detail.getNum());

        entity.setAccountInitialBalance(accountStatistic.getInitialBalance());
        entity.setAccountInitialNum(accountStatistic.getNum());

        entity.setActualBenefit(detail.getNum());
        entity.setSubOrderNo(detail.getSubOrderNo());
        entity.setActualValue(detail.getActualValue());
        entity.setDescription(detail.getDescription());
        return entity;
    }

    /**
     * 查询用户可用的权益
     *
     * @param queryDTO 用户可用权益查询DTO
     * @return 可用权益列表
     */
    @Override
    public List<SmallVenueUserAvailableBenefitDTO> findUserAvailableBenefit(AvailableBenefitQueryDTO queryDTO) {
        //查询用户账户状态
        Account account = getOrElseThrow(accountRepository.get(queryDTO.getMerchantId(), queryDTO.getUserId(), queryDTO.getCardNo()));
        if (!checkAccountStatus(account)) {
            log.warn("[小场地会员账户]--当前账户状态：{}", account.getStatus());
            throw new BusinessException(AccountErrorCode.ACCOUNT_STATUS_ERROR);
        }
        if (account.getParentAccountId() != null) {
            //附属卡，需要获取主卡的账户的权益数据
            queryDTO.setCardNo("");
            queryDTO.setAccountId(account.getParentAccountId());
        }
        queryDTO.setClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        if (svBenefitPerf) {
            log.debug("[小场地会员账户]--过滤已使用完的权益明细: {}", queryDTO.getIgnoreEmptyBenefit());
            if (Objects.nonNull(queryDTO.getIgnoreEmptyBenefit())) {
                queryDTO.setIgnoreEmptyBenefit(queryDTO.getIgnoreEmptyBenefit());
            }
        } else {
            queryDTO.setIgnoreEmptyBenefit(null);
        }
        List<AccountBenefit> list = accountRepository.findUserAllBenefit(queryDTO);
        if (CollectionUtils.isNotEmpty(list)) {
            // 过滤掉生效时间外的记录
            list = list.stream().filter(record -> {
                if (Objects.equals(record.getExpiryDateCategory(), ExpiryDateCategoryEnum.TIME_INTERVAL.getValue())) {
                    LocalDateTime begin = parseLocalDateTimeOrElse(record.getUpTime(), "1970-01-01 00:00:00");
                    LocalDateTime end = parseLocalDateTimeOrElse(record.getDownTime(), "9999-01-01 00:00:00");
                    return begin.isBefore(LocalDateTime.now()) && end.isAfter(LocalDateTime.now());
                }
                if (Objects.equals(record.getExpiryDateCategory(), ExpiryDateCategoryEnum.ONE_DAY_TIME_INTERVAL.getValue())) {
                    LocalTime begin = parseLocalTimeOrElse(record.getUpTime(), "00:00:00");
                    LocalTime end = parseLocalTimeOrElse(record.getDownTime(), "23:59:59");
                    return begin.isBefore(LocalTime.now()) && end.isAfter(LocalTime.now());
                }
                return true;
            }).collect(Collectors.toList());
            Set<Long> set = list.stream().map(AccountBenefit::getId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(set)) {
                return Lists.newArrayList();
            }
            //获取权益类型在用户今天使用数量
            Map<Long, BigDecimal> map = getAccountBenefitUse(queryDTO.getMerchantId(), queryDTO.getUserId(), new ArrayList<>(set),
                    account.getMerchantUserId());
            List<SmallVenueUserAvailableBenefitDTO> result = new ArrayList<>(list.size());
            list.forEach(record -> {
                SmallVenueUserAvailableBenefitDTO dto = new SmallVenueUserAvailableBenefitDTO();
                dto.setAccountBenefitId(record.getId());
                dto.setMerchantId(record.getMerchantId());
                dto.setUserId(record.getUserId());
                dto.setMerchantUserId(record.getMerchantUserId());
                dto.setBalance(record.getBalance());
                dto.setUseRuleId(record.getUseRuleId());
                dto.setMerchantBenefitClassifyId(record.getMerchantBenefitClassifyId());
                dto.setStoreId(record.getStoreId());
                dto.setExpiryDateCategory(record.getExpiryDateCategory());
                dto.setUpTime(record.getUpTime());
                dto.setDownTime(record.getDownTime());
                dto.setCreateTime(record.getCreateTime());
                dto.setUpdateTime(record.getUpdateTime());
                dto.setTotal(record.getTotal());
                if (map != null && map.containsKey(record.getId())) {
                    dto.setUseNum(map.get(record.getId()));
                } else {
                    dto.setUseNum(ZERO);
                }

                result.add(dto);
            });
            return result;
        }
        return Lists.newArrayList();
    }

    @SuppressWarnings("UnusedReturnValue")
    SmallVenueStoredStatistics saveOrUpdateAccountStatistics4Increment(BenefitIncrementDTO dto,
                                                                       Account account,
                                                                       Map<Long, SmallVenueStoredStatistics> statisticsMap,
                                                                       BenefitIncrementItemDTO.BenefitDetailsDTO detail,
                                                                       Date now) {
        return Optional.ofNullable(statisticsMap.get(detail.getMerchantBenefitClassifyId()))
                .map(accountStatistics -> {
                    SmallVenueStoredStatistics entity = generateUpdatedStatistics4Increment(detail, dto.getRecord().getRecordType(), accountStatistics);
                    entity.setUpdated(now);
                    entity.setUpdatedby(dto.getOperatorId());
                    log.info("[小场地会员账户]--更新已有权益类型统计: {}", entity);
                    accountRepository.updateAccountStatistics(dto.getMerchantId(), entity);
                    return accountStatistics;
                })
                .orElseGet(() -> {
                    SmallVenueStoredStatistics statistics = initAccountStatistics(account, dto, detail, now);
                    log.info("[小场地会员账户]--增加权益类型统计: {}", statistics);
                    statisticsMap.put(statistics.getMerchantBenefitClassifyId(), statistics);
                    return statistics;
                });
    }

    private void checkParam(AccountInitDTO initDTO) {
        if (Objects.isNull(initDTO.getMerchantId()) && Objects.isNull(initDTO.getUserId())) {
            log.warn("[小场地会员账户]--账户初始化失败, initDTO: {}", initDTO);
            throw new BusinessException(AccountErrorCode.ACCOUNT_CREATE_FAIL);
        }
    }

    private AccountBenefit generateUpdatedBenefit(BenefitIncrementDTO dto,
                                                  BenefitIncrementItemDTO.BenefitDetailsDTO detail,
                                                  AccountBenefit accountBenefit,
                                                  boolean isIncreaseByRefund, Date now) {
        AccountBenefit ab = new AccountBenefit();
        ab.setId(accountBenefit.getId());
        if (!isIncreaseByRefund) {
            ab.setTotal(accountBenefit.getTotal().add(detail.getNum()));
            accountBenefit.setTotal(accountBenefit.getTotal().add(detail.getNum()));
        }
        ab.setBalance(accountBenefit.getBalance().add(detail.getNum()));
        accountBenefit.setBalance(accountBenefit.getBalance().add(detail.getNum()));
        ab.setUpdateBy(dto.getOperatorId());
        ab.setUpdateTime(now);
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--更新账户权益, accountBenefit: {}", ab);
        }
        return ab;
    }

    /**
     * 获取权益当日使用数据
     *
     * @param merchantId        商户id
     * @param userId            用户id
     * @param accountBenefitIds 权益明细id
     * @param merchantUserId    商户用户id
     * @return 权益当日使用数据
     */
    public Map<Long, BigDecimal> getAccountBenefitUse(Long merchantId, Long userId, List<Long> accountBenefitIds, Long merchantUserId) {
        Map<Long, BigDecimal> map = null;
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<AccountRecord> list = accountRepository.findByAccountBenefit(userId, merchantId, accountBenefitIds,
                Arrays.asList(SV_EQUIPMENT_CONSUMPTION.getCode(), SV_EQUIPMENT_CONSUMPTION_REFUND.getCode(),
                    SV_STORED_VALUE_DEDUCTION.getCode(), SV_STORED_VALUE_DEDUCTION_REFUND.getCode(),
                    SV_EXCHANGE.getCode(), SV_EXCHANGE_REFUND.getCode()), merchantUserId);
        stopWatch.stop();
        long totalTimeMillis = stopWatch.getTotalTimeMillis();
        if (totalTimeMillis > 1000) {
            log.warn("[小场地会员账户]--获取用户可用权益耗时：{}ms, size: {} merchantId: {}, userId: {}", totalTimeMillis,
                    accountBenefitIds.size(), merchantId, userId);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            Map<Long, List<AccountRecord>> recordMap = list.stream().collect(Collectors.groupingBy(AccountRecord::getAccountBenefitId));
            map = new HashMap<>(recordMap.size());
            for (Map.Entry<Long, List<AccountRecord>> it : recordMap.entrySet()) {
                List<AccountRecord> records = it.getValue();
                BigDecimal useNum = ZERO;
                for (AccountRecord record : records) {
                    if (AdjustTypeEnum.INCREMENT.getType().equals(record.getMode())) {
                        //增加,储值兑换类型的权益增加不计入
                        if (!Objects.equals(record.getRecordType(), SV_EXCHANGE.getCode())) {
                            useNum = useNum.subtract(record.getActualBenefit());
                            useNum = useNum.max(ZERO);
                        }

                    } else if (AdjustTypeEnum.DECREMENT.getType().equals(record.getMode())) {
                        //回退,退兑换的权益扣减不计入
                        if (!Objects.equals(record.getRecordType(), SV_EXCHANGE_REFUND.getCode())) {
                            useNum = useNum.add(record.getActualBenefit());
                        }
                    }
                }
                map.put(it.getKey(), useNum.max(BigDecimal.ZERO));
            }
        }
        return map;
    }

    void updateDecrementStatistics(BenefitDecrementDTO.BenefitDetailsDTO detail,
                                   SmallVenueStoredStatistics statistics,
                                   AccountBenefit accountBenefit,
                                   Integer recordType,
                                   Long operatorId, Date now) {
        Long merchantId = statistics.getMerchantId();
        SmallVenueStoredStatistics entity = generateUpdatedStatistics4Decrement(detail, statistics, accountBenefit, recordType);
        entity.setUpdated(now);
        entity.setUpdatedby(operatorId);
        accountRepository.updateAccountStatistics(merchantId, entity);
    }

    private AccountRecord assembleDecreaseRecord(Account account, AccountBenefit accountBenefit, BenefitDecrementDTO dto,
                                                 BenefitDecrementDTO.BenefitDetailsDTO detail, Date now,
                                                 Map<Long, AccountStatistic> accountStatisticMap) {
        AccountRecord entity = new AccountRecord();
        mapStruct.assignAccountRecord(dto.getRecord(), entity);

        if (StringUtils.isNotBlank(detail.getDescription())) {
            entity.setDescription(detail.getDescription());
        }

        entity.setCardNo(dto.getCardNo());
        entity.setAccountBenefitId(accountBenefit.getId());
        entity.setUserId(dto.getUserId());
        entity.setMerchantUserId(account.getMerchantUserId());
        entity.setMerchantId(dto.getMerchantId());
        entity.setStoreId(dto.getStoreId());

        entity.setMerchantBenefitClassifyId(detail.getMerchantBenefitClassifyId());
        entity.setMerchantBenefitClassifyName(detail.getMerchantBenefitClassifyName());
        entity.setActualValue(detail.getActualValue());
        entity.setBenefitClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        entity.setOutTradeNo(dto.getOrderNo());
        entity.setOrderNo(dto.getOrderNo());
        entity.setRefundNo(dto.getRefundNo());
        entity.setCreateTime(now);
        entity.setCreatedby(dto.getOperatorId());

        // operationType  相当于一个大分类，recordType 属于 operationType 的子分类
        entity.setOperationType(AccountRecordOperationTypeEnum.BENEFIT_MODIFY.getCode());
        entity.setMode(AdjustTypeEnum.DECREMENT.getType());
        entity.setBenefitId(DEFAULT_BENEFIT_ID);

        BigDecimal abs = detail.getNum().abs();
        AccountStatistic summary = accountStatisticMap.get(accountBenefit.getMerchantBenefitClassifyId());
        entity.setInitialBenefit(calcInitialBenefit(accountBenefit.getBalance(), abs, AdjustTypeEnum.DECREMENT.getType()));
        if (Objects.nonNull(summary)) {
            entity.setAccountInitialBalance(summary.getInitialBalance());
            entity.setAccountInitialNum(summary.getNum());
        }
        entity.setOriginalBenefit(abs);
        entity.setActualBenefit(abs);
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--记录账户权益流水, record: {}", entity);
        }
        return entity;
    }

    static BigDecimal calcInitialBenefit(BigDecimal current, BigDecimal value, Integer mode) {
        if (mode == 1) {
            return MathUtils.subtract(current, value);
        } else if (mode == 2) {
            return MathUtils.add(current, value);
        }
        return current;
    }


    private AccountBenefit saveInAccountBenefit(AccountBenefit outAccountBenefit, BigDecimal transferNum, AccountTransferTuple accountTuple, Long operatorId, Date now) {
        AccountBenefit newBenefit = generateNewBenefit(outAccountBenefit, transferNum, operatorId, now);
        newBenefit.setAccountId(accountTuple.getInAccount().getId());
        if (log.isDebugEnabled()) {
            log.debug("[小场地]--转账--保存转入账户权益明细 AccountBenefit：{}", newBenefit);
        }
        accountRepository.saveAccountBenefit(newBenefit);
        return newBenefit;
    }

    private void updateOutAccountBenefit(BigDecimal newBalance, Long outBenefitId, Long merchantId, Long operatorId, Date now) {
        AccountBenefit updatedOutBenefit = generateUpdatedAccountBenefit(outBenefitId, newBalance, null, operatorId, now);
        if (log.isDebugEnabled()) {
            log.debug("[小场地]--转账--更新转出账户权益明细 AccountBenefit：{}", updatedOutBenefit);
        }
        accountRepository.updateAccountBenefit(merchantId, updatedOutBenefit);
    }

    private List<AccountBenefit> getOutAccountBenefits(Map<Long, List<AccountBenefit>> outAccountBenefitMap, Long classifyId) {
        List<AccountBenefit> accountBenefits = outAccountBenefitMap.get(classifyId);
        if (CollectionUtils.isEmpty(accountBenefits)) {
            log.warn("[小场地]--转账--转出权益明细列表为空 merchantBenefitClassifyId： {}", classifyId);
            throw new BusinessException(BENEFIT_NOT_FOUND);
        }
        return accountBenefits;
    }

    private void updateInAccountBenefit(BigDecimal transferNum, Long operatorId, Date now, Long merchantId, AccountBenefit inAccountBenefit) {
        BigDecimal balance = inAccountBenefit.getBalance().add(transferNum);
        BigDecimal total = inAccountBenefit.getTotal().add(transferNum);
        AccountBenefit inBenefit = generateUpdatedAccountBenefit(inAccountBenefit.getId(), balance, total, operatorId, now);
        if (log.isDebugEnabled()) {
            log.debug("[小场地]--转账--无限期权益转入, AccountBenefit: {}", inBenefit);
        }
        accountRepository.updateAccountBenefit(merchantId, inBenefit);
    }

    private AccountRecord assembleTransferOutRecord(AccountBenefit accountBenefit,
                                                    BigDecimal transferNum) {
        return this.assembleTransferRecord(accountBenefit, transferNum, null, AdjustTypeEnum.DECREMENT, AccountRecordTypeEnum.SV_TRANSFER_OUT);
    }

    private AccountRecord assembleTransferRecord(AccountBenefit accountBenefit,
                                                 BigDecimal transferNum,
                                                 BigDecimal initialBenefit,
                                                 AdjustTypeEnum adjustTypeEnum,
                                                 AccountRecordTypeEnum type) {
        AccountRecord record = new AccountRecord();
        record.setAccountId(accountBenefit.getAccountId());
        record.setMerchantUserId(accountBenefit.getMerchantUserId());
        record.setAccountBenefitId(accountBenefit.getId());
        record.setMerchantBenefitClassifyId(accountBenefit.getMerchantBenefitClassifyId());
        record.setBenefitClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        record.setOperationType(AccountRecordOperationTypeEnum.BENEFIT_MODIFY.getCode());
        record.setMode(adjustTypeEnum.getType());
        record.setBenefitId(DEFAULT_BENEFIT_ID);

        if (Objects.equals(adjustTypeEnum, AdjustTypeEnum.DECREMENT)) {
            record.setInitialBenefit(accountBenefit.getBalance());
        } else {
            record.setInitialBenefit(initialBenefit);
            record.setActualValue(ZERO);
        }
        record.setOriginalBenefit(transferNum);
        record.setActualBenefit(transferNum);
        record.setRecordType(type.getCode());
        return record;
    }

    private AccountBenefit generateNewBenefit(AccountBenefit accountBenefit, BigDecimal transferNum, Long operatorId, Date now) {
        AccountBenefit newBenefit = new AccountBenefit();
        mapStruct.setAccountBenefitValue(accountBenefit, newBenefit);
        newBenefit.setUpdateTime(now);
        newBenefit.setUpdateBy(operatorId);
        newBenefit.setTotal(transferNum);
        newBenefit.setBalance(transferNum);
        newBenefit.setCreateBy(operatorId);
        newBenefit.setCreateTime(now);
        return newBenefit;
    }

    /**
     * 查询可以聚合的无限期转入权益
     */
    private Map<Long, Map<String, AccountBenefit>> getInAccountUnlimitedBenefitMap(Long merchantId, Long userId, Account account, List<Long> classifyIds) {
        List<AccountBenefit> benefits = accountRepository.selectBenefit(merchantId, userId, account.getId(), classifyIds, ExpiryDateCategoryEnum.NO_LIMIT);
        if (log.isDebugEnabled()) {
            log.debug("[小场地]--转账--查询转入账户无限期权益列表 benefits: {}", benefits);
        }
        return benefits
            .stream()
            .filter(SmallVenueAccountServiceImpl::couldAggregateBenefit)
            .collect(Collectors.groupingBy(
                AccountBenefit::getMerchantBenefitClassifyId,
                Collectors.toMap(SmallVenueAccountServiceImpl::compositeKey, Function.identity())
            ));
    }

    /**
     * group by merchantClassifyId
     */
    private Map<Long, List<AccountBenefit>> getOutAccountBenefitMap(Long merchantId, Long userId, Account outAccount, List<Long> classifyIds) {
        List<AccountBenefit> benefits = accountRepository.selectTransferOutBenefit(merchantId, userId, outAccount.getId(), classifyIds);
        if (log.isDebugEnabled()) {
            log.debug("[小场地]--转账--转出账户权益明细 out: {}", benefits);
        }
        return sortedMapList(benefits);
    }

    private void assignRecordValue(CardTransferDTO dto, Date now, Map<Long, String> classifyNameMap, List<AccountRecord> list) {
        list.forEach(record -> {
            mapStruct.assignAccountRecord(dto, record);
            record.setCreateTime(now);
            record.setMerchantBenefitClassifyName(classifyNameMap.get(record.getMerchantBenefitClassifyId()));
        });
    }

    private AccountRecord assembleAccountCancellationRecord(AccountCancellationDTO dto, Account account, Entry<Long, BigDecimal> accountBenefit, Map<Long, Long> frequencyValueTypeCountMap, Date now) {
        AccountRecord record = new AccountRecord();
        AccountRecordInfoDTO recordInfoDTO = dto.getRecord();
        mapStruct.assignAccountRecord(recordInfoDTO, record);
        record.setAccountId(account.getId());
        record.setMerchantUserId(account.getMerchantUserId());
        record.setCardNo(account.getCardNo());
        record.setOutTradeNo(dto.getOutTradeNo());
        record.setOrderNo(dto.getOutTradeNo());
        record.setUserId(dto.getUserId());
        record.setMerchantId(dto.getMerchantId());
        record.setStoreId(dto.getStoreId());
        record.setCreatedby(dto.getOperatorId());
        record.setBenefitClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        record.setBenefitId(DEFAULT_BENEFIT_ID);
        record.setInitialBenefit(accountBenefit.getValue());
        record.setOriginalBenefit(accountBenefit.getValue());
        record.setActualBenefit(accountBenefit.getValue());
        record.setAccountInitialBalance(accountBenefit.getValue());
        if (Objects.nonNull(frequencyValueTypeCountMap) && frequencyValueTypeCountMap.containsKey(accountBenefit.getKey())) {
            record.setAccountInitialNum(BigDecimal.valueOf(frequencyValueTypeCountMap.getOrDefault(accountBenefit.getKey(), 0L)));
        }
        record.setMerchantBenefitClassifyId(accountBenefit.getKey());
        record.setMode(AdjustTypeEnum.DECREMENT.getType());
        record.setCreateTime(now);
        record.setDescription(AccountRecordTypeEnum.SV_ACCOUNT_CANCELLATION.getDesc());
        record.setRecordType(AccountRecordTypeEnum.SV_ACCOUNT_CANCELLATION.getCode());
        record.setOperationType(AccountRecordOperationTypeEnum.BENEFIT_MODIFY.getCode());
        return record;
    }

    /**
     * 校验账户状态
     *
     * @param account 账户信息
     * @return true 正常
     * false 异常
     * @see AccountValidSpecification
     */
    private boolean checkAccountStatus(Account account) {
        if (AccountStatusEnum.NORMAL.getStatus().equals(account.getStatus())) {
            return account.getDownTime() == null || System.currentTimeMillis() <= account.getDownTime().getTime();
        }
        return false;
    }

    /**
     * 计算充值或者充值回滚后的权益信息
     */
    static List<BenefitRechargeSummaryDTO> calcRecharge(List<AccountRecord> records, boolean isRefund) {
        return records.stream()
                .filter(r -> rechargeFilter(isRefund, r))
                .filter(r -> Objects.nonNull(r.getMerchantBenefitClassifyId()))
                .collect(Collectors.groupingBy(AccountRecord::getMerchantBenefitClassifyId))
                .entrySet()
                .stream()
                .map(m -> {
                    BenefitRechargeSummaryDTO summary = new BenefitRechargeSummaryDTO();
                    summary.setMerchantBenefitClassifyId(m.getKey());
                    List<AccountRecord> list = m.getValue();
                    Comparator<AccountRecord> comparator = Comparator.comparing(AccountRecord::getAccountInitialBalance);
                    list.stream()
                            .reduce(isRefund ? BinaryOperator.maxBy(comparator) : BinaryOperator.minBy(comparator))
                            .ifPresent(a -> {
                                summary.setMerchantBenefitClassifyName(a.getMerchantBenefitClassifyName());
                                summary.setOriginBalance(a.getAccountInitialBalance());
                                summary.setOriginNum(a.getAccountInitialNum());
                                if (Objects.nonNull(summary.getOriginNum())) {
                                    summary.setRechargeNum(new BigDecimal(list.size()));
                                    BigDecimal num = getRechargedSummaryValue(isRefund, summary.getOriginNum(), summary.getRechargeNum());
                                    summary.setNum(num);
                                }
                            });
                    list.stream()
                            .map(AccountRecord::getActualBenefit)
                            .reduce(BigDecimal::add)
                            .ifPresent(rechargeBalance -> {
                                summary.setRechargeBalance(rechargeBalance);
                                BigDecimal balance = getRechargedSummaryValue(isRefund, summary.getOriginBalance(), summary.getRechargeBalance());
                                summary.setBalance(balance);
                            });
                    if (log.isDebugEnabled()) {
                        log.debug("[小场地]--会员卡账号权益汇总信息--订单充值 summary: {}", summary);
                    }
                    return summary;
                })
                .collect(Collectors.toList());
    }

    private static BigDecimal getRechargedSummaryValue(boolean isRefund, BigDecimal originValue, BigDecimal rechargeValue) {
        return isRefund ? MathUtils.subtract(originValue, rechargeValue) : MathUtils.add(originValue, rechargeValue);
    }

    private static boolean rechargeFilter(boolean isRefund, AccountRecord r) {
        List<AccountRecordTypeEnum> types;
        if (isRefund) {
            types = Lists.newArrayList(SV_REFUND, SV_EXCHANGE_REFUND, SV_GIFT_REFUND, SV_PRESENTATION_REFUND,SV_DEDUCTION);
            if (Objects.equals(r.getRecordType(), SV_EXCHANGE_REFUND.getCode())
                && Objects.equals(r.getMode(), AdjustTypeEnum.INCREMENT.getType())) {
                return false;
            }
        } else {
            types =Lists.newArrayList(SV_RECHARGE, SV_GIFT_RECYCLING, SV_PRESENTATION, SV_EXCHANGE);
            if (Objects.equals(r.getRecordType(), SV_GIFT_RECYCLING.getCode())
                    && Objects.isNull(r.getAccountInitialBalance())) {
                return false;
            }
            if (Objects.equals(r.getRecordType(), SV_EXCHANGE.getCode())
                && Objects.equals(r.getMode(), AdjustTypeEnum.DECREMENT.getType())) {
                return false;
            }
        }
        return  types.contains(AccountRecordTypeEnum.findByCode(r.getRecordType()));
    }

    /**
     * 计算储值抵扣或者储值抵扣回滚后的权益信息
     */
    static List<BenefitConsumptionSummaryDTO> calcConsumption(List<AccountRecord> records,
                                                              List<Long> presentationBenefitClassifyIds,
                                                              boolean isRefund) {
        return records.stream()
                .filter(r -> consumptionFilter(isRefund, presentationBenefitClassifyIds, r))
                .filter(r -> Objects.nonNull(r.getMerchantBenefitClassifyId()))
                .collect(Collectors.groupingBy(AccountRecord::getMerchantBenefitClassifyId))
                .entrySet()
                .stream()
                .map(m -> {
                    BenefitConsumptionSummaryDTO summary = new BenefitConsumptionSummaryDTO();
                    summary.setMerchantBenefitClassifyId(m.getKey());
                    List<AccountRecord> list = m.getValue();
                    if (isRefund) {
                        calcConsumptionRefund(summary, list);
                    } else {
                        calcConsumption(summary, list);
                    }
                    return summary;
                })
                .collect(Collectors.toList());
    }

    private static void calcConsumption(BenefitConsumptionSummaryDTO summary, List<AccountRecord> list) {
        list.stream()
                .filter(SmallVenueAccountServiceImpl::isConsumption)
                .max(Comparator.comparing(AccountRecord::getAccountInitialBalance))
                .ifPresent(a -> {
                    summary.setMerchantBenefitClassifyName(a.getMerchantBenefitClassifyName());
                    summary.setOriginBalance(a.getAccountInitialBalance());
                    summary.setLastConsumptionAccountBenefitId(a.getAccountBenefitId());
                });
        assignSummary(list, summary, SmallVenueAccountServiceImpl::isConsumption, AdjustTypeEnum.INCREMENT);
        BigDecimal balance = MathUtils.add(MathUtils.subtract(summary.getOriginBalance(), summary.getConsumption()), summary.getPresentation());
        summary.setBalance(balance);
    }

    private static boolean isConsumption(AccountRecord r) {
        return Objects.equals(r.getRecordType(), AccountRecordTypeEnum.SV_STORED_VALUE_DEDUCTION.getCode())
                || (Objects.equals(r.getRecordType(), SV_EXCHANGE.getCode()) && Objects.equals(r.getMode(), AdjustTypeEnum.DECREMENT.getType()) && Objects.nonNull(r.getAccountInitialBalance()))
                || (Objects.equals(r.getRecordType(), SV_EQUIPMENT_CONSUMPTION.getCode()) && Objects.nonNull(r.getAccountInitialBalance()));
    }

    private static boolean isConsumptionRefund(AccountRecord r) {
        return Objects.equals(r.getRecordType(), AccountRecordTypeEnum.SV_STORED_VALUE_DEDUCTION_REFUND.getCode())
                || (Objects.equals(r.getRecordType(), SV_EXCHANGE_REFUND.getCode()) && Objects.equals(r.getMode(), AdjustTypeEnum.INCREMENT.getType()) && Objects.nonNull(r.getAccountInitialBalance()))
                || (Objects.equals(r.getRecordType(), SV_EQUIPMENT_CONSUMPTION_REFUND.getCode()) && Objects.nonNull(r.getAccountInitialBalance()));
    }

    private static void assignSummary(List<AccountRecord> list, BenefitConsumptionSummaryDTO summary, Predicate<AccountRecord> predicate, AdjustTypeEnum increment) {
        list.stream()
                .filter(predicate)
                .map(AccountRecord::getActualBenefit)
                .reduce(BigDecimal::add)
                .ifPresent(summary::setConsumption);
        list.stream()
                .filter(r -> Objects.equals(r.getMode(), increment.getType()))
                .map(AccountRecord::getActualBenefit)
                .reduce(BigDecimal::add)
                .ifPresent(summary::setPresentation);
    }

    private static void calcConsumptionRefund(BenefitConsumptionSummaryDTO summary, List<AccountRecord> list) {
        list.stream()
                .filter(SmallVenueAccountServiceImpl::isConsumptionRefund)
                .min(Comparator.comparing(AccountRecord::getAccountInitialBalance))
                .ifPresent(a -> {
                    summary.setMerchantBenefitClassifyName(a.getMerchantBenefitClassifyName());
                    summary.setOriginBalance(a.getAccountInitialBalance());
                    summary.setLastConsumptionAccountBenefitId(a.getAccountBenefitId());
                });
        assignSummary(list, summary, SmallVenueAccountServiceImpl::isConsumptionRefund, AdjustTypeEnum.DECREMENT);
        BigDecimal balance = MathUtils.subtract(MathUtils.add(summary.getOriginBalance(), summary.getConsumption()), summary.getPresentation());
        summary.setBalance(balance);
    }

    @SuppressWarnings("unused")
    private static boolean consumptionFilter(boolean isRefund, List<Long> presentationBenefitClassifyIds, AccountRecord r) {
        // TODO 2022/3/30: 是否有可能需要根据 存在赠送逻辑的权益 来过滤
        if (isRefund) {
            return Objects.equals(r.getRecordType(), SV_STORED_VALUE_DEDUCTION_REFUND.getCode())
                    || Objects.equals(r.getRecordType(), AccountRecordTypeEnum.SV_PRESENTATION_REFUND.getCode())
                    || (Objects.equals(r.getRecordType(), SV_EXCHANGE_REFUND.getCode()) && Objects.equals(r.getMode(), AdjustTypeEnum.INCREMENT.getType()))
                    || Objects.equals(r.getRecordType(), SV_EQUIPMENT_CONSUMPTION_REFUND.getCode());
        }
        return Objects.equals(r.getRecordType(), AccountRecordTypeEnum.SV_STORED_VALUE_DEDUCTION.getCode())
                || (Objects.equals(r.getRecordType(), AccountRecordTypeEnum.SV_EXCHANGE.getCode()) && Objects.equals(r.getMode(), AdjustTypeEnum.DECREMENT.getType()))
                || Objects.equals(r.getRecordType(), SV_EQUIPMENT_CONSUMPTION.getCode());
    }


    static AccountStatistic getAccountStatistic(boolean isIncreaseByRefund,
                                                 Map<Long, AccountStatistic> accountStatisticMap,
                                                 BigDecimal value,
                                                 AccountBenefit ab) {
        AccountStatistic accountStatistic = accountStatisticMap.get(ab.getMerchantBenefitClassifyId());
        if (Objects.nonNull(accountStatistic)) {
            BigDecimal initialBalance;
            BigDecimal totalBalance =  Objects.isNull(accountStatistic.getTotalBalance())
                    // 已有的权益， totalRecharge 会为 null
                    ? accountStatistic.getInitialBalance()
                    // 没有的权益， totalRecharge 会为 0
                    : accountStatistic.getTotalBalance();
            initialBalance = totalBalance ;
            accountStatistic.setTotalBalance(MathUtils.add(totalBalance, value));
            accountStatistic.setInitialBalance(initialBalance);
            if (!isIncreaseByRefund && Objects.equals(ab.getValueType(), AccountBenefitNumTypeEnum.FREQUENCY.getCode())) {
                BigDecimal num;
//                num = accountStatistic.getNum().add(ONE);
                BigDecimal totalNum =  Objects.isNull(accountStatistic.getTotalNum())
                        ? accountStatistic.getNum()
                        : accountStatistic.getTotalNum();
                num = totalNum;
                accountStatistic.setTotalNum(MathUtils.add(totalNum, ONE));
                accountStatistic.setNum(num);
            }
        } else {
            accountStatistic = new AccountStatistic();
            accountStatistic.setTotalBalance(value);
            accountStatistic.setInitialBalance(ZERO);
            if (!isIncreaseByRefund && Objects.equals(ab.getValueType(), AccountBenefitNumTypeEnum.FREQUENCY.getCode())) {
                accountStatistic.setNum(ZERO);
                accountStatistic.setTotalNum(ONE);
            }
            accountStatisticMap.put(ab.getMerchantBenefitClassifyId(), accountStatistic);
        }
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--增加权益--更新缓存的已有权益分类统计 {}", accountStatisticMap);
        }
        return accountStatistic;
    }

    static void updateAccountStatisticMapValue(Map<Long, AccountStatistic> map,  BigDecimal value, AccountBenefit ab, AdjustTypeEnum mode) {
        AccountStatistic accountStatistic = map.get(ab.getMerchantBenefitClassifyId());
        if (!Objects.nonNull(accountStatistic)) {
            log.warn("[小场地会员账户]--减扣权益--权益类型不存在");
            return;
        }
        BigDecimal initialBalance;
        initialBalance = accountStatistic.getInitialBalance().add(value);
        accountStatistic.setInitialBalance(initialBalance);
        if (Objects.equals(ab.getValueType(), AccountBenefitNumTypeEnum.FREQUENCY.getCode())) {
            BigDecimal num;
            if(Objects.equals(0, MathUtils.add(ab.getBalance(), value).intValue())) {
                num = accountStatistic.getNum().add(ONE.negate());
            } else {
                num = accountStatistic.getNum();
            }
            accountStatistic.setNum(num);
        }
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--减扣权益--更新缓存的已有权益分类统计 {}", map);
        }
    }

    private Map<Long, AccountStatistic> getAccountStatisticMap(List<AccountBenefit> accountBenefits, Long storeId,
        Map<Long, List<Long>> merchantBenefitClassifyScopeMap) {
        if (Objects.nonNull(merchantBenefitClassifyScopeMap)) {
            accountBenefits = accountBenefits.stream().filter(benefit -> {
                List<Long> universalStores = merchantBenefitClassifyScopeMap.get(benefit.getMerchantBenefitClassifyId());
                return Objects.isNull(benefit.getStoreId()) || Objects.equals(benefit.getStoreId(), storeId)
                    || (CollectionUtils.isNotEmpty(universalStores) && universalStores.contains(benefit.getStoreId()));
            }).collect(Collectors.toList());
        }
        Map<Long, AccountStatistic> map = accountBenefits.stream()
                .collect(Collectors.groupingBy(AccountBenefit::getMerchantBenefitClassifyId))
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Entry::getKey, e -> {
                    List<AccountBenefit> values = e.getValue();
                    AccountStatistic accountStatistic = new AccountStatistic();
                    BigDecimal total = values.stream().map(AccountBenefit::getBalance).reduce(BigDecimal::add).orElse(ZERO);
                    accountStatistic.setInitialBalance(total);
                    values.stream().findFirst()
                            .filter(ab -> Objects.equals(ab.getValueType(), AccountBenefitNumTypeEnum.FREQUENCY.getCode()))
                            .ifPresent(t -> {
                                long count = values.stream().filter(b -> b.getBalance().compareTo(ZERO) > 0).count();
                                accountStatistic.setNum(new BigDecimal(count));
                            });
                    return accountStatistic;
                }));
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--增加权益--账号下已有权益分类统计 {}", map);
        }
        return map;
    }


    @Data
    public static class AccountStatistic {

        private BigDecimal initialBalance;

        private BigDecimal totalBalance;
        /**
         * 频次卡张树
         */
        private BigDecimal num;

        private BigDecimal totalNum;
    }


}
