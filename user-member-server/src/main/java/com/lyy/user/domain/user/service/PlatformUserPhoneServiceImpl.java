package com.lyy.user.domain.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneDTO;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneQueryParam;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneRecordDTO;
import com.lyy.user.application.user.IPlatformUserPhoneService;
import com.lyy.user.domain.user.entity.PlatformUserPhone;
import com.lyy.user.domain.user.repository.PlatformUserPhoneMapper;
import com.lyy.user.infrastructure.util.CommonConverterTools;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@Service
public class PlatformUserPhoneServiceImpl extends ServiceImpl<PlatformUserPhoneMapper, PlatformUserPhone> implements IPlatformUserPhoneService {

    @Resource
    private PlatformUserPhoneMapper platformUserPhoneMapper;

    @Override
    public Boolean saveOrUpdatePlatformUserPhone(PlatformUserPhoneDTO dto) {

        QueryWrapper<PlatformUserPhone> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", dto.getUserId());
        queryWrapper.eq("merchant_id", dto.getMerchantId());
        queryWrapper.eq("is_active", UserMemberSysConstants.ENABLE);
        if (dto.getMerchantUserId() != null) {
            queryWrapper.eq("merchant_user_id", dto.getMerchantUserId());
        }
        List<PlatformUserPhone> platformUserPhones = platformUserPhoneMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(platformUserPhones)) {
            List<Long> ids = platformUserPhones.stream().map(PlatformUserPhone::getId).collect(Collectors.toList());
            platformUserPhoneMapper.updateTelephoneByIds(ids, UserMemberSysConstants.DISABLE, dto);
        }
        PlatformUserPhone userPhone = CommonConverterTools.convert(PlatformUserPhone.class, dto);
        userPhone.setActive(UserMemberSysConstants.ENABLE);
        userPhone.setCreatedby(dto.getOperatorId());
        userPhone.setCreateTime(new Date());
        platformUserPhoneMapper.insert(userPhone);
        return true;
    }

    @Override
    public List<PlatformUserPhoneRecordDTO> list(PlatformUserPhoneQueryParam param) {

        QueryWrapper<PlatformUserPhone> queryWrapper = new QueryWrapper<>();
        if (param.getId() != null) {
            queryWrapper.eq("id", param.getId());
        }
        if (param.getUserId() != null) {
            queryWrapper.eq("user_id", param.getUserId());
        }
        if (param.getMerchantId() != null) {
            queryWrapper.eq("merchant_id", param.getMerchantId());
        }
        if (param.getActive() == null) {
            queryWrapper.eq("is_active", UserMemberSysConstants.ENABLE);
        } else {
            queryWrapper.eq("is_active", param.getActive());
        }
        if (!StringUtils.isEmpty(param.getTelephone())) {
            queryWrapper.eq("telephone", param.getTelephone());
        }
        queryWrapper.orderByDesc("create_time");

        List<PlatformUserPhone> platformUserPhones = platformUserPhoneMapper.selectList(queryWrapper);
        List<PlatformUserPhoneRecordDTO> convert = CommonConverterTools.convert(PlatformUserPhoneRecordDTO.class, platformUserPhones);
        return convert;
    }
}
