package com.lyy.user.domain.correction.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.lyy.user.domain.correction.dto.CorrectionDTO;
import com.lyy.user.domain.correction.dto.GcConditionDTO;
import com.lyy.user.domain.correction.repository.DataGCParser;
import com.lyy.user.infrastructure.constants.ShardingGCEnum;
import com.lyy.user.infrastructure.util.GcNodeUtils;
import com.lyy.user.infrastructure.util.ImitateShardingAlgorithmUtils;
import com.lyy.user.infrastructure.util.JSONUtil;
import com.lyy.user.interfaces.schedule.CommonGCAction;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import javax.sql.DataSource;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.shardingjdbc.jdbc.core.datasource.ShardingDataSource;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/9/26
 */
@Slf4j
//@Component
public class CommonGCActionImpl implements CommonGCAction, InitializingBean {

    @Setter(onMethod_ = @Autowired)
    private DataSource shardingDataSource;
    @Setter(onMethod_ = @Autowired)
    private RollBackGcAction rollBackGcAction;
    private final AtomicInteger retryCloseResource = new AtomicInteger(0);
    private Map<String, DataSource> dataSourceMap;
    Connection connection;
    Statement statement;
    private DataGCParser dataGcParser;


    // 是否进行数据删除
    @Value("${sharding.gc.switch:true}")
    private Boolean shardingDataGcSwitch;
    @Override
    public List<CorrectionDTO> findIdentityByPage() {
        String sql = dataGcParser.pageParser(ShardingGCEnum::getSourceClass);

        List<CorrectionDTO> result = new ArrayList<>();
        try {
            ResultSet resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                Long id = resultSet.getLong(DataGCParser.COLUMN_ID);
                Long merchantId = resultSet.getLong(DataGCParser.COLUMN_MERCHANT);
                CorrectionDTO correctionDTO = new CorrectionDTO();
                correctionDTO.setId(id);
                correctionDTO.setMerchantId(merchantId);
                result.add(correctionDTO);
            }

            resultSet.close();
        } catch (SQLException e) {
            log.error("CommonGCActionImpl query fail, err reason", e);
        }
        return result;
    }

    @Override
    public List findIdentityByPage(List<Long> indexId) {
        String sql = dataGcParser.pageParser(indexId);
        List result = new ArrayList<>();
        ArrayNode arrNode = GcNodeUtils.createArrNode();
        try {
            ResultSet resultSet = statement.executeQuery(sql);
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            Class className = ShardingGCEnum.getSourceClass(dataGcParser.getTableName());
            while (resultSet.next()) {
                ObjectNode node = GcNodeUtils.createNode();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object obj = resultSet.getObject(i);
                    if (Objects.nonNull(obj)) {
                        GcNodeUtils.putNode(node, columnName, String.valueOf(obj));
                    }
                }

                arrNode.add(node);
            }
            result = GcNodeUtils.convertNode(arrNode, className);
            resultSet.close();
        } catch (SQLException e) {
            log.error("CommonGCActionImpl query fail, err reason", e);
        }
        return result;
    }

    @Override
    public Boolean doAction(GcConditionDTO gcConditionDTO) {
        String tableName = gcConditionDTO.getTableName();
        Long pageSize = gcConditionDTO.getPageSize();
        Integer dsNo = gcConditionDTO.getDsNo();
        if (StringUtils.isEmpty(tableName)) {
            return false;
        }

        dataGcParser = DataGCParser.builder().pageSize(pageSize).tableName(tableName).build();
        List<CorrectionDTO> identityByPage = new ArrayList<>(Math.toIntExact(pageSize));

        do {
            try {
                StopWatch stopWatch = new StopWatch();
                stopWatch.start("calculate cost");
                identityByPage = findIdentityByPage();
                dataGcParser.nextIndex(identityByPage);
                List<CorrectionDTO> corrections = calculateSharding(identityByPage, dsNo);
                if (shardingDataGcSwitch && !corrections.isEmpty()) {
                    List<Long> indexId = corrections.stream().map(CorrectionDTO::getId).collect(Collectors.toList());
                    List refoundData = findIdentityByPage(indexId);
                    deleteAndBackUp(gcConditionDTO, refoundData, dsNo);
                }
                stopWatch.stop();
                log.info("CommonGCActionImpl # 耗时: {}", stopWatch.getLastTaskTimeMillis());
            } catch (Exception e) {
                log.error("DS[{}] 执行报错， 表: {}, err: {}", dsNo, dataGcParser.getTableName(), e.getLocalizedMessage());
            }
        } while (!identityByPage.isEmpty());

        dataGcParser = null;
        return true;
    }

    private void deleteAndBackUp(GcConditionDTO gcConditionDTO, List corrections, Integer dsNo) throws SQLException {
        String errList = JSONUtil.toJSONString(corrections);
        log.info("DS[{}], 错误的数据 #### {}", dsNo, errList);
        try {
            Integer code = gcConditionDTO.getCode();
            ShardingGCEnum shardingGCEnum = ShardingGCEnum.getData(code);
            Boolean deleteState = clearShardingData(corrections);
            if (deleteState) {
                // 保存备份表
                rollBackGcAction.backUp(corrections, shardingGCEnum.getBakTableName(), shardingGCEnum.getBackupClazz());
            }

            connection.commit();
        } catch (Exception e) {
            log.error("deleteAndBackUp fail, err msg: {}", e.getLocalizedMessage());
            connection.rollback();
        }
    }

    public List<CorrectionDTO> calculateSharding(List<CorrectionDTO> correctionDTO, Integer dsNo) {
        return correctionDTO.stream().filter(cdr -> {
            if (Objects.isNull(cdr)) {
                return false;
            }

            Long merchantId = cdr.getMerchantId();
            if (Objects.isNull(merchantId)) {
                return false;
            }

            int realDsNo = ImitateShardingAlgorithmUtils.findIndex(merchantId);
            cdr.setDsId(realDsNo);
            Integer dsId = cdr.getDsId();
            return !Objects.equals(dsNo, dsId);
        }).collect(Collectors.toList());
    }

    public Boolean clearShardingData(List correctionDTO) throws SQLException {
        List<Long> delIds = new ArrayList<>(correctionDTO.size());
        for (Object obj : correctionDTO) {
            JsonNode node = GcNodeUtils.objToNode(obj);
            JsonNode idNode = node.get("id");
            delIds.add(idNode.asLong());
        }

        connection.setAutoCommit(false);
        String delSql = dataGcParser.deleteParser(delIds);
        boolean execute = false;
        try {
            execute = statement.executeUpdate(delSql) > 1;
        } catch (SQLException e) {
            log.error("CommonGCActionImpl del fail, err reason", e);
        }
        return execute;
    }

    @Override
    public CommonGCAction chooseDS(Integer dsNo) {
        String dsName = "ds" + dsNo;
        DataSource dataSource = dataSourceMap.get(dsName);
        synchronized (this) {
            try {
                connection = dataSource.getConnection();
                statement = connection.createStatement();
            } catch (SQLException e) {
                throw new RuntimeException("初始化连接失败");
            }
        }

        rollBackGcAction.chooseDS(dsNo);
        return this;
    }

    @Override
    public void close() {
        releaseDs();
    }

    private void releaseDs() {
        try {
            synchronized (this) {
                retryCloseResource.getAndIncrement();
                connection.close();
                statement.close();
            }
        } catch (SQLException e) {
            if (retryCloseResource.get() < 3) {
                log.info("CommonGCActionImpl 关闭资源失败, 再次尝试");
                releaseDs();
            }
            log.error("CommonGCActionImpl 三次尝试关闭资源失败", e);

            retryCloseResource.set(0);
        }
        rollBackGcAction.close();
    }

    @Override
    public void afterPropertiesSet() {
        ShardingDataSource shardingDataSource = (ShardingDataSource) this.shardingDataSource;
        dataSourceMap = shardingDataSource.getDataSourceMap();
    }
}
