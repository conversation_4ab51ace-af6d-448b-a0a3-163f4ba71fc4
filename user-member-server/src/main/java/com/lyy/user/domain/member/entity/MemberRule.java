package com.lyy.user.domain.member.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 会员规则
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("um_member_rule")
public class MemberRule implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 商户ID
     */
    @TableField("merchant_id")
    private Long merchantId;

    /**
     * 会员等级
     */
    @TableField("member_level_id")
    private Long memberLevelId;

    /**
     * 规则类型--对应账户权益类型
     */
    @TableField("benefit_classify")
    private Short benefitClassify;

    /**
     * 权益值
     */
    @TableField("benefit_value")
    private BigDecimal benefitValue;

    /**
     * 权益单位
     */
    @TableField("benefit_unit")
    private String benefitUnit;

    /**
     * 有效期类型（0、无限期，1、日期区间可用，2、单日时间区间可用）
     * @see com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum
     */
    @TableField("expiry_date_category")
    private Short expiryDateCategory;

    /**
     * 上线时间
     */
    @TableField("up_time")
    private String upTime;

    /**
     * 下线时间
     */
    @TableField("down_time")
    private String downTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建者
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新者
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 是否有效 true 有效,false 无效
     */
    @TableField("active")
    private Boolean active;

}
