package com.lyy.user.domain.member.service;

import cn.hutool.core.date.DateUtil;
import cn.lyy.base.utils.SnowflakeIdWorker;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.constant.AccountBenefitResourceEnum;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyGroupEnum;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import com.lyy.user.account.infrastructure.member.dto.MemberRuleDTO;
import com.lyy.user.application.account.AccountService;
import com.lyy.user.application.member.IMemberRuleService;
import com.lyy.user.domain.member.entity.Member;
import com.lyy.user.domain.member.entity.MemberGroup;
import com.lyy.user.domain.member.entity.MemberLevel;
import com.lyy.user.domain.member.entity.MemberRule;
import com.lyy.user.domain.member.repository.MemberGroupMapper;
import com.lyy.user.domain.member.repository.MemberMapper;
import com.lyy.user.domain.member.repository.MemberRuleMapper;
import com.lyy.user.infrastructure.base.MerchantBaseServiceImpl;
import com.lyy.user.infrastructure.mq.OldMemberBenefitSender;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 会员规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
@Service
@Slf4j
public class MemberRuleServiceImpl extends MerchantBaseServiceImpl<MemberRuleMapper, MemberRule> implements IMemberRuleService {
    @Autowired
    private AccountService accountService;
    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;
    @Autowired
    private OldMemberBenefitSender oldMemberBenefitSender;

    @Resource
    private MemberGroupMapper memberGroupMapper;

    @Resource
    private MemberMapper memberMapper;

    /**
     * 根据会员等级信息保存对应的会员规则信息
     *
     *
     * @param merchantId
     * @param memberLevelId
     * @param memberRuleList
     * @return
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveOfMemberLevel(Long merchantId, Long memberLevelId, List<MemberRuleDTO> memberRuleList) {
        //删除原有规则
        UpdateWrapper<MemberRule> queryWrapper = new UpdateWrapper<MemberRule>()
                .eq(getShardingFieldKey(), merchantId)
                .eq("member_level_id",memberLevelId);
        queryWrapper.set("active", false);
        int del = getBaseMapper().update(null, queryWrapper);
        //增加规则
        int add= 0;
        if(memberRuleList != null && !memberRuleList.isEmpty()){
            List<MemberRule> list = memberRuleList.stream()
                    .map(rule->{
//                        checkExpiryDateCategoryFormat(rule);
                        MemberRule memberRule = new MemberRule();
                        BeanUtils.copyProperties(rule,memberRule,"id","memberLevelId");
                        memberRule.setMemberLevelId(memberLevelId);
                        memberRule.setMerchantId(merchantId);
                        memberRule.setCreateTime(new Date());
                        memberRule.setUpdateTime(new Date());
                        memberRule.setCreateBy(merchantId);
                        memberRule.setUpdateBy(merchantId);
                        memberRule.setActive(true);
                        return memberRule;
                    }).collect(Collectors.toList());
            MemberRuleServiceImpl proxy = (MemberRuleServiceImpl) AopContext.currentProxy();
            proxy.saveBatch(list);
            add = list.size();
        }
        log.info("{} 会员等级共删除 {} 条会员规则记录，新增 {} 条会员规则记录",memberLevelId,del,add);
        return true;
    }

    /**
     * 根据会员等级获取会员规则列表
     *
     * @param memberLevelIdList
     * @return
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<MemberRule> findByMemberLevel(Long merchantId,List<Long> memberLevelIdList) {
        if(memberLevelIdList == null || memberLevelIdList.isEmpty()){
            return Collections.emptyList();
        }
        QueryWrapper<MemberRule> queryWrapper = new QueryWrapper<MemberRule>()
                .eq(getShardingFieldKey(), merchantId)
                .in("member_level_id",memberLevelIdList)
                .and(wrapper -> wrapper.isNull("active").or().eq("active", true))
                .orderByAsc("member_level_id","benefit_classify");
        return list(queryWrapper);
    }

    /**
     * 根据会员等级删除对应的会员规则信息
     *
     *
     * @param merchantId
     * @param memberLevelId
     * @return
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public int removeByMemberLevel(Long merchantId, Long memberLevelId) {
        UpdateWrapper<MemberRule> wrapper = new UpdateWrapper<MemberRule>()
                .eq(getShardingFieldKey(), merchantId)
                .eq("member_level_id", memberLevelId);
        return getBaseMapper().delete(wrapper);
    }

    /**
     * 根据会员等级变化进行派权益或者扣权益
     *
     * @param member
     * @param memberLevelList
     * @param isUpgrade       是否为升级
     * @return
     */
//    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateBenefitOfLevel(Member member, List<MemberLevel> memberLevelList, boolean isUpgrade) {
        Long merchantId = member.getMerchantId();
        List<Long> memberLevelIdList = memberLevelList.stream()
                .map(MemberLevel::getId)
                .collect(Collectors.toList());
        List<MemberRule> memberRuleList = findByMemberLevel(merchantId, memberLevelIdList);
        log.info("{} 用户等级变化,需要处理权益-->{}", member.getUserId(), memberRuleList);
        if (CollectionUtils.isNotEmpty(memberRuleList)) {
            //更新会员的到期时间
            if (member.getMemberEndTime() != null) {
                //获取会员组时间
                MemberGroup memberGroup = memberGroupMapper.getMemberGroupById(member.getMerchantId(), member.getMemberGroupId());
                if (memberGroup != null) {
                    if (memberGroup.getMemberEffectiveTime() > 0) {
                        member.setMemberEndTime(DateUtils.addDays(new Date(), memberGroup.getMemberEffectiveTime()));
                    } else if (memberGroup.getMemberEffectiveTime() == -2) {
                        member.setMemberEndTime(memberGroup.getMemberEndTime());
                    }
                    //更新会员的有效期
                    if (log.isDebugEnabled()) {
                        log.debug("更新会员的有效期:{}", member);
                    }
                    memberMapper.updateMemberEndTimeById(merchantId, member.getId(), member.getMemberEndTime());
                }
            }
            if (isUpgrade) {
                incrementBenefit(member, memberRuleList);
            } else {
                log.warn("会员降级,member:{},memberRuleList:{}", member, memberRuleList);
                //降级赠送的权益不进行回退
//                decrementBenefit(member, memberRuleList);
            }
            return true;
        }
        return false;
    }

    /**
     * 根据当前等级，获取所有已经获得的会员规则权益
     * @param merchantId
     * @param memberLevelId
     * @return
     */
    @Override
    public List<MemberRule> findAllByMemberLevel(Long merchantId, Long memberLevelId) {
        return getBaseMapper().findAllByMemberLevel(merchantId,memberLevelId);
    }

    /**
     * 加权益
     * @param member
     * @param memberRuleList
     */
    private void incrementBenefit(Member member, List<MemberRule> memberRuleList) {
        //没有分组的权益
        String orderNo = Long.toString(snowflakeIdWorker.nextId());
        //合并不做有效期处理，因为可能存在同一种权益不同的有效期
        List<AccountBenefitAdjustDTO> accountBenefitAdjustList = memberRuleList.stream()
                .map(memberRule->{
                    AccountBenefitAdjustDTO accountBenefitAdjustDTO= new AccountBenefitAdjustDTO();
                    accountBenefitAdjustDTO.setAdjustType(AdjustTypeEnum.INCREMENT);
                    accountBenefitAdjustDTO.setUserId(member.getUserId());
                    accountBenefitAdjustDTO.setMerchantId(member.getMerchantId());
                    accountBenefitAdjustDTO.setResource(AccountBenefitResourceEnum.MEMBER_UPGRADE.getDescription());
                    accountBenefitAdjustDTO.setDescription("会员升级");
                    accountBenefitAdjustDTO.setMerchantUserId(member.getMerchantUserId());
                    accountBenefitAdjustDTO.setOrderNo(orderNo);
                    AccountRecordTypeEnum accountRecordType = getMemberRuleRecordType(accountBenefitAdjustDTO.getClassify());
                    accountBenefitAdjustDTO.setRecordType(accountRecordType.getCode());
                    //权益的
                    accountBenefitAdjustDTO.setClassify(memberRule.getBenefitClassify().intValue());
                    accountBenefitAdjustDTO.setAmount(memberRule.getBenefitValue());

                    if(Objects.nonNull(member.getMemberEndTime())){
                        //权益同步到会员有效期
                        accountBenefitAdjustDTO.setExpiryDateCategory(ExpiryDateCategoryEnum.TIME_INTERVAL);
                        accountBenefitAdjustDTO.setUpTime(DateUtil.format(member.getMemberStartTime(), "yyyy-MM-dd HH:mm:ss"));
                        accountBenefitAdjustDTO.setDownTime(DateUtil.format(member.getMemberEndTime(), "yyyy-MM-dd HH:mm:ss"));
                    }else{
                        accountBenefitAdjustDTO.setExpiryDateCategory(ExpiryDateCategoryEnum.NO_LIMIT);
                        log.warn("{} 会员升级派发无限期权益 {} ",member.getId(),accountBenefitAdjustDTO);
                    }
                    return accountBenefitAdjustDTO;
                }).collect(Collectors.toList());
        log.info("{} 商户的 {} 会员升级增加权益规则-->{}",member.getMerchantId(),member.getId(),accountBenefitAdjustList);
        accountBenefitAdjustList.forEach(accountBenefitAdjustDTO -> accountService.benefitAdd(accountBenefitAdjustDTO,true));
    }


    /**
     * 根据权益类型获取对应的派送权益记录类型
     * @param classify
     * @return
     */
    private AccountRecordTypeEnum getMemberRuleRecordType(Integer classify){
        return Arrays.stream(BenefitClassifyGroupEnum.values())
                .filter(group->group.getClassify().contains(classify))
                .findFirst()
                .map(group->{
                    if(BenefitClassifyGroupEnum.COINS.equals(group)){
                        return AccountRecordTypeEnum.GIFT_MEMBER_GIVE_COIN;
                    }else if(BenefitClassifyGroupEnum.MONEY.equals(group)){
                        return AccountRecordTypeEnum.GIFT_MEMBER_GIVE_BALANCE;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .orElse(AccountRecordTypeEnum.GIFT_MEMBER_GIVE_BALANCE);
    }
}
