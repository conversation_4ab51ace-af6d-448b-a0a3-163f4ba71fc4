package com.lyy.user.domain.user.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.lock.redis.RedisLock;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUnBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserQueryDTO;
import com.lyy.user.application.user.IMerchantUserService;
import com.lyy.user.application.user.ITagUserService;
import com.lyy.user.domain.user.repository.TagUserMapper;
import com.lyy.user.infrastructure.constants.RedisKey;
import com.lyy.user.infrastructure.repository.tag.TagRepository;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @description: 异步
 * @author: qgw
 * @date on 2021-06-23
 * @Version: 1.0
 */
@Slf4j
@Component
public class AsyncProcessHandler {

    @Value("${member.user.binding.size:1000}")
    private long bindingNumber;

    @Value("${member.user.unbinding.size:1000}")
    private long unbindingNumber;

    @Resource
    private TagUserMapper tagUserMapper;

    @Autowired
    private IMerchantUserService merchantUserService;

    @Autowired
    private ITagUserService tagUserService;
    @Autowired
    private TagRepository tagRepository;

    @Resource
    private RedisLock redisLock;

    @Async("taggingUserTaskExecutor")
    public void bindingUser(TagBindUserDTO dto) {
        try{
            log.debug("绑定标签-进入指按筛选条件给用户打标签:{}", dto);
            MerchantUserQueryDTO condition = new MerchantUserQueryDTO();
            BeanUtils.copyProperties(dto, condition);
            Page<MerchantUserListDTO> page = merchantUserService.merchantUserTotalByCondition(condition);
            log.debug("绑定标签-进入指按筛选条件给用户打标签");
            page.setSize(bindingNumber);
            dto.setChooseAll(false);
            for (int i = 1; ; i++) {
                page.setCurrent(i);
                long beginFirst = System.currentTimeMillis();
                List<MerchantUserListDTO> merchantUserListDTOS = merchantUserService.listMerchantUser(condition, page);
                log.debug("绑定标签-进入指按筛选条件给用户打标签,i={}，耗时时间:{}", i, System.currentTimeMillis() - beginFirst);
                if (CollectionUtils.isEmpty(merchantUserListDTOS)) {
                    log.warn("绑定标签-按筛选条件绑定用户,没有用户");
                    break;
                }
                List<Long> userIds = merchantUserListDTOS.stream()
                        .map(MerchantUserListDTO::getId)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(dto.getNotHandleUserIds())) {
                    userIds.removeAll(dto.getNotHandleUserIds());
                }
                if (CollectionUtils.isEmpty(userIds)) {
                    log.info("绑定标签-没有用户了,i={}", i);
                    break;
                }
                dto.setUserIds(userIds);
                long begin = System.currentTimeMillis();
                tagUserService.bindUser(dto);
                log.debug("绑定标签-进入指按筛选条件给用户打标签,i={}，耗时时间:{}", i, System.currentTimeMillis() - begin);
            }
        } finally {
            String lockKey = RedisKey.TAG_USER_All_BINDING_LOCK + "merchantId:" + dto.getMerchantId();
            redisLock.unlock(lockKey, "Y");
            log.debug("绑定标签-清除redis锁,lockKey：{}", lockKey);
        }
    }

    @Async("taggingUserTaskExecutor")
    public void unBindingUser(TagUnBindUserDTO dto) {
        try{
            log.info("解绑标签-进入按筛选条件解绑用户:{}", dto);
            TagUserQueryDTO condition = new TagUserQueryDTO();
            BeanUtils.copyProperties(dto, condition);
            boolean queryIdFlag = checkSearchNameIsNumeric(dto.getName());
            dto.setChooseAll(false);
            for (int i = 1; ; i++) {
                long beginFirst = System.currentTimeMillis();
                Long[] tagIds =dto.getTagIds().toArray(new Long[0]);
                List<TagUserInfoDTO> list = tagRepository.listTagUserInfoByTagIds(condition, queryIdFlag, tagIds, dto.getNotHandleUserIds(), 0L, unbindingNumber);
                log.debug("解绑标签-按筛选条件解绑用户,单次用户数量,i= {}，耗时时间:{}", i,System.currentTimeMillis()- beginFirst);
                if (CollectionUtils.isEmpty(list)) {
                    log.warn("解绑标签-按筛选条件解绑用户,没有用户,tagIds:{}", dto.getTagIds());
                    break;
                }
                List<Long> userIds = list.stream()
                        .map(TagUserInfoDTO::getBusinessUserId)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(dto.getNotHandleUserIds())) {
                    userIds.removeAll(dto.getNotHandleUserIds());
                }
                dto.setUserIds(userIds);
                long begin = System.currentTimeMillis();
                tagUserService.unBindUser(dto);
                log.debug("解绑标签-进入指按筛选条件解绑用户,i={}，用户数:{},耗时时间:{}", i,userIds.size(),System.currentTimeMillis()-begin);
            }
        } finally {
            String lockKey = RedisKey.TAG_USER_ALL_UNBINDING_LOCK + "merchantId:" + dto.getMerchantId();
            redisLock.unlock(lockKey, "Y");
            log.debug("解绑标签-清除redis锁,lockKey：{}", lockKey);
        }
    }


    private boolean checkSearchNameIsNumeric(String searchName) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(searchName)) {
            if (org.apache.commons.lang3.StringUtils.isNumeric(searchName)) {
                return true;
            }
        }
        return false;
    }

}
