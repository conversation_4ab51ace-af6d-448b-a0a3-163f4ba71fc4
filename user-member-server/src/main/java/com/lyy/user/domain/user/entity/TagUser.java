package com.lyy.user.domain.user.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * 用户标签
 */
@ToString
@Getter
@Setter
@TableName(value = "um_tag_user")
public class TagUser {

    @TableId
    private Long id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签编码
     */
    private String code;

    /**
     * 商户ID或平台ID
     */
    private Long merchantId;

    /**
     * 标签类型，1:自动, 2:手动
     */
    private Integer category;

    /**
     * 备注
     */
    private String description;

    /**
     * 是否可用，true:是，false：否
     */
    @TableField(value = "is_active")
    private Boolean active;


    /**
     * 0 平台用户标签
     * 1 商户用户标签
     *
     */
    private Integer tagType;

    /**
     * 标签的业务类型
     * @see com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum
     */
    private Integer businessType;

    /**
     * 记录是否已删除，1:是，0：否
     */
    private Integer state;

    private Date createTime;

    private Date updateTime;

    private Long createdby;

    private Long updatedby;

}