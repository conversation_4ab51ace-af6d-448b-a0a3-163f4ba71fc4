package com.lyy.user.domain.member.lifting;

import com.lyy.user.application.member.IMemberRuleService;
import com.lyy.user.application.member.IMemberService;
import com.lyy.user.domain.member.dto.MemberLiftingRuleRecordJsonDTO;
import com.lyy.user.domain.member.dto.MemberLiftingStrategyResultDTO;
import com.lyy.user.domain.member.entity.Member;
import com.lyy.user.domain.member.entity.MemberLevel;
import com.lyy.user.domain.member.entity.MemberLifting;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 降级策略
 * <AUTHOR>
 * @className: LiftingStrategyDemoting
 * @date 2021/4/6
 */
@Component("liftingStrategy:2")
@Slf4j
public class LiftingStrategyDemoting extends AbstractLiftingStrategy{
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IMemberRuleService memberRuleService;

    /**
     * 处理成功的记录
     *
     * @param memberLiftingStrategyResultDTO
     * @return
     */
    @Override
    public int handlerSuccess(MemberLiftingStrategyResultDTO memberLiftingStrategyResultDTO) {
        MemberLifting memberLifting = memberLiftingStrategyResultDTO.getMemberLifting();
        List<MemberLiftingRuleRecordJsonDTO> recordJsonList = MemberLiftingRuleRecordJsonDTO.exchangeToList(memberLiftingStrategyResultDTO.getMemberLiftingRuleMap());
        log.info("需要对 {} 用户的 {} 会员组进行降级 {} 成长值处理,记录数据为 {}", memberLiftingStrategyResultDTO.getMember().getUserId(),
                memberLifting.getMemberGroupId(), memberLifting.getGrowValue(), recordJsonList);
        String resources = MemberLiftingRuleRecordJsonDTO.toMapString(recordJsonList);
        Long growValue = calculateGrowValue(memberLifting, memberLiftingStrategyResultDTO.getMemberLiftingRuleMap());
        List<MemberLevel> memberLevelList = memberService.updateGrowValueAndLevel(memberLiftingStrategyResultDTO.getMember(), memberLifting, resources, growValue, memberLifting.getName());
        if (!memberLevelList.isEmpty()) {
            Member member = memberLiftingStrategyResultDTO.getMember();
            // 降级权益处理
            log.info("{} 用户在 {} 会员组上的会员信息发送降级变化为 -->{}", member.getUserId(), memberLifting.getMemberGroupId(), memberLevelList);
            memberRuleService.updateBenefitOfLevel(member, memberLevelList, false);

        }

        //处理降级功能
        return super.handlerSuccess(memberLiftingStrategyResultDTO);
    }
}
