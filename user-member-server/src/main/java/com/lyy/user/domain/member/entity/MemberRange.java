package com.lyy.user.domain.member.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 会员组范围
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("um_member_range")
public class MemberRange implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 商户ID
     */
    @TableField("merchant_id")
    private Long merchantId;

    /**
     * 会员组ID
     */
    @TableField("member_group_id")
    private Long memberGroupId;

    /**
     * 适用类型(1 品类,2 场地,3 设备,4 商品,5 非全选场地(忽略))
     */
    @TableField("applicable")
    private Short applicable;

    /**
     * 关联关系ID
     */
    @TableField("associated_id")
    private Long associatedId;

    /**
     * 是否有效 true 有效,false 无效
     */
    @TableField("active")
    private Boolean active;

}
