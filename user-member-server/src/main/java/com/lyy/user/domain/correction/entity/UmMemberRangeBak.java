package com.lyy.user.domain.correction.entity;

import java.io.Serializable;
import lombok.Data;

/**
 * 会员组范围
 * @TableName um_member_range_bak
 */
@Data
public class UmMemberRangeBak implements Serializable {

    private static final long serialVersionUID = -63065033846718596L;
    /**
     *
     */
    private Long id;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 会员组ID
     */
    private Long memberGroupId;

    /**
     * 适用类型(1 品类,2 场地,3 设备,4 商品,5 非全选场地(忽略))
     */
    private Integer applicable;

    /**
     * 关联关系ID
     */
    private Long associatedId;

    /**
     * 是否有效 true 有效,false 无效
     */
    private Boolean active;

}