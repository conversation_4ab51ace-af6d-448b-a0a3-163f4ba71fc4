package com.lyy.user.domain.statistics.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lyy.user.account.infrastructure.statistics.dto.StoredStatisticsNumInfoDTO;
import com.lyy.user.domain.statistics.entity.SmallVenueStoredStatistics;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

public interface SmallVenueStoredStatisticsMapper extends BaseMapper<SmallVenueStoredStatistics> {


    /**
     * 查询用户统计
     * @param userId
     * @param merchantUserId
     * @param merchantId
     * @return
     */
    List<SmallVenueStoredStatistics> selectStoredStatisticsByUserId(@Param("userId") Long userId,
                                                                    @Param("merchantUserId") Long merchantUserId,
                                                                    @Param("merchantId") Long merchantId);

    /**
     * 查询用户商家自定义权益最大值前N行
     * @param merchantId
     * @param merchantUserId
     * @param rowNumber
     * @return
     */
    List<SmallVenueStoredStatistics> selectStoredStatisticsMaxBalanceByMerchantUserId(
            @Param("merchantUserId") Long merchantUserId,
            @Param("merchantId") Long merchantId,
            @Param("rowNumber") int rowNumber
            );
    /**
     * 查询用户商家自定义权益最大值前N行
     * @param merchantUserIds
     * @param merchantId
     * @param rowNumber
     * @return
     */
    List<SmallVenueStoredStatistics> selectStoredStatisticsByMerchantUserIdsAndRowNumber(
            @Param("merchantUserIds") List<Long>  merchantUserIds,
            @Param("merchantId") Long merchantId,
            @Param("rowNumber") int rowNumber
            );

    /**
     * 查询剩余张数（次卡/年卡）
     *
     * @param userId
     * @param merchantId
     * @param merchantBenefitClassifyIds
     * @return
     */
    List<StoredStatisticsNumInfoDTO> selectStatisticsByMerchantBenefitClassifyIds(@Param("userId") Long userId,
                                                                                  @Param("merchantId") Long merchantId,
                                                                                  @Param("merchantBenefitClassifyIds") Set<Long> merchantBenefitClassifyIds);

    /**
     * 更新统计的用户id信息
     * @param userId
     * @param merchantUserId
     * @param merchantId
     * @param oldUserId
     * @param oldMerchantUserId
     * @return
     */
    Integer updateStatisticsIdInfo(@Param("userId") Long userId,
                                   @Param("merchantUserId") Long merchantUserId,
                                   @Param("merchantId") Long merchantId,
                                   @Param("oldUserId") Long oldUserId,
                                   @Param("oldMerchantUserId") Long oldMerchantUserId);

    /**
     * 更新统计的用户信息
     * @param userId
     * @param merchantUserId
     * @param merchantId
     * @param oldUserId
     * @param oldMerchantUserId
     * @param merchantBenefitClassifyIds
     * @return
     */
    Integer updateStatisticsIdInfoByMerchantBenefitClassifyIds(@Param("userId") Long userId,
                                   @Param("merchantUserId") Long merchantUserId,
                                   @Param("merchantId") Long merchantId,
                                   @Param("oldUserId") Long oldUserId,
                                   @Param("oldMerchantUserId") Long oldMerchantUserId,
                                   @Param("merchantBenefitClassifyIds") Set<Long> merchantBenefitClassifyIds);

    /**
     * 更新统计的用户信息
     * @param userId
     * @param merchantUserId
     * @param merchantId
     * @param merchantBenefitClassifyId
     * @param oldMerchantBenefitClassify
     * @return
     */
    Integer updateStatisticsIdInfoByMerchantBenefitClassifyId(@Param("userId") Long userId,
                                                              @Param("merchantUserId") Long merchantUserId,
                                                              @Param("merchantId") Long merchantId,
                                                              @Param("merchantBenefitClassifyId") Long merchantBenefitClassifyId,
                                                              @Param("oldMerchantBenefitClassify") SmallVenueStoredStatistics oldMerchantBenefitClassify);

}