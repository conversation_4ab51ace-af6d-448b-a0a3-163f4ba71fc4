package com.lyy.user.domain.user.service;

import com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.account.infrastructure.constant.UserSourceEnum;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingMerchantUserLinkDTO;
import com.lyy.user.application.user.ITagUserService;
import com.lyy.user.domain.user.dto.TagUserParamDTO;
import com.lyy.user.infrastructure.constants.TradeTypeGroupEnum;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @description: 创建商家用户异步自动打标签
 * @author: qgw
 * @date on 2021-05-25.
 * @Version: 1.0
 */
@Slf4j
@Component
public class MerchantAutoTagAsyncHandler {

    @Autowired
    @Lazy
    private ITagUserService tagUserService;

    @Async("taggingUserTaskExecutor")
    public void autoCreateTag(TagUserParamDTO dto) {
        log.debug("自动创建标签,dto:{}", dto);
        String tagTypeName = null;
        Integer tradeType = dto.getTradeType();
        if (tradeType != null) {
            tagTypeName = TradeTypeGroupEnum.findGroupPayType(tradeType);
        }
        if (StringUtils.isBlank(tagTypeName)) {
            tagTypeName = chooseTagTypeName(dto.getUserType());
        }
        List<TaggingMerchantUserLinkDTO> merchantTagList = new ArrayList<>();
        if (StringUtils.isNotBlank(tagTypeName)) {

            //商家支付方式
            TaggingMerchantUserLinkDTO merchantPayWayTag = new TaggingMerchantUserLinkDTO();
            merchantPayWayTag.setName(tagTypeName);
            merchantPayWayTag.setBusinessType(TagBusinessTypeEnum.PAY_TYPE.getStatus());
            merchantPayWayTag.setUserId(dto.getMerchantUserId());
            merchantPayWayTag.setMerchantId(dto.getMerchantId());
            merchantPayWayTag.setOperatorId(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID);
            merchantTagList.add(merchantPayWayTag);
        }

        if (StringUtils.isNotBlank(dto.getGender())) {
            String gender = dto.getGender().trim();
            //商家性别标签
            TaggingMerchantUserLinkDTO merchantSexTag = new TaggingMerchantUserLinkDTO();
            merchantSexTag.setName(gender);
            merchantSexTag.setBusinessType(TagBusinessTypeEnum.SEX.getStatus());
            merchantSexTag.setUserId(dto.getMerchantUserId());
            merchantSexTag.setMerchantId(dto.getMerchantId());
            merchantSexTag.setOperatorId(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID);
            merchantTagList.add(merchantSexTag);
        }

        //商户场地标签 //场地类型标签
        if (StringUtils.isNotBlank(dto.getStoreName())) {
            TaggingMerchantUserLinkDTO merchantTag = new TaggingMerchantUserLinkDTO();
            merchantTag.setName(dto.getStoreName().trim());
            merchantTag.setBusinessType(TagBusinessTypeEnum.GROUP_NAME.getStatus());
            merchantTag.setUserId(dto.getMerchantUserId());
            merchantTag.setMerchantId(dto.getMerchantId());
            merchantTag.setOperatorId(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID);
            merchantTagList.add(merchantTag);
        }

        //设备类型标签
        if (StringUtils.isNotBlank(dto.getEquipmentTypeName())) {
            TaggingMerchantUserLinkDTO equipTag = new TaggingMerchantUserLinkDTO();
            equipTag.setName(dto.getEquipmentTypeName().trim());
            equipTag.setBusinessType(TagBusinessTypeEnum.EQUIPMENT.getStatus());
            equipTag.setUserId(dto.getMerchantUserId());
            equipTag.setMerchantId(dto.getMerchantId());
            equipTag.setOperatorId(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID);
            merchantTagList.add(equipTag);
        }
        if (!CollectionUtils.isEmpty(merchantTagList)) {
            tagUserService.taggingMerchant(merchantTagList);
        }

    }





    /**
     * 根据用户类型返回用户标签名字
     * 用户类别
     * W：微信
     * A：支付宝
     * J：京东
     * U：云闪付
     * O：其他
     */
    private String chooseTagTypeName(String userType) {
        if (StringUtils.isBlank(userType)) {
            return null;
        }
        if (UserSourceEnum.WE_CHAT_WEB.getUserType().equals(userType)) {
            return "微信";
        }
        if (UserSourceEnum.ALI_WEB.getUserType().equals(userType)) {
            return "支付宝";
        }
        if (UserSourceEnum.JD.getUserType().equals(userType)) {
            return "京东";
        }
        if (UserSourceEnum.UNION.getUserType().equals(userType)) {
            return "云闪付";
        }
        if (UserSourceEnum.OTHER.getUserType().equals(userType)) {
            return null;
        }
        return null;
    }
}
