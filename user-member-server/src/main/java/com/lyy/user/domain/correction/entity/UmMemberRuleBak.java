package com.lyy.user.domain.correction.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 会员规则
 * @TableName um_member_rule_bak
 */
@Data
public class UmMemberRuleBak implements Serializable {

    private static final long serialVersionUID = 4898918839088770835L;
    /**
     * 
     */
    private Long id;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 会员等级
     */
    private Long memberLevelId;

    /**
     * 规则类型--对应账户权益类型
     */
    private Integer benefitClassify;

    /**
     * 权益值
     */
    private BigDecimal benefitValue;

    /**
     * 权益单位
     */
    private String benefitUnit;

    /**
     * 有效期类型（0、无限期，1、日期区间可用，2、单日时间区间可用）
     */
    private Integer expiryDateCategory;

    /**
     * 上线时间
     */
    private String upTime;

    /**
     * 下线时间
     */
    private String downTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 是否有效 true 有效,false 无效
     */
    private Boolean active;

}