package com.lyy.user.domain.account.dto;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @ClassName: AccountInitDTO
 * @description: 账户初始化实体
 * @author: pengkun
 * @date: 2022/01/14
 **/
@Setter
@Getter
@ToString
public class AccountInitDTO {
    /**
     * 商户用户id
     */
    private Long merchantUserId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 商户id
     */
    private Long merchantId;
    /**
     * 类型
     */
    private Integer classify;
    /**
     * 卡号
     */
    private String cardNo;
    /**
     * 门店id
     */
    private Long storeId;
    /**
     * 押金
     */
    private BigDecimal deposit;
    /**
     * 是否是默认账户
     */
    private Boolean defaultFlag;
    /**
     * 操作人Id
     */
    private Long operatorId;
    /**
     * 描述信息
     */
    private String description;
    /**
     * 总数
     */
    private BigDecimal total;
    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 会员卡账户过期时间
     */
    private Date downTime;

    /**
     * 过期类型
     */
    private Integer expiredType;
}
