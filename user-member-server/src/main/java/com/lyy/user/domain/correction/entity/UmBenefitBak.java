package com.lyy.user.domain.correction.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 权益表
 * @TableName um_benefit_bak
 */
@Data
public class UmBenefitBak implements Serializable {

    private static final long serialVersionUID = -2778938484410789993L;

    /**
     * 
     */
    private Long id;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 标题

     */
    private String title;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 权益编码

     */
    private String code;

    /**
     * 权益分类

     */
    private Integer classify;

    /**
     * 权益数量
     */
    private Long benefitCount;

    /**
     * 可用有效期类型 0、无限期 1、日期区间可用 2、单日时间区间可用
     */
    private Integer expiryDateCategory;

    /**
     * 上线时间
     */
    private String upTime;

    /**
     * 下线时间
     */
    private String downTime;

    /**
     * 可见有效期类型
0、无限期  1、日期区间可用 2、单日时间区间可用
     */
    private Integer showDateCategory;

    /**
     * 可见上线时间
     */
    private String showUpTime;

    /**
     * 可见下线时间
     */
    private String showDownTime;

    /**
     * 是否可用
     */
    @TableField(value = "is_active")
    private Boolean active;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

}