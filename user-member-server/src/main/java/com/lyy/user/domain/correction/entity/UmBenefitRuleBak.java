package com.lyy.user.domain.correction.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 权益规则表
 * @TableName um_benefit_rule_bak
 */
@Data
public class UmBenefitRuleBak implements Serializable {

    private static final long serialVersionUID = 7193663938555408003L;
    /**
     * 
     */
    private Long id;

    /**
     * 
     */
    private Long benefitId;

    /**
     * 生成方式 1、售卖 2、赠送 3、活动 4、标签 5、任务
     */
    private Integer generateType;

    /**
     * 生成权益 1、充值余额 2、充值币 3、商家赠送余额 4、商家赠送币
5、平台赠送余额 6、平台赠送币 7、设备启动次数(时间) 8、项目次数 9、设备使用时长 10、积分 11、成长值 12、电量 13、水量
     */
    private Integer benefitClassify;

    /**
     * 
     */
    private Integer ruleValue;

    /**
     * 
     */
    private String ruleUnit;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

    /**
     * 商户Id
     */
    private Long merchantId;

}