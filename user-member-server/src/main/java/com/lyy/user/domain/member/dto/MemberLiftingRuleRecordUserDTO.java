package com.lyy.user.domain.member.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 用户的触发规则记录
 * <AUTHOR>
 * @className: MemberLiftingRuleRecordUserDTO
 * @date 2021/4/14
 */
@Data
public class MemberLiftingRuleRecordUserDTO {

    /**
     * 会员id
     */
    private Long memberId;
    /**
     * 规则id
     */
    private Long memberLiftingRuleId;
    /**
     * 统计次数
     */
    private Integer countNum;

    /**
     * 统计范围值数据的和
     */
    private BigDecimal sumRangeValue;
}
