package com.lyy.user.domain.user.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserQueryDTO;
import com.lyy.user.domain.user.dto.MerchantUserTagDTO;
import com.lyy.user.domain.user.entity.TagUser;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 标签用户查询Mapper
 * <AUTHOR>
 */
public interface TagUserMapper extends BaseMapper<TagUser> {

    List<TagUser> selectListByMerchantUser(@Param("merchantUserId") Long merchantUserId,@Param("merchantId") Long merchantId, @Param("tagType") Integer tagType,@Param("businessTypeList") List<Integer> businessTypeList);

    List<MerchantUserTagDTO> selectNewListByMerchantUser(@Param("merchantUserIds") List<Long> merchantUserIds,
                                                         @Param("merchantId") Long merchantId,
                                                         @Param("tagType") Integer tagType,
                                                         @Param("businessTypeList") List<Integer> businessTypeList);

    int updateTagUser(@Param("tagUser") TagUser tagUser);

    void changeTagStatus(@Param("active") Boolean active, @Param("state") Integer state, @Param("ids") List<Long> ids, @Param("merchantId") Long merchantId, @Param("operatorId") Long operatorId);

    void deleteByTagId(@Param("tagId")  Long tagId,@Param("merchantId") Long merchantId);

    List<TagUser> selectBatchIds(@Param("tagId") List<Long> tagIds, @Param("merchantId") Long merchantId);

    Long countListTagByUser(@Param("dto") TagUserQueryDTO dto);


    List<TagUserListDTO> listTagByUser(@Param("dto") TagUserQueryDTO dto,
                                       @Param("start") Long start ,
                                       @Param("pageSize") Long pageSize);


    /**
     *
     * @param dto
     * @param queryIdFlag
     * @return
     */
    Long countListTagUserInfoByTagIds(@Param("dto") TagUserQueryDTO dto,
                                      @Param("queryIdFlag") Boolean queryIdFlag,
                                     @Param("tagIds") Long[] tagIds
    );



    List<TagUserInfoDTO> listTagUserInfoByTagIds(@Param("dto") TagUserQueryDTO dto,
                                                 @Param("queryIdFlag") Boolean queryIdFlag,
                                                 @Param("tagIds") Long[] tagIds,
                                                 @Param("start") Long start ,
                                                 @Param("pageSize") Long pageSize);

    List<TagUserInfoDTO> listTagUserInfoByTagIdsV2(@Param("dto") TagUserQueryDTO dto,
            @Param("queryIdFlag") Boolean queryIdFlag,
            @Param("tagIds") Long[] tagIds,
            @Param("notHandleUserIds")  List<Long> notHandleUserIds,
            @Param("start") Long start,
            @Param("pageSize") Long pageSize);

}