package com.lyy.user.domain.benefit.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 权益表
 *
 * <AUTHOR>
 * @create 2021/3/30 16:18
 */
@ToString
@Getter
@Setter
@TableName(value = "um_benefit")
public class Benefit {

    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private Long id;

    /**
     *  商户ID
     */
    @TableField(value="merchant_id")
    private Long merchantId;


    @TableField(value="title")
    private String title;


    /**
     * 子标题
     */
    @TableField(value= "sub_title")
    private String subTitle;

    /**
     * 权益编码
     */
    private String code;

    /**
     *  权益类型
     */
    private Integer classify;

    /**
     * 权益数量
     */
    @TableField(value="benefit_count")
    private Long benefitCount;


    /**
     * 过期时间类型
     * @see com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum
     */
    @TableField(value="expiry_date_category")
    private Integer expiryDateCategory;

    /**
     * 上线时间
     */
    @TableField(value = "up_time")
    private String upTime;

    /**
     * 下线时间
     */
    @TableField(value = "down_time")
    private String downTime;

    /**
     * 可见有效期类型
     */
    @TableField(value="show_date_category")
    private Integer showDateCategory;

    /**
     * 显示上架时间
     */
    @TableField(value ="show_up_time")
    private String  showUpTime;

    /**
     * 显示下架时间
     */
    @TableField(value="show_down_time")
    private String showDownTime;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 数据是否活跃可用
     */
    @TableField(value = "is_active")
    private Boolean active;
}
