package com.lyy.user.application.user;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lyy.user.account.infrastructure.user.dto.tag.TagBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserNumberDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserNumberParam;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagSimpleInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagSimpleInfoParam;
import com.lyy.user.account.infrastructure.user.dto.tag.TagStatusDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUnBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserDetailDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserSaveDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingMerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingMerchantUserLinkDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.UpdateSpecificBusinessTagsParam;
import com.lyy.user.domain.user.entity.TagUser;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
public interface ITagUserService extends IService<TagUser> {

    /**
     * 查询用户标签（分页）
     * @return
     */
    Page<TagUserListDTO> list(TagUserQueryDTO param);


    /**
     * 列出所有标签
     *
     * @param param param
     * @return {@link List }<{@link TagUserListDTO }>
     */
    List<TagUserListDTO> listAllTag(TagUserQueryDTO param);

    /**
     * 根据用户查所属标签
     * @param dto
     * @return
     */
    Page<TagUserListDTO> listTagByUser(TagUserQueryDTO dto);

    /**
     * 根据条件查标签的用户
     * @param dto
     * @return
     */
    TagUserDetailDTO findByTagId(TagUserQueryDTO dto);


    /**
     * 根据标签ID及商家ID获取对应用户数
     * @param dto
     * @return
     */
    List<TagCountUserNumberDTO> findMemberCountByTagIds(TagCountUserNumberParam dto);

    /**
     * 根据条件查标签的用户数量
     * @param dto
     * @return
     */
    Long countFindByTagId(TagCountUserQueryDTO dto);



    /**
     * 新增用户标签
     * @return
     * @return
     */
    Long saveOrUpdateTagUser(TagUserSaveDTO tagUserSaveDTO);


    /**
     * 变更标签状态
     * @param dto
     * @return
     */
    Boolean changeTagStatus(TagStatusDTO dto);


    /**
     * 标签绑定用户
     * @param dto
     * @return
     */
    Boolean bindUser(TagBindUserDTO dto);


    /**
     * 标签解绑用户
     * @param dto
     * @return
     */
    Boolean unBindUser(TagUnBindUserDTO dto);


    /**
     * 修改场地标签或者设备类型的标签
     * @param dto
     * @return
     */
    Long updateTagName(TagDTO dto);

    /**
     * 打标签
     * @param dto
     * @return
     */
    void taggingUser(TaggingMerchantUserDTO dto);

    void taggingUserWithIdempotent(String keyNo, TaggingMerchantUserDTO dto);


    /**
     * 打商家标签
     * @param list
     * @return
     */
    Boolean taggingMerchant(List<TaggingMerchantUserLinkDTO> list);

    void syncMerchantTag(List<Long> merchantIds, List<Long> tagIds);


    /**
     * 批量更新会员所属特定类型标签接口(清空旧的，只保留新的标签)
     * @param dto
     * @return
     */
    Boolean batchUpdateSpecificBusinessTagsByUserId(UpdateSpecificBusinessTagsParam dto);

    /**
     *
     * @param param
     * @return
     */
    List<TagSimpleInfoDTO> tagSimpleInfoList(TagSimpleInfoParam param);
}
