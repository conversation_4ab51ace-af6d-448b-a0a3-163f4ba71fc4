package com.lyy.user.application.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneDTO;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneQueryParam;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneRecordDTO;
import com.lyy.user.domain.user.entity.PlatformUserPhone;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
public interface IPlatformUserPhoneService extends IService<PlatformUserPhone> {


    /**
     * 保存或修改平台用户电话
     * @param dto
     * @return
     */
    Boolean saveOrUpdatePlatformUserPhone(PlatformUserPhoneDTO dto);

    /**
     * 获取平台用户电话
     * @return
     */
    List<PlatformUserPhoneRecordDTO> list(PlatformUserPhoneQueryParam param);
}
