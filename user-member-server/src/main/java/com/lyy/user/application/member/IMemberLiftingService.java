package com.lyy.user.application.member;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.member.dto.MemberLiftingSaveDTO;
import com.lyy.user.domain.member.entity.MemberLifting;
import com.lyy.user.infrastructure.base.IBaseService;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 会员升级策略 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
public interface IMemberLiftingService extends IBaseService<MemberLifting> {

    /**
     * 保存或更新升级策略信息
     * @param memberLiftingSaveDTO
     * @return
     */
    Long saveOrUpdate(MemberLiftingSaveDTO memberLiftingSaveDTO);

    /**
     * 根据会员组获取对应的会员策略
     *
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    List<MemberLifting> findByMemberGroup(Long merchantId, Long memberGroupId);

    /**
     * 根据id获取详情信息
     *
     * @param merchantId
     * @param memberLiftingId
     * @return
     */
    MemberLiftingSaveDTO getInfoById(Long merchantId, Long memberLiftingId);

    /**
     * 根据会员组信息，获取对应的升级策略
     *
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    List<MemberLiftingSaveDTO> findInfoByMemberGroup(Long merchantId, Long memberGroupId);

    /**
     * 根据策略id删除对应的数据
     *
     * @param merchantId
     * @param memberLiftingIds
     * @return
     */
    int removeByLiftingIds(Long merchantId, Collection<Long> memberLiftingIds);

    /**
     * 分页获取存在不满足条件的升级规则内容
     * @param page
     * @param merchantId
     * @return
     */
    Page<MemberLifting> findOfJudgeFalse(Page page, Long merchantId);

    /**
     * 批量更新会员组的升级策略信息
     *
     *
     * @param merchantId
     * @param memberGroupId
     * @param memberLiftingList
     * @return
     */
    int saveOrUpdateBatchMemberLifting(Long merchantId, Long memberGroupId, List<MemberLiftingSaveDTO> memberLiftingList);


    /**
     * 检查是否为默认的升级任务
     * @param memberLifting
     * @return
     */
    boolean checkLiftingDefault(MemberLiftingSaveDTO memberLifting);


    /**
     * 获取会员组对应的保存策略信息
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    List<MemberLiftingSaveDTO> findSaveInfoByGroup(Long merchantId, Long memberGroupId);

    /**
     * 分页获取降级策略
     * @param page
     * @param merchantId
     * @return
     */
    Page<MemberLifting> findLiftingDemoting(Page page, Long merchantId);

    /**
     * 获取场地的升降级策略
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    MemberLifting getSmallVenueLiftingInfoByGroup(Long merchantId, Long memberGroupId);




}
