package com.lyy.user.application.report;

import com.lyy.user.account.infrastructure.common.DataList;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberCardReportDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberCardReportQueryDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberCardStoreValueReportDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberReportDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberReportQueryDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberStoreValueRecordDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberStoreValueRecordQueryDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberStoreValueReportDto;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date : 2022-12-21 11:27
 **/
public interface SmallMemberReportService {

    /**
     * 会员报表
     * @param memberReportQueryDto
     * @return
     */
    DataList<SmallVenueMemberReportDto> queryUserMemberReport(SmallVenueMemberReportQueryDto memberReportQueryDto);

    /**
     * 会员报表合计
     * @param smallVenueMemberReportQueryDto
     * @return
     */
    SmallVenueMemberStoreValueReportDto totalQueryMemberStoreValueReport(SmallVenueMemberReportQueryDto smallVenueMemberReportQueryDto);

    /**
     * 会员卡报表
     * @param smallVenueMemberCardReportQueryDto
     * @return
     */
    DataList<SmallVenueMemberCardReportDto> queryUserMemberCardReport(SmallVenueMemberCardReportQueryDto smallVenueMemberCardReportQueryDto);

    /**
     * 会员卡报表合计
     * @param smallVenueMemberCardReportQueryDto
     * @return
     */
    SmallVenueMemberCardStoreValueReportDto totalQueryUserMemberCardReport(SmallVenueMemberCardReportQueryDto smallVenueMemberCardReportQueryDto);

    /**
     * 会员储值变更记录
     * @param smallVenueMemberStoreValueRecordQueryDto
     * @return
     */
    DataList<SmallVenueMemberStoreValueRecordDto> pageQueryMemberStoreValueReport(SmallVenueMemberStoreValueRecordQueryDto smallVenueMemberStoreValueRecordQueryDto);

    /**
     * 会员储值变更记录合计
     * @param smallVenueMemberStoreValueRecordQueryDto
     * @return
     */
    BigDecimal totalQueryMemberStoreValueChangeReport(SmallVenueMemberStoreValueRecordQueryDto smallVenueMemberStoreValueRecordQueryDto);

    /**
     * 会员报表v2
     * @param memberReportQueryDto
     * @return
     */
    DataList<SmallVenueMemberReportDto> queryUserMemberReportV2(SmallVenueMemberReportQueryDto memberReportQueryDto);
}
