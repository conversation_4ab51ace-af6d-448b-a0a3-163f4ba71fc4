package com.lyy.user.application.statistics;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantStatisticsRecordDTO;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserRankConditionDTO;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserRankRecordDTO;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserStatisticsDailyDTO;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserStatisticsListDTO;
import com.lyy.user.account.infrastructure.statistics.dto.StatisticsUserQueryDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsConditionDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsRecordDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsUpdateDTO;
import java.util.List;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/19 14:44
 */
public interface StatisticsService {

    void updateStatistics(UserStatisticsUpdateDTO param);

    void updateStatisticsWithUserInit(UserStatisticsUpdateDTO param);

    void updateStatisticsWithIdempotent(UserStatisticsUpdateDTO param, boolean initUser);

    /**
     *
     * @param param
     * @return
     */
    UserStatisticsRecordDTO find(UserStatisticsConditionDTO param);

    /**
     * 获取商户统计数据
     * @param merchantId 商户ID
     * @return
     */
    MerchantStatisticsRecordDTO getMerchantStatistics(Long merchantId);

    /**
     * 初始化指定商户下的 商户用户统计数据
     * @param merchantId    商户id
     * @param userId    用户id
     * @param merchantUserId 商户用户id
     */
    void initStatistics(Long merchantId, Long userId, Long merchantUserId);

    Page<MerchantUserStatisticsListDTO> queryStatisticsUserList(StatisticsUserQueryDTO statisticsUserQueryDTO);

    /**
     * 获取用户当日统计
     * @param param
     * @return
     */
    MerchantUserStatisticsDailyDTO getUserCurrentDateStatistics(UserStatisticsConditionDTO param);

    /**
     * 获取商家用户充值排行榜
     * @param condition
     * @return
     */
    List<MerchantUserRankRecordDTO> getRechargeRank(MerchantUserRankConditionDTO condition);
}
