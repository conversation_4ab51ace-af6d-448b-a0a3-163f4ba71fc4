package com.lyy.user.application.user;

import com.lyy.user.account.infrastructure.user.dto.MerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.UserAppDTO;
import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;

import java.util.List;

/**
 * @ClassName: IUserService
 * @description: 平台用户
 * @author: pengkun
 * @date: 2021/04/01
 **/
public interface IUserService {

    /**
     * 根据平台用户id和商户id获取用户信息
     * @param userId    平台用户Id
     * @param merchantId    商户Id
     * @return
     */
    UserInfoDTO getUserInfoByUserIdAndMerchantId(Long userId,Long merchantId,Boolean initStatisticsAysn);


    /**
     * 根据平台用户id和商户id获取用户信息(若用户不存在，创建用户不处理标签)
     * @param userId
     * @param merchantId
     * @return
     */
    UserInfoDTO getUserInfoNoTagByUserIdAndMerchantId(Long userId,Long merchantId);

    /**
     * 根据平台用户id,商户用户id,商户id获取用户信息
     * @param userId    平台用户id
     * @param merchantUserId    商户用户id
     * @param merchantId    商户id
     * @return
     */
    UserInfoDTO getUserInfoByUserIdAndMerchantUserId(Long userId,Long merchantUserId,Long merchantId);

    /**
     * 根据openId或unionId获取用户信息
     * @param openId    openId
     * @param unionId   unionId
     * @param merchantId 商户id
     * @return
     */
    UserInfoDTO getUserInfoByOpenIdOrUnionId(String openId,String unionId,Long merchantId);

    /**
     * 根据openId或unionId获取平台用户信息
     * @param telephone 手机号码
     * @param merchantId    商户Id
     * @return
     */
    List<UserInfoDTO> listByTelephoneAndMerchantId(String telephone,Long merchantId);

    /**
     * 更新绑定的手机号码
     * @param dto
     * @return
     */
    Boolean updateTelephone(MerchantUserDTO dto);

    /**
     * 更新平台用户信息
     * @param userCreateDTO
     * @return
     */
    Boolean updatePlatformUserInfo(UserCreateDTO userCreateDTO);

    /**
     * 注销用户
     * @param userId 用户id
     * @param merchantUserId    商户用户id
     * @param merchantId    商户id
     * @param operationUserId   操作人id
     * @return
     */
    Boolean deleteUserInfo(Long userId, Long merchantUserId,Long merchantId,Long operationUserId);

    /**
     * 创建用户APP信息
     * @param userAppDTO
     * @return
     */
    Boolean createUserApp(UserAppDTO userAppDTO);

    /**
     * 更新用户信息
     * @param dto
     * @return
     */
    Boolean updateUserInfo(MerchantUserDTO dto);

    /**
     * 根据openId和appId获取平台用户信息
     * @param openId    openId
     * @param appId appId
     * @return
     */
    UserInfoDTO getPlatformUserInfoByOpenIdAndAppId(String openId,String appId);

    /**
     * 根据平台用户id获取平台用户信息
     *
     * @param userId 用户id
     * @return
     */
    UserInfoDTO getPlatformUserInfoByUserId(Long userId);

    /**
     * 根据openId和appId获取用户信息
     *
     * @param openId     openId
     * @param appId      appId
     * @param merchantId 商户id
     * @return
     */
    UserInfoDTO getUserInfoByOpenIdAndAppId(String openId, String appId, Long merchantId);
}
