package com.lyy.user.application.member;

import com.lyy.user.account.infrastructure.member.dto.MemberLiftingRuleDTO;
import com.lyy.user.domain.member.dto.MemberLiftingRuleTouchCheckDTO;
import com.lyy.user.domain.member.dto.MemberLiftingRuleTouchResultDTO;
import com.lyy.user.domain.member.entity.MemberLiftingRule;
import com.lyy.user.infrastructure.base.IBaseService;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 会员升级策略规则 服务类
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
public interface IMemberLiftingRuleService extends IBaseService<MemberLiftingRule> {
    /**
     * 根据会员策略获取规则列表
     *
     * @param merchantId
     * @param memberLiftingId
     * @return
     */
    default List<MemberLiftingRule> findByMemberLifting(Long merchantId, Long memberLiftingId) {
        return findByMemberLifting(merchantId, Collections.singletonList(memberLiftingId));
    }

    /**
     * 根据会员策略获取规则列表
     *
     * @param merchantId
     * @param memberLiftingIdList
     * @return
     */
    List<MemberLiftingRule> findByMemberLifting(Long merchantId, List<Long> memberLiftingIdList);

    /**
     * 根据会员策略获取规则列表
     * @param merchantId    商户id
     * @param memberLiftingIdList   升级策略id
     * @param active    是否删除
     * @return
     */
    List<MemberLiftingRule> findByMemberLifting(Long merchantId, List<Long> memberLiftingIdList, boolean active);


    /**
     * 根据策略保存规则列表信息
     *
     * @param merchantId
     * @param memberLiftingId
     * @param ruleList
     * @return
     */
    boolean saveOfMemberLifting(Long merchantId, Long memberLiftingId, List<MemberLiftingRuleDTO> ruleList);

    /**
     * 保存默认的计费规则，针对小场地商户
     * @param memberLiftingRuleDTO
     * @return
     */
    MemberLiftingRule saveDefaultMemberLiftingRule(MemberLiftingRuleDTO memberLiftingRuleDTO);
    /**
     * 根据升级策略删除对应的规则数据
     *
     * @param merchantId
     * @param memberLiftingIds
     * @return
     */
    int removeByMemberLifting(Long merchantId, Collection<Long> memberLiftingIds);

    /**
     * 检查触发的记录列表
     * @param memberLiftingRuleTouchCheckDTO
     * @return
     */
    List<MemberLiftingRuleTouchResultDTO> touchLiftingRule(MemberLiftingRuleTouchCheckDTO memberLiftingRuleTouchCheckDTO);

}
