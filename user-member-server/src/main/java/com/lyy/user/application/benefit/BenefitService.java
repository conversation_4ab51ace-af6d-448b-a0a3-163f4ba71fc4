package com.lyy.user.application.benefit;

import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustRefundDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitInfoDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitListDTO;
import com.lyy.user.account.infrastructure.benefit.dto.GeneralGroupBenefitSaveDTO;
import com.lyy.user.account.infrastructure.benefit.dto.GenerateAccountDTO;
import com.lyy.user.account.infrastructure.benefit.dto.GroupBenefitMergeDTO;
import com.lyy.user.account.infrastructure.benefit.vo.GeneralGroupBenefitSaveVO;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.domain.benefit.dto.BenefitRecordCountDTO;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/3/31 17:04
 */
public interface BenefitService {

    /**
     * 查询用户权益列表
     * @param lyyUserId
     * @param merchantId
     * @return
     */
    List<BenefitListDTO> getLyyUserBenefitList(Long lyyUserId,Long merchantId);

    /**
     * 生成权益账户
     * @param generateAccountDTO
     */
    void generateBenefitAccount(GenerateAccountDTO generateAccountDTO);

    /**
     * 生成场地权益
     * @param merchantId
     * @param benefitClassifyEnum
     * @param groupIdList
     * @param equipmentTypeIdList
     * @param expiryDateCategory
     * @param upTime
     * @param downTime
     * @return
     */
    BenefitInfoDTO generateGroupEquipmentTypeBenefit(Long merchantId, BenefitClassifyEnum benefitClassifyEnum, List<Long> groupIdList,List<Long> equipmentTypeIdList, Integer expiryDateCategory, String upTime, String downTime);


    /**
     * 获取商户 指定类型的权益，如不存在则自动生成
     * @param merchantId
     * @param classifyCode
     * @param applicable
     * @param associatedId
     * @param expiryDateCategory 过期时间类型
     * @param upTime
     * @param downTime
     * @return
     */
    BenefitInfoDTO getMerchantBenefit(Long merchantId, Integer classifyCode, Integer applicable, Long associatedId,
                                      Integer expiryDateCategory, String upTime, String downTime);

    /**
     * 获取权益详情
     * @param id 权益主键ID
     * @param merchantId 商户ID，用来提交查询效率
     * @return
     */
    BenefitInfoDTO getBenefitById(Long id, Long merchantId);

    /**
     * 通用场地权益保存
     *
     * @param generalGroupBenefitSaveDTO
     * @return
     */
    List<GeneralGroupBenefitSaveVO> saveGeneralGroupBenefit(GeneralGroupBenefitSaveDTO generalGroupBenefitSaveDTO);

    /**
     * 普通场地权益保存
     * @param generalGroupBenefitSaveDTO
     */
    void saveGroupBenefit(GeneralGroupBenefitSaveDTO generalGroupBenefitSaveDTO);


    /**
     * 根据账号记录获取对应的权益信息,主要通过支付单号(outTradeNo)或业务单号(orderNo)来查找
     * @param accountBenefitAdjustDTO
     * @param mode 订单的模式 1:增加，2：扣减
     * @param classifyList 检查的类型列表
     * @return
     */
    List<BenefitRecordCountDTO> getBenefitFromAccountRecord(AccountBenefitAdjustDTO accountBenefitAdjustDTO, int mode,List<Integer> classifyList);
    /**
     * 根据账号记录获取对应的权益信息,主要通过支付单号(outTradeNo)或业务单号(orderNo)来查找
     * @param accountBenefitAdjustRefundDTO
     * @param mode 订单的模式 1:增加，2：扣减
     * @return
     */
    default List<BenefitRecordCountDTO> getBenefitFromAccountRecord(AccountBenefitAdjustRefundDTO accountBenefitAdjustRefundDTO, int mode){
        return getBenefitFromAccountRecord(accountBenefitAdjustRefundDTO,mode,accountBenefitAdjustRefundDTO.getClassifyList());
    }

    /**
     * 2.0开启场地通用更新权益使用范围
     *
     * @param generalGroupBenefitSaveDTO
     * @return
     */
    Boolean updateSaaSBenefit(GeneralGroupBenefitSaveDTO generalGroupBenefitSaveDTO);

    /**
     * 场地通用权益合并
     *
     * @param groupBenefitMergeDTO
     * @return
     */
    Boolean groupBenefitMerge(GroupBenefitMergeDTO groupBenefitMergeDTO);

    /**
     * 新场地合并
     * @param groupBenefitMergeDTO
     * @return
     */
    Boolean groupBenefitMergeWithNewGroupList(GroupBenefitMergeDTO groupBenefitMergeDTO);
}
