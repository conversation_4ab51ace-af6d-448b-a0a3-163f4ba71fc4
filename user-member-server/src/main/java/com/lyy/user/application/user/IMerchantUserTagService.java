package com.lyy.user.application.user;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserInfoQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagExternalSystemQuery;
import com.lyy.user.account.infrastructure.user.dto.tag.TagOfExternalSystemDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagOfExternalSystemVO;
import com.lyy.user.domain.user.entity.MerchantUserTag;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
public interface IMerchantUserTagService extends IService<MerchantUserTag> {

    /**
     * 查询标签用户
     *
     * @param queryDTO
     * @return
     */
    Page<MerchantUserInfoDTO> listByTagIds(MerchantUserInfoQueryDTO queryDTO);

    /**
     * 查询用户标签-其他平台相关
     *
     * @param dto
     * @return
     */
    TagOfExternalSystemVO getTagOfExternalSystem(TagOfExternalSystemDTO dto);

    List<TagOfExternalSystemVO> listTagOfExternalSystem(TagExternalSystemQuery query);
}
