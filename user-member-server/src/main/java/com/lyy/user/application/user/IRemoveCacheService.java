package com.lyy.user.application.user;

/**
 * <AUTHOR>
 * <p>
 * 清除缓存
 */
public interface IRemoveCacheService {


    /**
     * 用于清除缓存
     *
     * @param userId     平台用户ID
     * @param merchantId 商户ID
     */
    void removeUserMerchantCache(long userId, long merchantId);

    /**
     * 用于清除缓存
     *
     * @param merchantUserId 商户用户
     * @param merchantId     商户
     */
    void removeMerchantUserCache(long merchantUserId, long merchantId);
}
