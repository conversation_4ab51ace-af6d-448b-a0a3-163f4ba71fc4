package com.lyy.user.application.account;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitCountDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDataDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDecreaseReqDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitModifyDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitResultDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitScopeDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitScopeQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountClassifyBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountClassifyBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountConditionDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountConsumption;
import com.lyy.user.account.infrastructure.account.dto.AccountCreateDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordCountDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordSaveDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountStatusDTO;
import com.lyy.user.account.infrastructure.account.dto.AddSupplementaryCardDTO;
import com.lyy.user.account.infrastructure.account.dto.BenefitRollbackDTO;
import com.lyy.user.account.infrastructure.account.dto.ConsumeDTO;
import com.lyy.user.account.infrastructure.account.dto.PayRefundBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.RecordBenefitScopeDTO;
import com.lyy.user.account.infrastructure.account.dto.RecordBenefitScopeQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.SmallVenueAccountInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.SmallVenueAllCardDTO;
import com.lyy.user.account.infrastructure.account.dto.SupplementaryCardCheckDTO;
import com.lyy.user.account.infrastructure.account.dto.SupplyAnonymousCardDataDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountStatusBatchDTO;
import com.lyy.user.account.infrastructure.account.dto.request.OrderBenefitRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.request.StoreBenefitClearDTO;
import com.lyy.user.account.infrastructure.account.dto.response.OrderBenefitCountDTO;
import com.lyy.user.account.infrastructure.account.dto.response.OrderBenefitInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.HasSupplementaryCardDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountDetailsDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountListDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalRecordSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.ReissueCardDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueAccountRecordSaveDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueRecordListDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueRecordSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.UpdateCardStatusDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.request.SmallVenueAccountRecordSaveBatchDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.AccountSupplementaryCardQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalCardCountInfo;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitConsumeInfoDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitPreDeductionDTO;
import com.lyy.user.account.infrastructure.common.DataList;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.domain.account.entity.Account;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 类描述：账户领域application层
 * <p>
 *
 * <AUTHOR>
 * @since 2021/3/31 09:22
 */
public interface AccountService {

    /**
     * 新增账户权益
     * @param accountList 账户集合
     * @return 无
     */
    void increaseAccountBenefit(List<AccountCreateDTO> accountList);

    /**
     * 根据商户、用户查询所有账户
     * @param condition
     * @return
     */
    List<AccountDTO> getByUserAndMerchant(AccountConditionDTO condition);

    /**
     * 扣减账户的权益
     * @return
     */
    void decreaseAccountBenefit(List<AccountBenefitAdjustDTO> adjust);

    /**
     * 调整权益数量
     * @param adjust 调整详情
     * @param checkBalance 是否检查余额，如果为true，余额不够会抛出异常，如果为false，余额不够会减为0
     * @param allowNegative 是否允许扣为负数
     */
    void decreaseAccountBenefit(List<AccountBenefitAdjustDTO> adjust, boolean checkBalance,Boolean allowNegative);


    /**
     * 权益消耗
     * @param consume 具体消耗参数
     * @return
     */
    void benefitConsume(ConsumeDTO consume);

    /**
     * 查询权益的数量map
     * @param param
     * @return
     */
    Map<Integer, BigDecimal> benefitCount(AccountQueryDTO param);

    /**
     * 清除余额余币
     * @return 清除的权益，按类型统计
     */
    List<AccountBenefitResultDTO> clearBalanceAndCoin(AccountBenefitQueryDTO param);


    /**
     * 权益增加业务（之前的权益调整业务迁移）
     * @param accountBenefitAdjustDTO
     * @param isAddTotal 是否加统计（应对有退款的项）
     */
    void benefitAdd(AccountBenefitAdjustDTO accountBenefitAdjustDTO,boolean isAddTotal);

    /**
     * 权益回滚
     * @param rollback
     * @param retryFlag 是否重试
     */
    void benefitRollback(BenefitRollbackDTO rollback, boolean retryFlag);

    /**
     * 权益回滚 延迟队列处理
     * @param rollback
     * @return
     */
    boolean benefitRollbackDelayQueue(BenefitRollbackDTO rollback);

    void merchantClearBenefit(List<AccountBenefitAdjustDTO> param);

    void merchantClearBatchBenefit(List<AccountBenefitAdjustDTO> accountBenefitAdjustList);

    /**
     *
     * @param queryDTO
     * @return
     */
    AccountBenefitDataDTO findAccountBenefitData(AccountBenefitQueryDTO queryDTO);

    /**
     *
     * @param benefitQueryDTO
     * @return
     */
    Page<AccountBenefitDTO> listBenefitDetail(AccountBenefitQueryDTO benefitQueryDTO);


    /**
     * 统计账户权益
     * @param merchantId
     * @param merchantUserId
     * @param classifyList
     * @return
     */
    AccountBenefitCountDTO sumAccountBenefit(Long merchantId, Long merchantUserId, List<Integer> classifyList);




    Page<AccountConsumption> listRecord(AccountRecordQueryDTO param);


    /**
     * 直接更新account的数据，主要用于券数据的更新
     *
     * @param merchantId
     * @param merchantUserId
     * @param classify
     * @param amount
     * @param adjustType
     * @param isAddTotal
     * @return
     */
    int updateAccountAmount(Long merchantId, Long merchantUserId, Integer classify, BigDecimal amount, AdjustTypeEnum adjustType, boolean isAddTotal);


    /**
     * 券权益单独处理
     *
     * @param accountBenefitAdjustDTO
     * @param userInfoDTO
     * @param isAddTotal
     * @return
     */
    boolean adjustCouponBenefit(AccountBenefitAdjustDTO accountBenefitAdjustDTO, UserInfoDTO userInfoDTO, boolean isAddTotal);

    /**
     * 分页获取账户的券列表数据
     * @param merchantId    商户id只作为确定数据库的作用，不用于搜索
     * @param classify  权益类型
     * @param page 分页参数
     * @param start  开始日期
     * @param end  结束日期
     * @return
     */
    Page<Account> findAccountPage(Long merchantId, Integer classify, Page<Account> page, Date start, Date end);

    List<BenefitPreDeductionDTO> benefitPreDeductionConsume(ConsumeDTO consume);

    /**
     * 保存消费记录
     *
     * @param recordDTO
     * @return
     */
    Boolean saveRecord(AccountRecordSaveDTO recordDTO);

    /**
     * 支付充值订单退款-根据订单号扣减权益
     * @param payRefundBenefitDTO
     * @return
     */
    Boolean payRefundRollbackBenefit(PayRefundBenefitDTO payRefundBenefitDTO);

    /**
     * 根据订单号退回抵扣的权益
     * @param rollbackDTO
     * @return
     */
    List<BenefitPreDeductionDTO> refundBenefitByOrderNo(BenefitRollbackDTO rollbackDTO);

    /**
     * 扣减并返回实扣金额
     * @param consume
     * @return
     */
    BenefitConsumeInfoDTO callbackRealConsume(ConsumeDTO consume);
    /**
     * 查询会员卡状态
     *
     * @param merchantId
     * @param cardNo
     * @return
     */
    AccountStatusDTO findCardStatus(Long merchantId, String cardNo);

    List<AccountStatusBatchDTO> findBatchCardStatus(Long merchantId, List<String> cardNos);


    /**
     * 查询账户信息
     * @param merchantId
     * @param keyword
     * @return
     */
    SmallVenueAccountInfoDTO getAccountInfo(Long merchantId, String keyword);

    /**
     * 查询用户主卡列表
     * @param merchantId
     * @param userId
     * @param hasSupplementaryCard
     * @return
     */
    List<SmallVenueAllCardDTO> selectUserAllCard(Long merchantId, Long userId, Boolean hasSupplementaryCard);


    /**
     * 附属卡添加校验
     *
     * @param supplementaryCardCheckDTO
     * @return
     */
    Boolean supplementaryCardCheck(SupplementaryCardCheckDTO supplementaryCardCheckDTO);

    /**
     * 添加附属卡
     *
     * @param addSupplementaryCardDTO
     * @return
     */
    Integer addSupplementaryCard(AddSupplementaryCardDTO addSupplementaryCardDTO);


    /**
     * 会员卡挂失/恢复
     *
     * @param updateCardStatusDTO
     * @return
     */
    Integer cardReportLossOrRecover(UpdateCardStatusDTO updateCardStatusDTO);

    /**
     * 会员卡禁用/恢复
     *
     * @param updateCardStatusDTO
     * @return
     */
    Integer cardDisabledOrRecover(UpdateCardStatusDTO updateCardStatusDTO);

    /**
     * 会员卡补卡/换卡
     *
     * @param reissueCardDTO
     * @return
     */
    Integer reissueCard(ReissueCardDTO reissueCardDTO);

    /**
     * 商品销售记录、兑换记录、商品回收记录保存
     * @param smallVenueAccountRecordSaveDTO
     * @return
     */
    Boolean smallVenueSaveRecord(SmallVenueAccountRecordSaveDTO smallVenueAccountRecordSaveDTO);

    /**
     * 商品销售记录、兑换记录、商品回收记录批量保存
     */
    Boolean smallVenueSaveRecordBatch(SmallVenueAccountRecordSaveBatchDTO dto);

    /**
     * 商品购买记录、商品销售记录、兑换记录、商品回收记录、设备消费记录、储值变更记录查询
     *
     * @param smallVenueRecordSelectDTO
     * @return
     */
    DataList<SmallVenueRecordListDTO> smallVenueRecord(SmallVenueRecordSelectDTO smallVenueRecordSelectDTO);

    /**
     * 查询主卡是否存在附属卡
     * @param hasSupplementaryCardDTO
     * @return
     */
    Boolean hasSupplementaryCard(HasSupplementaryCardDTO hasSupplementaryCardDTO);

    /**
     * 会员卡续期
     * @param merchantId
     * @param cardNo
     * @param extendedDays
     * @param updateBy
     * @return
     */
    Boolean renewalCard(Long merchantId, String cardNo, Integer extendedDays, Long updateBy);


    /**
     * 不记名卡的资料补充
     *
     * @param supplyAnonymousCardDataDTO
     * @return
     */
    Boolean supplyAnonymousCardData(SupplyAnonymousCardDataDTO supplyAnonymousCardDataDTO);


    Long countRecord(AccountRecordCountDTO accountRecordCountDTO);

    List<AccountBenefitScopeDTO> listBenefitWithScope(AccountBenefitScopeQueryDTO query);

    /**
     * 根据订单和权益类型查询消耗权益的使用范围
     * @param queryDTO
     * @return
     */
    List<RecordBenefitScopeDTO> findRecordBenefitScopeByOrderNo(RecordBenefitScopeQueryDTO queryDTO);

    /**
     * 根据订单获取扣款明细记录
     * @param queryDTO
     * @return
     */
    List<AccountRecordDTO> findRecordByOrderNoAndCondition(AccountRecordQueryDTO queryDTO);

    /**
     * 用户充值抵扣金退款校验
     *
     * @param merchantId
     * @param userId
     * @param outTradeNO
     * @return
     */
    Boolean deductionRechargeRefundCheck(Long merchantId, Long userId, String outTradeNO);

    /**
     * 移动端流水记录列表
     * @param mobileTerminalRecordSelectDTO
     * @return
     */
    DataList<SmallVenueRecordListDTO> mobileTerminalRecord(MobileTerminalRecordSelectDTO mobileTerminalRecordSelectDTO);

    /**
     * 移动端会员卡列表查询
     * @param mobileTerminalAccountSelectDTO
     * @return
     */
    DataList<MobileTerminalAccountListDTO> mobileTerminalAccountList(MobileTerminalAccountSelectDTO mobileTerminalAccountSelectDTO);

    /**
     * 移动端会员卡详情
     * @param merchantId
     * @param userId
     * @param accountId
     * @return
     */
    MobileTerminalAccountDetailsDTO mobileTerminalAccountDetails(Long merchantId, Long userId, Long accountId);

    /**
     * 获取指定类型权益信息
     */
    List<AccountClassifyBenefitDTO> findAccountClassifyBenefit(AccountClassifyBenefitQueryDTO request);

    /**
     * 扣减账户权益并打标签
     */
    Boolean decreaseAccountBenefitAndTag(AccountBenefitDecreaseReqDTO request);

    OrderBenefitInfoDTO listOrderBenefitRecord(OrderBenefitRecordQueryDTO query);


    /**
     * 订单消耗的权益
     * @param query
     * @return
     */
    OrderBenefitCountDTO listOrderConsume(OrderBenefitRecordQueryDTO query);

    void benefitModify(AccountBenefitModifyDTO modify);

    void storeBenefitClear(StoreBenefitClearDTO param);

    List<SmallVenueAccountInfoDTO> getAccountInfoByCardNos(Long merchantId, List<String> cardNos);

    List<MobileTerminalCardCountInfo> getSupplementaryCard(AccountSupplementaryCardQueryDTO dto);
}
