package com.lyy.user.application.member;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lyy.user.account.infrastructure.member.dto.MemberGrowRecordDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGrowRecordListDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGrowRecordQueryDTO;
import com.lyy.user.domain.member.entity.MemberGrowRecord;
import java.util.Collection;
import java.util.Map;

/**
 * <p>
 * 会员成长值记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
public interface IMemberGrowRecordService extends IService<MemberGrowRecord> {

    /**
     * 新增会员成长值
     *
     * @param dto
     * @returna
     */
    void saveMemberGrowRecord(MemberGrowRecordDTO dto);

    /**
     * 根据升级策略统计某个用户的记录数据
     *
     *
     * @param merchantId
     * @param userId
     * @param memberLiftingIds
     * @return
     */
    Map<Long,Integer> countRecordByMemberLifting(Long merchantId, Long userId, Collection<Long> memberLiftingIds);

    /**
     * 查询用户成长值记录
     * @param param
     * @return
     */
    Page<MemberGrowRecordListDTO> list(MemberGrowRecordQueryDTO param);
}
