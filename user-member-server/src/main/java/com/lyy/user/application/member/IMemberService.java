package com.lyy.user.application.member;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.member.dto.MemberDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberInfoDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberTouchRuleDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberUserInfoDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberUserPageDTO;
import com.lyy.user.account.infrastructure.member.dto.UpdateUserMemberLevelDTO;
import com.lyy.user.domain.member.entity.Member;
import com.lyy.user.domain.member.entity.MemberLevel;
import com.lyy.user.domain.member.entity.MemberLifting;
import com.lyy.user.domain.member.entity.MemberLiftingRuleRecord;
import com.lyy.user.infrastructure.base.IBaseService;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 会员表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
public interface IMemberService extends IBaseService<Member> {
    /**
     * 保存会员信息
     *
     * @param memberDTO
     * @return
     */
    Long saveOrUpdate(MemberDTO memberDTO);

    /**
     * 根据用户id与会员组id获取对应的会员信息，若没有会员信息，则初始化
     *
     * @param merchantId
     * @param userId
     * @param memberGroupId
     * @return
     */
    Member getMemberOrInit(Long merchantId, Long userId, Long memberGroupId);

    /**
     * 根据用户id与会员组id获取对应的会员信息
     *
     * @param merchantId
     * @param userId
     * @param memberGroupId
     * @return
     */
    Member getMember(Long merchantId, Long userId, Long memberGroupId);

    /**
     * 根据规则来更新会员对应的信息
     *
     * @param memberTouchRuleDTO
     * @return
     */
    int updateMemberOfRule(MemberTouchRuleDTO memberTouchRuleDTO);

    int updateMemberOfRuleWithIdempotent(MemberTouchRuleDTO memberTouchRuleDTO);

    /**
     * 回退会员触发规则信息
     *
     * @param memberTouchRuleDTO
     * @return
     */
    boolean removeMemberOfRule(MemberTouchRuleDTO memberTouchRuleDTO);

    /**
     * 根据用户的升级策略更新对应会员的成长值
     *
     * @param merchantId
     * @param userId     用户id
     * @return 更新的升级策略策略数量
     */
    default int updateGrowValueOfLifting(Long merchantId, Long userId) {
        return updateGrowValueOfLifting(merchantId, userId, null);
    }

    /**
     * 根据用户的升级策略更新对应会员的成长值
     *
     * @param merchantId
     * @param userId        用户id
     * @param newRecordList 新补充的记录列表
     * @return 更新的升级策略策略数量
     */
    int updateGrowValueOfLifting(Long merchantId, Long userId, List<MemberLiftingRuleRecord> newRecordList);

    /**
     * 更新会员成长值的数值
     *
     * @param merchantId
     * @param memberId   会员id
     * @param growValue  更新的成长值
     * @param isUpgrade  是否为升级功能，true为升级，false为降级
     * @return
     */
    boolean updateGrowValueOfId(Long merchantId, Long memberId, Long growValue, boolean isUpgrade);

    /**
     * 更新会员的等级
     *
     * @param merchantId
     * @param memberId         会员id
     * @param oldMemberLevelId 旧会员等级id
     * @param newMemberLevelId 新会员等级id
     * @return
     */
    boolean updateLevel(Long merchantId, Long memberId, Long oldMemberLevelId, Long newMemberLevelId);

    /**
     * 更新会员的成长值,若可以升降级的同时升降级处理，并返回对应的等级信息
     *
     * @param member
     * @param memberLifting
     * @param resources     来源记录数据
     * @param growValue     变动成长值
     * @param description   描述
     * @return
     */
    List<MemberLevel> updateGrowValueAndLevel(Member member, MemberLifting memberLifting, String resources, Long growValue, String description);

    /**
     * 根据用户组信息分页获取会员信息
     *
     * @param page          分页信息
     * @param merchantId
     * @param memberGroupId
     * @param memberLevelId
     * @return
     */
    Page<MemberInfoDTO> findMemberByMemberGroup(Page page, Long merchantId, Long memberGroupId, Long memberLevelId);

    /**
     * 获取相同会员组下的在某个时间段内没有数据的会员信息
     *
     * @param memberLifting
     * @param startTime
     * @param endTime
     * @return
     */
    List<Member> getNotRecordOfTime(MemberLifting memberLifting, LocalDateTime startTime, LocalDateTime endTime);


    /**
     * 分页获取用户的会员信息
     *
     * @param page
     * @param merchantId
     * @param userId
     * @return
     */
    Page<MemberUserPageDTO> findPageMemberUser(Page page, Long merchantId, Long userId);

    /**
     * 更新会员为新的等级
     *
     * @param member
     * @param memberLevel
     * @return
     */
    boolean updateMemberLevel(Member member, MemberLevel memberLevel);

    /**
     * 增加会员成长值变化记录
     *
     * @param member
     * @param memberLifting
     * @param resources
     * @param growValue
     * @param description
     * @param isUpgrade
     */
    void addMemberGrowRecord(Member member, MemberLifting memberLifting, String resources, Long growValue, String description, boolean isUpgrade);

    /**
     * 获取小场地用户信息
     *
     * @param merchantId
     * @param userId
     * @return
     */
    MemberUserInfoDTO getSmallVenueUserMemberInfo(Long merchantId, Long userId);

    Boolean initSmallVenueUserMemberLevel(Long merchantId, Long userId);

    /**
     * 手动更新用户的 会员等级
     *
     * @param updateUserMemberLevelDTO
     * @return
     */
    Boolean updateUserMemberLevel(UpdateUserMemberLevelDTO updateUserMemberLevelDTO);

    /**
     * 获取商户指定等级的用户数
     * @param merchantId    商户id
     * @param levelIds  会员等级id
     * @return
     */
    Boolean hasUserInMemberLevelIds(Long merchantId, List<Long> levelIds);
}
