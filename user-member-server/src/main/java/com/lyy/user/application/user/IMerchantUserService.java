package com.lyy.user.application.user;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.common.DataList;
import com.lyy.user.account.infrastructure.user.dto.MerchantIntegralUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserCheckPasswordDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserUpdatePasswordDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserUpdateTelephoneDTO;
import com.lyy.user.account.infrastructure.user.dto.PayoutBenefitDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueMobileUserListSelectDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueUserDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueUserListSelectDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueUserMobileVO;
import com.lyy.user.account.infrastructure.user.dto.UpdateMerchantUserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserAndLevelInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserInfoByKeywordDTO;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.infrastructure.base.IBaseService;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IMerchantUserService  extends IBaseService<MerchantUser> {

    /**
     * 保存或修改商户用户信息
     * @param dto
     * @return
     */
    Long saveOrUpdateMerchantUser(MerchantUserDTO dto);

    /**
     * 保存或修改商户用户信息
     * @param dto
     * @return
     */
    Long saveOrUpdateMerchantUser(MerchantUserDTO dto, String password, Integer systemFlag);

    /**
     * 根据商户用户Id获取商户用户信息
     * @param merchantUserId    商户用户Id
     * @param merchantId    商户id
     * @return
     */
    MerchantUser findById(Long merchantUserId,Long merchantId);

    /**
     * 根据平台用户Id和商户Id获取商户用户信息
     * @param userId    平台用户Id
     * @param merchantId    商户Id
     * @return
     */
    MerchantUser findByUserIdAndMerchantId(Long userId,Long merchantId);

    /**
     * 根据手机号和商户Id获取商户用户信息
     * @param telephone 手机号码
     * @param merchantId    商户Id
     * @return
     */
    List<MerchantUser> findByTelephoneAndMerchantId(String telephone,Long merchantId);

    /**
     * 更新商户用户手机号码
     * @param merchantId    商户id
     * @param merchantUserId 商户用户id
     * @param userId    平台用户id
     * @param telephone 手机号
     * @param updateby  操作人
     * @return
     */
    Boolean updateTelephone(Long merchantId,Long merchantUserId,Long userId,String telephone,Long updateby);

    /**
     * 根据用户id和商户id更新商户用户信息
     * @param merchantUser
     * @return
     */
    Boolean updateByIdAndMerchantId(MerchantUser merchantUser);

    /**
     * 根据条件查询所有用户数量
     * @param merchantUserQueryDTO
     * @return
     */
    Page<MerchantUserListDTO> merchantUserTotalByCondition(MerchantUserQueryDTO merchantUserQueryDTO);

    /**
     * 根据条件查询用户列表
     * @param merchantUserQueryDTO
     * @param merchantUserListDTOPage
     * @return
     */
    List<MerchantUserListDTO> listMerchantUser(MerchantUserQueryDTO merchantUserQueryDTO, Page<MerchantUserListDTO> merchantUserListDTOPage);

    /**
     * 商户查询用户数据
     * @param merchantUserQueryDTO
     * @return
     */
    Page<MerchantUserListDTO> queryUserListByMerchant(MerchantUserQueryDTO merchantUserQueryDTO);

    /**
     * 给用户派发福利
     * @param payoutWelfareDTO
     */
    void payoutWelfare(PayoutBenefitDTO payoutWelfareDTO);

    /**
     * 商户查询用户数据 总数
     * @param merchantUserQueryDTO
     * @return
     */
    Long countUserListByMerchant(MerchantUserQueryDTO merchantUserQueryDTO);

    /**
     * 关键字查询会员信息
     *
     * @param merchantId
     * @param keywords
     * @param keyWordsType
     * @see UserMemberSysConstants.SMALL_VENUE_SEARCH_TYPE_USERID
     * @return
     */
    MerchantUserInfoByKeywordDTO searchByKeywords(Long merchantId, String keywords, Integer keyWordsType);


    /**
     * 更新会员密码
     *
     * @param merchantUserUpdatePasswordDTO
     * @return
     */
    Boolean updateMerchantUserPassword(MerchantUserUpdatePasswordDTO merchantUserUpdatePasswordDTO);

    /**
     * 校验会员密码
     * @param merchantUserCheckPasswordDTO
     * @return
     */
    Boolean checkMerchantUserPassword(MerchantUserCheckPasswordDTO merchantUserCheckPasswordDTO);

    /**
     * 小场地会员列表,带会员等级查询
     * @param smallVenueUserListSelectDTO
     * @return
     */
    DataList<SmallVenueUserDTO> selectSmallVenueUserList(SmallVenueUserListSelectDTO smallVenueUserListSelectDTO);

    /**
     * 小场地会员列表
     * @param smallVenueUserListSelectDTO
     * @return
     */
    DataList<SmallVenueUserMobileVO> smallVenueMobileUserList(SmallVenueMobileUserListSelectDTO smallVenueUserListSelectDTO);

    /**
     * 更新会员信息
     *
     * @param updateDTO
     * @return
     */
    Integer updateMerchantUserInfo(UpdateMerchantUserInfoDTO updateDTO);

    /**
     * 更新会员手机号码
     * @param merchantUserUpdateTelephoneDTO
     * @return
     */
    Boolean updateMerchantUserTelephone(MerchantUserUpdateTelephoneDTO merchantUserUpdateTelephoneDTO);

    /**
     * 根据条件获取商户用户信息
     * @param queryDTO 查询条件
     * @return
     */
    MerchantUserDTO getMerchantUserByCondition(MerchantIntegralUserQueryDTO queryDTO);

    /**
     * 移动端会员注销
     * @param merchantId
     * @param merchantUserId
     * @return
     */
    Integer mobileTerminalMerchantUserCancellation(Long merchantId, Long merchantUserId, Long operatorId);

    /**
     * 关键字查询会员信息
     *
     * @param merchantId
     * @param keywords
     * @param keyWordsType
     * @return
     */
    MerchantUserAndLevelInfoDTO searchBaseInfoKeywordsAndType(Long merchantId, String keywords, Integer keyWordsType);
}
