package com.lyy.user.application.member;

import com.lyy.user.account.infrastructure.member.dto.MemberLevelDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberLevelSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.SmallVenueMemberLevelSaveDTO;
import com.lyy.user.domain.member.entity.MemberLevel;
import com.lyy.user.infrastructure.base.IBaseService;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;

/**
 * <p>
 * 会员等级 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
public interface IMemberLevelService extends IBaseService<MemberLevel> {

    /**
     * 保存或更新会员的等级信息
     * @param memberLevelSaveDTO
     * @return
     */
    Long saveOrUpdate(MemberLevelSaveDTO memberLevelSaveDTO);

    /**
     * 根据会员组获取对应的会员等级，过滤0成长值的
     *
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    default List<MemberLevel> findByMemberGroup(Long merchantId, Long memberGroupId){
        return findByMemberGroup(merchantId,memberGroupId,false);
    }
    /**
     * 根据会员组获取对应的会员等级
     * @param merchantId
     * @param memberGroupId
     * @param isContainZero 是否包含0成长值等级的
     * @return
     */
    List<MemberLevel> findByMemberGroup(Long merchantId, Long memberGroupId,boolean isContainZero);

    /**
     * 查询小场地对应会员等级信息
     * @param merchantId
     * @param memberGroupId
     * @param isContainZero
     * @return
     */
    List<MemberLevelDTO> findSmallVenueMemberLevelByMemberGroup(Long merchantId, Long memberGroupId, boolean isContainZero);


    /**
     * 根据id获取详情信息
     *
     * @param merchantId
     * @param memberLevelId
     * @return
     */
    MemberLevelSaveDTO getInfoById(Long merchantId, Long memberLevelId);


    /**
     * 删除会员等级信息，并删除对应的会员规则信息
     *
     * @param merchantId
     * @param memberLevelId
     * @return
     */
    int removeByMemberLevel(Long merchantId, Long memberLevelId);

    /**
     * 获取第一个会员等级
     *
     * @param merchantId
     * @param memberGroupId 会员组
     * @param liftingStrategy 升级策略
     * @return
     */
    MemberLevel getFirstLevel(Long merchantId, Long memberGroupId, Short liftingStrategy);

    /**
     * 根据会员的成长值获取需要变动的等级
     *
     * @param merchantId
     * @param memberId
     * @param isUpgrade 是否为升级功能，true为升级，false为降级
     * @return
     */
    List<MemberLevel> findLevelOfMemberGrowValue(Long merchantId, Long memberId, boolean isUpgrade);

    /**
     * 批量更新会员组等级信息
     * @param merchantId
     * @param memberGroupId
     * @param memberLevelList
     * @return
     */
    int saveOrUpdateBatchMemberLevel(Long merchantId, Long memberGroupId, List<MemberLevelSaveDTO> memberLevelList);


    /**
     * 获取下一个等级
     * @param merchantId
     * @param memberLevelId
     * @return
     */
    default MemberLevel findNextLevel(Long merchantId, Long memberLevelId) {
        List<MemberLevel> list = findNextLevel(merchantId, memberLevelId, 1, true);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }
    /**
     * 获取上一个等级
     * @param merchantId
     * @param memberLevelId
     * @return
     */
    default MemberLevel findLastLevel(Long merchantId, Long memberLevelId){
        List<MemberLevel> list = findNextLevel(merchantId, memberLevelId, 1, false);
        if(CollectionUtils.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }
    /**
     * 获取下一个等级的列表
     * @param merchantId
     * @param memberLevelId
     * @param size 需要获取的最大个数
     * @return
     */
    List<MemberLevel> findNextLevel(Long merchantId, Long memberLevelId,int size,boolean isNext);

    /**
     * 获取0成长值的等级id
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    Long getZeroLevelId(Long merchantId, Long memberGroupId);

    /**
     * 保存小场地的会员级别
     * @param smallVenueMemberLevelSaveDTO
     */
    void saveOrUpdateSmallVenueMemberLevel(SmallVenueMemberLevelSaveDTO smallVenueMemberLevelSaveDTO);
}
