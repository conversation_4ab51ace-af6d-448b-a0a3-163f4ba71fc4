package com.lyy.user.application.benefit;

import com.lyy.user.account.infrastructure.account.dto.SmallVenueMergeAccountInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.request.AccountBenefitUpdateDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.AccountCancellationDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.AvailableBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitDecrementDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitIncrementBatchDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitIncrementDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitIncrementItemDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitRefundQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitRefundResultDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.CardTransferDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MerchantBenefitIncrementDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueAccountBenefitTransferDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueAccountBenefitTransferQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueUserAvailableBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.UserAccountBenefit;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.request.AccountDefaultStatusUpdateDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.request.BenefitConsumptionSummaryQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.response.AccountRecordBenefitSummaryDTO;
import com.lyy.user.domain.account.dto.AccountInitDTO;
import com.lyy.user.domain.account.entity.Account;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2022/1/8
 */
public interface SmallVenueAccountService {

    /**
     * for 兼容
     */
    Long DEFAULT_BENEFIT_ID = 0L;

    Optional<Account> get(Long merchantId, Long userId, String cardNo);

    /**
     * @param supplier 在 Supplier 中行初始化 Account 对象即可
     * @see SmallVenueAccountService#initAccountIfAbsent
     */
    Account initAccount(Supplier<AccountInitDTO> supplier);

    /**
     * 如果不存在则初始化会员卡账户
     */
    Account initAccountIfAbsent(Long merchantId, Long userId, String cardNo, BigDecimal deposit, Supplier<AccountInitDTO> supplier);
    Account initAccountIfAbsent(Long merchantId, Long userId, Long storeId, BenefitIncrementItemDTO item, Supplier<AccountInitDTO> supplier);

    void increaseAccountBenefit(BenefitIncrementDTO dto);

    Boolean increaseAccountBenefitLockWait(BenefitIncrementDTO dto);

    void increaseAccountBenefitBatch(BenefitIncrementBatchDTO dto);

    void decreaseAccountBenefit(BenefitDecrementDTO dto);

    void transfer(CardTransferDTO dto);

    void cancellation(AccountCancellationDTO dto);

    List<UserAccountBenefit> listBenefit(Long merchantId, String cardNo, Integer status);

    /**
     * 查询用户可用的权益
     *
     * @param queryDTO 可用权益查询DTO
     * @return
     */
    List<SmallVenueUserAvailableBenefitDTO> findUserAvailableBenefit(AvailableBenefitQueryDTO queryDTO);

    /**
     * 根据单号获取消费记录和消费记录中对应的权益明细
     *
     * @param queryDTO 退款参数
     */
    BenefitRefundResultDTO findAccountRecordsAndAccountBenefits(BenefitRefundQueryDTO queryDTO);

    AccountRecordBenefitSummaryDTO benefitsConsumptionSummary(BenefitConsumptionSummaryQueryDTO query);

    void adjustDefaultAccount(AccountDefaultStatusUpdateDTO dto);

    /**
     * 商家给用户批量增加权益
     * @param dto
     */
    void merchantBatchIncrementAccountBenefit(MerchantBenefitIncrementDTO dto);


    /**
     * 初始化多金宝账户
     *
     * @param merchantId     商户id
     * @param storeId        场地id
     * @param userId         用户id
     * @param merchantUserId 商户用户id
     * @return
     */
    Long initVenueAccount(Long merchantId, Long userId, Long merchantUserId, Long storeId);

    /**
     * 合并会员账号储值
     *
     * @param smallVenueMergeAccountInfoDTO
     */
    void mergeUserMemberAccount(SmallVenueMergeAccountInfoDTO smallVenueMergeAccountInfoDTO);

    /**
     * 查询娱乐会员在场地下的储值
     *
     * @param smallVenueAccountBenefitTransferQueryDTO
     * @return
     */
    List<SmallVenueAccountBenefitTransferDTO> selectTotalTransferAccountBenefit(SmallVenueAccountBenefitTransferQueryDTO smallVenueAccountBenefitTransferQueryDTO);

    /**
     * 多金宝清除娱乐余额余币
     */
    void smallVenueClearBalanceAndCoin(SmallVenueAccountBenefitTransferQueryDTO smallVenueAccountBenefitTransferQueryDTO);

    /**
     * 多金宝更新账号信息
     */
    void updateVenueAccount(Account account);

    /**
     * 批量更新过期时间
     *
     * @param accountBenefitUpdateDTOList 帐户权益
     * @return {@link Boolean}
     */
    Boolean updateExpireBatch(List<AccountBenefitUpdateDTO> accountBenefitUpdateDTOList);

    Map<Long, BigDecimal> getAccountBenefitUse(Long merchantId, Long userId, List<Long> accountBenefitIds, Long merchantUserId);
}
