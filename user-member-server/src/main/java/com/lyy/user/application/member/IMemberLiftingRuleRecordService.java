package com.lyy.user.application.member;

import com.lyy.user.domain.member.dto.MemberLiftingRuleRecordUserDTO;
import com.lyy.user.domain.member.entity.MemberLiftingRuleRecord;
import com.lyy.user.infrastructure.base.IBaseService;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 会员升级策略规则记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-06
 */
public interface IMemberLiftingRuleRecordService extends IBaseService<MemberLiftingRuleRecord> {


    /**
     * 根据记录id更新状态信息
     *
     * @param merchantId
     * @param recordIds
     * @param oldStatus
     * @param newStatus
     * @return
     */
    int updateStatusOfIds(Long merchantId, List<Long> recordIds, short oldStatus, short newStatus);


    /**
     * 根据状态与截止时间查询用户的升级记录信息
     *
     * @param merchantId
     * @param userId
     * @param status
     * @param endTime 可以为空
     * @return
     */
    default List<MemberLiftingRuleRecord> findByStatusAndEndTime(Long merchantId, Long userId, Short status, LocalDateTime endTime){
        return findByStatusAndEndTime(merchantId,userId,null,status,endTime);
    }

    /**
     * 根据状态与截止时间查询用户的升级记录信息
     * @param merchantId
     * @param userId
     * @param memberLiftingRuleId 可以为空
     * @param status
     * @param endTime 可以为空
     * @return
     */
    List<MemberLiftingRuleRecord> findByStatusAndEndTime(Long merchantId,Long userId,Long memberLiftingRuleId, Short status, LocalDateTime endTime);

    /**
     * 根据升级策略规则统计用户的升降级记录
     * @param merchantId
     * @param memberLiftingRuleIdList
     * @param memberIds
     * @param status
     * @param isCheckEndTime
     */
    List<MemberLiftingRuleRecordUserDTO>  countUserRecordByRule(Long merchantId, List<Long> memberLiftingRuleIdList, List<Long> memberIds, Short status, boolean isCheckEndTime);
}
