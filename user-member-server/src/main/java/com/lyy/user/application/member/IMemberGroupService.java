package com.lyy.user.application.member;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupInfoSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupRangCheckDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupSaveDTO;
import com.lyy.user.domain.member.entity.MemberGroup;
import com.lyy.user.infrastructure.base.IBaseService;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 会员组表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
public interface IMemberGroupService extends IBaseService<MemberGroup> {

    /**
     * 保存或更新
     * @param memberGroupSaveDTO
     * @return
     */
    Long saveOrUpdate(MemberGroupSaveDTO memberGroupSaveDTO);

    /**
     * 分页查找会员组数据
     * @param memberGroupDTO
     * @param memberGroupStatus
     * @return
     */
    Page<MemberGroup> findMemberGroupOfPage(Page page, MemberGroupDTO memberGroupDTO, Integer memberGroupStatus);

    /**
     * 根据id获取详情信息
     *
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    MemberGroupSaveDTO getInfoById(Long merchantId, Long memberGroupId);

    /**
     * 删除会员组信息，并删除范围信息
     *
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    boolean removeByMemberGroup(Long merchantId, Long memberGroupId);

    /**
     * 根据会员组统计对应的会员信息，默认全部会员信息
     *
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    default Integer countMemberByGroup(Long merchantId, Long memberGroupId){
        return countMemberByGroup(merchantId,memberGroupId,false);
    }


    /**
     * 根据会员组统计对应的会员信息
     * @param merchantId
     * @param memberGroupId
     * @param isEffective 是否有效，若为true，则表示会员未删除，并且处于有效时间内容
     * @return
     */
    Integer countMemberByGroup(Long merchantId, Long memberGroupId,boolean isEffective);

    /**
     * 根据会员组统计各个等级对应的会员信息
     *
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    Map<Long,Integer> countLevelMemberByGroup(Long merchantId, Long memberGroupId);

    /**
     * 获取小场地商户的默认会员组
     * @param merchantId
     * @return
     */
    MemberGroup  getSmallVenueMemberGroup(Long merchantId);

    /**
     * 根据范围查找到对应的会员组列表
     * @param memberGroupRangCheckDTO
     * @return
     */
    List<MemberGroup> getMemberGroupListOfRange(MemberGroupRangCheckDTO memberGroupRangCheckDTO);

    /**
     * 获取默认会员组信息
     * @return
     */
    MemberGroupInfoSaveDTO getDefaultMemberGroup();

    /**
     * 更新会员组状态
     * @param memberGroupDTO
     * @return
     */
    Boolean updateStatus(MemberGroupDTO memberGroupDTO);


    /**
     * 统计某个名称的会员组数量
     * @param merchantId 商户id
     * @param name  会员组名称
     * @param notIdList 排除的id
     * @return
     */
    int countMemberGroupOfName(Long merchantId, String name, List<Long> notIdList);

    /**
     * 获取小场地商户默认的会员组信息
     * @param merchantId
     * @return
     */
    MemberGroupInfoSaveDTO getSmallVenueDefaultMemberGroup(Long merchantId);

    /**
     * 获取商户有效的会员组的会员数
     *
     * @param merchantId    商户id
     * @param memberGroupId 会员组id
     * @return
     */
    Integer countEffectiveMemberByGroup(Long merchantId, Long memberGroupId);
}
