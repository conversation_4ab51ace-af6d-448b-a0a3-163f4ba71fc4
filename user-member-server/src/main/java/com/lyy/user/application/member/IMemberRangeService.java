package com.lyy.user.application.member;

import com.lyy.user.account.infrastructure.member.dto.MemberRangeAssociatedDTO;
import com.lyy.user.domain.member.entity.MemberRange;
import com.lyy.user.infrastructure.base.IBaseService;
import java.util.List;

/**
 * <p>
 * 会员组范围 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
public interface IMemberRangeService extends IBaseService<MemberRange> {
    /**
     * 获取会员组的范围列表
     *
     * @param merchantId
     * @param memberGroupId
     * @return
     */
    List<MemberRangeAssociatedDTO> getRangeAssociatedList(Long merchantId, Long memberGroupId);

    /**
     * 更新会员组的范围
     *
     * @param merchantId
     * @param memberGroupId
     * @param memberRangeAssociatedDTOList
     * @return
     */
    boolean updateRangeAssociated(Long merchantId, Long memberGroupId, List<MemberRangeAssociatedDTO> memberRangeAssociatedDTOList);

}
