package com.lyy.user.application.benefit;

import com.lyy.user.account.infrastructure.benefit.dto.ConsumeRuleSaveDTO;
import com.lyy.user.domain.benefit.entity.BenefitConsumeRule;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/4/6 16:42
 */
public interface BenefitConsumeRuleService {

    /**
     * 保存商户的权益消耗规则表
     *
     * @param merchantId  商户Id
     * @param operationId 操作人Id
     */
    void saveAllBenefitConsume(Long merchantId, Long operationId);

    /**
     * 批量处理权益消耗规则
     * @param consumeRuleSaveDTO
     */
    void batchSaveOrUpdateBenefitConsume(ConsumeRuleSaveDTO consumeRuleSaveDTO);

    /**
     * 获取默认的消费规则
     *
     * @return
     */
    List<BenefitConsumeRule> getDefaultBenefitConsumeRule(List<Integer> classifies);
}
