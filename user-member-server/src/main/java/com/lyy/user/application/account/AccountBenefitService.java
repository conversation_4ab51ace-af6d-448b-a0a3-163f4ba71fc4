package com.lyy.user.application.account;

import com.lyy.user.account.infrastructure.account.dto.BenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.UserAccountBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.AccountBenefitReqDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitDetailSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitStatisticsDetailDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueBenefitDetailDTO;
import com.lyy.user.account.infrastructure.common.DataList;
import java.util.List;

/**
 * 类描述：账户权益操作
 * <p>
 *
 * <AUTHOR>
 * @since 2021/5/11 16:36
 */
public interface AccountBenefitService {


    /**
     * 权益详情(余额详情)
     *
     * @param benefitDetailSelectDTO
     * @return
     */
    DataList<SmallVenueBenefitDetailDTO> smallVenueBenefitDetail(BenefitDetailSelectDTO benefitDetailSelectDTO);

    /**
     * 查询权益统计数据（根据门店进行分组）
     * @param merchantId
     * @param userId
     * @param accountId
     * @return
     */
    BenefitStatisticsDetailDTO benefitStatisticsGroupByStoreId(Long merchantId, Long userId, Long accountId);

    /**
     * 查询权益批次详情
     * @param accountBenefitReqDTO
     * @return
     */
    DataList<SmallVenueBenefitDetailDTO> queryAccountBenefitList(AccountBenefitReqDTO accountBenefitReqDTO);

    List<UserAccountBenefitDTO> findBenefitByIds(BenefitQueryDTO query);
}
