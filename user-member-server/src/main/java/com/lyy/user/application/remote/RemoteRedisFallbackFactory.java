package com.lyy.user.application.remote;

import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.cache.request.RemoteEvict;
import cn.lyy.cache.request.RemoteExpire;
import cn.lyy.cache.request.RemoteGet;
import cn.lyy.cache.request.RemotePut;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> {<EMAIL>}
 * @date 2020/7/3 15:39
 **/
@Component
@Slf4j
public class RemoteRedisFallbackFactory implements FallbackFactory<IRemoteRedisCacheApi> {

    @Override
    public IRemoteRedisCacheApi create(Throwable throwable) {
        if (throwable != null && StringUtils.isNotEmpty(throwable.getMessage())) {
            log.error(throwable.getMessage(), throwable);
        }
        return new IRemoteRedisCacheApi() {
            @Override
            public BaseResponse<Object> remoteGet(RemoteGet remote) {
                log.error("缓存中心获取缓存信息熔断[remoteGet],{}", remote);
                return error();
            }

            @Override
            public BaseResponse<Void> remotePut(RemotePut remote) {
                log.error("缓存中心设置缓存信息熔断[remotePut],{}", remote);
                return error();
            }

            @Override
            public BaseResponse<Object> remotePutIfAbsent(RemotePut remote) {
                log.error("缓存中心设置缓存信息熔断[remotePutIfAbsent],{}", remote);
                return error();
            }

            @Override
            public BaseResponse<Void> remoteEvict(RemoteEvict remote) {
                log.error("缓存中心清除缓存信息熔断[remoteEvict],{}", remote);
                return error();
            }

            @Override
            public BaseResponse<Long> remoteExpireTTL(RemoteExpire remote) {
                log.error("缓存中心获取缓存过期时间熔断[remoteExpireTTL],{}", remote);
                return error();
            }

            @Override
            public BaseResponse<Void> remoteExpire(RemoteExpire remote) {
                log.error("缓存中心设置缓存过期时间熔断[remoteExpire],{}", remote);
                return error();
            }

            private BaseResponse error() {
                BaseResponse response = new BaseResponse();
                response.setCode(ResponseCodeEnum.FAIL.getCode());
                response.setMessage("接口熔断");
                return response;
            }
        };
    }
}
