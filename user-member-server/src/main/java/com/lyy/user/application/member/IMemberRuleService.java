package com.lyy.user.application.member;

import com.lyy.user.account.infrastructure.member.dto.MemberRuleDTO;
import com.lyy.user.domain.member.entity.Member;
import com.lyy.user.domain.member.entity.MemberLevel;
import com.lyy.user.domain.member.entity.MemberRule;
import com.lyy.user.infrastructure.base.IBaseService;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 会员规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
public interface IMemberRuleService extends IBaseService<MemberRule> {


    /**
     * 根据会员等级信息保存对应的会员规则信息
     *
     * @param merchantId
     * @param memberLevelId
     * @param memberRuleList
     * @return
     */
    boolean saveOfMemberLevel(Long merchantId, Long memberLevelId, List<MemberRuleDTO> memberRuleList);


    /**
     * 根据会员等级获取会员规则列表
     *
     * @param merchantId
     * @param memberLevelId
     * @return
     */
    default List<MemberRule> findByMemberLevel(Long merchantId, Long memberLevelId) {
        return findByMemberLevel(merchantId, Arrays.asList(memberLevelId));
    }

    /**
     * 根据会员等级获取会员规则列表
     *
     * @param memberLevelIdList
     * @return
     */
    List<MemberRule> findByMemberLevel(Long merchantId, List<Long> memberLevelIdList);


    /**
     * 根据会员等级删除对应的会员规则信息
     *
     * @param merchantId
     * @param memberLevelId
     * @return
     */
    int removeByMemberLevel(Long merchantId, Long memberLevelId);

    /**
     * 根据会员等级变化进行派权益或者扣权益
     *
     * @param member
     * @param memberLevelList
     * @param isUpgrade       是否为升级
     * @return
     */
    boolean updateBenefitOfLevel(Member member, List<MemberLevel> memberLevelList, boolean isUpgrade);

    /**
     * 根据当前等级，获取所有已经获得的会员规则权益
     *
     * @param merchantId
     * @param memberLevelId
     * @return
     */
    List<MemberRule> findAllByMemberLevel(Long merchantId, Long memberLevelId);
}
