package com.lyy.user.interfaces.assembler;

import com.lyy.user.account.infrastructure.member.dto.MemberLevelDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberRuleDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberUserInfoDTO;
import com.lyy.user.application.member.IMemberGroupService;
import com.lyy.user.application.member.IMemberLevelService;
import com.lyy.user.application.member.IMemberLiftingService;
import com.lyy.user.application.member.IMemberRuleService;
import com.lyy.user.domain.member.entity.Member;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 会员信息转换
 * <AUTHOR>
 * @className: MemberUserInfoAssembler
 * @date 2021/6/3
 */
@Component
public class MemberUserInfoAssembler implements Assembler<Member, MemberUserInfoDTO> {
    @Autowired
    private IMemberGroupService memberGroupService;
    @Autowired
    private IMemberLevelService memberLevelService;
    @Autowired
    private IMemberRuleService memberRuleService;
    @Autowired
    private IMemberLiftingService memberLiftingService;




    /**
     * 装配 请实现装配过程
     *
     * @param source
     * @param target
     */
    @Override
    public void assemble(Member source, MemberUserInfoDTO target) {
        BeanUtils.copyProperties(source, target);
        //会员组名称
        Optional.ofNullable(memberGroupService.getById(target.getMerchantId(), target.getMemberGroupId()))
                .ifPresent(mg -> target.setMemberGroupName(mg.getName()));
        //等级名称
        Optional.ofNullable(memberLevelService.getById(target.getMerchantId(), target.getMemberLevelId()))
                .ifPresent(ml -> target.setMemberLevelName(ml.getName()));
        //下一个等级
        Optional.ofNullable(memberLevelService.findNextLevel(target.getMerchantId(), target.getMemberLevelId()))
                .ifPresent(ml -> {
                    MemberLevelDTO memberLevelDTO = new MemberLevelDTO();
                    BeanUtils.copyProperties(ml, memberLevelDTO);
                    target.setNextMemberLevel(memberLevelDTO);
                });

        //已经解锁的权益
        Optional.ofNullable(memberRuleService.findAllByMemberLevel(target.getMerchantId(), target.getMemberLevelId()))
                .filter(list -> !list.isEmpty())
                .ifPresent(list -> {
                    List<MemberRuleDTO> memberRuleDTOList = list.stream()
                            .map(mr -> {
                                MemberRuleDTO memberRuleDTO = new MemberRuleDTO();
                                BeanUtils.copyProperties(mr, memberRuleDTO);
                                return memberRuleDTO;
                            })
                            .collect(Collectors.toList());
                    target.setMemberRuleList(memberRuleDTOList);
                });
        //任务
        Optional.ofNullable(memberLiftingService.findInfoByMemberGroup(target.getMerchantId(), target.getMemberGroupId()))
                .ifPresent(target::setMemberLiftingList);
    }
}
