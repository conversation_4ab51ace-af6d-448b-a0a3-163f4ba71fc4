package com.lyy.user.interfaces.controller.user;


import static java.util.Optional.ofNullable;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.user.dto.tag.TagBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserNumberDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserNumberParam;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagSimpleInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagSimpleInfoParam;
import com.lyy.user.account.infrastructure.user.dto.tag.TagStatusDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUnBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserDetailDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserSaveDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingMerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingMerchantUserLinkDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingUserLinkDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.UpdateSpecificBusinessTagsParam;
import com.lyy.user.account.infrastructure.user.dto.tag.ValidList;
import com.lyy.user.application.user.ITagUserService;
import com.lyy.user.infrastructure.execption.BusinessException;
import io.swagger.annotations.Api;
import java.util.List;
import java.util.Objects;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户标签控制类
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@Api(tags = "用户标签管理")
@RestController
@RequestMapping("/tag/user")
@Slf4j
public class TagUserController {

    @Autowired
    private ITagUserService tagUserService;

    /**
     * 查询已打给用户的标签
     *
     * @param param 商户用户Id
     * @return
     */
    @PostMapping("/list")
    public RespBody<Page<TagUserListDTO>> list(@Valid @RequestBody TagUserQueryDTO param) {
        log.debug("查询已打给用户的标签,list:{}", param);
        return RespBody.ok(tagUserService.list(param));
    }

    @PostMapping("/list-all")
    public RespBody<List<TagUserListDTO>> listAllTag(@Valid @RequestBody TagUserQueryDTO param) {
        log.debug("查询已打给指定用户的所有标签,入参:{}", param);
        if(!ofNullable(param).map(TagUserQueryDTO::getMerchantId).isPresent()) {
            throw new BusinessException(UserErrorCode.TAG_USER_QUERY_PARAM_ERROR.getCode(), "必须指定商户ID");
        }
        return RespBody.ok(tagUserService.listAllTag(param));
    }


    /**
     * 查询商户用户的标签
     */
    @PostMapping("/listTagByUser")
    public RespBody<Page<TagUserListDTO>> listTagByUser(@Valid @RequestBody TagUserQueryDTO dto) {
        log.debug("查询商户用户的标签, dto:{}", dto);
        return RespBody.ok(tagUserService.listTagByUser(dto));
    }

    /**
     * 根据标签ID查询用户
     *
     * @param dto
     * @return
     */
    @PostMapping("/findByTagId")
    public RespBody<TagUserDetailDTO> findByTagId(@Valid @RequestBody TagUserQueryDTO dto) {
        log.debug("根据标签ID查询用户,dto:{}", dto);
        return RespBody.ok(tagUserService.findByTagId(dto));
    }

    /**
     * 根据标签ID及商家ID获取对应用户数
     *
     * @param dto
     * @return
     */
    @PostMapping("/findMemberCountByTagIds")
    public RespBody<List<TagCountUserNumberDTO>> findMemberCountByTagIds(@Valid @RequestBody TagCountUserNumberParam dto) {
        log.debug("根据标签ID及商家ID获取对应用户数,dto:{}", dto);
        return RespBody.ok(tagUserService.findMemberCountByTagIds(dto));
    }


    /**
     * 根据条件查商户的标签下用户数量
     *
     * @param dto
     * @return
     */
    @PostMapping("/countFindByTagId")
    public RespBody<Long> countFindByTagId(@Valid @RequestBody TagCountUserQueryDTO dto) {
        log.debug("根据条件查商户的标签下用户数量,dto:{}", dto);
        return RespBody.ok(tagUserService.countFindByTagId(dto));
    }

    /**
     * 新增或修改用户标签
     *
     * @param dto
     * @return
     */
    @PostMapping("/saveOrUpdateTagUser")
    public RespBody<Long> saveOrUpdateTagUser(@Valid @RequestBody TagUserSaveDTO dto) {
        log.debug("新增或修改用户标签,dto:{}", dto);
        return RespBody.ok(tagUserService.saveOrUpdateTagUser(dto));
    }

    /**
     * 变更标签状态
     *
     * @param dto
     * @return
     */
    @PostMapping("/changeTagStatus")
    public RespBody<Boolean> changeTagStatus(@Valid @RequestBody TagStatusDTO dto) {
        log.debug("变更标签状态,dto:{}", dto);
        return RespBody.ok(tagUserService.changeTagStatus(dto));
    }


    /**
     * 标签绑定用户
     *
     * @param dto
     * @return
     */
    @PostMapping("/bindUser")
    public RespBody<Boolean> bindUser(@Valid @RequestBody TagBindUserDTO dto) {
        log.debug("标签绑定用户,dto:{}", dto);
        return RespBody.ok(tagUserService.bindUser(dto));
    }

    /**
     * 标签解绑用户
     *
     * @param dto
     * @return
     */
    @PostMapping("/unBindUser")
    public RespBody<Boolean> unBindUser(@Valid @RequestBody TagUnBindUserDTO dto) {
        log.debug("标签解绑用户,dto:{}", dto);
        return RespBody.ok(tagUserService.unBindUser(dto));
    }

    /**
     * 修改标签名称
     *
     * @param dto
     * @return
     */
    @PostMapping("/updateTagName")
    public RespBody<Boolean> updateTagName(@Valid @RequestBody TagDTO dto) {
        log.debug("修改标签名称,dto:{}", dto);
        if (dto.getTagId() == null && StringUtils.isBlank(dto.getOldName())) {
            return RespBody.fail(UserErrorCode.TAG_ID_AND_ORIGINAL_NAME_CAN_NOT_BOTH_NULL_PARAM_ERROR);
        }
        Long tagId = tagUserService.updateTagName(dto);
        return RespBody.ok(Objects.nonNull(tagId));
    }

    /**
     * 修改标签名称V2
     */
    @PostMapping("/name/modify")
    public RespBody<Long> modifyTagName(@Valid @RequestBody TagDTO dto) {
        log.debug("修改标签名称V2, dto:{}", dto);
        if (dto.getTagId() == null && StringUtils.isBlank(dto.getOldName())) {
            return RespBody.fail(UserErrorCode.TAG_ID_AND_ORIGINAL_NAME_CAN_NOT_BOTH_NULL_PARAM_ERROR);
        }
        return RespBody.ok(tagUserService.updateTagName(dto));
    }

    /**
     * 打标签
     *
     * @param dto
     * @return
     */
    @PostMapping("/taggingUser")
    public RespBody<Boolean> taggingUser(@Valid @RequestBody TaggingMerchantUserDTO dto) {
        log.debug("打标签,dto:{}", dto);
        tagUserService.taggingUser(dto);
        return RespBody.ok(Boolean.TRUE);
    }


    /**
     * 打平台标签
     *
     * @param list
     * @return
     */
    @PostMapping("/tagging/platformTag")
    public RespBody<Boolean> taggingPlatformUser(@Valid @RequestBody ValidList<TaggingUserLinkDTO> list, BindingResult bindingResult) {
        log.debug("打平台标签,list:{}", list);
        log.info("废弃打平台标签:");
        return RespBody.ok(Boolean.TRUE);
    }

    /**
     * 打商家标签
     *
     * @param list
     * @return
     */
    @PostMapping("/tagging/merchantTag")
    public RespBody<Boolean> taggingMerchant(@Valid @RequestBody ValidList<TaggingMerchantUserLinkDTO> list, BindingResult bindingResult) {
        log.debug("打商家标签,list:{}", list);
        if (bindingResult.hasErrors()) {
            log.error("打商家标签异常:{}", bindingResult.getFieldError());
            throw new BusinessException(UserErrorCode.TAG_USER_SAVE_ERROR.getCode(), bindingResult.getFieldError().getDefaultMessage());
        }

        tagUserService.taggingMerchant(list.getList());
        return RespBody.ok(Boolean.TRUE);
    }

    @GetMapping("/tagging/syncMerchantTag")
    public RespBody<Boolean> syncMerchantTag(@RequestParam List<Long> merchantIds, @RequestParam List<Long> tagIds) {
        log.debug("测试绑定标签与用户:,merchantIds:{},tagIds:{}", merchantIds, tagIds);
        ;

        tagUserService.syncMerchantTag(merchantIds, tagIds);
        return RespBody.ok(Boolean.TRUE);
    }


    /**
     * 批量更新会员所属特定类型标签接口(清空旧的，只保留新的标签)
     *
     * @param dto
     * @return
     */
    @PostMapping("/batchUpdateSpecificBusinessTagsByUserId")
    public RespBody<Boolean> batchUpdateSpecificBusinessTagsByUserId(@Valid @RequestBody UpdateSpecificBusinessTagsParam dto) {
        log.debug("批量更新会员所属特定类型标签接口,dto:{}", dto);
        return RespBody.ok(tagUserService.batchUpdateSpecificBusinessTagsByUserId(dto));
    }

    /**
     * 根据条件查询标签信息
     *
     * @return
     */
    @PostMapping("/tagSimpleInfoList")
    public RespBody<List<TagSimpleInfoDTO>> tagSimpleInfoList(@Valid @RequestBody TagSimpleInfoParam param) {
        log.debug("根据条件查询标签信息, param:{}", param);
        return RespBody.ok(tagUserService.tagSimpleInfoList(param));
    }

}
