package com.lyy.user.interfaces.schedule;

import com.lyy.user.domain.correction.dto.GcConditionDTO;
import com.lyy.user.domain.correction.service.RollBackGcAction;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.util.ShardingUtil;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * @date 2023/10/13
 */
@Slf4j
//@JobHandler(value = "dirtyDataRollBackJob")
//@Component
public class CommonMemberDataRollBack extends IJobHandler {

    @Value("#{'${sharding.gc.needClearDS:0,1,2,3}'.split(',')}")
    private List<Integer> needClearDsNo;

    @Setter(onMethod_ = @Autowired)
    private RollBackGcAction rollBackGcAction;

    @Override
    public ReturnT<String> execute(String spec) throws Exception {
        if (StringUtils.isBlank(spec)) {
            return FAIL;
        }

        String[] split = spec.split(";");
        List<Integer> tableNos = Arrays.stream(Optional.of(split[0])
                        .map(info -> info.split(","))
                        .orElse(new String[0]))
                .map(Integer::valueOf).collect(Collectors.toList());
        int currentIndex = ShardingUtil.getShardingVo().getIndex();
        log.info("CommonMemberDataRollBack => 当前分片[{}] ### 参数: [{}]", currentIndex, spec);

        List<Long> merchantIds = split.length > 1 ? Arrays.stream(Optional.of(split[1])
                        .map(info -> info.split(","))
                        .orElse(new String[0]))
                .filter(StringUtils::isNotEmpty)
                .map(Long::valueOf).collect(Collectors.toList()) : null;
        if (needClearDsNo.contains(currentIndex)) {
            try (RollBackGcAction rollBack = rollBackGcAction.chooseDS(currentIndex)) {
                for (Integer tableNo : tableNos) {
                    if (CollectionUtils.isEmpty(merchantIds)) {
                        GcConditionDTO gcConditionDTO = GcConditionDTO
                                .builder()
                                .dsNo(currentIndex)
                                .code(tableNo)
                                .pageSize(1000L)
                                .build();
                        rollBackGcAction.doAction(gcConditionDTO);
                        continue;
                    }

                    // 指定商户
                    for (Long merchantId : merchantIds) {
                        GcConditionDTO gcConditionDTO = GcConditionDTO
                                .builder()
                                .dsNo(currentIndex)
                                .code(tableNo)
                                .pageSize(1000L)
                                .merchantId(merchantId)
                                .build();
                        rollBackGcAction.doAction(gcConditionDTO);
                    }
                }
            } catch (Exception e) {
                log.error("清理回滚 fail, err msg: {}", e.getLocalizedMessage());
                return FAIL;
            }
        }
        return SUCCESS;
    }
}
