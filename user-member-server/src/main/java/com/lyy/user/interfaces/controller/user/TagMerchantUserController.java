package com.lyy.user.interfaces.controller.user;


import cn.lyy.base.utils.SnowflakeIdWorkerUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserInfoQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagExternalSystemQuery;
import com.lyy.user.account.infrastructure.user.dto.tag.TagOfExternalSystemDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagOfExternalSystemVO;
import com.lyy.user.application.user.IMerchantUserTagService;
import com.lyy.user.infrastructure.util.JSONUtil;
import io.swagger.annotations.Api;
import java.util.List;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商户标签管理
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@Api(tags = "商户标签管理")
@RestController
@RequestMapping("/tag/merchant/user")
@Slf4j
public class TagMerchantUserController {

    @Autowired
    private IMerchantUserTagService tagMerchantUserService;

    /**
     * 查询标签用户
     *
     * @param queryDTO 标签Id
     * @return
     */
    @PostMapping("/listByTagIds")
    public RespBody<Page<MerchantUserInfoDTO>> listByTagIds(@Valid @RequestBody MerchantUserInfoQueryDTO queryDTO) {
        log.debug("查询标签用户,queryDTO:{}", queryDTO);
        return RespBody.ok(tagMerchantUserService.listByTagIds(queryDTO));
    }

    @GetMapping("/next/id")
    public ResponseEntity<?> nextId() {
        long id = SnowflakeIdWorkerUtils.INSTANCE.getSnowflakeIdWorker().nextId();
        return ResponseEntity.ok(id);
    }

    /**
     * 查询用户标签-其他平台相关
     */
    @PostMapping("/external-system/get")
    public RespBody<TagOfExternalSystemVO> getTagOfExternalSystem(@RequestBody TagOfExternalSystemDTO dto) {
        if (dto.getMerchantUserId() == null && dto.getUserId() == null) {
            return RespBody.fail(GlobalErrorCode.BAD_REQUEST, "merchantUserId和lyyUserId必须其中一个不为空", null);
        }
        return RespBody.ok(tagMerchantUserService.getTagOfExternalSystem(dto));
    }

    /**
     * 批量查询用户其他平台相关标签
     */
    @GetMapping("/external-system")
    public RespBody<List<TagOfExternalSystemVO>> listTagOfExternalSystem(TagExternalSystemQuery query) {
        if (query.getMerchantUserId() == null && query.getUserId() == null) {
            return RespBody.fail(GlobalErrorCode.BAD_REQUEST, "merchantUserId和lyyUserId必须其中一个不为空", null);
        }
        log.debug("批量查询用户其他平台相关标签: {}", log.isDebugEnabled() ? JSONUtil.toJSONString(query) : query);
        return RespBody.ok(tagMerchantUserService.listTagOfExternalSystem(query));
    }
}
