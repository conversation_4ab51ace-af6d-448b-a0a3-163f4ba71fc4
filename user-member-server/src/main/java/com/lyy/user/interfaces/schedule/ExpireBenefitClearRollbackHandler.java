package com.lyy.user.interfaces.schedule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lyy.user.domain.benefit.entity.Benefit;
import com.lyy.user.domain.benefit.entity.BenefitTmp;
import com.lyy.user.domain.benefit.repository.BenefitMapper;
import com.lyy.user.domain.benefit.repository.BenefitTmpMapper;
import com.lyy.user.interfaces.assembler.BenefitAssembler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.util.ShardingUtil;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 过期um_benefit记录清除回滚
 */
@Slf4j
@JobHandler(value = "expireBenefitClearRollback")
@Component
public class ExpireBenefitClearRollbackHandler extends IJobHandler {

    @Resource
    private BenefitMapper benefitMapper;

    @Resource
    private BenefitTmpMapper benefitTmpMapper;

    @Resource
    private BenefitAssembler benefitAssembler;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        if (StringUtils.isBlank(param)) {
            return FAIL;
        }
        String[] params = param.split(",");
        List<Long> merchantIds;
        //清除次数, 默认1次
        try {
            merchantIds = Arrays.stream(params).map(Long::parseLong).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("定时任务参数解析异常，", e);
            return FAIL;
        }
        int currentIndex = ShardingUtil.getShardingVo().getIndex();
        int total = ShardingUtil.getShardingVo().getTotal();
        log.info("[过期um_benefit记录清除回滚] 开始, total={}, currentIndex={}, merchants={}", total, currentIndex, merchantIds);
        if (currentIndex >= 4) {
            log.debug("本分片超过处理分片上限，不执行任务");
            return SUCCESS;
        }
        try {
            expireBenefitClearRollback((long) currentIndex, merchantIds);
        } catch (Exception e) {
            log.error("[过期um_benefit记录清除回滚]定时任务执行异常，", e);
            return FAIL;
        }
        return SUCCESS;
    }

    public void expireBenefitClearRollback(long dummyMerchantId, List<Long> merchantIds) {
        long shardMin = dummyMerchantId * 32;
        long shardMax = shardMin + 32 - 1;
        List<Long> shardMerchantIds = merchantIds.stream()
                .filter(merchantId -> merchantId % 128 >= shardMin && merchantId % 128 <= shardMax).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shardMerchantIds)) {
            log.info("[过期um_benefit记录清除回滚] 分库 [{}] 无此分片商户需处理", dummyMerchantId);
            return;
        }
        log.info("[过期um_benefit记录清除回滚] 处理分库：[{}] 的数据, 处理商户：{}", dummyMerchantId, shardMerchantIds);
        for (Long shardMerchantId : shardMerchantIds) {
            for (int i = 1;;i++) {
                List<BenefitTmp> benefitTmps = benefitTmpMapper.selectList(getRollbackBenefitWrapper(shardMerchantId, 1000));
                if (CollectionUtils.isEmpty(benefitTmps)) {
                    break;
                }
                List<Benefit> benefits = benefitTmps.stream().map(benefitAssembler::toBenefit).collect(Collectors.toList());
                Integer rows = benefitMapper.insertBatch(benefits);
                if (rows > 0) {
                    List<Long> benefitIds = benefitTmps.stream().map(BenefitTmp::getId).collect(Collectors.toList());
                    benefitTmpMapper.deleteBatchIds(benefitIds);
                    log.info("[过期um_benefit记录清除回滚], 第[{}]次回滚，商户：[{}], 回滚条数：[{}], benefitIds：{}", i, shardMerchantId, rows, benefitIds);
                }
            }
        }
    }

    private LambdaQueryWrapper<BenefitTmp> getRollbackBenefitWrapper(Long merchantId, Integer limit) {
        String limitStr = "limit " + limit;
        return Wrappers.<BenefitTmp>lambdaQuery()
                .eq(BenefitTmp::getMerchantId, merchantId)
                .last(limitStr);
    }

}
