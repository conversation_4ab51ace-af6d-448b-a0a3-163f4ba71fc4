package com.lyy.user.interfaces.controller.statistics;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantStatisticsRecordDTO;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserRankConditionDTO;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserRankRecordDTO;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserStatisticsDailyDTO;
import com.lyy.user.account.infrastructure.statistics.dto.MerchantUserStatisticsListDTO;
import com.lyy.user.account.infrastructure.statistics.dto.StatisticsUserQueryDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsConditionDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsRecordDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsUpdateDTO;
import com.lyy.user.application.statistics.StatisticsService;
import com.lyy.user.infrastructure.execption.BusinessException;
import io.swagger.annotations.Api;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 统计接口
 *
 * <AUTHOR>
 * @since 2021/4/20 10:13
 */
@Api(tags = "会员统计")
@Slf4j
@RestController
@RequestMapping("/statistics")
public class StatisticsController {

    @Resource
    private StatisticsService statisticsService;

    /**
     * 更新用户统计（包括每日+总计）
     * @param param
     * @return
     */
    @PostMapping("/update")
    public RespBody<Void> updateStatistics(@RequestBody @Valid UserStatisticsUpdateDTO param, BindingResult result) {
        log.debug("更新统计信息 param: {}", param);
        if (result.hasErrors()) {
            throw new BusinessException(GlobalErrorCode.BAD_REQUEST);
        }
        statisticsService.updateStatisticsWithIdempotent(param, false);
        return RespBody.ok();
    }

    /**
     * 获取商户总统计信息
     * @param merchantId
     * @return
     */
    @GetMapping("/getMerchantStatistics")
    public RespBody<MerchantStatisticsRecordDTO> getMerchantStatistics(@RequestParam("merchantId") Long merchantId) {
        log.debug("获取商户总统计信息 merchantId: {}", merchantId);
        return RespBody.ok(statisticsService.getMerchantStatistics(merchantId));
    }


    /**
     * 查询统计用户列表
     * @param statisticsUserQueryDTO
     * @return
     */
    @PostMapping("/queryStatisticsUserList")
    public RespBody<Page<MerchantUserStatisticsListDTO>> queryStatisticsUserList(@RequestBody @Validated StatisticsUserQueryDTO statisticsUserQueryDTO){
        log.debug("查询统计消费记录,param:{}", statisticsUserQueryDTO);
        Page<MerchantUserStatisticsListDTO> page = statisticsService.queryStatisticsUserList(statisticsUserQueryDTO);
        return RespBody.ok(page);
    }

    /**
     * 获取用户总统计信息
     * @param param
     * @return
     */
    @PostMapping("/find")
    public RespBody<UserStatisticsRecordDTO> find(@RequestBody @Valid UserStatisticsConditionDTO param, BindingResult result) {
        log.info("获取用户总统计信息 param: {}", param);
        if (result.hasErrors()) {
            throw new BusinessException(GlobalErrorCode.BAD_REQUEST);
        }

        return RespBody.ok(statisticsService.find(param));
    }

    /**
     * 获取用户当日统计信息
     * @param param
     * @return
     */
    @PostMapping("/daily/current-date")
    public RespBody<MerchantUserStatisticsDailyDTO> getUserCurrentDateStatistics(@RequestBody @Valid UserStatisticsConditionDTO param, BindingResult result) {
        log.info("获取用户当日统计信息 param: {}", param);
        if (result.hasErrors()) {
            throw new BusinessException(GlobalErrorCode.BAD_REQUEST);
        }

        return RespBody.ok(statisticsService.getUserCurrentDateStatistics(param));
    }

    /**
     * 获取用户当日统计信息
     * @param condition
     * @return
     */
    @PostMapping("/recharge-rank")
    public RespBody<List<MerchantUserRankRecordDTO>> getRechargeRank(@RequestBody MerchantUserRankConditionDTO condition) {
        log.info("获取用户充值排行榜 condition: {}", condition);
        return RespBody.ok(statisticsService.getRechargeRank(condition));
    }
}
