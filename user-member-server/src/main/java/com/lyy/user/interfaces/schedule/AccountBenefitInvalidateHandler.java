package com.lyy.user.interfaces.schedule;

import static java.util.Optional.ofNullable;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.constant.AccountBenefitStatusEnum;
import com.lyy.user.account.infrastructure.constant.AccountRecordOperationTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.application.account.AccountService;
import com.lyy.user.application.benefit.SmallVenueAccountService;
import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.domain.account.entity.AccountBenefit;
import com.lyy.user.domain.account.entity.AccountRecord;
import com.lyy.user.domain.account.repository.AccountBenefitMapper;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.repository.account.AccountBenefitRepository;
import com.lyy.user.infrastructure.repository.account.AccountRecordRepository;
import com.lyy.user.infrastructure.repository.account.SmallVenueAccountRepository;
import com.lyy.user.infrastructure.support.specification.account.FrequencyBenefitSpecification;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.util.ShardingUtil;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 类描述：处理权益过期
 * <p>
 *
 * <AUTHOR>
 * @since 2021/5/13 17:01
 */
@Slf4j
@JobHandler(value = "accountBenefitInvalidate")
@Component
public class AccountBenefitInvalidateHandler extends IJobHandler {

    @Resource
    private AccountBenefitMapper accountBenefitMapper;

    @Resource
    private AccountService accountService;

    @Resource(name = "smallVenueAccountRepository")
    private SmallVenueAccountRepository accountRepository;
    @Resource
    private AccountRecordRepository accountRecordRepository;
    @Resource
    private AccountBenefitRepository accountBenefitRepository;

    @Override
    public ReturnT<String> execute(String param) throws Exception {

        int currentIndex = ShardingUtil.getShardingVo().getIndex();
        int total = ShardingUtil.getShardingVo().getTotal();
        log.info("[处理权益过期] 开始, total={}, currentIndex={}", total, currentIndex);
        for (long currentShard = 0; currentShard < 128; currentShard++) {
            log.debug("currentShard % total = {}", currentShard % total);
            if (currentShard % total == currentIndex) {
                invalidateBenefit(currentShard);
                smallVenueBenefitExpire(currentShard);
            }
        }
        return SUCCESS;
    }

    public void invalidateBenefit(long dummyMerchantId) {
        log.info("[处理权益过期] 处理分表：[{}] 的数据", dummyMerchantId);
        int pageIndex = 1;
        int pageSize = 100;
        IPage<AccountBenefit> page;
        do {
            page = accountBenefitRepository.pageExpiredBenefit(dummyMerchantId, pageIndex, pageSize);
            List<AccountBenefit> records = page.getRecords();
            if (records.isEmpty()) {
                return;
            }
            records.forEach(this::expireBenefit);
        } while (true);
    }

    private void expireBenefit(AccountBenefit accountBenefit) {
        try {
            if (ofNullable(accountBenefit.getBalance()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0) {
                decreaseBenefit(accountBenefit);
            }
            AccountBenefit update = new AccountBenefit();
            update.setStatus(2);
            update.setUpdateTime(new Date());
            accountBenefitMapper.update(update, new UpdateWrapper<AccountBenefit>()
                    .eq("id", accountBenefit.getId())
                    .eq("merchant_id", accountBenefit.getMerchantId()));
        } catch (BusinessException e) {
            log.warn("", e);
        } catch (Exception e) {
            log.error("处理权益过期异常", e);
        }
    }

    private void decreaseBenefit(AccountBenefit accountBenefit) {
        AccountBenefitAdjustDTO adjustDTO = new AccountBenefitAdjustDTO();
        adjustDTO.setId(accountBenefit.getId());
        adjustDTO.setAdjustType(AdjustTypeEnum.DECREMENT);
        adjustDTO.setAmount(accountBenefit.getBalance());
        adjustDTO.setUserId(accountBenefit.getUserId());
        adjustDTO.setMerchantId(accountBenefit.getMerchantId());
        adjustDTO.setMerchantUserId(accountBenefit.getMerchantUserId());
        adjustDTO.setDescription("系统定时器过期");
        adjustDTO.setBenefitId(accountBenefit.getBenefitId());
        adjustDTO.setRecordType(AccountRecordTypeEnum.EXPIRED_ALL.getCode());
        accountService.decreaseAccountBenefit(Arrays.asList(adjustDTO), false,null);
    }

    void smallVenueBenefitExpire(long dummyMerchantId) {
        log.info("[小场地处理权益过期]--处理分表：[{}] 的数据", dummyMerchantId);
        int pageIndex = 1;
        int pageSize = 100;
        IPage<AccountBenefit> page;
        FrequencyBenefitSpecification frequencyBenefitSpecification = new FrequencyBenefitSpecification();
        do {
            Date date = new Date();
            page = accountRepository.pageExpiredBenefit(dummyMerchantId, pageIndex, pageSize);
            List<AccountBenefit> accountBenefits = page.getRecords();
            if (accountBenefits.isEmpty()) {
                return;
            }
            List<Long> accountIds = accountBenefits.stream()
                    .map(AccountBenefit::getAccountId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, String> cardMap = accountRepository.selectAccountCard4ExpiredBenefit(dummyMerchantId, accountIds)
                    .stream()
                    .collect(Collectors.toMap(Account::getId, Account::getCardNo));
            AccountBenefitInvalidateHandler handler = (AccountBenefitInvalidateHandler)AopContext.currentProxy();
            accountBenefits.forEach(accountBenefit -> {
                try {
                    handler.expireBenefit(frequencyBenefitSpecification, date, cardMap, accountBenefit);
                } catch (Exception e) {
                    log.error("小场地处理权益过期异常,accountBenefit:{}", accountBenefit, e);
                }
            });
        } while (true);

    }

    @Transactional(rollbackFor = Exception.class)
    public void expireBenefit(FrequencyBenefitSpecification frequencyBenefitSpecification, Date date, Map<Long, String> cardMap,
                              AccountBenefit accountBenefit) {
        if (log.isDebugEnabled()) {
            log.debug("[小场地处理权益过期]--过期权益明细 {}", accountBenefit);
        }
        AccountBenefit entity = assembleBenefit(date, accountBenefit);
        accountRepository.updateAccountBenefit(accountBenefit.getMerchantId(), entity);

        boolean isFrequency = frequencyBenefitSpecification.isSatisfiedBy(accountBenefit.getValueType());
        accountRepository.updateAccountStatistics4Expired(accountBenefit.getMerchantId(),
                accountBenefit.getUserId(),
                accountBenefit.getMerchantBenefitClassifyId(),
                accountBenefit.getBalance().negate(),
                isFrequency, date);
        AccountRecord accountRecord = assembleRecord(accountBenefit, cardMap.get(accountBenefit.getAccountId()), date);
        try {
            accountRecordRepository.insert(accountRecord);
        } catch (DuplicateKeyException exception) {
            log.error("权益过期会员流水添加异常,重试", exception);
            accountRecordRepository.insert(accountRecord);
        }
    }

    private AccountBenefit assembleBenefit(Date date, AccountBenefit accountBenefit) {
        AccountBenefit entity = new AccountBenefit();
        entity.setId(accountBenefit.getId());
        entity.setBalance(BigDecimal.ZERO);
        entity.setUpdateTime(date);
        entity.setStatus(AccountBenefitStatusEnum.EXPIRED.getStatus());
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--权益过期--权益明细过期更新, entity: {}", entity);
        }
        return entity;
    }

    private AccountRecord assembleRecord(AccountBenefit accountBenefit, String cardNo, Date date) {
        AccountRecord record = new AccountRecord();
        record.setAccountId(accountBenefit.getAccountId());
        record.setCardNo(cardNo);
        record.setRecordType(AccountRecordTypeEnum.SV_STORED_VALUE_EXPIRED.getCode());
        record.setAccountBenefitId(accountBenefit.getId());
        record.setUserId(accountBenefit.getUserId());
        record.setMerchantUserId(accountBenefit.getMerchantUserId());
        record.setMerchantId(accountBenefit.getMerchantId());
        record.setStoreId(accountBenefit.getStoreId());

        record.setMerchantBenefitClassifyId(accountBenefit.getMerchantBenefitClassifyId());
        record.setBenefitClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        record.setCreateTime(date);
        record.setConsumeType(AccountRecordOperationTypeEnum.BENEFIT_MODIFY.getCode());

        // operationType  相当于一个大分类，recordType 属于 operationType 的子分类
        record.setOperationType(AccountRecordOperationTypeEnum.BENEFIT_MODIFY.getCode());
        record.setMode(AdjustTypeEnum.DECREMENT.getType());
        record.setBenefitId(SmallVenueAccountService.DEFAULT_BENEFIT_ID);

        record.setInitialBenefit(accountBenefit.getBalance());
        record.setOriginalBenefit(accountBenefit.getBalance());
        record.setActualBenefit(accountBenefit.getBalance());
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--权益过期--记录账户权益流水, record: {}", record);
        }
        return record;
    }

}
