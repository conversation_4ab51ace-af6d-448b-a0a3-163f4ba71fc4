package com.lyy.user.interfaces.assembler;

import com.lyy.user.account.infrastructure.member.dto.MemberLevelDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberUserLevelDTO;
import com.lyy.user.application.member.IMemberLevelService;
import com.lyy.user.application.member.IMemberLiftingService;
import com.lyy.user.domain.member.entity.Member;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 会员等级信息转换
 * <AUTHOR>
 * @className: MemberUserLevelAssembler
 * @date 2021/6/5
 */
@Component
public class MemberUserLevelAssembler implements Assembler<Member, MemberUserLevelDTO> {

    @Autowired
    private IMemberLevelService memberLevelService;
    @Autowired
    private IMemberLiftingService memberLiftingService;

    /**
     * 装配 请实现装配过程
     *
     * @param source
     * @param target
     */
    @Override
    public void assemble(Member source, MemberUserLevelDTO target) {
        BeanUtils.copyProperties(source,target);

        //等级信息
        Optional.ofNullable(memberLevelService.findByMemberGroup(target.getMerchantId(),target.getMemberGroupId()))
                .ifPresent(levelList->{
                    List<MemberLevelDTO> memberLevelDTOList = levelList.stream()
                            .map(level->{
                                MemberLevelDTO memberLevelDTO = new MemberLevelDTO();
                                BeanUtils.copyProperties(level,memberLevelDTO);
                                return memberLevelDTO;
                            })
                            .collect(Collectors.toList());
                    target.setMemberLevelList(memberLevelDTOList);
                });

        //成长任务信息
        Optional.ofNullable(memberLiftingService.findInfoByMemberGroup(target.getMerchantId(),target.getMemberGroupId()))
                .ifPresent(memberLiftingList->target.setMemberLiftingList(memberLiftingList));
    }
}
