package com.lyy.user.interfaces.schedule.v2;

import static java.util.Optional.ofNullable;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.constant.AccountBenefitStatusEnum;
import com.lyy.user.account.infrastructure.constant.AccountRecordOperationTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.AccountBenefitNumTypeEnum;
import com.lyy.user.application.account.AccountService;
import com.lyy.user.application.benefit.SmallVenueAccountService;
import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.domain.account.entity.AccountBenefit;
import com.lyy.user.domain.account.entity.AccountRecord;
import com.lyy.user.domain.account.repository.AccountBenefitMapper;
import com.lyy.user.domain.doris.entity.VUmAccountBenefitExpired;
import com.lyy.user.domain.doris.repository.MemberBenefitDorisRepository;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.repository.account.AccountBenefitRepository;
import com.lyy.user.infrastructure.repository.account.AccountRecordRepository;
import com.lyy.user.infrastructure.repository.account.SmallVenueAccountRepository;
import com.lyy.user.infrastructure.support.specification.account.FrequencyBenefitSpecification;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

/**
 * 类描述：处理权益过期
 * <p>
 *
 * <AUTHOR>
 * @since 2021/5/13 17:01
 */
@Slf4j
@JobHandler(value = "accountBenefitV2InvalidateJob")
@Component
public class AccountBenefitInvalidateJob extends IJobHandler {

    @Resource
    private AccountBenefitMapper accountBenefitMapper;

    @Resource
    private AccountService accountService;

    @Resource(name = "smallVenueAccountRepository")
    private SmallVenueAccountRepository accountRepository;
    @Resource
    private AccountRecordRepository accountRecordRepository;
    @Resource
    private AccountBenefitRepository accountBenefitRepository;
    @Resource
    private MemberBenefitDorisRepository memberBenefitDorisRepository;

    @Value("${member.benefit.expiredPageSize:200}")
    private Integer EXPIRED_BENEFIT_PAGE_SIZE;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        StopWatch stopWatch = new StopWatch();
        try {
            Long merchantId = null;
            if (StringUtils.isNotBlank(param)) {
                log.info("指定商家执行过期任务: {}", param);
                try {
                    merchantId = Long.valueOf(param);
                } catch (NumberFormatException e) {
                    log.info("参数错误");
                    return ReturnT.FAIL;
                }
            }
            stopWatch.start();
            invalidateBenefit(merchantId);
            stopWatch.stop();
            log.info("过期权益处理耗时: {}秒", stopWatch.getTotalTimeSeconds());
            stopWatch.start();
            smallVenueBenefitExpire(merchantId);
            stopWatch.stop();
            log.info("多金宝过期权益处理耗时: {}秒", stopWatch.getTotalTimeSeconds());
        } finally {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
        }
        return SUCCESS;
    }

    public void invalidateBenefit(Long mid) {
        int pageIndex = 1;
        IPage<VUmAccountBenefitExpired> page;
        do {
            page = memberBenefitDorisRepository.listExpiredBenefit(mid, pageIndex, EXPIRED_BENEFIT_PAGE_SIZE);
            log.info("[Doris过期权益]-查询页数: {}, 页大小: {}", pageIndex, EXPIRED_BENEFIT_PAGE_SIZE);
            List<VUmAccountBenefitExpired> records = page.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                return;
            }
            Map<Long, List<VUmAccountBenefitExpired>> map = records.stream()
                    .collect(Collectors.groupingBy(VUmAccountBenefitExpired::getMerchantId));
            for (Entry<Long, List<VUmAccountBenefitExpired>> entry : map.entrySet()) {
                Long merchantId = entry.getKey();
                List<VUmAccountBenefitExpired> list = entry.getValue();
                log.info("[Doris过期权益]-当前页数：{} 商家: {}, 数量: {}", pageIndex, merchantId, list.size());
                List<Long> ids = list.stream().map(VUmAccountBenefitExpired::getId).collect(Collectors.toList());
                List<AccountBenefit> accountBenefits = accountBenefitRepository.listAccountBenefit(merchantId, ids);
                if (CollectionUtils.isEmpty(accountBenefits)) {
                    log.warn("[Doris过期权益]-Doris存在，PGSQL不存在权益明细，merchantId: {} accountBenefitId: {}", merchantId, ids);
                }
                accountBenefits.forEach(this::expireBenefit);
            }
            pageIndex++;
        } while (true);
    }

    private void expireBenefit(AccountBenefit accountBenefit) {
        try {
            if (ofNullable(accountBenefit.getBalance()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0) {
                decreaseBenefit(accountBenefit);
            }
            AccountBenefit update = new AccountBenefit();
            update.setStatus(2);
            update.setUpdateTime(new Date());
            accountBenefitMapper.update(update, new UpdateWrapper<AccountBenefit>()
                    .eq("id", accountBenefit.getId())
                    .eq("merchant_id", accountBenefit.getMerchantId()));
        } catch (BusinessException e) {
            log.warn("处理权益过期业务异常 {} ", accountBenefit, e);
        } catch (Exception e) {
            log.error("处理权益过期异常 {}", accountBenefit, e);
        }
    }

    private void decreaseBenefit(AccountBenefit accountBenefit) {
        AccountBenefitAdjustDTO adjustDTO = new AccountBenefitAdjustDTO();
        adjustDTO.setId(accountBenefit.getId());
        adjustDTO.setAdjustType(AdjustTypeEnum.DECREMENT);
        adjustDTO.setAmount(accountBenefit.getBalance());
        adjustDTO.setUserId(accountBenefit.getUserId());
        adjustDTO.setMerchantId(accountBenefit.getMerchantId());
        adjustDTO.setMerchantUserId(accountBenefit.getMerchantUserId());
        adjustDTO.setDescription("系统定时器过期");
        adjustDTO.setBenefitId(accountBenefit.getBenefitId());
        adjustDTO.setRecordType(AccountRecordTypeEnum.EXPIRED_ALL.getCode());
        adjustDTO.setExcludeUserTag(true);
        accountService.decreaseAccountBenefit(Collections.singletonList(adjustDTO), false, null);
    }

    void smallVenueBenefitExpire(Long mid) {
        log.info("[Doris多金宝过期权益查询]");
        int pageIndex = 1;
        IPage<VUmAccountBenefitExpired> page;
        FrequencyBenefitSpecification frequencyBenefitSpecification = new FrequencyBenefitSpecification();
        do {
            Date date = new Date();
            page = memberBenefitDorisRepository.listVenueExpiredBenefit(mid, pageIndex, EXPIRED_BENEFIT_PAGE_SIZE);
            log.info("[Doris多金宝过期权益查询]-页数: {}, 页大小: {}", pageIndex, EXPIRED_BENEFIT_PAGE_SIZE);
            List<VUmAccountBenefitExpired> records = page.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                return;
            }
            Map<Long, List<VUmAccountBenefitExpired>> map = records.stream()
                    .collect(Collectors.groupingBy(VUmAccountBenefitExpired::getMerchantId));
            for (Entry<Long, List<VUmAccountBenefitExpired>> entry : map.entrySet()) {
                Long merchantId = entry.getKey();
                List<VUmAccountBenefitExpired> list = entry.getValue();
                log.info("[Doris多金宝过期权益]-当前页数：{} 商家: {}, 数量: {}", pageIndex, merchantId, list.size());
                List<Long> ids = list.stream().map(VUmAccountBenefitExpired::getId).collect(Collectors.toList());
                List<AccountBenefit> accountBenefits = accountBenefitRepository.listAccountBenefit(merchantId, ids);
                if (CollectionUtils.isEmpty(accountBenefits)) {
                    log.warn("[Doris多金宝过期权益]-Doris存在，PGSQL不存在权益明细，merchantId: {} accountBenefitId: {}", merchantId, ids);
                    continue;
                }
                List<Long> accountIds = accountBenefits.stream()
                        .map(AccountBenefit::getAccountId)
                        .distinct()
                        .collect(Collectors.toList());
                Map<Long, String> cardMap = accountRepository.selectAccountCard4ExpiredBenefit(merchantId, accountIds)
                        .stream()
                        .collect(Collectors.toMap(Account::getId, Account::getCardNo));
                AccountBenefitInvalidateJob handler = (AccountBenefitInvalidateJob) AopContext.currentProxy();
                accountBenefits.forEach(accountBenefit -> {
                    try {
                        handler.expireBenefit(frequencyBenefitSpecification, date, cardMap, accountBenefit);
                    } catch (Exception e) {
                        log.error("小场地处理权益过期异常,accountBenefit:{}", accountBenefit, e);
                    }
                });
            }
            pageIndex++;
        } while (true);

    }

    @Transactional(rollbackFor = Exception.class)
    public void expireBenefit(FrequencyBenefitSpecification frequencyBenefitSpecification, Date date, Map<Long, String> cardMap,
                              AccountBenefit accountBenefit) {
        if (log.isDebugEnabled()) {
            log.debug("[小场地处理权益过期]--过期权益明细 {}", accountBenefit);
        }
        AccountBenefit entity = assembleBenefit(date, accountBenefit);
        accountRepository.updateAccountBenefit(accountBenefit.getMerchantId(), entity);

        boolean isFrequency = frequencyBenefitSpecification.isSatisfiedBy(accountBenefit.getValueType());
        accountRepository.updateAccountStatistics4Expired(accountBenefit.getMerchantId(),
                accountBenefit.getUserId(),
                accountBenefit.getMerchantBenefitClassifyId(),
                accountBenefit.getBalance().negate(),
                isFrequency, date);
        AccountRecord accountRecord = assembleRecord(accountBenefit, cardMap.get(accountBenefit.getAccountId()), date);
        try {
            accountRecordRepository.insert(accountRecord);
        } catch (DuplicateKeyException exception) {
            log.error("权益过期会员流水添加异常,重试", exception);
            accountRecordRepository.insert(accountRecord);
        }
    }

    private AccountBenefit assembleBenefit(Date date, AccountBenefit accountBenefit) {
        AccountBenefit entity = new AccountBenefit();
        entity.setId(accountBenefit.getId());
        entity.setBalance(BigDecimal.ZERO);
        entity.setUpdateTime(date);
        entity.setStatus(AccountBenefitStatusEnum.EXPIRED.getStatus());
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--权益过期--权益明细过期更新, entity: {}", entity);
        }
        return entity;
    }

    private AccountRecord assembleRecord(AccountBenefit accountBenefit, String cardNo, Date date) {
        AccountRecord record = new AccountRecord();
        record.setAccountId(accountBenefit.getAccountId());
        record.setCardNo(cardNo);
        record.setRecordType(AccountRecordTypeEnum.SV_STORED_VALUE_EXPIRED.getCode());
        record.setAccountBenefitId(accountBenefit.getId());
        record.setUserId(accountBenefit.getUserId());
        record.setMerchantUserId(accountBenefit.getMerchantUserId());
        record.setMerchantId(accountBenefit.getMerchantId());
        record.setStoreId(accountBenefit.getStoreId());

        record.setMerchantBenefitClassifyId(accountBenefit.getMerchantBenefitClassifyId());
        record.setBenefitClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        record.setCreateTime(date);
        record.setConsumeType(AccountRecordOperationTypeEnum.BENEFIT_MODIFY.getCode());

        // operationType  相当于一个大分类，recordType 属于 operationType 的子分类
        record.setOperationType(AccountRecordOperationTypeEnum.BENEFIT_MODIFY.getCode());
        record.setMode(AdjustTypeEnum.DECREMENT.getType());
        record.setBenefitId(SmallVenueAccountService.DEFAULT_BENEFIT_ID);

        record.setInitialBenefit(accountBenefit.getBalance());
        record.setOriginalBenefit(accountBenefit.getBalance());
        record.setActualBenefit(accountBenefit.getBalance());
        record.setAccountInitialBalance(accountBenefit.getBalance());
        if (Objects.equals(accountBenefit.getValueType(), AccountBenefitNumTypeEnum.FREQUENCY.getCode())) {
            record.setAccountInitialNum(BigDecimal.ONE);
        }
        if (log.isDebugEnabled()) {
            log.debug("[小场地会员账户]--权益过期--记录账户权益流水, record: {}", record);
        }
        return record;
    }

}
