package com.lyy.user.interfaces.schedule;

import com.lyy.user.domain.correction.dto.GcConditionDTO;
import com.lyy.user.infrastructure.constants.ShardingGCEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.util.ShardingUtil;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * @date 2023/9/25
 * <p>
 * <p>
 * um_tag_user um_benefit um_benefit_rule um_benefit_scope um_benefit_consume_rule um_member_group um_member_grow_rule um_member_level
 * um_member_lifting um_member_lifting_rule um_member_range um_member_rule um_temp_merchant
 */
@Slf4j
//@JobHandler(value = "dirtyDataGCJob")
//@Component
public class CommonMemberDataGC extends IJobHandler {

    @Value("#{'${sharding.gc.needClearDS:0,1,2,3}'.split(',')}")
    private List<Integer> needClearDsNo;

    @Setter(onMethod_ = @Autowired)
    private CommonGCAction commonGcAction;

    /**
     * s ======== 0 (ShardingGCEnum :: code) ; 500
     *
     * @param spec
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String spec) throws Exception {
        if (StringUtils.isBlank(spec)) {
            return FAIL;
        }

        String[] split = spec.split(";");
        List<Integer> tableNos = Arrays.stream(Optional.of(split[0])
                        .map(info -> info.split(","))
                        .orElse(new String[0]))
                .map(Integer::valueOf).collect(Collectors.toList());
        if (!ShardingGCEnum.validateInput(tableNos)) {
            return FAIL;
        }

        Long pageSize = split.length > 1 ? Long.parseLong(split[1]) : 1000L;
        int currentIndex = ShardingUtil.getShardingVo().getIndex();
        log.info("CommonMemberDataGC => 当前分片[{}] ### 参数: [{}]", currentIndex, spec);

        if (needClearDsNo.contains(currentIndex)) {
            // 任务分担在两台机器上做, 用xxj的sharding映射数据库DS
            try (CommonGCAction var1 = commonGcAction.chooseDS(currentIndex)) {
                for (Integer tableno : tableNos) {
                    String tableName = ShardingGCEnum.getData(tableno).getTableName();
                    // each table need to clear
                    GcConditionDTO gcConditionDTO = GcConditionDTO
                            .builder()
                            .dsNo(currentIndex)
                            .code(tableno)
                            .tableName(tableName)
                            .pageSize(pageSize)
                            .build();
                    Boolean tableGc = commonGcAction.doAction(gcConditionDTO);
                    log.info("表: {}, 清理状态, {}", tableName, tableGc);
                }

            } catch (Exception ex) {
                log.error("定时清理 fail, err msg: {}", ex.getLocalizedMessage());
                return FAIL;
            }

        }
        return SUCCESS;
    }

}
