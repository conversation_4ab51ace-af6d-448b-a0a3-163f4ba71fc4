package com.lyy.user.interfaces.controller.member;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.member.dto.MemberGrowRecordDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGrowRecordListDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGrowRecordQueryDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.application.member.IMemberGrowRecordService;
import io.swagger.annotations.Api;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 会员成长值记录
 *
 * <AUTHOR>
 * @since 2021-04-07
 */
@Api(tags = "会员成长值管理")
@RestController
@RequestMapping("/rest/member/grow/record")
@Slf4j
public class MemberGrowRecordController {

    @Autowired
    private IMemberGrowRecordService memberGrowRecordService;

    /**
     * 查询用户成长值记录
     * @param param    商户用户Id
     * @return
     */
    @PostMapping("/list")
    public RespBody<Page<MemberGrowRecordListDTO>> list(@Valid @RequestBody MemberGrowRecordQueryDTO param) {
        log.info("[会员count优化-待确定项]-查询用户成长值记录,list:{}", param);
        return RespBody.ok(memberGrowRecordService.list(param));
    }

    /**
     * 新增会员成长值记录
     *
     * @param dto
     * @returna
     */
    @PostMapping("/saveMemberGrowRecord")
    public RespBody saveMemberGrowRecord(@Valid @RequestBody MemberGrowRecordDTO dto) {
        log.debug("新增会员成长值,dto:{}", dto);
        memberGrowRecordService.saveMemberGrowRecord(dto);
        return RespBody.ok();
    }

}
