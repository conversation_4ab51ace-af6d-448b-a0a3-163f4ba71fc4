package com.lyy.user.interfaces.assembler;

import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @className: CommAssembler
 * @date 2021/3/31
 */
public class CommAssembler<S, T> implements Assembler<S, T> {
    /**
     * 装配 请实现装配过程
     *
     * @param source
     * @param target
     */
    @Override
    public void assemble(S source, T target) {
        BeanUtils.copyProperties(source,target);
    }
}
