package com.lyy.user.interfaces.assembler;

import com.lyy.user.account.infrastructure.account.dto.request.ThirdPlatformAccountRecordSaveDTO;
import com.lyy.user.domain.user.entity.ThirdPlatformAccountRecord;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2023/4/23
 */
@Mapper
public interface ThirdPlatformAccountAssembler {

    ThirdPlatformAccountAssembler INSTANCE = Mappers.getMapper(ThirdPlatformAccountAssembler.class);


    ThirdPlatformAccountRecord fromThirdPlatformAccountRecordSaveDTO(ThirdPlatformAccountRecordSaveDTO dto);

}
