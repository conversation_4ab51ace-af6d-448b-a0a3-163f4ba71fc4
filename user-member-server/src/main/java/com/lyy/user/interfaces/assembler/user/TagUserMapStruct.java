package com.lyy.user.interfaces.assembler.user;

import com.lyy.user.account.infrastructure.user.dto.tag.TagUserListDTO;
import com.lyy.user.domain.user.entity.TagUser;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 产品图结构
 *
 * <AUTHOR>
 * @date 2024/09/25
 */
@Mapper
public interface TagUserMapStruct {

    TagUserMapStruct INSTANCE = Mappers.getMapper(TagUserMapStruct.class);

    TagUserListDTO toTagUserListDTO(TagUser tagUser);
}