package com.lyy.user.interfaces.assembler;


import com.lyy.user.account.infrastructure.member.dto.MemberGroupLevelDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupResponseDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberRangeAssociatedDTO;
import com.lyy.user.application.member.IMemberGroupService;
import com.lyy.user.application.member.IMemberLevelService;
import com.lyy.user.application.member.IMemberRangeService;
import com.lyy.user.domain.member.entity.MemberGroup;
import com.lyy.user.domain.member.entity.MemberLevel;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 会员组信息返回
 * <AUTHOR>
 * @className: MemberGroupResponseAssembler
 * @date 2021/4/9
 */
@Component
@Slf4j
public class MemberGroupResponseAssembler implements Assembler<MemberGroup, MemberGroupResponseDTO> {

    @Autowired
    private IMemberGroupService memberGroupService;
    @Autowired
    private IMemberRangeService memberRangeService;
    @Autowired
    private IMemberLevelService memberLevelService;

    /**
     * 装配 请实现装配过程
     *
     * @param source
     * @param target
     */
    @Override
    public void assemble(MemberGroup source, MemberGroupResponseDTO target) {
        BeanUtils.copyProperties(source,target);
        //会员总数
        target.setMemberCount(memberGroupService.countMemberByGroup(target.getMerchantId(), target.getId()));
        //会员范围内容,具体id信息没有转换为具体信息
        List<MemberRangeAssociatedDTO> memberRangeAssociatedList = memberRangeService.getRangeAssociatedList(target.getMerchantId(), target.getId());
        target.setMemberRangeAssociatedList(memberRangeAssociatedList);

        //会员等级信息处理
        Map<Long,Integer> levelCountMap = memberGroupService.countLevelMemberByGroup(target.getMerchantId(),target.getId());
        List<MemberLevel> levelList = memberLevelService.findByMemberGroup(target.getMerchantId(),target.getId());
        List<MemberGroupLevelDTO> memberLevelList = levelList.stream()
                .map(memberLevel -> {
                    MemberGroupLevelDTO memberGroupLevelDTO = new MemberGroupLevelDTO();
                    BeanUtils.copyProperties(memberLevel,memberGroupLevelDTO);
                    Integer levelMemberCount = Optional.ofNullable(levelCountMap.get(memberGroupLevelDTO.getId()))
                            .orElse(0);
                    memberGroupLevelDTO.setLevelMemberCount(levelMemberCount);
                    return memberGroupLevelDTO;
                }).collect(Collectors.toList());
        target.setMemberLevelList(memberLevelList);

    }
}
