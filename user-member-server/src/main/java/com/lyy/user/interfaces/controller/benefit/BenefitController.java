package com.lyy.user.interfaces.controller.benefit;

import com.lyy.user.account.infrastructure.benefit.dto.BenefitInfoDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitListDTO;
import com.lyy.user.account.infrastructure.benefit.dto.GeneralGroupBenefitSaveDTO;
import com.lyy.user.account.infrastructure.benefit.dto.GenerateAccountDTO;
import com.lyy.user.account.infrastructure.benefit.dto.GroupBenefitMergeDTO;
import com.lyy.user.account.infrastructure.benefit.vo.GeneralGroupBenefitSaveVO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.application.benefit.BenefitService;
import com.lyy.user.infrastructure.util.JSONUtil;
import io.swagger.annotations.Api;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 权益基础接口
 *
 * <AUTHOR>
 * @create 2021/3/31 16:18
 */
@Api(tags = "商户权益信息管理")
@Slf4j
@RestController
@RequestMapping(value = "/benefit")
public class BenefitController {

    @Resource
    private BenefitService benefitService;


    /**
     * 保存通用场地的权益信息
     * @param saveDTO
     * @return
     */
    @PostMapping(value = "/saveGeneralGroupBenefit")
    public RespBody<List<GeneralGroupBenefitSaveVO>> saveGeneralGroupBenefit(@RequestBody GeneralGroupBenefitSaveDTO saveDTO){
        log.info("通用场地权益保存，param:{}", log.isDebugEnabled()?JSONUtil.toJSONString(saveDTO) : saveDTO);
        List<GeneralGroupBenefitSaveVO> list = benefitService.saveGeneralGroupBenefit(saveDTO);
        return RespBody.ok(list);
    }

    /**
     * 保存普通场地的权益权益信息
     * @param generalGroupBenefitSaveDTO
     * @return
     */
    @PostMapping(value = "/saveGroupBenefit")
    public RespBody saveGroupBenefit(@RequestBody GeneralGroupBenefitSaveDTO generalGroupBenefitSaveDTO){
        log.info("普通场地权益保存，param:{}",generalGroupBenefitSaveDTO);
        benefitService.saveGroupBenefit(generalGroupBenefitSaveDTO);
        return RespBody.ok();
    }

    /**
     * 根据不同的触发生成方式来生成对应权益账户
     *
     * @param generateAccountDTO
     * @return
     */
    @PostMapping(value = "/generateAccount")
    public RespBody generateBenefitAccount(@RequestBody GenerateAccountDTO generateAccountDTO){
        log.debug("生成消费规则，param:{}",generateAccountDTO);
        benefitService.generateBenefitAccount(generateAccountDTO);
        return RespBody.ok();
    }


    /**
     * 获取商户权益，如不存在则创建
     * @param merchantId
     * @param benefitClassifyCode
     * @param applicable 适用类型
     * @param associatedId 管理ID
     * @return
     */
    @GetMapping(value = "/getMerchantBenefit")
    public RespBody<BenefitInfoDTO> getMerchantBenefit(@RequestParam("merchantId") Long merchantId,
                                                       @RequestParam("benefitClassifyCode") Integer benefitClassifyCode,
                                                       @RequestParam(value = "applicable",required = false) Integer applicable,
                                                       @RequestParam(value = "expiryDateCategory",required = false) Integer expiryDateCategory,
                                                       @RequestParam(value = "upTime",required = false) String upTime,
                                                       @RequestParam(value = "downTime",required = false) String downTime,
                                                       @RequestParam(value = "associatedId",required = false) Long associatedId){
        log.debug("获取指定商户的权益信息,merchantId:{},benefitClassifyCode:{},applicable:{},associatedId:{}",merchantId,benefitClassifyCode,applicable,
                associatedId);
        BenefitInfoDTO benefitInfoDTO = benefitService.getMerchantBenefit(merchantId,benefitClassifyCode,applicable,associatedId, expiryDateCategory, upTime, downTime);
        return  RespBody.ok(benefitInfoDTO);
    }

    /**
     * 获取权益详情
     * @param id
     * @return
     */
    @GetMapping(value = "/getBenefitById")
    public RespBody<BenefitInfoDTO> getBenefitById(@RequestParam("id") Long id,@RequestParam(value = "merchantId",required = false)  Long merchantId){
        log.debug("获取权益详情,id:{},merchantId:{}",id,merchantId);
        BenefitInfoDTO benefitInfoDTO = benefitService.getBenefitById(id,merchantId);
        return  RespBody.ok(benefitInfoDTO);
    }

    /**
     * 查询用户权益列表
     * @param lyyUserId 用户ID
     * @param merchantId 商户ID
     * @return
     */
    @GetMapping(value = "/getLyyUserBenefitList")
    public RespBody<List<BenefitListDTO>> getLyyUserBenefitList(@RequestParam Long lyyUserId,
                                                                @RequestParam Long merchantId){
        log.debug("查询用户权益列表,lyyUserId:{},merchantId:{}",lyyUserId,merchantId);
        List<BenefitListDTO> list = benefitService.getLyyUserBenefitList(lyyUserId,merchantId);
        return RespBody.ok(list);
    }

    /**
     * 2.0开启场地通用更新权益使用范围
     *
     * @param generalGroupBenefitSaveDTO
     * @return
     */
    @PostMapping(value = "/updateSaaSBenefit")
    public RespBody<Boolean> updateSaaSBenefit(@RequestBody GeneralGroupBenefitSaveDTO generalGroupBenefitSaveDTO) {
        log.debug("2.0开启场地通用更新权益使用范围:{}", generalGroupBenefitSaveDTO);
        return RespBody.ok(benefitService.updateSaaSBenefit(generalGroupBenefitSaveDTO));
    }

    /**
     * 场地通用权益合并
     *
     * @param groupBenefitMergeDTO
     * @return
     */
    @PostMapping(value = "/groupBenefitMerge")
    public RespBody<Boolean> groupBenefitMerge(@RequestBody @Valid GroupBenefitMergeDTO groupBenefitMergeDTO) {
        log.info("场地通用权益合并,groupBenefitMergeDTO:{}", groupBenefitMergeDTO);
        return RespBody.ok(benefitService.groupBenefitMerge(groupBenefitMergeDTO));
    }


    /**
     * 场地合并，新场地单场地权益处理
     * @param groupBenefitMergeDTO
     * @return
     */
    @PostMapping(value = "/groupBenefitMergeWithNewGroupList")
    public RespBody<Boolean> groupBenefitMergeWithNewGroupList(@RequestBody @Valid GroupBenefitMergeDTO groupBenefitMergeDTO) {
        log.info("场地合并，新场地单场地权益处理,groupBenefitMergeDTO:{}", groupBenefitMergeDTO);
        return RespBody.ok(benefitService.groupBenefitMergeWithNewGroupList(groupBenefitMergeDTO));
    }




}
