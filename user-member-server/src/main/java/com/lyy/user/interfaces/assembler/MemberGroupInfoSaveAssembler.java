package com.lyy.user.interfaces.assembler;

import com.lyy.user.account.infrastructure.member.dto.MemberGroupInfoSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberLevelSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberLiftingSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberRangeAssociatedDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberRuleDTO;
import com.lyy.user.application.member.IMemberGroupService;
import com.lyy.user.application.member.IMemberLevelService;
import com.lyy.user.application.member.IMemberLiftingRuleService;
import com.lyy.user.application.member.IMemberLiftingService;
import com.lyy.user.application.member.IMemberRangeService;
import com.lyy.user.application.member.IMemberRuleService;
import com.lyy.user.domain.member.entity.MemberGroup;
import com.lyy.user.domain.member.entity.MemberLevel;
import com.lyy.user.domain.member.entity.MemberRule;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 转换会员保存信息
 * <AUTHOR>
 * @className: MemberGroupSaveAssembler
 * @date 2021/4/21s
 */
@Component
public class MemberGroupInfoSaveAssembler implements Assembler<MemberGroup, MemberGroupInfoSaveDTO> {


    @Autowired
    private IMemberGroupService memberGroupService;
    @Autowired
    private IMemberRangeService memberRangeService;
    @Autowired
    private IMemberLevelService memberLevelService;
    @Autowired
    private IMemberRuleService memberRuleService;
    @Autowired
    private IMemberLiftingService memberLiftingService;
    @Autowired
    private IMemberLiftingRuleService memberLiftingRuleService;

    @Override
    public MemberGroupInfoSaveDTO convert(MemberGroup source, Class<MemberGroupInfoSaveDTO> memberGroupInfoSaveDTOClass) {
        MemberGroupInfoSaveDTO memberGroupInfoSaveDTO = new MemberGroupInfoSaveDTO();
        MemberGroupSaveDTO memberGroupSaveDTO = new MemberGroupSaveDTO();
        BeanUtils.copyProperties(source,memberGroupSaveDTO);
        memberGroupInfoSaveDTO.setMemberGroupSaveDTO(memberGroupSaveDTO);
        assemble(source,memberGroupInfoSaveDTO);
        return memberGroupInfoSaveDTO;
    }

    /**
     * 装配 请实现装配过程
     *
     * @param source
     * @param target
     */
    @Override
    public void assemble(MemberGroup source, MemberGroupInfoSaveDTO target) {
        //会员总数
        //会员范围内容,具体id信息没有转换为具体信息
        List<MemberRangeAssociatedDTO> memberRangeAssociatedList = memberRangeService.getRangeAssociatedList(source.getMerchantId(), source.getId());
        target.getMemberGroupSaveDTO().setMemberRangeAssociatedList(memberRangeAssociatedList);
        //会员等级信息处理
        List<MemberLevel> levelList = memberLevelService.findByMemberGroup(source.getMerchantId(), source.getId());
        if (!levelList.isEmpty()) {
            //处理规则数据
            List<Long> levelIds = levelList.stream()
                    .map(MemberLevel::getId)
                    .collect(Collectors.toList());
            List<MemberRule> memberRules = memberRuleService.findByMemberLevel(source.getMerchantId(), levelIds);
            Map<Long, List<MemberRuleDTO>> ruleMap = memberRules.stream()
                    .map(memberRule -> {
                        MemberRuleDTO memberRuleDTO = new MemberRuleDTO();
                        BeanUtils.copyProperties(memberRule, memberRuleDTO);
                        return memberRuleDTO;
                    })
                    .collect(Collectors.groupingBy(MemberRuleDTO::getMemberLevelId));
            //转换等级信息
            List<MemberLevelSaveDTO> memberLevelList = levelList.stream()
                    .map(memberLevel -> {
                        MemberLevelSaveDTO memberGroupLevelDTO = new MemberLevelSaveDTO();
                        BeanUtils.copyProperties(memberLevel, memberGroupLevelDTO);
                        List<MemberRuleDTO> memberRuleList = Optional.ofNullable(ruleMap.get(memberLevel.getId()))
                                .orElse(Collections.emptyList());
                        memberGroupLevelDTO.setMemberRuleList(memberRuleList);
                        return memberGroupLevelDTO;
                    }).collect(Collectors.toList());
            target.setMemberLevelList(memberLevelList);
        } else {
            target.setMemberLevelList(Collections.emptyList());
        }

        List<MemberLiftingSaveDTO> memberLiftingList = memberLiftingService.findSaveInfoByGroup(source.getMerchantId(), source.getId());
        if (CollectionUtils.isNotEmpty(memberLiftingList)) {
            List<MemberLiftingSaveDTO> memberLiftingSaveList = memberLiftingList.stream()
                    //过滤默认的升降级策略
                    .filter(memberLifting -> !memberLiftingService.checkLiftingDefault(memberLifting))
                    .collect(Collectors.toList());
            target.setMemberLiftingList(memberLiftingSaveList);
        } else {
            target.setMemberLiftingList(Collections.emptyList());
        }
    }
}
