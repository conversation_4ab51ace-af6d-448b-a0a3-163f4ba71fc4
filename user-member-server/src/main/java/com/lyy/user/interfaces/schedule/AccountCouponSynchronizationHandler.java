package com.lyy.user.interfaces.schedule;

import cn.hutool.core.date.DateUtil;
import cn.lyy.base.communal.bean.BaseResponse;
import cn.lyy.base.communal.constant.ResponseCodeEnum;
import cn.lyy.marketing.api.service.EnjoyActivityClient;
import cn.lyy.marketing.dto.constants.ActivityConfigTypeEnum;
import cn.lyy.marketing.dto.constants.ActivityUserStatusEnum;
import cn.lyy.marketing.dto.promotion.UserCouponDTO;
import cn.lyy.marketing.dto.promotion.request.UserCouponRequestDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.application.account.AccountService;
import com.lyy.user.domain.account.entity.Account;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.util.ShardingUtil;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.LongAdder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 账号商家券权益同步处理
 *
 * <AUTHOR>
 * @className: AccountCouponSynchronizationHandler
 * @date 2021/6/27
 */
@Slf4j
@JobHandler(value = "accountCouponSynchronization")
@Component
public class AccountCouponSynchronizationHandler extends IJobHandler {

    @Autowired
    private AccountService accountService;
    @Autowired
    private EnjoyActivityClient enjoyActivityClient;

    @Value("${coupon.query.pageSize:10000}")
    private int pageSize;

    private static final Set<ActivityConfigTypeEnum> MERCHANT_PAYOUT_COUPON_SET;

    static {
        MERCHANT_PAYOUT_COUPON_SET = new HashSet<>();
        MERCHANT_PAYOUT_COUPON_SET.add(ActivityConfigTypeEnum.PURCHASE_COUPON_RANDOM_FEE);
        MERCHANT_PAYOUT_COUPON_SET.add(ActivityConfigTypeEnum.PURCHASE_COUPON_FIXED_FEE);
        MERCHANT_PAYOUT_COUPON_SET.add(ActivityConfigTypeEnum.PURCHASE_COUPON_RANDOM_COINS);
        MERCHANT_PAYOUT_COUPON_SET.add(ActivityConfigTypeEnum.PURCHASE_COUPON_FIXED_COINS);
        MERCHANT_PAYOUT_COUPON_SET.add(ActivityConfigTypeEnum.PURCHASE_COUPON_DRAW_FEE);
        MERCHANT_PAYOUT_COUPON_SET.add(ActivityConfigTypeEnum.PURCHASE_COUPON_FREE);
        MERCHANT_PAYOUT_COUPON_SET.add(ActivityConfigTypeEnum.PURCHASE_COUPON_PRESENT);
        MERCHANT_PAYOUT_COUPON_SET.add(ActivityConfigTypeEnum.PURCHASE_COUPON_EXPERIENCE);
        MERCHANT_PAYOUT_COUPON_SET.add(ActivityConfigTypeEnum.GIFT_VOUCHERS);
        MERCHANT_PAYOUT_COUPON_SET.add(ActivityConfigTypeEnum.DISCOUNT_COUPON_EXPERIENCE);
        MERCHANT_PAYOUT_COUPON_SET.add(ActivityConfigTypeEnum.PURCHASE_DISCOUNT_COUPON);
        MERCHANT_PAYOUT_COUPON_SET.add(ActivityConfigTypeEnum.WX_DISCOUNT_CARD_COUPON);
        MERCHANT_PAYOUT_COUPON_SET.add(ActivityConfigTypeEnum.LIKE_VIDEO_SHOW_COUPON);
        MERCHANT_PAYOUT_COUPON_SET.add(ActivityConfigTypeEnum.USER_SPECIFIC_PACKAGES);
        MERCHANT_PAYOUT_COUPON_SET.add(ActivityConfigTypeEnum.DRAINAGE_ORDER_COUPON);
    }

    @Override
    public ReturnT<String> execute(String param) throws Exception {

        int currentIndex = ShardingUtil.getShardingVo().getIndex();
        int total = ShardingUtil.getShardingVo().getTotal();
        log.info("[处理商家券权益同步] 开始, total={}, currentIndex={}", total, currentIndex);
        Date now = new Date();
        Date start  = DateUtil.parseDate(DateUtil.offsetDay(now, -1).toDateStr());
        Date end = DateUtil.parseDate(DateUtil.formatDate(now));
        for (long currentShard = 0; currentShard < 128; currentShard++) {
            long index = currentShard % total;
            log.info("currentShard % total = {}", index);
            if (index == currentIndex) {
                try {
                    long startTime = System.currentTimeMillis();
                    int size = runTask(currentShard, start, end);
                    log.info("{} 机器 执行定时任务处理商家券权益同步-->用时为 {} 秒,共处理 {} 商户表的 {} 个任务", currentIndex,
                            (System.currentTimeMillis() - startTime) / 1000, currentShard, size);
                    //防止一直阻塞占用资源
                    Thread.sleep(10);
                } catch (Exception e) {
                    log.info("{} 机器 执行定时任务处理商家券权益同步，处理 {} 商户表出错", currentIndex, currentShard);
                    log.error("处理商家券权益同步异常,{}", e.getMessage(), e);
                    Thread.currentThread().interrupt();
                }
            }
        }


        return SUCCESS;
    }

    private int runTask(Long merchantId, Date startTime, Date endTime) {
        LongAdder longAdder = new LongAdder();
        Page<Account> page = new Page<>();
        page.setSize(pageSize);
        for (int i = 1; i < Integer.MAX_VALUE; i++) {
            // 先获取有商家券的权益信息
            page.setCurrent(i);
            Page<Account> pages = accountService.findAccountPage(merchantId, BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON.getCode(), page, startTime, endTime);
            log.info("[处理商家券权益同步] page-->{}", page);
            if (pages.getRecords().isEmpty()) {
                break;
            }
            pages.getRecords().forEach(account -> {
                //获取用户当前可用的券数量信息
                int couponNum = findCouponInfo(account.getMerchantId(), account.getUserId());
                if (log.isInfoEnabled()) {
                    log.info("[处理商家券权益同步] {} 商户 {} 用户有 {} 券数量,权益系统中券数量为 {} ", account.getMerchantId(), account.getUserId(), couponNum, account.getBalance());
                }
                if (couponNum < 0) {
                    //获取失败的，不用处理
                    return;
                }
                //对比当前商家券信息，若不一致则需要重新更新数据
                BigDecimal couponBalance = new BigDecimal(couponNum).subtract(account.getBalance());
                int compare = couponBalance.compareTo(BigDecimal.ZERO);
                if (compare > 0) {
                    //增加券数量
                    accountService.updateAccountAmount(account.getMerchantId(), account.getMerchantUserId(),
                            BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON.getCode(),
                            couponBalance, AdjustTypeEnum.INCREMENT, false);
                    longAdder.increment();
                } else if (compare < 0) {
                    //减少券数量
                    accountService.updateAccountAmount(account.getMerchantId(), account.getMerchantUserId(),
                            BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON.getCode(),
                            couponBalance, AdjustTypeEnum.DECREMENT, false);
                    longAdder.increment();
                }
            });
        }
        return longAdder.intValue();
    }


    /**
     * 通过营销获取券信息
     *
     * @param distributor
     * @param lyyUserId
     * @return
     */
    private int findCouponInfo(Long distributor, Long lyyUserId) {
        UserCouponRequestDTO request = new UserCouponRequestDTO();
        request.setUser(lyyUserId);
        //按摩类不传设备类型  查询商家下所有卷
        request.setDistributor(distributor);
        request.setStatus(ActivityUserStatusEnum.WAIT_USE);
        BaseResponse<Map<String, List<UserCouponDTO>>> response = enjoyActivityClient.findUserCoupon(request);
        if (log.isDebugEnabled()) {
            log.debug("[处理商家券权益同步] {} 商户 {} 用户中获取券信息-->{}", distributor, lyyUserId, response);
        }
        if (Objects.isNull(response) || ResponseCodeEnum.SUCCESS.getCode() != response.getCode()) {
            log.warn("[处理商家券权益同步] {} 商户 {} 用户中获取券信息失败", distributor, lyyUserId);
            return -1;
        }
        return Optional.ofNullable(response.getData())
                .filter(m -> !m.isEmpty())
                //提取可用的券数
                .map(map -> map.get("CanUsed"))
                .filter(list -> !CollectionUtils.isEmpty(list))
                //提取券数量
                .map(list -> (int) list.stream()
                        .filter(coupon -> MERCHANT_PAYOUT_COUPON_SET.contains(coupon.getRewardType()))
                        .count())
//                .map(List::size)
                .orElse(0);

    }


//    MERCHANT_PAYOUT_COUPON
}
