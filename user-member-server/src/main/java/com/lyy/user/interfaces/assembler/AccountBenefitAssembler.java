package com.lyy.user.interfaces.assembler;

import com.lyy.user.account.infrastructure.account.dto.AccountBenefitScopeDTO;
import com.lyy.user.account.infrastructure.account.dto.request.OrderBenefitRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.response.OrderAccountBenefit;
import com.lyy.user.account.infrastructure.account.dto.response.OrderBenefitRecord;
import com.lyy.user.domain.account.dto.AccountBenefitScopeDO;
import com.lyy.user.domain.account.entity.AccountBenefit;
import com.lyy.user.domain.account.entity.AccountRecord;
import com.lyy.user.infrastructure.repository.account.AccountRecordRepository.Query;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @since 2022/4/26 - 14:11
 */
@Mapper(componentModel = "spring")
public interface AccountBenefitAssembler {

    @Mapping(target = "classifyName", ignore = true)
    AccountBenefitScopeDTO fromAccountBenefitScopeDO(AccountBenefitScopeDO s);

    @Mapping(target = "startTime", ignore = true)
    @Mapping(target = "endTime", ignore = true)
    Query toRecordQuery(OrderBenefitRecordQueryDTO s);

    @Mapping(target = "commodityCategoryCode", source = "goodsType")
    @Mapping(target = "commodityId", source = "goodsId")
    @Mapping(target = "classify", source = "benefitClassify")
    OrderBenefitRecord toOrderBenefitRecord(AccountRecord accountRecord);

    @Mapping(target = "accountBenefitId", source = "id")
    OrderAccountBenefit toOrderAccountBenefit(AccountBenefit accountBenefit);
}
