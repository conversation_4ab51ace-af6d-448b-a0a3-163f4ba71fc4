package com.lyy.user.interfaces.controller.member;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupInfoSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupPageRequestDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupPageResponseDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupRangCheckDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupResponseDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberInfoDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberLevelDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberLevelSaveDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserDTO;
import com.lyy.user.application.member.IMemberGroupService;
import com.lyy.user.application.member.IMemberLevelService;
import com.lyy.user.application.member.IMemberLiftingService;
import com.lyy.user.application.member.IMemberService;
import com.lyy.user.application.user.IMerchantUserService;
import com.lyy.user.domain.member.entity.MemberGroup;
import com.lyy.user.domain.member.entity.MemberLevel;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.interfaces.assembler.AssemblerFactory;
import com.lyy.user.interfaces.assembler.CommAssembler;
import com.lyy.user.interfaces.assembler.MemberGroupInfoSaveAssembler;
import com.lyy.user.interfaces.assembler.MemberGroupPageResponseAssembler;
import com.lyy.user.interfaces.assembler.MemberGroupResponseAssembler;
import io.swagger.annotations.Api;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 会员组操作控制器
 *
 * <AUTHOR>
 * @date 2021/3/30
 */
@Api(tags = "会员组管理")
@RestController
@RequestMapping("rest/member/group")
@Slf4j
public class MemberGroupController {

    @Autowired
    private IMemberGroupService memberGroupService;
    @Autowired
    private IMemberLevelService memberLevelService;
    @Autowired
    private IMemberLiftingService memberLiftingService;

    @Autowired
    private MemberGroupResponseAssembler memberGroupResponseAssembler;
    @Autowired
    private MemberGroupPageResponseAssembler memberGroupPageResponseAssembler;
    @Autowired
    private MemberGroupInfoSaveAssembler memberGroupInfoSaveAssembler;

    @Autowired
    private IMemberService memberService;
    @Autowired
    private IMerchantUserService merchantUserService;

    private AssemblerFactory factory = new AssemblerFactory();
    private CommAssembler commAssembler = new CommAssembler();

    /**
     * 保存会员组
     *
     * @param memberGroupSaveDTO
     * @return
     */
    @PostMapping("save")
    public RespBody<Long> save(@RequestBody MemberGroupSaveDTO memberGroupSaveDTO) {
        Long id = memberGroupService.saveOrUpdate(memberGroupSaveDTO);
        return RespBody.ok(id);
    }

    /**
     * 保存会员组全部信息，包括等级，升级策略等信息
     *
     * @param memberGroupInfoSaveDTO
     * @return
     */
    @PostMapping("/saveMemberGroupInfo")
    public RespBody<Long> saveMemberGroupInfo(@RequestBody MemberGroupInfoSaveDTO memberGroupInfoSaveDTO) {
        log.debug("saveMemberGroupInfo,param:{}", memberGroupInfoSaveDTO);
        checkMemberGroupInfoSaveDTO(memberGroupInfoSaveDTO);
        Long memberGroupId = memberGroupService.saveOrUpdate(memberGroupInfoSaveDTO.getMemberGroupSaveDTO());
        Long merchantId = memberGroupInfoSaveDTO.getMemberGroupSaveDTO().getMerchantId();
        memberLevelService.saveOrUpdateBatchMemberLevel(merchantId, memberGroupId, memberGroupInfoSaveDTO.getMemberLevelList());
        memberLiftingService.saveOrUpdateBatchMemberLifting(merchantId, memberGroupId, memberGroupInfoSaveDTO.getMemberLiftingList());
        return RespBody.ok(memberGroupId);
    }

    /**
     * 校验会员组保存信息
     *
     * @param memberGroupInfoSaveDTO
     */
    private void checkMemberGroupInfoSaveDTO(MemberGroupInfoSaveDTO memberGroupInfoSaveDTO) {
        MemberGroupSaveDTO memberGroupSaveDTO = memberGroupInfoSaveDTO.getMemberGroupSaveDTO();
        if (StringUtils.isBlank(memberGroupSaveDTO.getName())) {
            throw new BusinessException(GlobalErrorCode.BAD_REQUEST.getCode(), "会员等级名称不能为空");
        }
        if (Objects.isNull(memberGroupSaveDTO.getLiftingStrategy())) {
            throw new BusinessException(GlobalErrorCode.BAD_REQUEST.getCode(), "会员等级升降策略不能为空");
        }
        List<Long> notIdList = Optional.ofNullable(memberGroupSaveDTO.getId())
                .map(Arrays::asList).orElse(null);
        int count = memberGroupService.countMemberGroupOfName(memberGroupSaveDTO.getMerchantId(), memberGroupSaveDTO.getName(), notIdList);
        if (count > 0) {
            throw new BusinessException(GlobalErrorCode.BAD_REQUEST.getCode(), "会员等级名称已经存在");
        }
        if (Objects.nonNull(memberGroupSaveDTO.getId())) {
            MemberGroup memberGroup = memberGroupService.getById(memberGroupSaveDTO.getMerchantId(), memberGroupSaveDTO.getId());
            if (Objects.isNull(memberGroup)) {
                throw new BusinessException(GlobalErrorCode.BAD_REQUEST.getCode(), "会员等级不存在，不允许编辑");
            }
            if (!Boolean.FALSE.equals(memberGroup.getDel())) {
                throw new BusinessException(GlobalErrorCode.BAD_REQUEST.getCode(), "会员等级已经删除，不允许编辑");
            }
        }

        List<MemberLevelSaveDTO> memberLevelList = memberGroupInfoSaveDTO.getMemberLevelList();
        if (CollectionUtils.isEmpty(memberLevelList)) {
            throw new BusinessException(GlobalErrorCode.BAD_REQUEST.getCode(), "会员等级的升降等级不能为空");
        }
        long levelSize = memberLevelList.stream()
                .map(MemberLevelSaveDTO::getGrowValue)
                .filter(growValue -> growValue > 0)
                .count();
        if (levelSize > 5) {
            throw new BusinessException(GlobalErrorCode.BAD_REQUEST.getCode(), "会员等级的升降等级最多为5级，目前为" + levelSize);
        }
        if (Objects.isNull(memberGroupSaveDTO.getMemberEffectiveTime())) {
            throw new BusinessException(GlobalErrorCode.BAD_REQUEST.getCode(), "会员有效期不能为空");
        }
        if (memberGroupSaveDTO.getMemberEffectiveTime() <= 0 && memberGroupSaveDTO.getMemberEffectiveTime() != -2) {
            throw new BusinessException(GlobalErrorCode.BAD_REQUEST.getCode(), "会员有效期设置错误");
        }

        //判断会员等级是不是可以进行删除
        List<Long> levelIds = memberLevelList.stream().map(MemberLevelDTO::getId).filter(Objects::nonNull).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(notIdList) && !CollectionUtils.isEmpty(levelIds)) {
            //会员组id和会员等级id不为空，才进行校验会员等级的删除
            //获取会员组的所有未删除的等级
            List<MemberLevel> memberLevels = memberLevelService.findByMemberGroup(memberGroupSaveDTO.getMerchantId(), notIdList.get(0), false);
            if (!CollectionUtils.isEmpty(memberLevels)) {
                List<Long> dbLevelIds = memberLevels.stream().map(MemberLevel::getId).collect(Collectors.toList());
                log.debug("商户:{},会员组:{},更新的会员等级id:{},库中的会员等级id:{}", memberGroupSaveDTO.getMerchantId(), notIdList.get(0), levelIds, dbLevelIds);
                //获取传入的等级不在数据库中的即为删除的会员等级
                dbLevelIds.removeAll(levelIds);
                if (log.isDebugEnabled()) {
                    log.debug("商户:{},会员组:{},需要删除的会员等级id为:{}", memberGroupSaveDTO.getMerchantId(), notIdList.get(0), dbLevelIds);
                }
                if (!CollectionUtils.isEmpty(dbLevelIds)) {
                    //查询这些会员等级的用户数是否大于0
                    boolean flag = memberService.hasUserInMemberLevelIds(memberGroupSaveDTO.getMerchantId(), dbLevelIds);
                    if (flag) {
                        throw new BusinessException(GlobalErrorCode.BAD_REQUEST.getCode(), "不能删除拥有会员的会员等级");
                    }
                }
            }
        }

    }


    /**
     * 获取会员组详情(包括统计会员信息)
     *
     * @param merchantId    商户ID
     * @param memberGroupId 会员组ID
     * @return
     */
    @GetMapping("getInfoCountById")
    public RespBody<MemberGroupResponseDTO> getInfoCountById(@RequestParam("merchantId") Long merchantId, @RequestParam("memberGroupId") Long memberGroupId) {
        MemberGroup memberGroup = memberGroupService.getById(merchantId, memberGroupId);
        MemberGroupResponseDTO memberGroupResponseDTO = factory.convert(memberGroupResponseAssembler, memberGroup, MemberGroupResponseDTO.class);
        return RespBody.ok(memberGroupResponseDTO);
    }

    /**
     * 获取会员组详情(用于保存信息)
     *
     * @param merchantId    商户ID
     * @param memberGroupId 会员组ID
     * @return
     */
    @GetMapping("getInfoSaveById")
    public RespBody<MemberGroupInfoSaveDTO> getInfoSaveById(@RequestParam("merchantId") Long merchantId, @RequestParam("memberGroupId") Long memberGroupId) {
        MemberGroup memberGroup = memberGroupService.getById(merchantId, memberGroupId);
        MemberGroupInfoSaveDTO memberGroupInfoSaveDTO = factory.convert(memberGroupInfoSaveAssembler, memberGroup, MemberGroupInfoSaveDTO.class);
        return RespBody.ok(memberGroupInfoSaveDTO);
    }

    /**
     * 更新会员组状态
     *
     * @param memberGroupDTO
     * @return
     */
    @PostMapping("updateStatus")
    public RespBody<Boolean> updateStatus(@RequestBody MemberGroupDTO memberGroupDTO) {
        Boolean result = memberGroupService.updateStatus(memberGroupDTO);
        return RespBody.ok(result);
    }

    /**
     * 获取默认会员组信息
     *
     * @return
     */
    @GetMapping("/getDefaultMemberGroup")
    public RespBody<MemberGroupInfoSaveDTO> getDefaultMemberGroup() {
        MemberGroupInfoSaveDTO memberGroupInfoSaveDTO = memberGroupService.getDefaultMemberGroup();
        return RespBody.ok(memberGroupInfoSaveDTO);
    }

    /**
     * 获取小场地默认会员组信息
     *
     * @param merchantId 商户ID
     * @return
     */
    @GetMapping("/getSmallVenueDefaultMemberGroup")
    public RespBody<MemberGroupInfoSaveDTO> getSmallVenueDefaultMemberGroup(@RequestParam("merchantId") Long merchantId) {
        log.debug("getSmallVenueDefaultMemberGroup,param:{}", merchantId);
        MemberGroupInfoSaveDTO memberGroupInfoSaveDTO = memberGroupService.getSmallVenueDefaultMemberGroup(merchantId);
        return RespBody.ok(memberGroupInfoSaveDTO);
    }

    /**
     * 分页获取会员数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/findByPage")
    public RespBody<Page<MemberGroupPageResponseDTO>> findByPage(@RequestBody MemberGroupPageRequestDTO dto) {
        log.info("分页获取会员组数据:{}", dto);
        Page<MemberGroup> page = memberGroupService.findMemberGroupOfPage(new Page<>(dto.getCurrent(), dto.getSize())
                , dto, Optional.ofNullable(dto.getMemberGroupStatus()).orElse(-1));
        Page<MemberGroupPageResponseDTO> page2 = new Page<>(page.getCurrent(), page.getSize(), page.getTotal(), page.isSearchCount());
        if (page.getRecords() != null && !page.getRecords().isEmpty()) {
            List<MemberGroupPageResponseDTO> list = page.getRecords().stream()
                    .map(memberGroup -> factory.convert(memberGroupPageResponseAssembler, memberGroup, MemberGroupPageResponseDTO.class))
                    .collect(Collectors.toList());
            page2.setRecords(list);
        }

        return RespBody.ok(page2);
    }

    /**
     * 根据用户组信息分页获取会员信息
     *
     * @param current       当前页
     * @param size
     * @param merchantId    商户ID
     * @param memberGroupId
     * @return
     */
    @GetMapping("findMemberByMemberGroup")
    public RespBody<Page<MemberInfoDTO>> findMemberByMemberGroup(@RequestParam("current") Integer current, @RequestParam("size") Integer size,
                                                                 @RequestParam("merchantId") Long merchantId, @RequestParam("memberGroupId") Long memberGroupId,
                                                                 @RequestParam(value = "memberLevelId", required = false) Long memberLevelId) {
        Page<MemberInfoDTO> page = memberService.findMemberByMemberGroup(new Page(current, size), merchantId, memberGroupId, memberLevelId);
        if (page.getRecords() != null && !page.getRecords().isEmpty()) {
            //获取会员组信息
            MemberGroupDTO memberGroupDTO;
            MemberGroup memberGroup = memberGroupService.getById(merchantId, memberGroupId);
            if (memberGroup != null) {
                memberGroupDTO = factory.convert(commAssembler, memberGroup, MemberGroupDTO.class);
            } else {
                memberGroupDTO = null;
            }
            //会员等级信息
            List<Long> memberLevelIdList = page.getRecords().stream()
                    .map(MemberInfoDTO::getMemberLevelId)
                    .collect(Collectors.toList());
            List<MemberLevel> memberLevelList = memberLevelService.listByIds(merchantId, memberLevelIdList);
            Map<Long, MemberLevelDTO> memberLevelMap = memberLevelList.stream()
                    .collect(Collectors.toMap(MemberLevel::getId, memberLevel -> factory.convert(commAssembler, memberLevel, MemberLevelDTO.class)));

            //会员信息
            List<Long> merchantUserIdList = page.getRecords().stream()
                    .map(MemberInfoDTO::getMerchantUserId)
                    .collect(Collectors.toList());
            List<MerchantUser> merchantUserList = merchantUserService.listByIds(merchantId, merchantUserIdList);
            Map<Long, MerchantUserDTO> merchantUserMap = merchantUserList.stream()
                    .collect(Collectors.toMap(MerchantUser::getId, merchantUser -> factory.convert(commAssembler, merchantUser, MerchantUserDTO.class)));

            //重新组装会员信息，并增加会员组与会员等级信息
            List<MemberInfoDTO> list = page.getRecords().stream()
                    .map(member -> {
                        MemberInfoDTO memberInfoDTO = factory.convert(commAssembler, member, MemberInfoDTO.class);
                        BeanUtils.copyProperties(member, memberInfoDTO);
                        memberInfoDTO.setMemberGroupDTO(memberGroupDTO);
                        memberInfoDTO.setMemberLevelDTO(memberLevelMap.get(member.getMemberLevelId()));
                        memberInfoDTO.setMerchantUserDTO(merchantUserMap.get(member.getMerchantUserId()));
                        return memberInfoDTO;
                    }).collect(Collectors.toList());
            page.setRecords(list);
        }
        return RespBody.ok(page);
    }

    /**
     * 删除会员组信息
     *
     * @param merchantId    商户ID
     * @param memberGroupId
     * @return
     */
    @GetMapping("removeByMemberGroup")
    public RespBody<Boolean> removeByMemberGroup(@RequestParam("merchantId") Long merchantId, @RequestParam("memberGroupId") Long memberGroupId) {
        boolean result = memberGroupService.removeByMemberGroup(merchantId, memberGroupId);
        return RespBody.ok(result);
    }

    @PostMapping("/getMemberGroupListOfRange")
    public RespBody<List<MemberGroupDTO>> getMemberGroupListOfRange(@RequestBody MemberGroupRangCheckDTO memberGroupRangCheckDTO) {
        List<MemberGroup> memberGroupList = memberGroupService.getMemberGroupListOfRange(memberGroupRangCheckDTO);
        List<MemberGroupDTO> memberGroupDTOS = memberGroupList.stream()
                .map(memberGroup -> {
                    MemberGroupDTO memberGroupDTOTemp = new MemberGroupDTO();
                    BeanUtils.copyProperties(memberGroup, memberGroupDTOTemp);
                    return memberGroupDTOTemp;
                }).collect(Collectors.toList());
        return RespBody.ok(memberGroupDTOS);
    }


}
