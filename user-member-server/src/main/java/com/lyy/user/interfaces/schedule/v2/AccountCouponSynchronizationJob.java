package com.lyy.user.interfaces.schedule.v2;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.domain.doris.entity.MemberCouponNumber;
import com.lyy.user.domain.doris.repository.MemberBenefitDorisRepository;
import com.lyy.user.infrastructure.repository.account.AccountBenefitRepository;
import com.lyy.user.infrastructure.repository.account.AccountRepository;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.atomic.LongAdder;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

/**
 * 账号商家券权益同步处理
 */
@Slf4j
@JobHandler(value = "accountCouponV2SynchronizationJob")
@Component
public class AccountCouponSynchronizationJob extends IJobHandler {

    @Resource
    private MemberBenefitDorisRepository memberBenefitDorisRepository;
    @Resource
    private AccountRepository accountRepository;
    @Resource
    private AccountBenefitRepository accountBenefitRepository;

    @Value("${member.coupon.pageSize:100}")
    private int pageSize;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Long merchantId = null;
        if (StringUtils.isNotBlank(param)) {
            log.info("指定商家同步商家券数量: {}", param);
            try {
                merchantId = Long.valueOf(param);
            } catch (NumberFormatException e) {
                log.info("参数错误");                return ReturnT.FAIL;
            }
        }
        StopWatch stopWatch = new StopWatch();
        try {
            stopWatch.start();
            syncMerchantCouponNum(merchantId);
            stopWatch.stop();
            log.info("商家券同步处理耗时: {}秒", stopWatch.getTotalTimeSeconds());
            return SUCCESS;
        } catch (Exception e) {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
            log.error("商家券同步处理异常", e);
            return ReturnT.FAIL;
        }

    }

    public void syncMerchantCouponNum(Long merchantId) {
        LongAdder longAdder = new LongAdder();
        for (int i = 1; i < Integer.MAX_VALUE; i++) {
            // 先获取有商家券的权益信息
            IPage<MemberCouponNumber> pages = memberBenefitDorisRepository.listMemberCouponNumber(merchantId, i, pageSize);
            log.info("[Doris]商家券数量同步,当前页-->{}", i);
            List<MemberCouponNumber> records = pages.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                return;
            }
            records.forEach(couponNumber -> {
                try {
                    syncCouponNum(couponNumber, longAdder);
                } catch (Exception e) {
                    log.error("[Doris]商家券数量同步, 当前数据同步失败: {}", couponNumber, e);
                }
            });
        }
        log.info("[Doris]商家券数量同步完成，共{}条", longAdder.intValue());
    }

    private void syncCouponNum(MemberCouponNumber couponNumber, LongAdder longAdder) {
        Integer couponClassify = BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON.getCode();
        Account account = accountRepository.getMerchantCouponAccount(couponNumber.getMerchantId(), couponNumber.getUserId());
        if (account == null) {
            log.info("[Doris]商家券数量同步,商家券账户不存在,merchantId:{},userId:{}", couponNumber.getMerchantId(), couponNumber.getUserId());
            return;
        }
        //对比当前商家券信息，若不一致则需要重新更新数据
        int couponBalance = couponNumber.getMarketingNum() - account.getBalance().intValue();
        BigDecimal amount = BigDecimal.valueOf(couponBalance).abs();
        Long mid = couponNumber.getMerchantId();
        Long userId = couponNumber.getUserId();
        log.info("[Doris]商家券数量同步记录：{}, PG券数量: {}", couponNumber, account.getBalance().intValue());
        if (couponBalance > 0) {
            //增加券数量
            accountRepository.updateAccountAmount(mid, userId, couponClassify, amount, AdjustTypeEnum.INCREMENT);
            longAdder.increment();
        } else if (couponBalance < 0) {
            //减少券数量
            accountRepository.updateAccountAmount(mid, userId, couponClassify, amount, AdjustTypeEnum.DECREMENT);
            longAdder.increment();
        }
    }

}
