package com.lyy.user.interfaces.schedule;

import com.lyy.user.domain.correction.dto.GcConditionDTO;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/25
 */
public interface CommonGCAction extends AutoCloseable {

    List findIdentityByPage();

    List findIdentityByPage(List<Long> indexId);

    Boolean doAction(GcConditionDTO gcConditionDTO);

//    List calculateSharding(List<CorrectionDTO> correctionDTO, Integer dsNo);

//    Long clearShardingData(List<CorrectionDTO> correctionDTO);

    CommonGCAction chooseDS(Integer dsNo);
    @Override
    void close();

//    Boolean backUp(List list);
//
//    Boolean RollBack();

}
