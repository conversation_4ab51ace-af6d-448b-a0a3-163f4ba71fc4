package com.lyy.user.interfaces.controller.account;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.error.member.infrastructure.AccountErrorCode;
import com.lyy.idempotent.core.annotation.Idempotent;
import com.lyy.user.account.infrastructure.account.dto.AccountAdjustRecordDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustRefundDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDataDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDecreaseReqDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitModifyDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitResultDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitScopeDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitScopeQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountClassifyBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountClassifyBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountConditionDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountConsumption;
import com.lyy.user.account.infrastructure.account.dto.AccountDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordCountDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordSaveDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountStatusDTO;
import com.lyy.user.account.infrastructure.account.dto.AddSupplementaryCardDTO;
import com.lyy.user.account.infrastructure.account.dto.BenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.BenefitRollbackDTO;
import com.lyy.user.account.infrastructure.account.dto.ConsumeDTO;
import com.lyy.user.account.infrastructure.account.dto.PayRefundBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.RecordBenefitScopeDTO;
import com.lyy.user.account.infrastructure.account.dto.RecordBenefitScopeQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.RecordTypeDTO;
import com.lyy.user.account.infrastructure.account.dto.SmallVenueAccountInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.SmallVenueAllCardDTO;
import com.lyy.user.account.infrastructure.account.dto.SupplementaryCardCheckDTO;
import com.lyy.user.account.infrastructure.account.dto.SupplyAnonymousCardDataDTO;
import com.lyy.user.account.infrastructure.account.dto.UserAccountBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountStatusBatchDTO;
import com.lyy.user.account.infrastructure.account.dto.request.AccountAdjustRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.request.AccountInfoByCardsQueryDto;
import com.lyy.user.account.infrastructure.account.dto.request.OrderBenefitRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.request.StoreBenefitClearDTO;
import com.lyy.user.account.infrastructure.account.dto.response.OrderBenefitCountDTO;
import com.lyy.user.account.infrastructure.account.dto.response.OrderBenefitInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.*;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.request.SmallVenueAccountRecordSaveBatchDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitConsumeInfoDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitPreDeductionDTO;
import com.lyy.user.account.infrastructure.common.DataList;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeGroupEnum;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.application.account.AccountBenefitService;
import com.lyy.user.application.account.AccountRecordService;
import com.lyy.user.application.account.AccountService;
import com.lyy.user.application.benefit.BenefitService;
import com.lyy.user.domain.benefit.dto.BenefitRecordCountDTO;
import com.lyy.user.infrastructure.execption.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 *  账户对外api
 *
 * <AUTHOR>
 * @since 2021/4/1 18:10
 */
@SuppressWarnings("JavadocDeclaration")
@Api(tags = "会员账户管理")
@Slf4j
@RestController
@RequestMapping("/account")
public class AccountController {

    @Resource
    private AccountService accountService;
    @Resource
    private AccountBenefitService accountBenefitService;
    @Autowired
    private BenefitService benefitService;
    @Autowired
    private AccountRecordService accountRecordService;
    /**
     * 查询账户流水（分页）-消费记录
     * @param param
     * @return
     */
    @PostMapping("/record/listRecord")
    public RespBody<Page<AccountConsumption>> listRecord(@RequestBody @Validated AccountRecordQueryDTO param) {
        log.debug("查询账户流水（分页）-消费记录,param：{}",param);
        if (param.getMerchantId() == null) {
            return RespBody.ok(new Page<>());
        }
        return RespBody.ok(accountService.listRecord(param));
    }
    /**
     * 权益消耗
     * @param consume
     * @return
     */
    @PostMapping("/benefit/consume")
    @Idempotent(keys = {"#consume.orderNo", "#consume.recordType"},
        idempotentExp = "#consume.resource == '商家券核销' or #consume.resource == '商家退款' "
                + "or #consume.resource == '支付扣广告红包权益' or #consume.resource == '支付充值退款退权益'")
    public RespBody<Void> benefitConsume(@RequestBody @Validated ConsumeDTO consume) {
        log.debug("账户权益消耗,param：{}",consume);
        if (consume.getCheckBalance() == null) {
            consume.setCheckBalance(Boolean.TRUE);
        }
        if (consume.getAllowNegative() == null) {
            consume.setAllowNegative(Boolean.FALSE);
        }
        accountService.benefitConsume(consume);
        return RespBody.ok();
    }

    /**
     * 权益变更
     */
    @PostMapping("/benefit/modify")
    @Idempotent(keys = {"#modify.orderNo", "#modify.merchantId", "#modify.userId"})
    public RespBody<Void> benefitModify(@RequestBody @Validated AccountBenefitModifyDTO modify) {
        log.info("账户权益变更,param:{}", modify);
        if (!CollectionUtils.isEmpty(modify.getAdjustList()) || !CollectionUtils.isEmpty(modify.getConsumeList())) {
            accountService.benefitModify(modify);
        }
        return RespBody.ok();
    }


    /**
     * 权益消耗-预计算扣除
     * @param consume
     * @return
     */
    @PostMapping("/benefit/preDeductionConsume")
    public RespBody<List<BenefitPreDeductionDTO>> benefitPreDeductionConsume(@RequestBody ConsumeDTO consume) {
        log.info("[新会员中心先使用]-权益扣除预计算,param：{}",consume);
        if (consume.getCheckBalance() == null) {
            consume.setCheckBalance(Boolean.TRUE);
        }
        if (consume.getAllowNegative() == null) {
            consume.setAllowNegative(Boolean.FALSE);
        }
        return RespBody.ok(accountService.benefitPreDeductionConsume(consume));
    }

    /**
     *  <a href="https://chandao.leyaoyao.com/task-view-6194.html">https://chandao.leyaoyao.com/task-view-6194.html</a><br>
     * 扣减并返回实扣金额，不校验金额是否足够<br>
     * 即允许余额不足仍扣减至0，<br>
     * 例如：余额0.7，扣1元，扣款0.7，余额更新为0，成功，接口需要返回实扣金额0.7
     * @param consume
     * @return
     */
    @PostMapping("/benefit/callback/realConsume")
    public RespBody<BenefitConsumeInfoDTO> callbackRealConsume(@RequestBody @Validated ConsumeDTO consume) {
        log.debug("[权益扣减返回实扣金额],param：{}", consume);
        if (consume.getCheckBalance() == null) {
            consume.setCheckBalance(Boolean.TRUE);
        }
        if (consume.getAllowNegative() == null) {
            consume.setAllowNegative(Boolean.FALSE);
        }
        return RespBody.ok(accountService.callbackRealConsume(consume));
    }
    /**
     * 权益回滚（根据支付单或者业务单）
     * @return
     */
    @PostMapping("/benefit/rollback")
    public RespBody<Void> benefitRollback(@RequestBody BenefitRollbackDTO rollback,
                                          @RequestParam(value = "retryFlag",required = false, defaultValue = "false") Boolean retryFlag) {
        log.debug("账户权益回滚,param:{},retryFlag:{}", rollback, retryFlag);
        accountService.benefitRollback(rollback, retryFlag);
        return RespBody.ok();
    }

    /**
     * 支付充值订单退款-根据订单号扣减权益
     * @param param
     * @return
     */
    @PostMapping("/benefit/payRefundRollbackBenefit")
    public RespBody<Boolean> payRefundRollbackBenefit(@RequestBody @Validated PayRefundBenefitDTO param) {
        log.debug("支付充值订单退款-根据订单号扣减权益,param：{}",param);
        return RespBody.ok(accountService.payRefundRollbackBenefit(param));
    }


    /**
     * 商家调整权益
     *      （只适合做权益新增，不适合做权益扣减，权益扣减使用权益消耗接口）
     * @param accountBenefitAdjustDTO
     * @return
     */
    @PostMapping("/benefit/merchant/adjust")
    @Idempotent(keys = {"#accountBenefitAdjustDTO.refundNo != null ? #accountBenefitAdjustDTO.refundNo : #accountBenefitAdjustDTO.outTradeNo",
            "#accountBenefitAdjustDTO.recordType","#accountBenefitAdjustDTO.classify","#accountBenefitAdjustDTO.id"},
        idempotentExp = "#accountBenefitAdjustDTO.outTradeNo != '0'")
    public RespBody merchantAdjustBenefit(@RequestBody @Validated AccountBenefitAdjustDTO accountBenefitAdjustDTO){
        log.info("商家调整权益，param:{}", accountBenefitAdjustDTO);
        long start = System.currentTimeMillis();
        boolean isAddTotal = accountBenefitAdjustDTO.getAddTotal() == null || accountBenefitAdjustDTO.getAddTotal();
        accountService.benefitAdd(accountBenefitAdjustDTO, isAddTotal);
        log.info("商家调整权益接口耗时：{}", System.currentTimeMillis() - start);
        return RespBody.ok();
    }

    /**
     * 商家调整权益退还，主要解决返还的权益需要有有效期限制
     *
     * @param accountBenefitAdjustRefundDTO
     * @return
     */
    @PostMapping("/benefit/merchant/refund")
    public RespBody merchantRefundBenefit(@RequestBody AccountBenefitAdjustRefundDTO accountBenefitAdjustRefundDTO){
        if(log.isInfoEnabled()){
            log.debug("商家退还权益，param:{}", accountBenefitAdjustRefundDTO);
        }
        if (CollectionUtils.isEmpty(accountBenefitAdjustRefundDTO.getClassifyList())){
            //若没有选定可能的权益类型，则取默认的类型
            accountBenefitAdjustRefundDTO.setClassifyList(Collections.singletonList(accountBenefitAdjustRefundDTO.getClassify()));
        }
        List<BenefitRecordCountDTO> benefitFromAccountRecordList = benefitService.getBenefitFromAccountRecord(accountBenefitAdjustRefundDTO,2);
        if(!CollectionUtils.isEmpty(benefitFromAccountRecordList)){
            if (log.isDebugEnabled()){
                log.debug("原订单消耗权益情况-->{}", benefitFromAccountRecordList);
            }
            AtomicReference<BigDecimal> reFundAmount = new AtomicReference<>(accountBenefitAdjustRefundDTO.getAmount());
            benefitFromAccountRecordList
                    .forEach(benefit->{
                        if(reFundAmount.get().compareTo(BigDecimal.ZERO) <= 0){
                            //已经没了，不需要再处理了
                            return;
                        }
                        //防止数据有干扰，必须拷贝一个对象出来
                        AccountBenefitAdjustDTO accountBenefitAdjustDTO = new AccountBenefitAdjustDTO();
                        BeanUtils.copyProperties(accountBenefitAdjustRefundDTO,accountBenefitAdjustDTO);

                        Optional.ofNullable(benefit.getExpiryDateCategory())
                                .ifPresent(category->{
                                    ExpiryDateCategoryEnum expiryDateCategory = Arrays.stream(ExpiryDateCategoryEnum.values())
                                            .filter(e->e.getValue() == category)
                                            .findFirst()
                                            .orElse(ExpiryDateCategoryEnum.NO_LIMIT);
                                    accountBenefitAdjustDTO.setExpiryDateCategory(expiryDateCategory);
                                });
                        accountBenefitAdjustDTO.setUpTime(benefit.getUpTime());
                        accountBenefitAdjustDTO.setDownTime(benefit.getDownTime());
                        BigDecimal amount = reFundAmount.get().compareTo(benefit.getActualBenefit()) >= 0?benefit.getActualBenefit():reFundAmount.get();
                        reFundAmount.set(reFundAmount.get().subtract(amount));
                        accountBenefitAdjustDTO.setAmount(amount);
                        accountBenefitAdjustDTO.setClassify(benefit.getClassify());
                        if(log.isInfoEnabled()){
                            log.debug("{} 商户 {} 用户退还 {} 类型的 {} 权益，数据为 {}",accountBenefitAdjustDTO.getMerchantId(),
                                    accountBenefitAdjustDTO.getUserId(),accountBenefitAdjustDTO.getClassify(),benefit.getId(),amount);
                        }
                        if(log.isDebugEnabled()){
                            log.debug("商家退还权益，更新后参数 param:{}", accountBenefitAdjustDTO);
                        }
                        accountService.benefitAdd(accountBenefitAdjustDTO,false);
                    });
            if(reFundAmount.get().compareTo(BigDecimal.ZERO) > 0){
                accountBenefitAdjustRefundDTO.setAmount(reFundAmount.get());
                if(log.isWarnEnabled()){
                    log.warn("经过退还记录处理后还剩下 {} 余额,需要使用默认的加权益逻辑来处理权益 dto: {}", accountBenefitAdjustRefundDTO.getAmount(), accountBenefitAdjustRefundDTO);
                }
                accountService.benefitAdd(accountBenefitAdjustRefundDTO,false);
            }

        }else{
            accountService.benefitAdd(accountBenefitAdjustRefundDTO,false);
        }
        return RespBody.ok();
    }

    /**
     * 余额余币清除接口，目前用于运营后台
     *
     * @return 清除的权益，按类型统计
     */
    @PostMapping("/benefit/balance/coin/clear")
    public RespBody<List<AccountBenefitResultDTO>> clearBalanceAndCoin(@RequestBody @Validated AccountBenefitQueryDTO param) {
        log.debug("余额余币清除接口，param:{}", param);
        List<AccountBenefitResultDTO> benefits = accountService.clearBalanceAndCoin(param);
        return RespBody.ok(benefits);
    }

    /**
     * 用户B端商户清除指定用户的权益
     * @param accountBenefitAdjustDTOList
     * @return
     */
    @PostMapping("/benefit/merchant/clear")
    public RespBody<Void> merchantClearBenefit(@RequestBody List<AccountBenefitAdjustDTO> accountBenefitAdjustDTOList) {
        log.debug("商户清除指定用户的权益，param:{}",accountBenefitAdjustDTOList);
        accountService.merchantClearBenefit(accountBenefitAdjustDTOList);
        return RespBody.ok();
    }

    /**
     * 清除指定用户的指定批次权益
     * @param accountBenefitAdjustList
     * @return
     */
    @PostMapping("/benefit/merchant/batch/clear")
    public RespBody<Void> merchantClearBatchBenefit(@RequestBody List<AccountBenefitAdjustDTO> accountBenefitAdjustList) {
        log.debug("商户清除指定用户的指定批次权益，param:{}", accountBenefitAdjustList);
        accountService.merchantClearBatchBenefit(accountBenefitAdjustList);
        return RespBody.ok();
    }

    /**
     * 查询权益数量
     * @param param
     * @return
     */
    @PostMapping("/benefit/count")
    public RespBody<Map<Integer, BigDecimal>> benefitCount(@RequestBody @Validated AccountQueryDTO param) {
        log.debug("查询权益数量接口，param:{}",param);
        if (param.getMerchantUserId() == null && param.getUserId() == null) {
            log.error("查询权益数量参数错误,param:{}", param);
            return RespBody.build(AccountErrorCode.ACCOUNT_QUERY_PARAM_ERROR, null);
        }
        return RespBody.ok(accountService.benefitCount(param));
    }

    /**
     * 用户账户信息
     * @param condition 账户查询条件
     * @return
     */
    @PostMapping("/info")
    public RespBody<List<AccountDTO>> accountInfo(@RequestBody @Validated AccountConditionDTO condition){
        log.info("用户账户信息查询：{}",condition);
        return RespBody.ok(accountService.getByUserAndMerchant(condition));
    }

    /**
     * 用户权益信息-汇总
     * @param queryDTO
     * @return
     */
    @PostMapping("/benefit/findAccountBenefitData")
    public RespBody<AccountBenefitDataDTO> findAccountBenefitData(@RequestBody @Validated AccountBenefitQueryDTO queryDTO){
        log.info("用户权益信息统计：{}",queryDTO);
        return RespBody.ok(accountService.findAccountBenefitData(queryDTO));
    }


    /**
     * 查询权益及生效范围
     *
     * @param query query
     * @return 权益及范围列表
     */
    @PostMapping("/benefit/scope")
    public RespBody<List<AccountBenefitScopeDTO>> listBenefitWithScope(@RequestBody @Validated AccountBenefitScopeQueryDTO query) {
        if (log.isDebugEnabled()) {
            log.debug("查询权益及生效范围：{}", query);
        }
        List<AccountBenefitScopeDTO> list = accountService.listBenefitWithScope(query);
        return RespBody.ok(list);
    }



    /**
     * 用户权益信息-明细
     * @param benefitQueryDTO query
     * @return 明细列表
     */
    @PostMapping("/benefit/listBenefitDetail")
    public RespBody<Page<AccountBenefitDTO>> listBenefitDetail(@RequestBody @Validated AccountBenefitQueryDTO benefitQueryDTO){
        log.info("用户权益信息-明细：{}",benefitQueryDTO);
        return RespBody.ok(accountService.listBenefitDetail(benefitQueryDTO));
    }

    /**
     * 消费记录保存
     *
     * @param recordDTO
     * @return
     */
    @PostMapping(value = "/record/save")
    @Idempotent(keys = {"#recordDTO.outTradeNo", "#recordDTO.recordType", "#recordDTO.mode"},
        idempotentExp = "#recordDTO.recordType == 1020 or #recordDTO.recordType == 1121 or #recordDTO.resource == 'XDL'")
    public RespBody<Boolean> saveRecord(@RequestBody @Validated AccountRecordSaveDTO recordDTO){
        if(log.isDebugEnabled()) {
            log.debug("消费记录保存,recordDTO:{}", recordDTO);
        }
        return RespBody.ok(accountService.saveRecord(recordDTO));
    }

    /**
     * 按摩类余额抵扣根据订单号进行权益退回
     * @param rollbackDTO
     * @return
     */
    @PostMapping(value = "/benefit/refundBenefitByOrderNo")
    public RespBody<List<BenefitPreDeductionDTO>> refundBenefitByOrderNo(@RequestBody  BenefitRollbackDTO rollbackDTO) {
        if(log.isDebugEnabled()){
            log.debug("根据订单号退回支付扣除/清除充值新增的权益,rollbackDTO:{}", rollbackDTO);
        }
        return RespBody.ok(accountService.refundBenefitByOrderNo(rollbackDTO));
    }


    /**
     * 查询会员卡状态
     *
     * @param cardNo
     * @param merchantId
     * @return
     */
    @GetMapping("/smallVenue/cardStatus")
    public RespBody<AccountStatusDTO> findCardStatus(@RequestParam("cardNo") String cardNo, @RequestParam("merchantId") Long merchantId) {
        log.info("查询会员卡状态,cardNo:{},merchantId:{}", cardNo, merchantId);
        return RespBody.ok(accountService.findCardStatus(merchantId, cardNo));
    }

    /**
     * 批量查询会员卡状态
     *
     * @return
     */
    @PostMapping("/smallVenue/batchCardStatus")
    public RespBody<List<AccountStatusBatchDTO>> findBatchCardStatus(@RequestBody AccountInfoByCardsQueryDto dto) {
        log.info("查询会员卡状态,dto:{}", dto);
        return RespBody.ok(accountService.findBatchCardStatus(dto.getMerchantId(),dto.getCardNos()));
    }


    /**
     * 根据会员卡/会员id/手机号查询账户
     * @param merchantId
     * @param keyword
     * @return
     */
    @GetMapping("/smallVenue/getAccountInfo")
    public RespBody<SmallVenueAccountInfoDTO> getAccountInfo(@RequestParam("merchantId") Long merchantId, @RequestParam("keyword") String keyword) {
        log.info("根据会员卡/会员id/手机号查询账户, keyword:{},merchantId:{}", keyword, merchantId);
        return RespBody.ok(accountService.getAccountInfo(merchantId, keyword));
    }


    /**
     * 获取用户的所有的会员主卡
     * @param merchantId
     * @param userId
     * @return
     */
    @GetMapping("/smallVenue/allCard")
    public RespBody<List<SmallVenueAllCardDTO>> selectUserAllCard(@RequestParam("merchantId") Long merchantId,
                                                                  @RequestParam("userId") Long userId,
                                                                  @RequestParam(value = "hasSupplementaryCard", required = false, defaultValue = "false")
                                                                              Boolean hasSupplementaryCard) {
        log.info("获取用户的所有的会员主卡,userId:{},merchantId:{}, hasSupplementaryCard: {}", userId, merchantId, hasSupplementaryCard);
        return RespBody.ok(accountService.selectUserAllCard(merchantId, userId, hasSupplementaryCard));
    }


    /**
     * 附属卡添加校验
     * @param supplementaryCardCheckDTO
     * @return
     */
    @PostMapping("/smallVenue/addSupplementaryCard/check")
    public RespBody<Boolean> supplementaryCardCheck(@RequestBody @Valid SupplementaryCardCheckDTO supplementaryCardCheckDTO) {
        log.info("附属卡添加校验,supplementaryCardCheckDTO:{}", supplementaryCardCheckDTO);
        return RespBody.ok(accountService.supplementaryCardCheck(supplementaryCardCheckDTO));
    }

    /**
     * 添加附属卡
     * @param addSupplementaryCardDTO
     * @return
     */
    @PostMapping("/smallVenue/addSupplementaryCard")
    public RespBody<Boolean> addSupplementaryCard(@RequestBody @Valid AddSupplementaryCardDTO addSupplementaryCardDTO) {
        log.info("附属卡添加,addSupplementaryCardDTO:{}", addSupplementaryCardDTO);
        if (Objects.isNull(addSupplementaryCardDTO.getAccountId())
                && StringUtils.isBlank(addSupplementaryCardDTO.getCardNo())) {
            throw new BusinessException(GlobalErrorCode.BAD_REQUEST);
        }
        return RespBody.ok(accountService.addSupplementaryCard(addSupplementaryCardDTO) > 0);
    }


    /**
     * 权益批次详情(余额详情)
     * @param accountBenefitReqDTO
     * @return
     */
    @PostMapping("/benefit/detail/page")
    public RespBody<DataList<SmallVenueBenefitDetailDTO>> queryAccountBenefitList(@RequestBody @Valid AccountBenefitReqDTO accountBenefitReqDTO) {
        log.info("权益批次详情(余额详情),accountBenefitReqDTO:{}", accountBenefitReqDTO);
        return RespBody.ok(accountBenefitService.queryAccountBenefitList(accountBenefitReqDTO));
    }

    /**
     * 权益详情(余额详情)
     * @param benefitDetailSelectDTO
     * @return
     */
    @PostMapping("/smallVenue/benefit/detail")
    public RespBody<DataList<SmallVenueBenefitDetailDTO>> smallVenueBenefitDetail(@RequestBody @Valid BenefitDetailSelectDTO benefitDetailSelectDTO) {
        log.info("权益详情(余额详情),benefitDetailSelectDTO:{}", benefitDetailSelectDTO);
        return RespBody.ok(accountBenefitService.smallVenueBenefitDetail(benefitDetailSelectDTO));
    }

    /**
     * 查询权益统计数据（根据门店进行分组）
     * @param merchantId
     * @param userId
     * @param accountId
     * @return
     */
    @GetMapping("/smallVenue/benefitStatistics")
    public RespBody<BenefitStatisticsDetailDTO> benefitStatistics(@RequestParam("merchantId") Long merchantId, @RequestParam("userId") Long userId, @RequestParam("accountId") Long accountId) {
        log.info("查询权益统计数据(根据门店进行分组),userId:{},merchantId:{},accountId:{}", userId, merchantId, accountId);
        return RespBody.ok(accountBenefitService.benefitStatisticsGroupByStoreId(merchantId, userId, accountId));
    }

    /**
     * 会员卡挂失/恢复
     *
     * @param updateCardStatusDTO
     * @return
     */
    @PostMapping("/smallVenue/cardReportLossOrRecover")
    public RespBody<Boolean> cardReportLossOrRecover(@RequestBody @Valid UpdateCardStatusDTO updateCardStatusDTO) {
        log.info("会员卡挂失/恢复,updateCardStatusDTO:{}", updateCardStatusDTO);
        return RespBody.ok(accountService.cardReportLossOrRecover(updateCardStatusDTO) > 0);
    }

    /**
     * 会员卡禁用/恢复
     *
     * @param updateCardStatusDTO
     * @return
     */
    @PostMapping("/smallVenue/cardDisabledOrRecover")
    public RespBody<Boolean> cardDisabledOrRecover(@RequestBody @Valid UpdateCardStatusDTO updateCardStatusDTO) {
        log.info("会员卡禁用/恢复,updateCardStatusDTO:{}", updateCardStatusDTO);
        return RespBody.ok(accountService.cardDisabledOrRecover(updateCardStatusDTO) > 0);
    }

    /**
     * 会员卡补卡/换卡
     * @param reissueCardDTO
     * @return
     */
    @PostMapping("/smallVenue/reissueCard")
    public RespBody<Boolean> reissueCard(@RequestBody @Valid ReissueCardDTO reissueCardDTO) {
        log.info("会员卡补卡/换卡,reissueCardDTO:{}", reissueCardDTO);
        return RespBody.ok(accountService.reissueCard(reissueCardDTO) > 0);
    }

    /**
     * 商品销售记录、兑换记录、商品回收记录保存
     * @param smallVenueAccountRecordSaveDTO
     * @return
     */
    @PostMapping("/smallVenue/saveRecord")
    public RespBody<Boolean> smallVenueSaveRecord(@RequestBody @Valid SmallVenueAccountRecordSaveDTO smallVenueAccountRecordSaveDTO) {
        log.info("商品销售记录、兑换记录、商品回收记录保存,smallVenueAccountRecordSaveDTO:{}", smallVenueAccountRecordSaveDTO);
        return RespBody.ok(accountService.smallVenueSaveRecord(smallVenueAccountRecordSaveDTO));
    }

    /**
     * 商品销售记录、兑换记录、商品回收记录批量保存
     */
    @PostMapping("/smallVenue/saveRecord/batch")
    public RespBody<Boolean> smallVenueSaveRecordBatch(@RequestBody @Valid SmallVenueAccountRecordSaveBatchDTO dto) {
        log.info("商品销售记录、兑换记录、商品回收记录批量保存,param:{}", dto);
        return RespBody.ok(accountService.smallVenueSaveRecordBatch(dto));
    }


    /**
     * 商品购买记录、商品销售记录、兑换记录、商品回收记录、设备消费记录、储值变更记录查询
     * @param smallVenueRecordSelectDTO
     * @return
     */
    @PostMapping("/smallVenue/record")
    public RespBody<DataList<SmallVenueRecordListDTO>> smallVenueRecord(@RequestBody @Valid SmallVenueRecordSelectDTO smallVenueRecordSelectDTO) {
        log.info("商品购买记录、商品销售记录、兑换记录、商品回收记录、设备消费记录、储值变更记录查询,smallVenueRecordSelectDTO:{}", smallVenueRecordSelectDTO);
        return RespBody.ok(accountService.smallVenueRecord(smallVenueRecordSelectDTO));
    }


    /**
     * 查询主卡是否存在附属卡
     *
     * @param hasSupplementaryCardDTO
     * @return
     */
    @PostMapping("/smallVenue/hasSupplementaryCard")
    public RespBody<Boolean> hasSupplementaryCard(@RequestBody @Valid HasSupplementaryCardDTO hasSupplementaryCardDTO) {
        log.info("查询主卡是否存在附属卡,hasSupplementaryCardDTO:{}", hasSupplementaryCardDTO);
        return RespBody.ok(accountService.hasSupplementaryCard(hasSupplementaryCardDTO));
    }

    /**
     * 会员卡续期
     * @param cardRenewalDTO
     * @return
     */
    @PostMapping("/smallVenue/renewalCard")
    public RespBody<Boolean> renewalCard(@RequestBody @Valid CardRenewalDTO cardRenewalDTO) {
        log.info("会员卡续期,cardRenewalDTO:{}", cardRenewalDTO);
        return RespBody.ok(accountService.renewalCard(cardRenewalDTO.getMerchantId(), cardRenewalDTO.getCardNo(),
                cardRenewalDTO.getExtendedDays(), cardRenewalDTO.getOperatorId()));
    }


    /**
     * 小场地业务类型列表
     * @return
     */
    @GetMapping("/smallVenue/recordTypeList")
    public RespBody<List<RecordTypeDTO>> recordTypeList() {

        List<RecordTypeDTO> recordTypeDTOList = Arrays.stream(AccountRecordTypeEnum.values())
                .filter(e -> AccountRecordTypeGroupEnum.SMALL_VENUE.getType().equals(e.getGroupId()))
                .map(type -> new RecordTypeDTO()
                        .setRecordType(type.getCode())
                        .setRecordTypeName(type.getShowText())).collect(Collectors.toList());

        return RespBody.ok(recordTypeDTOList);
    }

    /**
     * 不记名卡的资料补充
     * @param supplyAnonymousCardDataDTO
     * @return
     */
    @PostMapping("/smallVenue/supplyAnonymousCardData")
    public RespBody<Boolean> supplyAnonymousCardData(@RequestBody @Valid SupplyAnonymousCardDataDTO supplyAnonymousCardDataDTO) {
        log.info("不记名卡的资料补充:SupplyAnonymousCardDataDTO:{}", supplyAnonymousCardDataDTO);
        return RespBody.ok(accountService.supplyAnonymousCardData(supplyAnonymousCardDataDTO));
    }

    /**
     * 商户下总流水数量
     * @param accountRecordCountDTO
     * @return
     */
    @PostMapping("/countRecord")
    public RespBody<Long> countRecord(@RequestBody AccountRecordCountDTO accountRecordCountDTO) {
        log.debug("商户下总流水数量,param：{}",accountRecordCountDTO);
        return RespBody.ok(accountService.countRecord(accountRecordCountDTO));
    }

    @PostMapping("/benefit/findByIds")
    public RespBody<List<UserAccountBenefitDTO>> findBenefitByIds(@RequestBody @Validated BenefitQueryDTO query) {
        log.debug("批量获取权益批次详情,param:{}", query);
        if (Objects.isNull(query.getMerchantId()) || org.apache.commons.collections.CollectionUtils.isEmpty(query.getAccountBenefitIds())) {
            return RespBody.fail(GlobalErrorCode.BAD_REQUEST);
        }
        return RespBody.ok(accountBenefitService.findBenefitByIds(query));
    }

    /**
     * 根据订单和权益类型查询消耗权益的使用范围
     * @param queryDTO
     * @return
     */
    @PostMapping("/record/findRecordBenefitScopeByOrderNo")
    public RespBody<List<RecordBenefitScopeDTO>> findRecordBenefitScopeByOrderNo(@RequestBody @Validated RecordBenefitScopeQueryDTO queryDTO) {
        if (log.isDebugEnabled()) {
            log.debug("findRecordBenefitScopeByOrderNo:{}", queryDTO);
        }
        return RespBody.ok(accountService.findRecordBenefitScopeByOrderNo(queryDTO));
    }

    /**
     * 根据订单查询消费明细
     * @param queryDTO
     * @return
     */
    @PostMapping("/record/findRecordByOrderNoAndCondition")
    public RespBody<List<AccountRecordDTO>> findRecordByOrderNoAndCondition(@RequestBody AccountRecordQueryDTO queryDTO) {
        if (log.isDebugEnabled()) {
            log.debug("findRecordByOrderNoAndCondition:{}", queryDTO);
        }
        return RespBody.ok(accountService.findRecordByOrderNoAndCondition(queryDTO));
    }

    /**
     * 用户充值抵扣金退款校验
     *
     * @param merchantId 商户id
     * @param userId     用户id
     * @param outTradeNo 订单号
     * @return
     */
    @GetMapping(value = "/deductionRechargeRefundCheck")
    public RespBody<Boolean> deductionRechargeRefundCheck(@RequestParam("merchantId") Long merchantId,
                                                          @RequestParam("userId") Long userId,
                                                          @RequestParam("outTradeNo") String outTradeNo) {
        if (log.isDebugEnabled()) {
            log.debug("用户充值抵扣金退款校验,{},{},{}", merchantId, userId, outTradeNo);
        }
        return RespBody.ok(accountService.deductionRechargeRefundCheck(merchantId, userId, outTradeNo));
    }

    /**
     * 移动端账户流水记录查询
     * @param mobileTerminalRecordSelectDTO
     * @return
     */
    @PostMapping("/mobileTerminal/record")
    public RespBody<DataList<SmallVenueRecordListDTO>> mobileTerminalRecord(@RequestBody @Valid MobileTerminalRecordSelectDTO mobileTerminalRecordSelectDTO) {
        log.info("移动端账户流水记录查询,mobileTerminalRecordSelectDTO:{}", mobileTerminalRecordSelectDTO);
        return RespBody.ok(accountService.mobileTerminalRecord(mobileTerminalRecordSelectDTO));
    }


    /**
     * 移动端会员卡列表查询
     * @param mobileTerminalAccountSelectDTO
     * @return
     */
    @PostMapping("/mobileTerminal/accountList")
    public RespBody<DataList<MobileTerminalAccountListDTO>> mobileTerminalAccountList(@RequestBody @Valid MobileTerminalAccountSelectDTO mobileTerminalAccountSelectDTO) {
        log.info("移动端会员卡列表查询,mobileTerminalAccountSelectDTO:{}", mobileTerminalAccountSelectDTO);
        return RespBody.ok(accountService.mobileTerminalAccountList(mobileTerminalAccountSelectDTO));
    }

    /**
     * 移动端会员卡详情
     * @param merchantId
     * @param userId
     * @param accountId
     * @return
     */
    @GetMapping("/mobileTerminal/accountDetails")
    public RespBody<MobileTerminalAccountDetailsDTO> mobileTerminalAccountDetails(@RequestParam("merchantId") Long merchantId, @RequestParam("userId") Long userId, @RequestParam("accountId") Long accountId) {
        log.info("移动端会员卡详情,merchantId:{},userId:{},accountId:{}", merchantId, userId, accountId);
        return RespBody.ok(accountService.mobileTerminalAccountDetails(merchantId, userId, accountId));
    }

    /**
     * 获取账户指定类型权益信息（包含消耗规则、范围）
     */
    @PostMapping("/classify/benefits")
    public RespBody<List<AccountClassifyBenefitDTO>> findAccountClassifyBenefit(@RequestBody AccountClassifyBenefitQueryDTO request) {
        log.info("获取账户指定类型权益信息（包含消耗规则、范围）,request:{}", request);
        return RespBody.ok(accountService.findAccountClassifyBenefit(request));
    }

    /**
     * 扣减账户权益
     */
    @PostMapping("/benefit/decrease")
    public RespBody<Boolean> decreaseAccountBenefit(@RequestBody AccountBenefitDecreaseReqDTO request) {
        log.info("扣减账户权益,request:{}", request);
        return RespBody.ok(accountService.decreaseAccountBenefitAndTag(request));
    }

    /**
     * 根据订单查询流水和权益
     */
    @ApiOperation(value = "根据订单查询流水和权益")
    @PostMapping("/benefit/order/records")
    public RespBody<OrderBenefitInfoDTO> listOrderBenefitRecord(@RequestBody OrderBenefitRecordQueryDTO query) {
        log.info("根据订单查询流水和权益, query:{}", query);
        OrderBenefitInfoDTO dto = accountService.listOrderBenefitRecord(query);
        return RespBody.ok(dto);
    }

    /**
     *
     * 查询派送储值变更记录
     */
    @PostMapping("/record/listGrantCoinsRecord")
    public RespBody<Page<AccountAdjustRecordDTO>> listGrantCoinsRecord(@RequestBody AccountAdjustRecordQueryDTO param) {
        log.info("[会员count优化-待确定项]-查询派送储值变更记录,param：{}",param);
        if (param.getMerchantId() == null) {
            return RespBody.ok(new Page<>());
        }
        return RespBody.ok(accountRecordService.listGrantCoinsRecord(param));
    }


    /**
     * 根据订单权益消耗值
     */
    @ApiOperation(value = "根据订单权益消耗值")
    @PostMapping("/benefit/order/consume")
    public RespBody<OrderBenefitCountDTO> listOrderConsume(@RequestBody OrderBenefitRecordQueryDTO query) {
        log.info("根据订单查询流水和权益, query:{}", query);
        OrderBenefitCountDTO dto = accountService.listOrderConsume(query);
        return RespBody.ok(dto);
    }

    /**
     * 用户指定场地权益清除
     */
    @PostMapping("/benefit/store/clear")
    public RespBody<Void> storeBenefitClear(@RequestBody @Validated StoreBenefitClearDTO param) {
        log.debug("用户指定场地权益清除，param:{}", param);
        accountService.storeBenefitClear(param);
        return RespBody.ok();
    }

    @PostMapping("/smallVenue/getAccountInfoByCards")
    public RespBody<List<SmallVenueAccountInfoDTO>> getAccountInfoByCards(@RequestBody AccountInfoByCardsQueryDto dto) {
        log.info("根据会员卡查询账户, dto:{}",dto);
        return RespBody.ok(accountService.getAccountInfoByCardNos(dto.getMerchantId(),dto.getCardNos()));
    }

    @PostMapping("/smallVenue/getSupplementaryCard")
    public RespBody<List<MobileTerminalCardCountInfo>> getSupplementaryCard(@RequestBody AccountSupplementaryCardQueryDTO dto ) {
        log.info("根据会员卡查询账户, dto:{}",dto);
        return RespBody.ok(accountService.getSupplementaryCard(dto));
    }
}
