package com.lyy.user.interfaces.controller.benefit;

import com.lyy.user.interfaces.schedule.v2.AccountBenefitInvalidateJob;
import com.lyy.user.interfaces.schedule.v2.AccountCouponSynchronizationJob;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/12/3 - 18:34
 */
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/um/job")
public class JobController {

    private final AccountBenefitInvalidateJob accountBenefitInvalidateJob;
    private final AccountCouponSynchronizationJob accountCouponSynchronizationJob;

    @GetMapping("/invalidateBenefit")
    public boolean invalidateBenefit(Long merchantId) {
        accountBenefitInvalidateJob.invalidateBenefit(merchantId);
        return true;
    }

    @GetMapping("/sync-merchant-coupon")
    public boolean runCouponSyncTask(Long merchantId) {
        accountCouponSynchronizationJob.syncMerchantCouponNum(merchantId);
        return true;
    }

}
