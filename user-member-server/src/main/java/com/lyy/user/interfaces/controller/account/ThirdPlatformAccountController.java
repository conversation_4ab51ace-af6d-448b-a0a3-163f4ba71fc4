package com.lyy.user.interfaces.controller.account;

import com.lyy.user.account.infrastructure.account.dto.request.ThirdPlatformAccountRecordSaveDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.application.account.ThirdPlatformAccountService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/4/23
 */
@Api(tags = "第三方账户管理")
@Slf4j
@RestController
@RequestMapping("/third-platform-account")
public class ThirdPlatformAccountController {

    @Autowired
    private ThirdPlatformAccountService thirdPlatformAccountService;

    /**
     * 增加第三方资金变动记录
     *
     * @param dto DTO
     * @return boolean
     */
    @PostMapping("/record/add")
    public RespBody<Boolean> addThirdPlatformAccountRecord(@RequestBody @Validated ThirdPlatformAccountRecordSaveDTO dto) {
        return RespBody.ok(thirdPlatformAccountService.addThirdPlatformAccountRecord(dto));
    }

}
