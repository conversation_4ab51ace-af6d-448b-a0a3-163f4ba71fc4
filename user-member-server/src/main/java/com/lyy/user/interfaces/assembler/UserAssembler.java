package com.lyy.user.interfaces.assembler;

import com.lyy.user.account.infrastructure.user.dto.MerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.app.interfaces.facade.dto.UserDTO;
import com.lyy.user.app.interfaces.facade.dto.UserVO;
import com.lyy.user.domain.user.entity.MerchantUser;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2022/4/12 - 17:56
 */
@Mapper
public interface UserAssembler {

    UserAssembler INSTANCE = Mappers.getMapper(UserAssembler.class);

    @Mapping(target = "isactive", ignore = true)
    @Mapping(target = "lyyUserId", source = "id")
    @Mapping(target = "unionid", source = "unionId")
    @Mapping(target = "openid", source = "openId")
    @Mapping(target = "lyyCityId", source = "cityId")
    UserInfoDTO fromUserDTO(UserDTO s);


    @Mapping(target = "isactive", source = "isActive")
    @Mapping(target = "lyyUserId", source = "id")
    @Mapping(target = "unionid", source = "unionId")
    @Mapping(target = "openid", source = "openId")
    @Mapping(target = "lyyCityId", source = "cityId")
    UserInfoDTO toUserInfoDTO(UserVO s);


    @Mapping(target = "userType", source = "userSourceEnum.userType")
    @Mapping(target = "name", source = "nickname")
    @Mapping(target = "provinceCity", expression = "java(java.util.Optional.ofNullable(s.getProvince()).orElse(\"\") + java.util.Optional.ofNullable(s.getCityName()).orElse(\"\"))")
    @Mapping(target = "unionId", source = "unionid")
    @Mapping(target = "openId", source = "openid")
    @Mapping(target = "id", source = "lyyUserId")
    UserDTO fromUserCreateDTO(UserCreateDTO s);

    @Mapping(target = "userType", source = "userSourceEnum.userType")
    @Mapping(target = "name", source = "nickname")
    @Mapping(target = "provinceCity", expression = "java(java.util.Optional.ofNullable(s.getProvince()).orElse(\"\") + java.util.Optional.ofNullable(s.getCityName()).orElse(\"\"))")
    @Mapping(target = "unionId", source = "unionid")
    @Mapping(target = "openId", source = "openid")
    @Mapping(target = "id", source = "lyyUserId")
    UserVO toUserVO(UserCreateDTO s);

    @Mapping(target = "provinceCity", expression = "java(java.util.Optional.ofNullable(s.getProvince()).orElse(\"\") + java.util.Optional.ofNullable(s.getCityName()).orElse(\"\"))")
    @Mapping(target = "id", source = "userId")
    UserDTO fromMerchantUserDTO(MerchantUserDTO s);

    @Mapping(target = "lyyCityId", source = "cityId")
    @Mapping(target = "telephone", source = "telephone", defaultValue = "")
    @Mapping(target = "merchantUserId", source = "id")
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void assignUserInfoDTO(MerchantUser s, @MappingTarget UserInfoDTO t);

    UserDTO toUserDTO(UserVO s);


}
