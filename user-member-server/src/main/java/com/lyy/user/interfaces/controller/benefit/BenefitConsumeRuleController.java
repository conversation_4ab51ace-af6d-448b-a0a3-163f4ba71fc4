package com.lyy.user.interfaces.controller.benefit;

import com.lyy.user.account.infrastructure.benefit.dto.ConsumeRuleSaveDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.application.benefit.BenefitConsumeRuleService;
import io.swagger.annotations.Api;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: BenefitConsumeRuleController
 * @description: 权益消耗规则
 * @author: pengkun
 * @date: 2021/08/14
 **/
@Api(tags = "商户权益消耗规则管理")
@Slf4j
@RestController
@RequestMapping(value = "/benefit/consume")
public class BenefitConsumeRuleController {

    @Resource
    private BenefitConsumeRuleService benefitConsumeRuleService;

    /**
     * 初始化商户权益消耗规则
     *
     * @param merchantId  商户id
     * @param operationId 操作人id
     * @return
     */
    @GetMapping(value = "/initMerchantBenefitConsume")
    public RespBody<Void> initMerchantBenefitConsume(@RequestParam("merchantId") Long merchantId,
                                                     @RequestParam(value = "operationId", required = false) Long operationId) {
        log.debug("初始化商户默认权益消耗规则,merchantId:{},operationId:{}", merchantId, operationId);
        benefitConsumeRuleService.saveAllBenefitConsume(merchantId, operationId);
        return RespBody.ok();
    }

    /**
     * 批量商户权益消耗规则处理
     *
     * @param consumeRuleSaveDTO 保存参数
     * @return
     */
    @PostMapping(value = "/batchMerchantBenefitConsume")
    public RespBody<Void> batchMerchantBenefitConsume(@RequestBody ConsumeRuleSaveDTO consumeRuleSaveDTO) {
        log.debug("批量商户权益消耗规则处理,{}", consumeRuleSaveDTO);
        benefitConsumeRuleService.batchSaveOrUpdateBenefitConsume(consumeRuleSaveDTO);
        return RespBody.ok();
    }
}
