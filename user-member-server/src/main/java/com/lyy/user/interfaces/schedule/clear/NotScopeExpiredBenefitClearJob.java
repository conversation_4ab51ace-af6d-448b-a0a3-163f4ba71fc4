package com.lyy.user.interfaces.schedule.clear;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import com.lyy.user.domain.account.entity.AccountBenefit;
import com.lyy.user.domain.account.repository.AccountBenefitMapper;
import com.lyy.user.domain.benefit.entity.Benefit;
import com.lyy.user.domain.benefit.entity.BenefitScope;
import com.lyy.user.domain.benefit.entity.BenefitTmp;
import com.lyy.user.domain.benefit.repository.BenefitMapper;
import com.lyy.user.domain.benefit.repository.BenefitTmpMapper;
import com.lyy.user.domain.doris.entity.VUmBenefitExpired;
import com.lyy.user.domain.doris.repository.MemberBenefitDorisRepository;
import com.lyy.user.infrastructure.repository.benefit.BenefitRepository;
import com.lyy.user.interfaces.assembler.BenefitAssembler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

/**
 * 无使用范围已过期权益清理
 */
@Slf4j
@JobHandler(value = "notScopeExpiredBenefitClearJob")
@Component
public class NotScopeExpiredBenefitClearJob extends IJobHandler {

    @Resource
    private AccountBenefitMapper accountBenefitMapper;

    @Resource
    private BenefitMapper benefitMapper;

    @Resource
    private MemberBenefitDorisRepository memberBenefitDorisRepository;

    @Resource
    private BenefitRepository benefitRepository;

    @Resource
    private BenefitAssembler benefitAssembler;
    @Resource
    private BenefitTmpMapper benefitTmpMapper;

    @Value("${member.benefit.expiredPageSize:2}")
    private Integer EXPIRED_BENEFIT_PAGE_SIZE;

    @Value("${member.benefit.expiredClearPage:1000}")
    private Integer EXPIRED_BENEFIT_ClEAR_PAGE;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        StopWatch stopWatch = new StopWatch();
        try {
            Long merchantId = null;
            if (StringUtils.isNotBlank(param)) {
                log.info("指定商家执行无使用范围已过期权益清理任务: {}", param);
                try {
                    merchantId = Long.valueOf(param);
                } catch (NumberFormatException e) {
                    log.info("参数错误");
                    return ReturnT.FAIL;
                }
            }
            stopWatch.start();
            clearBenefit(merchantId);
            stopWatch.stop();
            log.info("无使用范围已过期权益清理处理耗时: {}秒", stopWatch.getTotalTimeSeconds());
        } finally {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
        }
        return SUCCESS;
    }

    public void clearBenefit(Long mid) {
        int pageIndex = 1;
        IPage<VUmBenefitExpired> page;
        do {
            if (pageIndex > EXPIRED_BENEFIT_ClEAR_PAGE) {
                log.info("[Doris无使用范围已过期权益]-已超过最大页数: {}", EXPIRED_BENEFIT_ClEAR_PAGE);
                return;
            }
            page = memberBenefitDorisRepository.listNotScopeExpiredBenefit(mid, pageIndex, EXPIRED_BENEFIT_PAGE_SIZE);
            log.info("[Doris无使用范围已过期权益]-查询页数: {}, 页大小: {}", pageIndex, EXPIRED_BENEFIT_PAGE_SIZE);
            List<VUmBenefitExpired> records = page.getRecords();
            log.info("records: {}", records);
            if (CollectionUtils.isEmpty(records)) {
                return;
            }
            Map<Long, List<VUmBenefitExpired>> map = records.stream()
                    .collect(Collectors.groupingBy(VUmBenefitExpired::getMerchantId));
            for (Entry<Long, List<VUmBenefitExpired>> entry : map.entrySet()) {
                Long merchantId = entry.getKey();
                List<VUmBenefitExpired> list = entry.getValue();
                log.info("[Doris无使用范围已过期权益]-当前页数：{} 商家: {}, 数量: {}", pageIndex, merchantId, list.size());
                List<Long> ids = list.stream().map(VUmBenefitExpired::getBenefitId).collect(Collectors.toList());
                List<Benefit> benefits = benefitRepository.listBenefit(merchantId, ids);
                log.info("benefits: {}", benefits);
                if (CollectionUtils.isEmpty(benefits)) {
                    log.info("[Doris无使用范围已过期权益]-未查询到权益信息");
                    continue;
                }
                List<Benefit> realNotScopeExpiredBenefits = validData(merchantId, benefits);
                try {
                    NotScopeExpiredBenefitClearJob handler = (NotScopeExpiredBenefitClearJob)AopContext.currentProxy();
                    handler.doClear(realNotScopeExpiredBenefits);
                } catch (Exception e) {
                    log.error("无使用范围已过期权益数据清理异常,merchantId:{},benefitIds:{}", merchantId,
                        realNotScopeExpiredBenefits.stream().map(Benefit::getId).collect(Collectors.toList()), e);
                }
            }
            pageIndex++;
        } while (true);
    }

    private List<Benefit> validData(Long merchantId, List<Benefit> benefits) {
        List<Benefit> realExpiredBenefits = benefits.stream()
            .filter(benefit -> Objects.equals(benefit.getExpiryDateCategory(), ExpiryDateCategoryEnum.TIME_INTERVAL.getValue())
                && DateUtil.parse(benefit.getDownTime()).before(new Date()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(realExpiredBenefits)) {
            log.info("[Doris无使用范围已过期权益]-未查询到真实过期权益");
            return Lists.newArrayList();
        }
        List<Long> realExpiredBenefitIds = realExpiredBenefits.stream().map(Benefit::getId).collect(Collectors.toList());
        if (realExpiredBenefitIds.size() != benefits.size()) {
            List<Benefit> noExpiredBenefits = benefits.stream().filter(benefit -> !realExpiredBenefitIds.contains(benefit.getId()))
                .collect(Collectors.toList());
            log.info("[Doris无使用范围已过期权益]-存在未实际过期权益:{}", noExpiredBenefits);
        }
        List<Long> hasScopeBenefitIds = benefitRepository.listScopeByBenefitIds(merchantId, realExpiredBenefitIds).stream()
            .map(BenefitScope::getBenefitId).distinct().collect(Collectors.toList());
        List<Benefit> realNotScopeExpiredBenefits = new ArrayList<>(realExpiredBenefits);
        if (CollectionUtils.isNotEmpty(hasScopeBenefitIds)) {
            log.info("[Doris无使用范围已过期权益]-存在有使用范围权益:{}", hasScopeBenefitIds);
            realNotScopeExpiredBenefits = realExpiredBenefits.stream().filter(benefit -> !hasScopeBenefitIds.contains(benefit.getId()))
                .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(realNotScopeExpiredBenefits)) {
            log.info("[Doris无使用范围已过期权益]-未查询到真实无使用范围已过期权益");
            return Lists.newArrayList();
        }
        List<AccountBenefit> hasBalanceAccountBenefits = accountBenefitMapper.selectList(Wrappers.<AccountBenefit>lambdaQuery()
            .select(AccountBenefit::getMerchantId, AccountBenefit::getBenefitId, AccountBenefit::getBalance)
            .eq(AccountBenefit::getMerchantId, merchantId)
            .in(AccountBenefit::getBenefitId, realNotScopeExpiredBenefits.stream().map(Benefit::getId).collect(Collectors.toList()))
            .gt(AccountBenefit::getBalance, BigDecimal.ZERO));
        if (CollectionUtils.isNotEmpty(hasBalanceAccountBenefits)) {
            List<Long> hasBalanceBenefitIds = hasBalanceAccountBenefits.stream().map(AccountBenefit::getBenefitId).distinct()
                .collect(Collectors.toList());
            List<Long> hasBalanceAccountBenefitIds = hasBalanceAccountBenefits.stream().map(AccountBenefit::getId).distinct()
                .collect(Collectors.toList());
            log.info("[Doris无使用范围已过期权益]-存在用户权益未消耗完权益:benefitIds:{},accountBenefitIds:{}", hasBalanceBenefitIds, hasBalanceAccountBenefitIds);
            realNotScopeExpiredBenefits = realNotScopeExpiredBenefits.stream()
                .filter(benefit -> !hasBalanceBenefitIds.contains(benefit.getId())).collect(Collectors.toList());
        }
        return realNotScopeExpiredBenefits;
    }

    @Transactional(rollbackFor = Exception.class)
    public void doClear(List<Benefit> benefits) {
        if (CollectionUtils.isEmpty(benefits)) {
            return;
        }
        List<BenefitTmp> accountBenefitTmpList = benefits.stream().map(benefitAssembler::toBenefitTmp).collect(Collectors.toList());
        benefitTmpMapper.insertBatch(accountBenefitTmpList);
        Long merchantId = benefits.get(0).getMerchantId();
        List<Long> clearBenefitIds = benefits.stream().map(Benefit::getId).collect(Collectors.toList());
        int rows = benefitMapper.delete(new LambdaQueryWrapper<Benefit>().eq(Benefit::getMerchantId, merchantId)
            .in(Benefit::getId, clearBenefitIds));
        log.info("[无使用范围已过期权益清理]-merchantId:{},clearCount:{},result:{}", merchantId, clearBenefitIds.size(), rows);
    }

}
