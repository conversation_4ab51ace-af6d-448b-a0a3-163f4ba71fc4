package com.lyy.user.interfaces.assembler;

import com.lyy.user.account.infrastructure.member.dto.MemberGroupPageResponseDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberRangeAssociatedDTO;
import com.lyy.user.application.member.IMemberGroupService;
import com.lyy.user.application.member.IMemberRangeService;
import com.lyy.user.domain.member.entity.MemberGroup;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 会员组分页信息返回
 * <AUTHOR>
 * @className: MemberGroupPageResponseAssembler
 * @date 2021/4/21
 */
@Component
@Slf4j
public class MemberGroupPageResponseAssembler implements Assembler<MemberGroup, MemberGroupPageResponseDTO> {
    @Autowired
    private IMemberGroupService memberGroupService;
    @Autowired
    private IMemberRangeService memberRangeService;
    /**
     * 装配 请实现装配过程
     *
     * @param source
     * @param target
     */
    @Override
    public void assemble(MemberGroup source, MemberGroupPageResponseDTO target) {
        BeanUtils.copyProperties(source,target);
        //会员总数
        target.setMemberCount(memberGroupService.countMemberByGroup(target.getMerchantId(), target.getId()));

        List<MemberRangeAssociatedDTO> list = memberRangeService.getRangeAssociatedList(target.getMerchantId(), target.getId());
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach(memberRangeAssociatedDTO -> {
                Integer count = Optional.ofNullable(memberRangeAssociatedDTO.getAssociatedIdList())
                        .map(List::size)
                        .orElse(0);
                switch (memberRangeAssociatedDTO.getApplicable()){
                    case 1:
                        target.setEquipmentTypeCount(count);
                        break;
                    case 2:
                        target.setEquipmentGroupCount(count);
                        break;
                    case 3:
                        target.setEquipmentCount(count);
                        break;
                    case 4:
                        target.setCommodityCount(count);
                        break;
                    default:
                }


            });
        }
        updateCountInfo(target);
        //更新会员组的状态
        target.setMemberGroupStatus(getMemberGroupStatus(target));
    }

    /**
     * 更新统计信息
     * @param target
     */
    private void updateCountInfo(MemberGroupPageResponseDTO target) {
        if(target.getEquipmentTypeCount() ==null){
            target.setEquipmentTypeCount(0);
        }
        if(target.getEquipmentGroupCount() ==null){
            target.setEquipmentGroupCount(0);
        }
        if(target.getEquipmentCount() ==null){
            target.setEquipmentCount(0);
        }
        if(target.getCommodityCount() ==null){
            target.setCommodityCount(0);
        }
    }

    /**
     * 获取会员组状态
     * 1:生效中
     * 2:已停用
     * 3:已过期
     * 4:已删除
     * 5:待生效
     *
     * @param target
     * @return
     */
    private int getMemberGroupStatus(MemberGroupPageResponseDTO target){
        if(target.getDel()){
            //4:已删除
            return 4;
        }
        if(!target.getActive()){
            // 2:已停用
            return 2;
        }
        long currentTimeMillis = System.currentTimeMillis();
        if(Objects.nonNull(target.getEndDate()) && target.getEndDate().getTime() < currentTimeMillis){
            // 3:已过期
            return 3;
        }
        if(Objects.nonNull(target.getStartDate()) && target.getStartDate().getTime() > currentTimeMillis){
            //5:待生效
            return 5;
        }
        //1:生效中
        return 1;
    }
}
