package com.lyy.user.interfaces.controller.report;

import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.common.DataList;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberCardReportDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberCardReportQueryDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberCardStoreValueReportDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberReportDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberReportQueryDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberStoreValueRecordDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberStoreValueRecordQueryDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberStoreValueReportDto;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.application.report.SmallMemberReportService;
import com.lyy.user.infrastructure.execption.BusinessException;
import io.swagger.annotations.Api;
import java.math.BigDecimal;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date : 2022-01-31 16:22
 **/
@Api(tags = "小场地会员报表")
@Slf4j
@RestController
@RequestMapping("/rest/report")
public class SmallVenueReportController {

    @Resource
    SmallMemberReportService smallMemberReportService;

    @Value("${smallVenue.report.switchValue:false}")
    Boolean switchValue;

    @PostMapping("/member/page")
    public RespBody<DataList<SmallVenueMemberReportDto>> pageQueryMemberReport(@RequestBody @Valid SmallVenueMemberReportQueryDto smallVenueMemberReportQueryDto) {
        checkSwitch();
        log.info("小场地会员报表分页查询" );
        DataList<SmallVenueMemberReportDto> page = smallMemberReportService.queryUserMemberReport(smallVenueMemberReportQueryDto);
        return RespBody.ok(page);
    }

    @PostMapping("/member/total")
    public RespBody<SmallVenueMemberStoreValueReportDto> totalQueryMemberReport(@RequestBody @Valid SmallVenueMemberReportQueryDto smallVenueMemberReportQueryDto) {
        checkSwitch();
        log.info("小场地会员报表剩余储值合计查询");
        SmallVenueMemberStoreValueReportDto total = smallMemberReportService.totalQueryMemberStoreValueReport(smallVenueMemberReportQueryDto);
        return RespBody.ok(total);
    }

    @PostMapping("/card/page")
    public RespBody<DataList<SmallVenueMemberCardReportDto>> pageQueryMemberReport(@RequestBody @Valid SmallVenueMemberCardReportQueryDto smallVenueMemberCardReportQueryDto) {
        checkSwitch();
        log.info("小场地会员卡报表分页查询" );
        DataList<SmallVenueMemberCardReportDto> page = smallMemberReportService.queryUserMemberCardReport(smallVenueMemberCardReportQueryDto);
        return RespBody.ok(page);
    }

    @PostMapping("/card/total")
    public RespBody<SmallVenueMemberCardStoreValueReportDto> totalQueryMemberReport(@RequestBody @Valid SmallVenueMemberCardReportQueryDto smallVenueMemberCardReportQueryDto) {
        checkSwitch();
        log.info("小场地会员卡报表剩余储值合计查询");
        SmallVenueMemberCardStoreValueReportDto total = smallMemberReportService.totalQueryUserMemberCardReport(smallVenueMemberCardReportQueryDto);
        return RespBody.ok(total);
    }

    @PostMapping("/storeValue/page")
    public RespBody<DataList<SmallVenueMemberStoreValueRecordDto>> pageQueryMemberStoreValueReport(@RequestBody @Valid SmallVenueMemberStoreValueRecordQueryDto smallVenueMemberStoreValueRecordQueryDto) {
        checkSwitch();
        log.info("小场地会员储值变更记录报表分页查询" );
        DataList<SmallVenueMemberStoreValueRecordDto> page = smallMemberReportService.pageQueryMemberStoreValueReport(smallVenueMemberStoreValueRecordQueryDto);
        return RespBody.ok(page);
    }

    @PostMapping("/storeValue/total")
    public RespBody<BigDecimal> totalQueryMemberStoreValueReport(@RequestBody @Valid SmallVenueMemberStoreValueRecordQueryDto smallVenueMemberStoreValueRecordQueryDto) {
        checkSwitch();
        log.info("小场地会员储值变更记录报表合计查询" );
        BigDecimal total = smallMemberReportService.totalQueryMemberStoreValueChangeReport(smallVenueMemberStoreValueRecordQueryDto);
        return RespBody.ok(total);
    }

    @PostMapping("/member/page/v2")
    public RespBody<DataList<SmallVenueMemberReportDto>> pageQueryMemberReportV2(@RequestBody @Valid SmallVenueMemberReportQueryDto smallVenueMemberReportQueryDto) {
        checkSwitch();
        log.info("小场地会员报表分页查询V2" );
        DataList<SmallVenueMemberReportDto> page = smallMemberReportService.queryUserMemberReportV2(smallVenueMemberReportQueryDto);
        return RespBody.ok(page);
    }

    public void checkSwitch(){
        if(!switchValue){
            throw new BusinessException(GlobalErrorCode.NO_PERMISSION.getCode(), "报表功能未开启");
        }
    }
}
