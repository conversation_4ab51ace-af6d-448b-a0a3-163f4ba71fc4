package com.lyy.user.interfaces.controller.user;


import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneDTO;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneQueryParam;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneRecordDTO;
import com.lyy.user.application.user.IPlatformUserPhoneService;
import io.swagger.annotations.Api;
import java.util.List;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 平台用户手机接口
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@Api(tags = "平台用户手机号管理")
@RestController
@RequestMapping("/platform/user/phone")
@Slf4j
public class PlatformUserPhoneController {

    @Autowired
    private IPlatformUserPhoneService platformUserPhoneService;

    /**
     * 新增或修改平台用户电话
     * @param dto
     * @return
     */
    @PostMapping("/saveOrUpdate")
    public RespBody saveOrUpdatePlatformUserPhone(@Valid @RequestBody PlatformUserPhoneDTO dto) {
        log.debug("新增或修改平台用户电话,dto:{}", dto);
        platformUserPhoneService.saveOrUpdatePlatformUserPhone(dto);
        return RespBody.ok();
    }

    /**
     * 获取平台用户电话
     * @return
     */
    @PostMapping("/list")
    public RespBody<List<PlatformUserPhoneRecordDTO>> list(@RequestBody PlatformUserPhoneQueryParam param) {
        log.debug("获取平台用户电话,list,param: {}", param);
        if (param.getId() == null && param.getMerchantId() == null && StringUtils.isEmpty(param.getTelephone())) {
            return RespBody.fail(UserErrorCode.PLATFORM_USER_QUERY_PARAM_ERROR);
        }
        return RespBody.ok(platformUserPhoneService.list(param));
    }



}
