package com.lyy.user.interfaces.assembler;

import com.lyy.user.domain.account.entity.AccountBenefit;
import com.lyy.user.domain.account.entity.AccountBenefitTmp;
import com.lyy.user.domain.benefit.entity.Benefit;
import com.lyy.user.domain.benefit.entity.BenefitTmp;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface BenefitAssembler {

   BenefitTmp toBenefitTmp(Benefit benefit);

   Benefit toBenefit(BenefitTmp benefitTmp);

   AccountBenefitTmp toAccountBenefitTmp(AccountBenefit benefit);

   AccountBenefit toAccountBenefit(AccountBenefitTmp benefitTmp);
}
