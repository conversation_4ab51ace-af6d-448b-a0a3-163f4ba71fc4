package com.lyy.user.interfaces.schedule.clear;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lyy.user.account.infrastructure.constant.AccountBenefitStatusEnum;
import com.lyy.user.domain.account.entity.AccountBenefit;
import com.lyy.user.domain.account.entity.AccountBenefitTmp;
import com.lyy.user.domain.account.repository.AccountBenefitMapper;
import com.lyy.user.domain.account.repository.AccountBenefitTmpMapper;
import com.lyy.user.domain.doris.entity.VUmAccountBenefitExpired6m;
import com.lyy.user.domain.doris.repository.MemberBenefitDorisRepository;
import com.lyy.user.infrastructure.repository.account.AccountBenefitRepository;
import com.lyy.user.interfaces.assembler.BenefitAssembler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

/**
 * 已过期6m用户权益批次清理
 */
@Slf4j
@JobHandler(value = "expired6mAccountBenefitClearJob")
@Component
public class Expired6mAccountBenefitClearJob extends IJobHandler {

    @Resource
    private AccountBenefitMapper accountBenefitMapper;

    @Resource
    private AccountBenefitRepository accountBenefitRepository;
    @Resource
    private MemberBenefitDorisRepository memberBenefitDorisRepository;

    @Resource
    private BenefitAssembler benefitAssembler;
    @Resource
    private AccountBenefitTmpMapper accountBenefitTmpMapper;

    @Value("${member.benefit.expiredPageSize:2}")
    private Integer EXPIRED_BENEFIT_PAGE_SIZE;

    @Value("${member.benefit.expiredClearPage:1000}")
    private Integer EXPIRED_BENEFIT_ClEAR_PAGE;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        StopWatch stopWatch = new StopWatch();
        try {
            Long merchantId = null;
            if (StringUtils.isNotBlank(param)) {
                log.info("指定商家执行已过期6m用户权益批次清理任务: {}", param);
                try {
                    merchantId = Long.valueOf(param);
                } catch (NumberFormatException e) {
                    log.info("参数错误");
                    return ReturnT.FAIL;
                }
            }
            stopWatch.start();
            clearAccountBenefit(merchantId);
            stopWatch.stop();
            log.info("已过期6m用户权益批次清理处理耗时: {}秒", stopWatch.getTotalTimeSeconds());
        } finally {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
        }
        return SUCCESS;
    }

    public void clearAccountBenefit(Long mid) {
        int pageIndex = 1;
        IPage<VUmAccountBenefitExpired6m> page;
        do {
            if (pageIndex > EXPIRED_BENEFIT_ClEAR_PAGE) {
                log.info("[Doris已过期6m权益]-已超过最大页数: {}", EXPIRED_BENEFIT_ClEAR_PAGE);
                return;
            }
            page = memberBenefitDorisRepository.listExpiredBenefit6m(mid, pageIndex, EXPIRED_BENEFIT_PAGE_SIZE);
            log.info("[Doris已过期6m权益]-查询页数: {}, 页大小: {}", pageIndex, EXPIRED_BENEFIT_PAGE_SIZE);
            List<VUmAccountBenefitExpired6m> records = page.getRecords();
            log.info("records: {}", records);
            if (CollectionUtils.isEmpty(records)) {
                return;
            }
            Map<Long, List<VUmAccountBenefitExpired6m>> map = records.stream()
                    .collect(Collectors.groupingBy(VUmAccountBenefitExpired6m::getMerchantId));
            for (Entry<Long, List<VUmAccountBenefitExpired6m>> entry : map.entrySet()) {
                Long merchantId = entry.getKey();
                List<VUmAccountBenefitExpired6m> list = entry.getValue();
                log.info("[Doris已过期6m权益]-当前页数：{} 商家: {}, 数量: {}", pageIndex, merchantId, list.size());
                List<Long> ids = list.stream().map(VUmAccountBenefitExpired6m::getId).collect(Collectors.toList());
                List<AccountBenefit> accountBenefits = accountBenefitRepository.listAccountBenefit(merchantId, ids);
                log.info("accountBenefits: {}", accountBenefits);
                if (CollectionUtils.isEmpty(accountBenefits)) {
                    log.info("[Doris已过期6m权益]-未查询到权益信息，跳过");
                    continue;
                }
                List<AccountBenefit> noExpiredBenefits = accountBenefits.stream()
                    .filter(accountBenefit -> !Objects.equals(accountBenefit.getStatus(), AccountBenefitStatusEnum.EXPIRED.getStatus()))
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(noExpiredBenefits)) {
                    log.info("[Doris已过期6m权益]-查询到未过期权益:{}", noExpiredBenefits);
                    accountBenefits = accountBenefits.stream()
                        .filter(accountBenefit -> Objects.equals(accountBenefit.getStatus(), AccountBenefitStatusEnum.EXPIRED.getStatus()))
                        .collect(Collectors.toList());
                }
                try {
                    Expired6mAccountBenefitClearJob handler = (Expired6mAccountBenefitClearJob)AopContext.currentProxy();
                    handler.doClear(accountBenefits);
                } catch (Exception e) {
                    log.error("权益过期数据清理异常,merchantId:{},accountBenefitIds:{}", merchantId,
                        accountBenefits.stream().map(AccountBenefit::getId).collect(Collectors.toList()), e);
                }
            }
            pageIndex++;
        } while (true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void doClear(List<AccountBenefit> accountBenefits) {
        if (CollectionUtils.isEmpty(accountBenefits)) {
            return;
        }
        List<AccountBenefitTmp> accountBenefitTmpList = accountBenefits.stream().map(benefitAssembler::toAccountBenefitTmp).collect(Collectors.toList());
        accountBenefitTmpMapper.insertBatch(accountBenefitTmpList);
        Long merchantId = accountBenefits.get(0).getMerchantId();
        List<Long> clearAccountBenefitIds = accountBenefits.stream().map(AccountBenefit::getId).collect(Collectors.toList());
        int rows = accountBenefitMapper.delete(new LambdaQueryWrapper<AccountBenefit>().eq(AccountBenefit::getMerchantId, merchantId)
            .in(AccountBenefit::getId, clearAccountBenefitIds));
        log.info("[已过期6m权益清理]-merchantId:{},clearCount:{},result:{}", merchantId, clearAccountBenefitIds.size(), rows);
    }

}
