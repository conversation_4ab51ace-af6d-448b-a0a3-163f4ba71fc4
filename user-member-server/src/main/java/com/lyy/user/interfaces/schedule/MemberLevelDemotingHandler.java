package com.lyy.user.interfaces.schedule;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.constant.MemberLiftingConditionEnum;
import com.lyy.user.account.infrastructure.constant.MemberLiftingRuleCategoryEnum;
import com.lyy.user.account.infrastructure.constant.MemberLiftingRuleRecordStatusEnum;
import com.lyy.user.application.member.IMemberGroupService;
import com.lyy.user.application.member.IMemberLiftingRuleRecordService;
import com.lyy.user.application.member.IMemberLiftingRuleService;
import com.lyy.user.application.member.IMemberLiftingService;
import com.lyy.user.application.member.IMemberService;
import com.lyy.user.domain.member.dto.MemberLiftingRuleRecordUserDTO;
import com.lyy.user.domain.member.dto.MemberLiftingStrategyResultDTO;
import com.lyy.user.domain.member.entity.Member;
import com.lyy.user.domain.member.entity.MemberLifting;
import com.lyy.user.domain.member.entity.MemberLiftingRule;
import com.lyy.user.domain.member.entity.MemberLiftingRuleRecord;
import com.lyy.user.domain.member.lifting.LiftingFactory;
import com.lyy.user.infrastructure.config.HintManagerHolder;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import com.xxl.job.core.util.ShardingUtil;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 会员定时降级
 * <AUTHOR>
 * @className: MemberLevelDaoHandler
 * @date 2021/8/2
 */
@Slf4j
@JobHandler(value = "memberLevelDemoting")
@Component
public class MemberLevelDemotingHandler extends IJobHandler {

    @Autowired
    private IMemberLiftingService memberLiftingService;

    @Autowired
    private IMemberGroupService memberGroupService;
    @Autowired
    private IMemberLiftingRuleService memberLiftingRuleService;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IMemberLiftingRuleRecordService memberLiftingRuleRecordService;

    @Value("${coupon.query.pageSize:10000}")
    private int pageSize;

    /**
     * execute handler, invoked when executor receives a scheduling request
     *
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        int currentIndex = ShardingUtil.getShardingVo().getIndex();
        int total = ShardingUtil.getShardingVo().getTotal();

        log.info("[会员定时降级] 开始, total={}, currentIndex={}", total, currentIndex);
        //MemberLifting 表只分库了，没有分表，因此只需要处理2次即可
        for (long currentShard = 0; currentShard < 128; currentShard++) {
            long index = currentShard % total;
            log.info("currentShard % total = {}", index);
            if (index == currentIndex) {
                try {
                    long startTime = System.currentTimeMillis();
                     int size = runTask(currentShard);
                    log.info("{} 机器 执行定时任务处理会员定时降级-->用时为 {} 秒,共处理 {} 商户表库的 {} 个任务",currentIndex,
                            (System.currentTimeMillis() - startTime) / 1000, currentShard, size);
                    //防止一直阻塞占用资源
                    Thread.sleep(10);
                }catch (Exception e){
                    log.info("{} 机器 执行定时任务处理会员定时降级，处理 {} 商户表库出错",currentIndex, currentShard);
                    log.error("处理会员定时降级异常,{}", e.getMessage(), e);
                    Thread.currentThread().interrupt();
                }
            }
        }
        return SUCCESS;
    }

    /**
     * 运行任务
     * @param merchantId
     * @return
     */
    public int runTask(long merchantId) {
        Page page = new Page();
        page.setSize(pageSize);
        int pages = Integer.MAX_VALUE;
        int taskSize = 0;
        HintManager hintManager = HintManagerHolder.getInstance();
        try {
            hintManager.setMasterRouteOnly();
            for (int i = 1; i <= pages; i++) {
                //分页获取需要降等级的策略
                page.setCurrent(i);
                Page<MemberLifting> memberLiftingPage = memberLiftingService.findLiftingDemoting(page,merchantId);
                log.info("第 {} 页 {} 商户有{}个降等级信息", i, merchantId, memberLiftingPage.getRecords().size());
                if(memberLiftingPage.getRecords().isEmpty()){
                    return taskSize;
                }
                pages = (int) memberLiftingPage.getPages();
                for (MemberLifting memberLifting: memberLiftingPage.getRecords()){
                    if(memberLiftingHandler(memberLifting) > 0){
                        taskSize++;
                    }
                }
            }
        }finally {
            hintManager.close();
        }
        return taskSize;
    }

    private int memberLiftingHandler(MemberLifting memberLifting) {
        Long merchantId = memberLifting.getMerchantId();
        int countNumber = memberGroupService.countEffectiveMemberByGroup(merchantId, memberLifting.getMemberGroupId());
        if (log.isDebugEnabled()) {
            log.debug("会员组降级,{} 商户,会员升级策略:{}, 处理用户数:{}", merchantId, memberLifting, countNumber);
        }
        if (countNumber <= 0) {
            log.info("{} 商户 {} 会员组没有会员，不用处理", merchantId, memberLifting.getMemberGroupId());
            return 0;
        }
        List<MemberLiftingRule> memberLiftingRuleList = memberLiftingRuleService.findByMemberLifting(merchantId, Collections.singletonList(memberLifting.getId()), false);
        if (CollectionUtils.isEmpty(memberLiftingRuleList)) {
            log.warn("{} 升降级没有对应的规则信息", memberLifting);
            return 0;
        }
        int rageDateMax = memberLiftingRuleList.stream()
                .map(MemberLiftingRule::getRangeDate)
                .filter(Objects::nonNull)
                .mapToInt(Short::intValue)
                .max()
                .orElse(0);
        if (rageDateMax <= 0) {
            return 0;
        }
        LocalDateTime localDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime startDateTime = localDateTime.plusDays(rageDateMax * -1);
        //结束时间要加1天，有可能是由于今天刚好添加的记录，防止重复处理权益
        LocalDateTime endDateTime = localDateTime.plusDays(1);
        List<Member> memberList = memberService.getNotRecordOfTime(memberLifting, startDateTime, endDateTime);
        if (memberList.isEmpty()) {
            log.info("{} 降等级在 {} - {} 内没有可以处理的用户信息", memberLifting, startDateTime, localDateTime);
            return 0;
        }
        log.info("{} 商户的{} 会员组的 {} 降等级策略,有{} 个会员信息需要处理",
                memberLifting.getMerchantId(), memberLifting.getMemberGroupId(), memberLifting.getId(), memberList.size());
        XxlJobLogger.log("{} 商户的{} 会员组的 {} 降等级策略,有{} 个会员信息需要处理",
                memberLifting.getMerchantId(), memberLifting.getMemberGroupId(), memberLifting.getId(), memberList.size());

        List<Long> memberLiftingRuleIdList = memberLiftingRuleList.stream()
                .map(MemberLiftingRule::getId)
                .collect(Collectors.toList());
        List<Long> memberIds = memberList.stream()
                .map(Member::getId)
                .collect(Collectors.toList());
        List<MemberLiftingRuleRecordUserDTO> recordUserList = memberLiftingRuleRecordService.countUserRecordByRule(merchantId, memberLiftingRuleIdList, memberIds, MemberLiftingRuleRecordStatusEnum.INIT.getValue(), true);
        List<Member> conformList = filterMember(memberLifting, memberLiftingRuleList, memberList, recordUserList);
        //提取会员id用于打印日志
        List<Long> memberIdList = conformList.stream().map(Member::getId).collect(Collectors.toList());
        log.info("触发 {} 降级策略,规则列表为 {}, 会员列表为 {} ", memberLifting, memberLiftingRuleList, memberIdList);
        XxlJobLogger.log("{} 商户触发 {} 降级策略,规则列表为 {}, 会员列表为 {}",
                memberLifting.getMerchantId(), memberLifting.getId(), memberLiftingRuleIdList, memberIdList);

        //开始处理会员的扣积分与降级处理
        //可以考虑异步
        conformList.stream().forEach(member -> {
            MemberLiftingStrategyResultDTO memberLiftingStrategyResultDTO = new MemberLiftingStrategyResultDTO();
            memberLiftingStrategyResultDTO.setMemberLifting(memberLifting);
            memberLiftingStrategyResultDTO.setMember(member);
            Map<MemberLiftingRule, Collection<MemberLiftingRuleRecord>> memberLiftingRuleMap = new HashMap<>(memberLiftingRuleList.size() * 2);
            memberLiftingStrategyResultDTO.setMemberLiftingRuleMap(memberLiftingRuleMap);
            for (MemberLiftingRule memberLiftingRule : memberLiftingRuleList) {
                //遍历规则
                List<MemberLiftingRuleRecord> recordList = memberLiftingRuleRecordService.findByStatusAndEndTime(member.getUserId(), memberLiftingRule.getId(), MemberLiftingRuleRecordStatusEnum.INIT.getValue(), localDateTime);
                MemberLiftingRule memberLiftingRuleKey = new MemberLiftingRule();
                BeanUtils.copyProperties(memberLiftingRule, memberLiftingRuleKey, "category");
                memberLiftingRuleKey.setCategory(MemberLiftingRuleCategoryEnum.CATEGORY_DEFAULT.getValue());
                memberLiftingRuleMap.put(memberLiftingRuleKey, recordList);
            }
            //用户的处理升降级
            log.info("处理降等级 -->{}", memberLiftingStrategyResultDTO);
            LiftingFactory.handlerByResult(memberLiftingStrategyResultDTO);
        });

        return conformList.size();

    }

    /**
     * 过滤出触发了不满足规则的会员信息
     * @param memberLifting
     * @param memberLiftingRuleList
     * @param memberList
     * @param recordUserList
     * @return
     */
    private List<Member> filterMember(MemberLifting memberLifting, List<MemberLiftingRule> memberLiftingRuleList, List<Member> memberList, List<MemberLiftingRuleRecordUserDTO> recordUserList) {
        Map<String,List<MemberLiftingRuleRecordUserDTO>> recordUserMap;
        if(org.apache.commons.collections.CollectionUtils.isEmpty(recordUserList)){
            recordUserMap = Collections.emptyMap();
        }else{
            recordUserMap = recordUserList.stream()
                    .collect(Collectors.groupingBy(record-> record.getMemberId()+":" + record.getMemberLiftingRuleId()));
        }
        List<Member> conformList = new ArrayList<>(memberList.size());
        for (Member member:memberList){
            //遍历用户
            if(memberLifting.getCondition().equals(MemberLiftingConditionEnum.OR_ONE.getValue())){
                //只需要一项就可以触发
                for(MemberLiftingRule memberLiftingRule:memberLiftingRuleList){
                    //遍历规则
                    if(checkMemberRule(member,memberLiftingRule,recordUserMap)){
                        conformList.add(member);
                        break;
                    }
                }
            }else if(memberLifting.getCondition().equals(MemberLiftingConditionEnum.AND_ALL.getValue())){
                //需要全部符合才能触发
                boolean result = true;
                for(MemberLiftingRule memberLiftingRule:memberLiftingRuleList) {
                    //遍历规则
                    if (!checkMemberRule(member, memberLiftingRule, recordUserMap)) {
                        result = false;
                        break;
                    }
                }
                if(result){
                    conformList.add(member);
                }
            }else{
                //条件异常直接返回失败
                log.warn("找不到 {} 升级策略对应的条件",memberLifting);
//                return 0;
            }
        }
        return conformList;
    }


    private boolean checkMemberRule(Member member, MemberLiftingRule memberLiftingRule, Map<String, List<MemberLiftingRuleRecordUserDTO>> recordUserMap) {
        String key = member.getId()+":" + memberLiftingRule.getId();
        List<MemberLiftingRuleRecordUserDTO> list = recordUserMap.get(key);
        boolean judgeCondition = Optional.ofNullable(memberLiftingRule.getJudgeCondition()).orElse(true);
        if(list == null || list.isEmpty()){
            return !judgeCondition;
        }
        MemberLiftingRuleRecordUserDTO memberLiftingRuleRecordUserDTO = list.get(0);
        MemberLiftingRuleCategoryEnum categoryEnum = MemberLiftingRuleCategoryEnum.getByValue(memberLiftingRule.getCategory());
        if(MemberLiftingRuleCategoryEnum.getCountCategoryList().contains(categoryEnum)){
            //统计次数的
            //登录次数，支付笔数，完善信息，绑定手机，关注公众号
            int num = memberLiftingRule.getRangeValue().intValue();
            if(num <= memberLiftingRuleRecordUserDTO.getCountNum()){
                return judgeCondition;
            }
        }else if(MemberLiftingRuleCategoryEnum.getSumCategoryList().contains(categoryEnum)){
            //统计总数的
            //消费金额
            BigDecimal count = memberLiftingRuleRecordUserDTO.getSumRangeValue();
            if(count.compareTo(memberLiftingRule.getRangeValue()) >= 0){
                //实际的总数大于等于规则规定的总数
                return judgeCondition;
            }
        }
        return !judgeCondition;
    }


}
