package com.lyy.user.interfaces.controller.account;

import com.lyy.error.member.infrastructure.AccountErrorCode;
import com.lyy.user.account.infrastructure.account.dto.SmallVenueMergeAccountInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.request.AccountBenefitUpdateDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.AccountCancellationDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.AvailableBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitDecrementDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitIncrementBatchDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitIncrementDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitIncrementItemDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitRefundQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitRefundResultDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.CardTransferDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MerchantBenefitIncrementDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueAccountBenefitTransferDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueAccountBenefitTransferQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueUserAvailableBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.UserAccountBenefit;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.request.AccountDefaultStatusUpdateDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.request.BenefitConsumptionSummaryQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.response.AccountRecordBenefitSummaryDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.application.benefit.SmallVenueAccountService;
import com.lyy.user.domain.account.dto.AccountInitDTO;
import com.lyy.user.domain.account.entity.Account;
import io.swagger.annotations.Api;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小场地账号相关
 *
 * <AUTHOR>
 * @since 2022/1/8
 */
@Api(tags = "多金宝-会员账户管理")
@Slf4j
@RestController
@RequestMapping("/account/smallVenue")
public class SmallVenueAccountController {

    @Resource
    private SmallVenueAccountService service;

    /**
     * 增加权益
     *
     * @param dto 增加权益DTO
     * @return boolean
     */
    @PostMapping("/benefit/increment")
    public RespBody<Boolean> increaseAccountBenefit(@RequestBody @Validated BenefitIncrementDTO dto) {
        if (log.isDebugEnabled()) {
            log.debug("增加权益 dto: {}", dto);
        }
        service.increaseAccountBenefit(dto);
        return RespBody.ok(true);
    }

    /**
     * 批量用户增加权益
     *
     * @param dto 增加权益DTO
     * @return boolean
     */
    @PostMapping("/benefit/increment/batch")
    public RespBody<Boolean> increaseAccountBenefitBatch(@RequestBody @Validated BenefitIncrementBatchDTO dto) {
        if (log.isDebugEnabled()) {
            log.debug("批量用户增加权益 dto: {}", dto);
        }
        service.increaseAccountBenefitBatch(dto);
        return RespBody.ok(true);
    }

    /**
     * 权益扣减
     *
     * @param dto 权益扣减DTO
     * @return boolean
     */
    @PostMapping("/benefit/decrement")
    public RespBody<Boolean> decreaseAccountBenefit(@RequestBody @Validated BenefitDecrementDTO dto) {
        if (log.isDebugEnabled()) {
            log.debug("权益扣减 dto: {}", dto);
        }
        service.decreaseAccountBenefit(dto);
        return RespBody.ok(true);
    }


    /**
     * 转账
     *
     * @param dto 会员卡转账DTO
     * @return boolean
     */
    @PostMapping("/transfer")
    public RespBody<Boolean> transfer(@RequestBody @Validated CardTransferDTO dto) {
        if (log.isDebugEnabled()) {
            log.debug("会员卡转账 dto: {}", dto);
        }
        service.transfer(dto);
        return RespBody.ok(true);
    }

    /**
     * 注销账户
     *
     * @param dto 注销账户DTO
     * @return boolean
     */
    @PostMapping("/cancellation")
    public RespBody<Boolean> cancellation(@RequestBody @Validated AccountCancellationDTO dto) {
        if (log.isDebugEnabled()) {
            log.debug("会员卡注销 dto: {}", dto);
        }
        service.cancellation(dto);
        return RespBody.ok(true);
    }

    /**
     * 获取会员卡账户下的权益明细
     */
    @GetMapping("/benefits/list")
    public RespBody<List<UserAccountBenefit>> listBenefit(@RequestParam Long merchantId,
                                                          @RequestParam String cardNo,
                                                          @RequestParam(required = false) Integer status) {
        if (log.isDebugEnabled()) {
            log.debug("获取会员卡账户下的权益明细 merchantId: {}, cardNo: {}, status: {}", merchantId, cardNo, status);
        }
        List<UserAccountBenefit> list = service.listBenefit(merchantId, cardNo, status);
        return RespBody.ok(list);
    }


    /**
     * 查询用户可用的权益数据
     *
     * @param queryDTO 用户可用权益查询DTO
     * @return
     */
    @PostMapping("/findUserAvailableBenefit")
    public RespBody<List<SmallVenueUserAvailableBenefitDTO>> findUserAvailableBenefit(@RequestBody @Validated AvailableBenefitQueryDTO queryDTO) {
        if (log.isDebugEnabled()) {
            log.debug("查询用户可用的权益数据,{}", queryDTO);
        }
        return RespBody.ok(service.findUserAvailableBenefit(queryDTO));
    }

    /**
     * 根据单号获取消费记录和消费记录中对应的权益明细
     *
     * @param queryDTO     查询参数
     * @return
     */
    @PostMapping("/findAccountRecordsAndAccountBenefits")
    public RespBody<BenefitRefundResultDTO> refundByOrderNo(@RequestBody @Validated BenefitRefundQueryDTO queryDTO) {
        if (log.isDebugEnabled()) {
            log.debug("根据单号获取消费记录和消费记录中对应的权益明细,{}", queryDTO);
        }
        return RespBody.ok(service.findAccountRecordsAndAccountBenefits(queryDTO));
    }

    /**
     * 当前订单的权益消费汇总信息
     *
     * @param query
     * @return
     */
    @PostMapping("/record/benefits/consumption/summary")
    public RespBody<AccountRecordBenefitSummaryDTO> benefitsConsumptionSummary(@RequestBody @Validated BenefitConsumptionSummaryQueryDTO query) {
        if (log.isDebugEnabled()) {
            log.debug("当前订单的权益消费汇总信息, {}", query);
        }
        return RespBody.ok(service.benefitsConsumptionSummary(query));
    }

    /**
     * 调整默认卡账户
     *
     * @param dto dto
     * @return
     */
    @PostMapping("/default/adjustment")
    public RespBody<Boolean> adjustDefaultAccount(@RequestBody @Validated AccountDefaultStatusUpdateDTO dto) {
        if (log.isDebugEnabled()) {
            log.debug("调整默认卡账户, dto: {}", dto);
        }
        service.adjustDefaultAccount(dto);
        return RespBody.ok(true);
    }

    /**
     * 商家批量增加权益
     *
     * @param dto 增加权益DTO
     * @return boolean
     */
    @PostMapping("/benefit/merchant/increment")
    public RespBody<Boolean> merchantIncrementAccountBenefit(@RequestBody @Validated MerchantBenefitIncrementDTO dto) {
        if (log.isDebugEnabled()) {
            log.debug("商家批量增加权益 dto: {}", dto);
        }
        service.merchantBatchIncrementAccountBenefit(dto);
        return RespBody.ok(true);
    }

    /**
     * 初始化多金宝账户
     *
     * @param merchantId     商户id
     * @param storeId        场地id
     * @param userId         用户id
     * @param merchantUserId 商户用户id
     * @return
     */
    @GetMapping("/initDefaultAccount")
    public RespBody<Long> initDefaultAccount(@RequestParam("merchantId") Long merchantId,
                                             @RequestParam("storeId") Long storeId,
                                             @RequestParam(value = "userId", required = false) Long userId,
                                             @RequestParam(value = "merchantUserId", required = false) Long merchantUserId) {
        if (log.isDebugEnabled()) {
            log.debug("初始化默认账户,merchantId:{},storeId:{},userId:{},merchantUserId:{}", merchantId, storeId, userId, merchantUserId);
        }
        return RespBody.ok(service.initVenueAccount(merchantId, userId, merchantUserId, storeId));
    }

    /**
     * 多金宝会员账户
     *
     * @param smallVenueMergeAccountInfoDTO
     * @return
     */
    @PostMapping("/merge/userMember")
    public RespBody<Boolean> mergeUserMemberAccount(@RequestBody SmallVenueMergeAccountInfoDTO smallVenueMergeAccountInfoDTO) {
        service.mergeUserMemberAccount(smallVenueMergeAccountInfoDTO);
        return RespBody.ok(true);
    }

    /**
     * 查询娱乐会员在场地下的储值
     *
     * @param smallVenueAccountBenefitTransferQueryDTO
     * @return
     */
    @PostMapping("/selectEnbleTransferAccountBenefit")
    public RespBody<List<SmallVenueAccountBenefitTransferDTO>> selectTransferAccountBenefit(@RequestBody SmallVenueAccountBenefitTransferQueryDTO smallVenueAccountBenefitTransferQueryDTO) {
        if (log.isDebugEnabled()) {
            log.debug("查询娱乐会员在场地下的储值 dto: {}", smallVenueAccountBenefitTransferQueryDTO);
        }
        return RespBody.ok(service.selectTotalTransferAccountBenefit(smallVenueAccountBenefitTransferQueryDTO));
    }

    /**
     * 多金宝清除娱乐余额余币
     *
     * @param smallVenueAccountBenefitTransferQueryDTO
     * @return
     */
    @PostMapping("/smallVenueClearBalanceAndCoin")
    public RespBody<Void> smallVenueClearBalanceAndCoin(@RequestBody SmallVenueAccountBenefitTransferQueryDTO smallVenueAccountBenefitTransferQueryDTO) {
        if (log.isDebugEnabled()) {
            log.debug("多金宝清除娱乐余额余币 dto: {}", smallVenueAccountBenefitTransferQueryDTO);
        }
        service.smallVenueClearBalanceAndCoin(smallVenueAccountBenefitTransferQueryDTO);
        return RespBody.ok();
    }

    /**
     * 初始化多金宝账户
     *
     * @param accountInitDTO 储值信息
     * @return
     */
    @PostMapping("/initAccountIfAbsent")
    public RespBody<Account> initAccountIfAbsent(@RequestBody AccountInitDTO accountInitDTO) {
        if (log.isDebugEnabled()) {
            log.debug("初始化默认账户,accountInitDTO:{}", accountInitDTO);
        }
        BenefitIncrementItemDTO item = new BenefitIncrementItemDTO();
        item.setCardNo(accountInitDTO.getCardNo());
        item.setDeposit(accountInitDTO.getDeposit());
        item.setExpiredType(accountInitDTO.getExpiredType());
        item.setDownTime(accountInitDTO.getDownTime());
        return RespBody.ok(service.initAccountIfAbsent(accountInitDTO.getMerchantId(), accountInitDTO.getUserId(), accountInitDTO.getStoreId(), item, () -> accountInitDTO));
    }

    @PostMapping("/updateVenueAccount")
    public RespBody<Void> updateVenueAccount(@RequestBody Account account) {
        if (log.isDebugEnabled()) {
            log.debug("更新储值账户,Account:{}", account);
        }
        if (account.getMerchantId() == null || account.getId() == null) {
            return RespBody.build(AccountErrorCode.ACCOUNT_QUERY_PARAM_ERROR, null);
        }
        service.updateVenueAccount(account);
        return RespBody.ok();
    }

    /**
     * 更新批量权益过期时间
     *
     * @param accountBenefitUpdateDTOList 待更新的权益列表dtolist
     * @return {@link RespBody}<{@link Boolean}>
     */
    @PostMapping("/benefit/expired/setup")
    public RespBody<Boolean> updateBatchBenefitExpire(@RequestBody List<AccountBenefitUpdateDTO> accountBenefitUpdateDTOList) {
        if(log.isDebugEnabled()) {
            log.debug("批量更新账号权益：{}", accountBenefitUpdateDTOList);
        }
        return RespBody.ok(service.updateExpireBatch(accountBenefitUpdateDTOList));
    }
}
