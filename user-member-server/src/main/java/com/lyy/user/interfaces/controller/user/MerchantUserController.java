package com.lyy.user.interfaces.controller.user;

import static com.lyy.error.constant.GlobalErrorCode.DEPRECATED_METHOD_EXCEPTION;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.member.dto.MemberBatchQueryRequestDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.user.dto.MerchantIntegralUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.PayoutBenefitDTO;
import com.lyy.user.application.user.IMerchantUserService;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.infrastructure.util.CommonConverterTools;
import io.swagger.annotations.Api;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.lyy.error.constant.GlobalErrorCode.DEPRECATED_METHOD_EXCEPTION;

/**
 * 商户用户对外接口
 *
 * @author: pengkun
 * @date: 2021/03/30
 **/
@Api(tags = "商户用户管理")
@Slf4j
@RestController
@RequestMapping("/merchant/user")
public class MerchantUserController {

    @Resource
    private IMerchantUserService merchantUserService;

    /**
     * 根据id获取商户用户信息
     * @param merchantUserId    商户用户id
     * @param merchantId    商户id
     * @return
     */
    @GetMapping("/findById")
    public RespBody<MerchantUserDTO> findById(@RequestParam("merchantUserId") Long merchantUserId,@RequestParam("merchantId")Long merchantId){
        log.debug("根据id获取商户用户信息,merchantUserId:{},merchantId:{}",merchantUserId,merchantId);
        MerchantUser merchantUser = merchantUserService.findById(merchantUserId,merchantId);
        MerchantUserDTO dto = null;
        if(merchantUser != null){
            dto = new MerchantUserDTO();
            BeanUtils.copyProperties(merchantUser,dto);
        }
        return RespBody.ok(dto);
    }
    
    /**
     * 根据平台用户ID和商户ID获取商户用户信息
     * @param userId 平台用户ID
     * @param merchantId 商户ID
     * @return 用户不存在时，body为null
     */
    @GetMapping("/findByUserIdAndMerchantId")
    public RespBody<MerchantUserDTO> findByUserIdAndMerchantId(@RequestParam("userId") Long userId, @RequestParam("merchantId") Long merchantId) {
        log.debug("根据平台用户id获取商户用户信息,userId:{},merchantId:{}", userId, merchantId);
        MerchantUser merchantUser = merchantUserService.findByUserIdAndMerchantId(userId, merchantId);
        return RespBody.ok(CommonConverterTools.convert(MerchantUserDTO.class, merchantUser));
    }

    /**
     * 根据手机号和商户Id获取商户用户信息
     * @param telephone 手机号码
     * @param merchantId    商户Id
     * @return
     */
    @GetMapping("/findByTelephoneAndMerchantId")
    public RespBody<List<MerchantUserDTO>> findByTelephoneAndMerchantId(@RequestParam("telephone")String telephone, @RequestParam("merchantId")Long merchantId){
        log.debug("根据手机号和商户Id获取商户用户信息,telephone:{},merchantId:{}",telephone,merchantId);
        List<MerchantUser> list = merchantUserService.findByTelephoneAndMerchantId(telephone, merchantId);
        return RespBody.ok(CommonConverterTools.convert(MerchantUserDTO.class,list));
    }

    /**
     * 商户查询用户会员
     * @param merchantUserQueryDTO
     * @return
     */
    @PostMapping("/queryUserListByMerchant")
    public RespBody<Page<MerchantUserListDTO>> queryUserListByMerchant(@RequestBody @Validated MerchantUserQueryDTO merchantUserQueryDTO){
        log.info("商户查询用户会员,param:{}", merchantUserQueryDTO);
        Page<MerchantUserListDTO> pageInfo =  merchantUserService.queryUserListByMerchant(merchantUserQueryDTO);
        return RespBody.ok(pageInfo);
    }

    /**
     * 商户查询会员 总数
     * @param merchantUserQueryDTO
     * @return
     */
    @PostMapping("/countUserListByMerchant")
    public RespBody<Long> countUserListByMerchant(@RequestBody @Validated MerchantUserQueryDTO merchantUserQueryDTO){
        log.info("商户查询用户会员总数,param:{}", merchantUserQueryDTO);
        if (merchantUserQueryDTO.getMerchantId() == null) {
            return RespBody.fail(GlobalErrorCode.BAD_REQUEST);
        }
        Long l =  merchantUserService.countUserListByMerchant(merchantUserQueryDTO);
        return RespBody.ok(l);
    }

    /**
     * 派发权益
     * @return
     */
    @PostMapping(value = "/payout/benefit")
    public RespBody<Void> payoutBenefit(@RequestBody PayoutBenefitDTO payoutBenefitDTO){
        log.info("商家派发权益，isAll:{}",payoutBenefitDTO.getIsAllUser());
        Optional.ofNullable(payoutBenefitDTO.getMerchantUserIdList()).ifPresent(r ->{
            log.info("派发人数:{}",r.size());
        });
        Optional.ofNullable(payoutBenefitDTO.getGroupIdList()).ifPresent(r ->{
            log.info("场地个数:{}",r.size());
        });
        Optional.ofNullable(payoutBenefitDTO.getEquipmentTypeIdList()).ifPresent(r ->{
            log.info("品类个数:{}",r.size());
        });
        merchantUserService.payoutWelfare(payoutBenefitDTO);
        return RespBody.ok();
    }

    /**
     * 商户用户 账户信息查询(废弃接口)
     * @param merchantUserId
     * @param merchantId
     * @return
     */
    @Deprecated
    @GetMapping(value = "/getIndexAccountInfo")
    public RespBody getIndexAccountInfo(@RequestParam("merchantUserId") Long merchantUserId,@RequestParam("merchantId") Long merchantId){
        log.info("查询首页的 商户用户账户信息,merchantUserId:{},merchantId:{}",merchantUserId,merchantId);
        return RespBody.fail(DEPRECATED_METHOD_EXCEPTION);
    }

    /**
     * 根据条件获取商户用户信息
     * @param queryDTO
     * @return
     */
    @PostMapping("/getMerchantUserInfoByCondition")
    public RespBody<MerchantUserDTO> getMerchantUserByCondition(@RequestBody @Validated MerchantIntegralUserQueryDTO queryDTO) {
        if (log.isDebugEnabled()) {
            log.debug("根据条件获取商户用户信息,{}", queryDTO);
        }
        return RespBody.ok(merchantUserService.getMerchantUserByCondition(queryDTO));
    }


    /**
     * 根据id批量获取商户用户信息
     * @return
     */
    @PostMapping("/findByUserIds")
    public RespBody<List<MerchantUserDTO>> findByUserIds(@RequestBody MemberBatchQueryRequestDTO request) {
        List<MerchantUser> list = merchantUserService.list(
                new QueryWrapper<MerchantUser>().lambda()
                        .eq(MerchantUser::getMerchantId, request.getMerchantId())
                        .in(MerchantUser::getUserId, request.getUserIds()));
        List<MerchantUserDTO> dtoList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(list)){
            dtoList = CommonConverterTools.convert(MerchantUserDTO.class, list);
        }
        return RespBody.ok(dtoList);
    }
}
