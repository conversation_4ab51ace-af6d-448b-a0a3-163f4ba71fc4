package com.lyy.user.interfaces.controller.member;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.member.infrastructure.MemberErrorCode;
import com.lyy.user.account.infrastructure.member.dto.MemberLevelDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberTouchRuleDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberUserInfoDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberUserLevelDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberUserPageDTO;
import com.lyy.user.account.infrastructure.member.dto.UpdateUserMemberLevelDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.application.member.IMemberLevelService;
import com.lyy.user.application.member.IMemberService;
import com.lyy.user.domain.member.entity.Member;
import com.lyy.user.domain.member.entity.MemberLevel;
import com.lyy.user.interfaces.assembler.AssemblerFactory;
import com.lyy.user.interfaces.assembler.CommAssembler;
import com.lyy.user.interfaces.assembler.MemberUserInfoAssembler;
import com.lyy.user.interfaces.assembler.MemberUserLevelAssembler;
import io.swagger.annotations.Api;
import java.util.Optional;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 会员操作
 * <AUTHOR>
 * @className: MemberController
 * @date 2021/4/22
 */
@Api(tags = "会员管理")
@RestController
@RequestMapping("rest/member/member")
@Slf4j
public class MemberController {
    @Autowired
    private IMemberService memberService;
    @Autowired
    private IMemberLevelService memberLevelService;

    @Autowired
    private MemberUserInfoAssembler memberUserInfoAssembler;
    @Autowired
    private MemberUserLevelAssembler memberUserLevelAssembler;
    private AssemblerFactory factory = new AssemblerFactory();
    private CommAssembler commAssembler = new CommAssembler();

    /**
     * 用户触发会员规则
     * @param memberTouchRuleDTO
     * @return
     */
    @PostMapping("/updateMemberOfRule")
    public RespBody<Integer> updateMemberOfRule(@Valid @RequestBody MemberTouchRuleDTO memberTouchRuleDTO) {
        if (log.isDebugEnabled()) {
            log.debug("用户触发会员规则,{}", memberTouchRuleDTO);
        }
        int result = memberService.updateMemberOfRule(memberTouchRuleDTO);
        return RespBody.ok(result);
    }

    /**
     * 手动触发更新用户的 会员等级
     *
     * @param updateUserMemberLevelDTO
     * @return
     */
    @PostMapping("/updateUserMemberLevel")
    public RespBody<Boolean> updateUserMemberLevel(@Valid @RequestBody UpdateUserMemberLevelDTO updateUserMemberLevelDTO) {
        Boolean b = memberService.updateUserMemberLevel(updateUserMemberLevelDTO);
        return RespBody.ok(b);
    }

    /**
     * 会员触发规则回退
     * @param memberTouchRuleDTO
     * @return
     */
    @PostMapping("/removeMemberOfRule")
    public RespBody<Boolean> removeMemberOfRule(@Valid @RequestBody MemberTouchRuleDTO memberTouchRuleDTO){
        boolean result = memberService.removeMemberOfRule(memberTouchRuleDTO);
        return RespBody.ok(result);
    }

    /**
     * 分页获取会员信息
     *
     * @param current
     * @param size
     * @param merchantId
     * @param userId
     * @return
     */
    @GetMapping("/findPageMemberUser")
    public RespBody<Page<MemberUserPageDTO>> findPageMemberUser(@RequestParam("current") Integer current, @RequestParam("size") Integer size,
                                                                @RequestParam("merchantId") Long merchantId, @RequestParam("userId") Long userId) {
        Page page1 = new Page(current, size);
        page1.setSearchCount(false);
        Page<MemberUserPageDTO> page = memberService.findPageMemberUser(page1, merchantId, userId);
        //处理下个等级的
        page.getRecords().parallelStream()
                .forEach(memberUserPageDTO -> updateNextLevel(memberUserPageDTO));
        return RespBody.ok(page);
    }
    /**
     * 获取会员的详细信息
     * @param merchantId 商户id
     * @param memberId  会员id
     * @return
     */
    @GetMapping("/getMemberUserInfo")
    public RespBody<MemberUserInfoDTO> getMemberUserInfo(@RequestParam("merchantId") Long merchantId,@RequestParam("memberId") Long memberId){
        Member member = memberService.getById(merchantId,memberId);
        if(member == null){
            return RespBody.fail(MemberErrorCode.MEMBER_NOT_EXISTS);
        }
        if(member.getDel()){
            return RespBody.fail(MemberErrorCode.MEMBER_DELETE);
        }
        MemberUserInfoDTO memberUserInfoDTO = factory.convert(memberUserInfoAssembler,member,MemberUserInfoDTO.class);
        return RespBody.ok(memberUserInfoDTO);
    }

    /**
     * 获取小场地用户会员信息
     * @param merchantId
     * @param userId
     * @return
     */
    @GetMapping("/getSmallVenueUserMemberInfo")
    public RespBody<MemberUserInfoDTO> getSmallVenueUserMemberInfo(@RequestParam("merchantId") Long merchantId,@RequestParam("userId") Long userId){
        log.debug("小场地用户会员信息,merchantId:{},userId:{}",merchantId,userId);
        MemberUserInfoDTO memberUserInfoDTO = memberService.getSmallVenueUserMemberInfo(merchantId,userId);
        return RespBody.ok(memberUserInfoDTO);
    }

    /**
     * 获取会员的详细信息
     * @param merchantId 商户id
     * @param memberId  会员id
     * @return
     */
    @GetMapping("/getMemberUserLevel")
    public RespBody<MemberUserLevelDTO> getMemberUserLevel(@RequestParam("merchantId") Long merchantId,@RequestParam("memberId") Long memberId){
        Member member = memberService.getById(merchantId,memberId);
        if(member == null){
            return RespBody.fail(MemberErrorCode.MEMBER_NOT_EXISTS);
        }
        if(member.getDel()){
            return RespBody.fail(MemberErrorCode.MEMBER_DELETE);
        }
        MemberUserLevelDTO memberUserLevelDTO= factory.convert(memberUserLevelAssembler,member, MemberUserLevelDTO.class);
        return RespBody.ok(memberUserLevelDTO);
    }

    /**
     * 更新下个会员等级
     * @param memberUserPageDTO
     */
    private void updateNextLevel(MemberUserPageDTO memberUserPageDTO) {
        MemberLevel memberLevel = memberLevelService.findNextLevel(memberUserPageDTO.getMerchantId(), memberUserPageDTO.getMemberLevelId());
        Optional.ofNullable(memberLevel)
                .ifPresent(ml -> {
                    MemberLevelDTO memberLevelDTO = factory.convert(commAssembler, ml, MemberLevelDTO.class);
                    memberUserPageDTO.setNextMemberLevel(memberLevelDTO);
                });
    }

    /**
     * 初始化小场地用户会员等级
     * @return
     */
    @GetMapping("/initSmallVenueUserMemberLevel")
    public RespBody initSmallVenueUserMemberLevel(@RequestParam("merchantId") Long merchantId,@RequestParam("userId") Long userId){
        log.debug("小场地用户会员初始化,userId:{},merchantId:{}",userId,merchantId);
        Boolean resultFlag =  memberService.initSmallVenueUserMemberLevel(merchantId,userId);
        return RespBody.ok(resultFlag);
    }

}
