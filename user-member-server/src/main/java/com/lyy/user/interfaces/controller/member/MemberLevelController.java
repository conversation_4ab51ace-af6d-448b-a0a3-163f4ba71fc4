package com.lyy.user.interfaces.controller.member;

import com.lyy.user.account.infrastructure.member.dto.MemberLevelDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberLevelSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.SmallVenueMemberLevelSaveDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.application.member.IMemberLevelService;
import io.swagger.annotations.Api;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 会员等级管理
 *
 * <AUTHOR>
 * @create 2022/1/11 15:38
 */
@Api(tags = "会员等级管理")
@RestController
@RequestMapping("/rest/member/level")
@Slf4j
public class MemberLevelController {

    @Resource
    private IMemberLevelService memberLevelService;

    /**
     * 会员等级列表
     *
     * @param memberGroupId 会员组id ,不传查商户默认的
     * @param merchantId    商户ID
     * @return
     */
    @GetMapping("/list")
    public RespBody<List<MemberLevelDTO>> list(@RequestParam(value = "memberGroupId", defaultValue = "-1") Long memberGroupId,
                                               @RequestParam("merchantId") Long merchantId) {
        log.debug("会员等级列表,merchantId:{},memberGroupId:{}", merchantId, memberGroupId);
        List<MemberLevelDTO> list = memberLevelService.findSmallVenueMemberLevelByMemberGroup(merchantId, memberGroupId, true);
        return RespBody.ok(list);
    }

    /**
     * 保存会员等级，新增或更新数据
     *
     * @param smallVenueMemberLevelSaveDTO
     * @return
     */
    @PostMapping("/saveOrUpdate")
    public RespBody saveOrUpdate(@RequestBody SmallVenueMemberLevelSaveDTO smallVenueMemberLevelSaveDTO) {
        log.debug("MemberLevelController.saveOrUpdate,param:{}", smallVenueMemberLevelSaveDTO);
        memberLevelService.saveOrUpdateSmallVenueMemberLevel(smallVenueMemberLevelSaveDTO);
        return RespBody.ok();
    }


    /**
     * 获取会员等级详情
     *
     * @param memberLevelId
     * @param merchantId
     * @return
     */
    @GetMapping("/detail")
    public RespBody<MemberLevelSaveDTO> detail(@RequestParam(value = "memberLevelId") Long memberLevelId, @RequestParam("merchantId") Long merchantId) {
        log.debug("会员等级详情,merchantId:{},id:{}", merchantId, memberLevelId);
        MemberLevelSaveDTO memberLevelSaveDTO = memberLevelService.getInfoById(merchantId, memberLevelId);
        return RespBody.ok(memberLevelSaveDTO);
    }

}
