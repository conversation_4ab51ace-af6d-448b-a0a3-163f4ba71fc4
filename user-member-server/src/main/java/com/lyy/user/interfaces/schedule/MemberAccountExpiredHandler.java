package com.lyy.user.interfaces.schedule;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lyy.user.account.infrastructure.constant.AccountStatusEnum;
import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.infrastructure.repository.account.SmallVenueAccountRepository;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.util.ShardingUtil;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/1/20
 */
@Slf4j
@JobHandler(value = "memberAccountExpiredHandler")
@Component
public class MemberAccountExpiredHandler extends IJobHandler {

    @Resource(name = "smallVenueAccountRepository")
    private SmallVenueAccountRepository accountRepository;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        int currentIndex = ShardingUtil.getShardingVo().getIndex();
        int total = ShardingUtil.getShardingVo().getTotal();
        log.info("[小场地会员卡过期处理], total={}, currentIndex={}", total, currentIndex);
        for (long currentShard = 0; currentShard < 128; currentShard++) {
            log.debug("currentShard % total = {}", currentShard % total);
            if (currentShard % total == currentIndex) {
                doExpired(currentShard);
            }
        }
        return SUCCESS;
    }

    /**
     * 虚拟商户 ID，用于分表取模
     */
    void doExpired(long dummyMerchantId) {
        log.info("[小场地会员卡过期处理] 处理分表：[{}] 的数据", dummyMerchantId);
        int pageIndex = 1;
        int pageSize = 100;
        IPage<Account> page;
        do {
            page = accountRepository.pageExpiredAccount(dummyMerchantId, pageIndex, pageSize);
            log.info("[小场地会员卡过期处理]--分页查询过期账户 index: {} page: {} ", page.getCurrent(), page.getTotal());
            if (page.getRecords().isEmpty()) {
                return;
            }
            page.getRecords().forEach(account -> {
                accountRepository.updateAccountStatus(account.getMerchantId(), account.getId(), AccountStatusEnum.OVERDUE);
            });
        } while (true);

    }

}
