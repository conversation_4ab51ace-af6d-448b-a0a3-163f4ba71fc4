package com.lyy.user.interfaces.schedule;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import com.lyy.user.domain.account.entity.AccountBenefit;
import com.lyy.user.domain.account.repository.AccountBenefitMapper;
import com.lyy.user.domain.benefit.entity.Benefit;
import com.lyy.user.domain.benefit.entity.BenefitTmp;
import com.lyy.user.domain.benefit.repository.BenefitMapper;
import com.lyy.user.domain.benefit.repository.BenefitTmpMapper;
import com.lyy.user.interfaces.assembler.BenefitAssembler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.util.ShardingUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 过期um_benefit记录清除
 */
@Slf4j
@JobHandler(value = "expireBenefitClear")
@Component
public class ExpireBenefitClearHandler extends IJobHandler {

    @Resource
    private BenefitMapper benefitMapper;

    @Resource
    private BenefitTmpMapper benefitTmpMapper;

    @Resource
    private AccountBenefitMapper accountBenefitMapper;

    @Resource
    private BenefitAssembler benefitAssembler;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        if (StringUtils.isBlank(param)) {
            return FAIL;
        }
        String[] params = param.split(";");
        int clearNum;
        int oneClearCount;
        List<Long> merchantIds = new ArrayList<>();
        //清除次数, 默认1次
        try {
            clearNum = params.length > 1 ? Integer.parseInt(params[1]) : 1;
            oneClearCount = Integer.parseInt(params[0]);
            if (params.length > 2) {
                String merchants = params[2];
                merchantIds = Arrays.stream(merchants.split(",")).map(Long::parseLong).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("定时任务参数解析异常，", e);
            return FAIL;
        }
        int currentIndex = ShardingUtil.getShardingVo().getIndex();
        int total = ShardingUtil.getShardingVo().getTotal();
        log.info("[过期um_benefit记录清除] 开始, total={}, currentIndex={}, clearNum={}, oneClearCount={}, merchants={}", total, currentIndex,
                clearNum, oneClearCount, merchantIds);
        if (currentIndex >= 4) {
            log.debug("本分片超过处理分片上限，不执行任务");
            return SUCCESS;
        }
        try {
            expireBenefitClear((long) currentIndex, clearNum, oneClearCount, merchantIds);
        } catch (Exception e) {
            log.error("[过期um_benefit记录清除]定时任务执行异常，", e);
            return FAIL;
        }
        return SUCCESS;
    }

    public void expireBenefitClear(long dummyMerchantId, int clearNum, int oneClearCount, List<Long> merchantIds) {
        long shardMin = dummyMerchantId * 32;
        long shardMax = shardMin + 32 - 1;
        List<Long> shardMerchantIds = merchantIds.stream()
                .filter(merchantId -> merchantId % 128 >= shardMin && merchantId % 128 <= shardMax).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(merchantIds) && CollectionUtils.isEmpty(shardMerchantIds)) {
            log.info("[过期um_benefit记录清除] 分库 [{}] 无此分片商户需处理", dummyMerchantId);
            return;
        }
        log.info("[过期um_benefit记录清除] 处理分库：[{}] 的数据, 处理商户：{}", dummyMerchantId, shardMerchantIds);
        if (!CollectionUtils.isEmpty(shardMerchantIds)) {
            merchantExpireBenefitClear(clearNum, oneClearCount, shardMerchantIds);
            return;
        }
        Long minId = 0L;
        String date = DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.yesterday()));
        for (int i = 1; i <= clearNum; i++) {
            List<Benefit> benefits = benefitMapper.findShardExpireNoScopeBenefit(shardMin, oneClearCount, date, minId);
            if (CollectionUtils.isEmpty(benefits)) {
                return;
            }
            minId = benefits.get(benefits.size() - 1).getId();
            Map<Long, List<Benefit>> groups = benefits.stream()
                    .filter(benefit -> merchantShardMatch(dummyMerchantId, benefit.getMerchantId()))
                    .collect(Collectors.groupingBy(benefit -> benefit.getMerchantId() % 128));
            for (Entry<Long, List<Benefit>> entry : groups.entrySet()) {
                List<Benefit> shardBenefits = entry.getValue();
                List<Long> benefitIds = shardBenefits.stream().map(Benefit::getId).collect(Collectors.toList());
                Long shard = entry.getKey();
                LambdaQueryWrapper<AccountBenefit> queryWrapper = getHasBalanceAccountBenefitQueryWrapper(shard,
                        benefitIds);
                List<AccountBenefit> accountBenefits = accountBenefitMapper.selectList(queryWrapper);
                List<Long> noSupportClearIds = accountBenefits.stream().map(AccountBenefit::getBenefitId).collect(Collectors.toList());
                List<Long> clearIds = benefitIds.stream().filter(id -> !noSupportClearIds.contains(id)).collect(Collectors.toList());
                int deleteRows = 0;
                if (!CollectionUtils.isEmpty(clearIds)) {
                    List<BenefitTmp> benefitTmpList = shardBenefits.stream().filter(benefit -> clearIds.contains(benefit.getId()))
                            .map(benefitAssembler::toBenefitTmp).collect(Collectors.toList());
                    benefitTmpMapper.insertBatch(benefitTmpList);
                    deleteRows = benefitMapper.delete(getDeleteBenefitWrapper(shardMin, clearIds));
                }
                log.info("[过期um_benefit记录清除],第[{}]次清除, [{}]分表处理条数：[{}], 清除ids:{}, 有余额无法清除ids:{}", i, shard, deleteRows,
                        clearIds, noSupportClearIds);
            }
        }
    }

    private boolean merchantShardMatch(Long dummyMerchantId, Long merchantId) {
        long shardMin = dummyMerchantId * 32;
        long shardMax = shardMin + 32 - 1;
        return merchantId % 128 >= shardMin && merchantId % 128 <= shardMax;
    }

    private void merchantExpireBenefitClear(int clearNum, int oneClearCount, List<Long> merchantIds) {
        log.info("[过期um_benefit记录清除] 处理商户：{}", merchantIds);
        Long minId = 0L;
        String date = DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.yesterday()));
        for (Long merchantId : merchantIds) {
            for (int i = 1; i <= clearNum; i++) {
                List<Benefit> benefits = benefitMapper.findMerchantExpireNoScopeBenefit(merchantId, oneClearCount, date, minId);
                if (CollectionUtils.isEmpty(benefits)) {
                    break;
                }
                minId = benefits.get(benefits.size() - 1).getId();
                List<Long> benefitIds = benefits.stream().map(Benefit::getId).collect(Collectors.toList());
                LambdaQueryWrapper<AccountBenefit> queryWrapper = getMerchantHasBalanceAccountBenefitQueryWrapper(merchantId, benefitIds);
                List<AccountBenefit> accountBenefits = accountBenefitMapper.selectList(queryWrapper);
                List<Long> noSupportClearIds = accountBenefits.stream().map(AccountBenefit::getBenefitId).collect(Collectors.toList());
                List<Long> clearIds = benefitIds.stream().filter(id -> !noSupportClearIds.contains(id)).collect(Collectors.toList());
                int deleteRows = 0;
                if (!CollectionUtils.isEmpty(clearIds)) {
                    List<BenefitTmp> benefitTmpList = benefits.stream().filter(benefit -> clearIds.contains(benefit.getId()))
                            .map(benefitAssembler::toBenefitTmp).collect(Collectors.toList());
                    benefitTmpMapper.insertBatch(benefitTmpList);
                    deleteRows = benefitMapper.delete(getMerchantDeleteBenefitWrapper(merchantId, clearIds));
                }
                log.info("[过期um_benefit记录清除],商户：[{}], 第[{}]次清除条数：[{}], 清除ids:{}, 有余额无法清除ids:{}", merchantId,
                        i, deleteRows, clearIds, noSupportClearIds);
            }
        }
    }

    private LambdaQueryWrapper<Benefit> getDeleteBenefitWrapper(Long merchantId, List<Long> benefitIds) {
        return Wrappers.<Benefit>lambdaQuery()
                .in(Benefit::getId, benefitIds)
                .and(wrapper -> wrapper.eq(Benefit::getMerchantId, merchantId).or().exists("select 1"));
    }

    private LambdaQueryWrapper<Benefit> getMerchantDeleteBenefitWrapper(Long merchantId, List<Long> benefitIds) {
        return Wrappers.<Benefit>lambdaQuery()
                .in(Benefit::getId, benefitIds)
                .eq(Benefit::getMerchantId, merchantId);
    }


    private LambdaQueryWrapper<AccountBenefit> getHasBalanceAccountBenefitQueryWrapper(Long merchantId, List<Long> benefitIds) {
        return Wrappers.<AccountBenefit>lambdaQuery()
                .select(AccountBenefit::getMerchantId, AccountBenefit::getBenefitId, AccountBenefit::getBalance)
//                .eq(AccountBenefit::getExpiryDateCategory, ExpiryDateCategoryEnum.TIME_INTERVAL.getValue())
                .in(AccountBenefit::getBenefitId, benefitIds)
                .gt(AccountBenefit::getBalance, BigDecimal.ZERO)
                .and(wrapper -> wrapper.eq(AccountBenefit::getMerchantId, merchantId).or().exists("select 1"));
    }


    private LambdaQueryWrapper<AccountBenefit> getMerchantHasBalanceAccountBenefitQueryWrapper(Long merchantId, List<Long> benefitIds) {
        return Wrappers.<AccountBenefit>lambdaQuery()
                .select(AccountBenefit::getMerchantId, AccountBenefit::getBenefitId, AccountBenefit::getBalance)
//                .eq(AccountBenefit::getExpiryDateCategory, ExpiryDateCategoryEnum.TIME_INTERVAL.getValue())
                .in(AccountBenefit::getBenefitId, benefitIds)
                .gt(AccountBenefit::getBalance, BigDecimal.ZERO)
                .eq(AccountBenefit::getMerchantId, merchantId);
    }

}
