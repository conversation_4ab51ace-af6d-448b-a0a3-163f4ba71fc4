package com.lyy.user.interfaces.controller.user;

import com.lyy.error.member.infrastructure.UserErrorCode;
import com.lyy.user.account.infrastructure.common.DataList;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserCheckPasswordDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserUpdatePasswordDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserUpdateTelephoneDTO;
import com.lyy.user.account.infrastructure.user.dto.MobileTerminalUserCancellationDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueMobileUserListSelectDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueUserDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueUserListSelectDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueUserMobileVO;
import com.lyy.user.account.infrastructure.user.dto.UpdateMerchantUserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.UserAppDTO;
import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserAndLevelInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserInfoByKeywordDTO;
import com.lyy.user.application.user.IMerchantUserService;
import com.lyy.user.application.user.IUserService;
import com.lyy.user.domain.user.service.UserInitFactory;
import com.lyy.user.infrastructure.execption.BusinessException;
import io.swagger.annotations.Api;
import java.util.List;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 *  用户信息接口
 *
 * @author: pengkun
 * @date: 2021/04/01
 **/
@Api(tags = "用户管理")
@Slf4j
@RestController()
@RequestMapping("/user")
public class UserController {

    @Autowired
    private IUserService userService;

    @Autowired
    private UserInitFactory userInitFactory;

    @Autowired
    private IMerchantUserService merchantUserService;

    /**
     * 根据平台用户id和商户id获取用户信息
     * @param userId    平台用户Id
     * @param merchantId    商户Id
     * @return
     */
    @GetMapping("/getUserInfoByUserIdAndMerchantId")
    public RespBody<UserInfoDTO> getUserInfoByUserIdAndMerchantId(@RequestParam("userId") Long userId,
                                                                  @RequestParam(value = "merchantId", required = false) Long merchantId) {
        log.debug("根据平台用户id和商户id获取用户信息,userId:{},merchantId:{}", userId, merchantId);
        return RespBody.ok(userService.getUserInfoByUserIdAndMerchantId(userId, merchantId,Boolean.FALSE));
    }

    /**
     * 根据平台用户id和商户id获取用户信息[内含异步初始化指定商户下的 商户用户统计数据]
     * @param userId    平台用户Id
     * @param merchantId    商户Id
     * @return
     */
    @GetMapping("/getUserInfoByUserIdAndMerchantIdAsyn")
    public RespBody<UserInfoDTO> getUserInfoByUserIdAndMerchantIdAsyn(@RequestParam("userId") Long userId,
                                                                  @RequestParam(value = "merchantId", required = false) Long merchantId) {
        log.debug("根据平台用户id和商户id获取用户信息,userId:{},merchantId:{}", userId, merchantId);
        return RespBody.ok(userService.getUserInfoByUserIdAndMerchantId(userId, merchantId,Boolean.TRUE));
    }

    /**
     * 根据平台用户id,商户用户id,商户id获取用户信息
     * @param userId    平台用户Id
     * @param merchantUserId    商户用户Id
     * @param merchantId    商户Id
     * @return
     */
    @GetMapping("/getUserInfoByUserIdAndMerchantUserId")
    public RespBody<UserInfoDTO> getUserInfoByUserIdAndMerchantUserId(@RequestParam("userId") Long userId,
                                                                      @RequestParam(value = "merchantUserId", required = false) Long merchantUserId,
                                                                      @RequestParam(value = "merchantId", required = false) Long merchantId) {
        log.debug("根据平台用户id,商户用户id,商户id获取用户信息,userId:{},merchantUserId:{},merchantId:{}", userId, merchantUserId, merchantId);
        return RespBody.ok(userService.getUserInfoByUserIdAndMerchantUserId(userId,merchantUserId,merchantId));
    }

    /**
     * 根据openId或unionId获取用户信息
     * @param openId    openId
     * @param unionId   unionId
     * @param merchantId 商户id
     * @return
     */
    @GetMapping("/getUserInfoByOpenIdOrUnionId")
    public RespBody<UserInfoDTO> getUserInfoByOpenIdOrUnionId(@RequestParam(value = "openId", required = false) String openId,
                                                              @RequestParam(value = "unionId", required = false) String unionId,
                                                              @RequestParam(value = "merchantId", required = false) Long merchantId){
        log.debug("根据openId或unionId获取用户信息,openId:{},unionId:{}", openId, unionId);
        if(StringUtils.isBlank(openId) && StringUtils.isBlank(unionId) && merchantId == null){
            throw new BusinessException(UserErrorCode.USER_QUERY_PARAM_ERROR.getCode(),UserErrorCode.USER_QUERY_PARAM_ERROR.getMessage());
        }
        return RespBody.ok(userService.getUserInfoByOpenIdOrUnionId(openId, unionId,merchantId));
    }

    /**
     * 根据手机号和商户id查询可切换的用户列表信息
     * @param telephone 手机号
     * @param merchantId    商户id
     * @return
     */
    @GetMapping("/listByTelephoneAndMerchantId")
    public RespBody<List<UserInfoDTO>> listByTelephoneAndMerchantId(@RequestParam("telephone") String telephone,
                                                                    @RequestParam("merchantId") Long merchantId) {
        log.debug("根据手机号和商户id查询可切换的用户列表信息,telephone:{},merchantId:{}", telephone, merchantId);
        return RespBody.ok(userService.listByTelephoneAndMerchantId(telephone, merchantId));
    }

    /**
     * 更新用户绑定的手机号码
     * @param dto
     * @return
     */
    @PostMapping("/updateTelephone")
    @Deprecated
    public RespBody<Boolean> updateTelephone(@RequestBody MerchantUserDTO dto){
        log.debug("更新绑定的手机号码,dto:{}",dto);
        if(dto.getUserId() == null && StringUtils.isBlank(dto.getTelephone())){
            throw new BusinessException(UserErrorCode.PLATFORM_USER_UPDATE_PARAM_ERROR.getCode(),UserErrorCode.PLATFORM_USER_UPDATE_PARAM_ERROR.getMessage());
        }
        return RespBody.ok(userService.updateTelephone(dto));
    }

    /**
     * 用户初始化
     * @param userCreateDTO
     * @return
     */
    @PostMapping("/initUserInfo")
    public RespBody<UserInfoDTO> initUserInfo(@RequestBody UserCreateDTO userCreateDTO){
        log.debug("用户初始化,dto:{}",userCreateDTO);
        UserInfoDTO userInfoDTO = userInitFactory.getHandler(userCreateDTO.getUserSourceEnum()).handle(userCreateDTO);
        if(userInfoDTO != null && userInfoDTO.getLyyUserId() != null){
            return RespBody.ok(userInfoDTO);
        }else {
            log.error("用户初始化失败");
            return RespBody.fail(UserErrorCode.PLATFORM_USER_SAVE_ERROR);
        }
    }

    /**
     * 更新平台用户信息
     * @param userCreateDTO
     * @return
     */
    @PostMapping("/updatePlatformUserInfo")
    public RespBody<Boolean> updatePlatformUserInfo(@RequestBody UserCreateDTO userCreateDTO){
        log.info("用户更新,dto:{}",userCreateDTO);
        if(userCreateDTO.getLyyUserId() == null){
            throw new BusinessException(UserErrorCode.PLATFORM_USER_UPDATE_PARAM_ERROR.getCode(),UserErrorCode.PLATFORM_USER_UPDATE_PARAM_ERROR.getMessage());
        }
        return RespBody.ok(userService.updatePlatformUserInfo(userCreateDTO));
    }

    /**
     * 注销用户
     * @param userId 平台用户id
     * @param merchantUserId    平台用户id
     * @param merchantId    商户id
     * @param operationUserId   操作人id
     * @return
     */
    @GetMapping("/deleteUserInfo")
    public RespBody<Boolean> deleteUserInfo(@RequestParam(value = "userId",required = false) Long userId,
                                            @RequestParam(value = "merchantUserId")Long merchantUserId,
                                            @RequestParam(value = "merchantId")Long merchantId,
                                            @RequestParam(value = "operationUserId",required = false)Long operationUserId){
        log.debug("注销用户,userId:{},merchantUserId:{},merchantId:{},operationUserId:{}",userId,merchantUserId,merchantId,operationUserId);
        return RespBody.ok(userService.deleteUserInfo(userId,merchantUserId, merchantId, operationUserId));
    }

    /**
     * 创建用户APP信息
     * @param userAppDTO
     * @return
     */
    @PostMapping("/createUserApp")
    public RespBody<Boolean> createUserApp(@RequestBody UserAppDTO userAppDTO){
        log.debug("创建用户APP信息,userAppDTO:{}",userAppDTO);
        return RespBody.ok(userService.createUserApp(userAppDTO));
    }

    /**
     * 更新用户信息（平台用户+商户用户）
     * @param dto
     * @return
     */
    @PostMapping("/updateUserInfo")
    public RespBody<Boolean> updateUserInfo(@RequestBody MerchantUserDTO dto){
        log.debug("更新用户信息,dto:{}",dto);
        if(dto.getId() == null && dto.getUserId() == null){
            throw new BusinessException(UserErrorCode.PLATFORM_USER_UPDATE_PARAM_ERROR.getCode(),UserErrorCode.PLATFORM_USER_UPDATE_PARAM_ERROR.getMessage());
        }
        return RespBody.ok(userService.updateUserInfo(dto));
    }

    /**
     * 根据openId和appId获取平台用户信息
     * @param openId
     * @param appId
     * @return
     */
    @GetMapping("/getPlatformUserInfoByOpenIdAndAppId")
    public RespBody<UserInfoDTO> getPlatformUserInfoByOpenIdAndAppId(@RequestParam("openId")String openId,
                                                                     @RequestParam("appId")String appId){
        log.debug("根据openId和appId获取平台用户信息,openId:{},appId:{}",openId,appId);
        return RespBody.ok(userService.getPlatformUserInfoByOpenIdAndAppId(openId, appId));
    }


    /**
     * 根据平台用户id获取平台用户信息（lyy_user）
     *
     * @param userId 平台用户Id
     * @return
     */
    @GetMapping("/getPlatformUserInfoByUserId")
    public RespBody<UserInfoDTO> getPlatformUserInfoByUserId(@RequestParam("userId") Long userId) {
        log.debug("根据平台用户id获取平台用户信息,userId:{}", userId);
        return RespBody.ok(userService.getPlatformUserInfoByUserId(userId));
    }


    /**
     * 根据会员卡/手机号码/平台用户id获取会员信息
     *
     * @param merchantId
     * @param keyWords
     * @return
     */
    @GetMapping("/smallVenue/searchByKeywords")
    public RespBody<MerchantUserInfoByKeywordDTO> searchByKeywords(@RequestParam("merchantId") Long merchantId, @RequestParam("keyWords") String keyWords) {
        log.info("根据会员卡/手机号码/平台用户id获取会员信息,merchantId:{},keyWords:{}", merchantId, keyWords);
        return RespBody.ok(merchantUserService.searchByKeywords(merchantId, keyWords, null));
    }

    /**
     * 根据会员卡/手机号码/平台用户id获取会员信息. 可根据 keyWordsType 限制 keyWords 是会员卡/平台用户id
     *
     * @param merchantId
     * @param keyWords
     * @param keyWordsType
     * @return
     */
    @GetMapping("/smallVenue/searchByKeywordsAndType")
    public RespBody<MerchantUserInfoByKeywordDTO> searchByKeywordsAndType(@RequestParam("merchantId") Long merchantId, @RequestParam("keyWords") String keyWords,
                                                                   @RequestParam(value = "keyWordsType") Integer keyWordsType) {
        log.info("根据会员卡/手机号码/平台用户id获取会员信息,merchantId:{},keyWords:{},keyWordsType:{}", merchantId, keyWords, keyWordsType);
        return RespBody.ok(merchantUserService.searchByKeywords(merchantId, keyWords, keyWordsType));
    }

    /**
     * 根据手机号码或平台用户id获取会员信息. 可根据 keyWordsType 限制 keyWords 是手机号码/平台用户id
     *
     * @param merchantId
     * @param keyWords
     * @param keyWordsType
     * @return
     */
    @GetMapping("/smallVenue/searchBaseInfoKeywordsAndType")
    public RespBody<MerchantUserAndLevelInfoDTO> searchBaseInfoKeywordsAndType(@RequestParam("merchantId") Long merchantId,
            @RequestParam("keyWords") String keyWords,
            @RequestParam(value = "keyWordsType") Integer keyWordsType) {
        log.info("根据手机号码或平台用户id获取会员信息,merchantId:{},keyWords:{},keyWordsType:{}", merchantId, keyWords, keyWordsType);
        return RespBody.ok(merchantUserService.searchBaseInfoKeywordsAndType(merchantId, keyWords, keyWordsType));
    }

    /**
     * 修改会员密码
     * @param merchantUserUpdatePasswordDTO
     * @return
     */
    @PostMapping("/smallVenue/updateUserPassword")
    public RespBody<Boolean> updateUserPassword(@RequestBody @Valid MerchantUserUpdatePasswordDTO merchantUserUpdatePasswordDTO) {
        log.info("修改会员密码,merchantUserUpdatePasswordDTO:{}", merchantUserUpdatePasswordDTO);
        return RespBody.ok(merchantUserService.updateMerchantUserPassword(merchantUserUpdatePasswordDTO));
    }

    /**
     * 校验会员密码
     * @param merchantUserCheckPasswordDTO
     * @return
     */
    @PostMapping("/smallVenue/checkUserPassword")
    public RespBody<Boolean> checkUserPassword(@RequestBody @Valid MerchantUserCheckPasswordDTO merchantUserCheckPasswordDTO) {
        log.info("校验会员密码,merchantUserCheckPasswordDTO:{}", merchantUserCheckPasswordDTO);
        return RespBody.ok(merchantUserService.checkMerchantUserPassword(merchantUserCheckPasswordDTO));
    }

    /**
     * 小场地会员列表
     *
     * @param smallVenueUserListSelectDTO
     * @return
     */
    @PostMapping("/smallVenue/list")
    public RespBody<DataList<SmallVenueUserDTO>> selectSmallVenueUserList(@RequestBody @Valid SmallVenueUserListSelectDTO smallVenueUserListSelectDTO) {
        log.info("小场地会员列表,smallVenueUserListSelectDTO:{}", smallVenueUserListSelectDTO);
        return RespBody.ok(merchantUserService.selectSmallVenueUserList(smallVenueUserListSelectDTO));
    }

    /**
     * 小场地会员列表-B端
     *
     * @param smallVenueMobileUserListSelectDTO
     * @return
     */
    @PostMapping("/smallVenue/mobile/list")
    public RespBody<DataList<SmallVenueUserMobileVO>> smallVenueMobileUserList(@RequestBody @Validated SmallVenueMobileUserListSelectDTO smallVenueMobileUserListSelectDTO) {
        log.info("小场地-B端-会员列表,smallVenueMobileUserListSelectDTO:{}", smallVenueMobileUserListSelectDTO);
        return RespBody.ok(merchantUserService.smallVenueMobileUserList(smallVenueMobileUserListSelectDTO));
    }


    /**
     * 更新会员信息
     *
     * @param updateMerchantUserInfoDTO
     * @return
     */
    @PostMapping("/smallVenue/updateMerchantUserInfo")
    public RespBody<Boolean> updateMerchantUserInfo(@RequestBody @Valid UpdateMerchantUserInfoDTO updateMerchantUserInfoDTO) {
        log.info("更新会员信息,updateMerchantUserInfo:{}", updateMerchantUserInfoDTO);
        return RespBody.ok(merchantUserService.updateMerchantUserInfo(updateMerchantUserInfoDTO) > 0);
    }

    /**
     * 修改会员手机号码
     * @param merchantUserUpdateTelephoneDTO
     * @return
     */
    @PostMapping("/smallVenue/updateUserTelephone")
    public RespBody<Boolean> updateUserTelephone(@RequestBody @Valid MerchantUserUpdateTelephoneDTO merchantUserUpdateTelephoneDTO) {
        log.info("修改会员手机号码,merchantUserUpdateTelephoneDTO:{}", merchantUserUpdateTelephoneDTO);
        return RespBody.ok(merchantUserService.updateMerchantUserTelephone(merchantUserUpdateTelephoneDTO));
    }

    /**
     * 移动端会员注销
     * @param cancellationDTO
     * @return
     */
    @PostMapping("/mobileTerminal/merchantUserCancellation")
    public RespBody<Boolean> mobileTerminalMerchantUserCancellation(@RequestBody @Valid MobileTerminalUserCancellationDTO cancellationDTO) {
        log.info("移动端会员注销,cancellationDTO:{}", cancellationDTO);
        return RespBody.ok(merchantUserService.mobileTerminalMerchantUserCancellation(cancellationDTO.getMerchantId(), cancellationDTO.getMerchantUserId(), cancellationDTO.getOperatorId()) > 0);
    }

    /**
     * 根据openId和appId获取用户信息
     *
     * @param openId     openId
     * @param appId      appId
     * @param merchantId 商户id
     * @return
     */
    @GetMapping("/getUserInfoByOpenIdAndAppId")
    public RespBody<UserInfoDTO> getUserInfoByOpenIdAndAppId(@RequestParam(value = "openId") String openId,
                                                             @RequestParam(value = "appId") String appId,
                                                             @RequestParam(value = "merchantId", required = false) Long merchantId) {
        if (log.isDebugEnabled()) {
            log.debug("根据openId和appId获取用户信息,openId:{},appId:{},merchantId:{}", openId, appId, merchantId);
        }
        return RespBody.ok(userService.getUserInfoByOpenIdAndAppId(openId, appId, merchantId));
    }
}
