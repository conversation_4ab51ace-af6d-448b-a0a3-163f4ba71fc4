package com.lyy.user.infrastructure.support.tuple;

import com.lyy.user.domain.account.entity.Account;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/1/20
 */
public class AccountTuple extends Tuple {

    public AccountTuple(Account cardAccount, Account defaultAccount) {
        super(new Object[]{cardAccount, defaultAccount});
    }

    public Optional<Account> getCardAccount() {
        return Optional.ofNullable((Account) get(0));
    }

    public Optional<Account> getDefaultAccount() {
        return Optional.ofNullable((Account) get(1));
    }

}
