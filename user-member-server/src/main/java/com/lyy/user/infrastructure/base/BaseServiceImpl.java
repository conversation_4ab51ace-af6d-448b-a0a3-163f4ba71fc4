package com.lyy.user.infrastructure.base;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.ReflectionKit;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import org.apache.ibatis.binding.MapperMethod;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @className: BaseServiceImpl
 * @date 2021/4/15
 */
public abstract class BaseServiceImpl<M extends BaseMapper<T>, T> extends ServiceImpl<M, T> implements IBaseService<T>{

    protected TableFieldInfo shardingFieldInfo;

    @PostConstruct
    private void init(){
        TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
        shardingFieldInfo = tableInfo.getFieldList().stream()
                .filter(tableFieldInfo ->getShardingFieldKey().equalsIgnoreCase(tableFieldInfo.getColumn()))
                .findFirst().orElseThrow(()->new RuntimeException("无法找到 [" + entityClass.getName() + "] 类对应的分库分表字段 " + getShardingFieldKey()));
    }

    /**
     * 根据分表与id获取对应的实体
     *
     * @param shardingField
     * @param id
     * @return
     */
    @Override
    public T getById(Serializable shardingField, Serializable id) {
        TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
        QueryWrapper<T> wrapper = new QueryWrapper<T>()
                .eq(getShardingFieldKey(),shardingField)
                .eq(tableInfo.getKeyColumn(),id)
                .last("limit 1");

        return getBaseMapper().selectOne(wrapper);
    }

    /**
     * 根据 ID 删除
     *
     * @param shardingField 分表字段
     * @param id            主键ID
     */
    @Override
    public int removeById(Serializable shardingField, Serializable id) {
        TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
        UpdateWrapper<T> wrapper = new UpdateWrapper<T>()
                .eq(getShardingFieldKey(),shardingField)
                .eq(tableInfo.getKeyColumn(),id);
        return getBaseMapper().delete(wrapper);
    }
    /**
     * 查询（根据ID 批量查询）
     *
     * @param shardingField 分表字段
     * @param idList        主键ID列表
     */
    @Override
    public List<T> listByIds(Serializable shardingField, Collection<? extends Serializable> idList) {
        TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
        QueryWrapper<T> wrapper = new QueryWrapper<T>()
                .eq(getShardingFieldKey(),shardingField)
                .in(tableInfo.getKeyColumn(),idList);
        return getBaseMapper().selectList(wrapper);
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param shardingField 分表字段
     * @param idList        主键ID列表
     */
    @Override
    public boolean removeByIds(Serializable shardingField, Collection<? extends Serializable> idList) {
        TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
        UpdateWrapper<T> wrapper = new UpdateWrapper<T>()
                .eq(getShardingFieldKey(),shardingField)
                .in(tableInfo.getKeyColumn(),idList);
        return  SqlHelper.retBool(getBaseMapper().delete(wrapper));
    }

    /**
     * 批量修改插入
     *
     * @param entityList 实体对象集合
     * @param batchSize  每次的数量
     */
    @Override
    public boolean saveOrUpdateBatch(Collection<T> entityList, int batchSize) {
        TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
        Assert.notNull(tableInfo, "error: can not execute. because can not find cache of TableInfo for entity!");
        String keyProperty = tableInfo.getKeyProperty();
        Assert.notEmpty(keyProperty, "error: can not execute. because can not find column for id from entity!");
        return executeBatch(entityList, batchSize, (sqlSession, entity) -> {
            Object idVal = ReflectionKit.getFieldValue(entity, keyProperty);
            Object shardingVal  = ReflectionKit.getFieldValue(entity, shardingFieldInfo.getProperty());
            if (StringUtils.checkValNull(idVal) || Objects.isNull(getById((Serializable) shardingVal,(Serializable) idVal))) {
                sqlSession.insert(tableInfo.getSqlStatement(SqlMethod.INSERT_ONE.getMethod()), entity);
            } else {
                MapperMethod.ParamMap<T> param = new MapperMethod.ParamMap<>();
                param.put(Constants.ENTITY, entity);
                sqlSession.update(tableInfo.getSqlStatement(SqlMethod.UPDATE_BY_ID.getMethod()), param);
            }
        });
    }
    /**
     * TableId 注解存在更新记录，否插入一条记录
     *
     * @param entity 实体对象
     */
    @Override
    public boolean saveOrUpdate(T entity) {
        if (null != entity) {
            Class<?> cls = entity.getClass();
            TableInfo tableInfo = TableInfoHelper.getTableInfo(cls);
            Assert.notNull(tableInfo, "error: can not execute. because can not find cache of TableInfo for entity!");
            String keyProperty = tableInfo.getKeyProperty();
            Assert.notEmpty(keyProperty, "error: can not execute. because can not find column for id from entity!");
            Object idVal = ReflectionKit.getFieldValue(entity, keyProperty);
            Object shardingVal  = ReflectionKit.getFieldValue(entity, shardingFieldInfo.getProperty());
            if(StringUtils.checkValNull(idVal) || Objects.isNull(getById((Serializable)shardingVal,(Serializable) idVal))){
                save(entity);
            }else{
                UpdateWrapper wrapper = new UpdateWrapper<T>()
                        .eq(tableInfo.getKeyColumn(),idVal)
                        .eq(shardingFieldInfo.getColumn(),shardingVal);
                return SqlHelper.retBool(getBaseMapper().update(entity,wrapper));
            }
        }
        return false;
    }

    @Override
    public boolean updateById(T entity) {
        if (null != entity) {
            Class<?> cls = entity.getClass();
            TableInfo tableInfo = TableInfoHelper.getTableInfo(cls);
            Assert.notNull(tableInfo, "error: can not execute. because can not find cache of TableInfo for entity!");
            String keyProperty = tableInfo.getKeyProperty();
            Assert.notEmpty(keyProperty, "error: can not execute. because can not find column for id from entity!");
            Object idVal = ReflectionKit.getFieldValue(entity, keyProperty);
            Object shardingVal  = ReflectionKit.getFieldValue(entity, shardingFieldInfo.getProperty());
            UpdateWrapper wrapper = new UpdateWrapper<T>()
                    .eq(tableInfo.getKeyColumn(),idVal)
                    .eq(shardingFieldInfo.getColumn(),shardingVal);
            return SqlHelper.retBool(getBaseMapper().update(entity,wrapper));
        }
        return false;
    }
}
