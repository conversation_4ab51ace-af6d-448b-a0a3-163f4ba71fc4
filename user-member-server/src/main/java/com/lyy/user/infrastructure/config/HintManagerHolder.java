package com.lyy.user.infrastructure.config;

import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.stereotype.Component;

/**
 * @ClassName: HintManagerHolder
 * @description: 保存线程共享的HintManager：解决HintManager无法重复getInstance问题
 * @author: pengkun
 * @date: 2021/07/15
 **/
public class HintManagerHolder {

    private static final ThreadLocal<HintManager> HINT_MANAGER_HOLDER = new ThreadLocal<>();

    /**
     * 从ThreadLocal中获取已有HintManager
     * @return
     */
    public static HintManager getInstance(){
        HintManager hintManager = HINT_MANAGER_HOLDER.get();
        if(hintManager == null){
            hintManager =  HintManager.getInstance();
            setInstance(hintManager);
        }
        return hintManager;
    }

    /**
     * 设置共享的 hintManager
     * @param hintManager
     */
    public static void setInstance(HintManager hintManager){
        HINT_MANAGER_HOLDER.set(hintManager);
    }

    /**
     * 移除原有存入的信息
     */
    public static void clear(){
        HintManager.clear();
        HINT_MANAGER_HOLDER.remove();
    }

}
