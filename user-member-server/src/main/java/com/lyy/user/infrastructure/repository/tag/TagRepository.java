package com.lyy.user.infrastructure.repository.tag;

import cn.lyy.cache.annotation.CacheEvict;
import cn.lyy.cache.annotation.Cacheable;
import cn.lyy.cache.annotation.Level2Cache;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserQueryDTO;
import com.lyy.user.domain.user.entity.MerchantUserTag;
import com.lyy.user.domain.user.entity.TagUser;
import com.lyy.user.domain.user.repository.MerchantUserTagMapper;
import com.lyy.user.domain.user.repository.TagUserMapper;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since 2023/4/14 - 17:19
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TagRepository {

    public final MerchantUserTagMapper merchantUserTagMapper;
    public final TagUserMapper tagUserMapper;

    public List<TagUser> listTag(TagQuery query) {
        return tagUserMapper.selectList(
                Wrappers.<TagUser>lambdaQuery()
                        .eq(TagUser::getMerchantId, query.getMerchantId())
                        .in(CollectionUtils.isNotEmpty(query.getNames()), TagUser::getName, query.getNames())
                        .in(CollectionUtils.isNotEmpty(query.getBusinessTypes()), TagUser::getBusinessType, query.getBusinessTypes())
        );
    }

    @Cacheable(value = "multilevel:cache:member:userTag", key = "#merchantId  + '-' + #businessUserId ",
            level2Cache = @Level2Cache(useCacheCenter = false, expireTime = 3, timeUnit = TimeUnit.HOURS, preloadTime = 1, forceRefresh = true))
    public MerchantUserTag getUserTag(Long merchantId, Long businessUserId) {
        return merchantUserTagMapper.getByBusinessUserId(merchantId, businessUserId);
    }

    public void removeUserTag(Long merchantId, List<Long> userIds, Long tagId) {
        log.debug("解绑标签用户, tagId:{}, userIds:{}", tagId, userIds);
        merchantUserTagMapper.deleteByTagIdAndUserIds(tagId, merchantId, userIds);
        userIds.forEach(userId -> this.clearMerchantUserTagCache(merchantId, userId));
    }

    @CacheEvict(value = "multilevel:cache:member:userTag", key = "#merchantId  + '-' + #businessUserId ")
    public void clearMerchantUserTagCache(Long merchantId, Long businessUserId) {
        log.info("清除用户标签缓存: key={}, userId={}, merchantId={}", "multilevel:cache:member:userTag", merchantId, businessUserId);
    }

    public List<TagUserInfoDTO> listTagUserInfoByTagIds(TagUserQueryDTO condition, boolean queryIdFlag, Long[] tagIds,
            List<Long> notHandleUserIds, long l, long unbindingNumber) {
        return tagUserMapper.listTagUserInfoByTagIdsV2(condition, queryIdFlag, tagIds, notHandleUserIds,0L, unbindingNumber);
    }

    @Data
    public static class TagQuery {
        private Long merchantId;
        private List<Integer> businessTypes;
        private List<String> names;
    }

}
