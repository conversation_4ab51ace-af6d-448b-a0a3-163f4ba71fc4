package com.lyy.user.infrastructure.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 升降策略枚举
 * <AUTHOR>
 * @className: LiftingStrategy
 * @date 2021/3/29
 */
@Getter
@AllArgsConstructor
public enum MemberLiftingStrategyEnum {
    //
    LIFTING_STRATEGY_UPGRADE((short)1,"升级策略"),
    LIFTING_STRATEGY_DEMOTING((short)2,"降级策略"),
    //降级为降1个等级
    LIFTING_STRATEGY_DEMOTING_LEVEL((short)3,"降等级策略"),
    ;

    private final Short value;
    private final String name;
}
