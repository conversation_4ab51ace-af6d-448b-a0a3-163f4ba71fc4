package com.lyy.user.infrastructure.execption;


/**
 * <AUTHOR>
 * 自定义运行时 状态码异常 包含一个状态码stateCode 的异常
 */
public class StateCodeException extends RuntimeException {


    /**
     * 自定义异常状态码
     */
    private final String stateCode;

    public String getStateCode() {
        return stateCode;
    }


    public StateCodeException(String stateCode, String message) {
        this(stateCode, message, null);
    }

    public StateCodeException(String stateCode, String message, Throwable cause) {
        super(message, cause);
        this.stateCode = stateCode;
    }


    public StateCodeException(String stateCode, String message, boolean writableStackTrace) {
        this(stateCode, message, null, true, writableStackTrace);
    }

    protected StateCodeException(String stateCode, String message, Throwable cause, boolean writableStackTrace) {
        this(stateCode, message, cause, true, writableStackTrace);
    }

    protected StateCodeException(String stateCode, String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.stateCode = stateCode;
    }

}
