package com.lyy.user.infrastructure.repository.user;

import static com.lyy.user.infrastructure.util.ResponseUtils.getData;

import com.lyy.user.app.infrastructure.resp.RespBody;
import com.lyy.user.app.interfaces.facade.dto.UserAppDTO;
import com.lyy.user.app.interfaces.facade.dto.UserDTO;
import com.lyy.user.app.interfaces.facade.dto.UserVO;
import com.lyy.user.app.interfaces.facade.rpc.IExternalUserRpc;
import com.lyy.user.app.interfaces.facade.rpc.IUserAppRpc;
import com.lyy.user.app.interfaces.facade.rpc.IUserRpc;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since 2022/4/12 - 14:57
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class UserRepository {

    private final IUserRpc userRpc;
    private final IUserAppRpc userAppRpc;
    private final IExternalUserRpc externalUserRpc;

    public List<UserDTO> list(List<Long> ids) {
        RespBody<List<UserDTO>> response = userRpc.findByIds(ids);
        return getData(response);
    }
    public UserDTO get(Long userId) {
        RespBody<UserDTO> response = userRpc.getByKey(userId);
        return getData(response);
    }
    public UserVO getByUserId(Long userId) {
        RespBody<UserVO> response = userRpc.getByKeyV2(userId);
        return getData(response);
    }

    public UserDTO getByOpenId(String openId) {
        RespBody<UserDTO> response = userRpc.getByOpenId(openId);
        return getData(response);
    }

    public UserDTO getByUnionId(String unionId) {
        RespBody<UserDTO> response = userRpc.getByUnionId(unionId);
        return getData(response);
    }

    public UserVO getByOpenIdV2(String openId) {
        RespBody<UserVO> response = userRpc.getByOpenIdV2(openId);
        return getData(response);
    }

    public UserVO getByUnionIdV2(String unionId) {
        RespBody<UserVO> response = userRpc.getByUnionIdV2(unionId);
        return getData(response);
    }

    @Deprecated
    public UserDTO getByUnionIdOrOpenId(String unionId, String openId) {
        if (StringUtils.isNotBlank(unionId)) {
            return this.getByUnionId(unionId);
        } else if (StringUtils.isNotBlank(openId)) {
            return this.getByOpenId(openId);
        }
        return null;
    }

    public UserVO getByUnionIdOrOpenIdV2(String unionId, String openId) {
        if (StringUtils.isNotBlank(unionId)) {
            return this.getByUnionIdV2(unionId);
        } else if (StringUtils.isNotBlank(openId)) {
            return this.getByOpenIdV2(openId);
        }
        return null;
    }

    public UserDTO insertOrUpdateUser(UserDTO dto) {
        log.debug("添加或更新用户信息");
        RespBody<UserDTO> response = userRpc.addOrUpdate(dto);
        return getData(response);
    }

    public Long signUp(UserDTO dto) {
        RespBody<Long> response = userRpc.signUp(dto);
        return getData(response);
    }

    public UserAppDTO insertOrUpdateUserApp(Long userId, String openId, String appId) {
        UserAppDTO dto = new UserAppDTO();
        dto.setUserId(userId);
        dto.setOpenId(openId);
        dto.setAppId(appId);
        return this.insertOrUpdateUserApp(dto);
    }

    public UserAppDTO insertOrUpdateUserApp(UserAppDTO dto) {
        RespBody<UserAppDTO> response = userAppRpc.addOrUpdate(dto);
        return getData(response);
    }

    public void updateUserAndIgnore(Long oldUserId, Long newUserId) {
        RespBody<Boolean> response = userRpc.updateUserAppAndIgnore(oldUserId, newUserId);
    }

    public UserDTO getByAppIdAndOpenId(String appId, String openId) {
        if (StringUtils.isBlank(appId)) {
            return null;
        }
        RespBody<UserDTO> response = userRpc.getByOpenIdAndAppId(openId, appId);
        return getData(response);
    }

    public UserVO getByAppIdAndOpenIdV2(String appId, String openId) {
        if (StringUtils.isBlank(appId)) {
            return null;
        }
        RespBody<UserVO> response = userRpc.getByOpenIdAndAppIdV2(openId, appId);
        return getData(response);
    }

    public Long getCityId(String cityName) {
        RespBody<Long> response = userRpc.getCityId(cityName);
        return getData(response);
    }

}
