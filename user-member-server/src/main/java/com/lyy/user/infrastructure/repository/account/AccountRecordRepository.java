package com.lyy.user.infrastructure.repository.account;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.lyy.user.domain.account.entity.AccountRecord;
import com.lyy.user.domain.account.repository.AccountRecordMapper;
import com.lyy.user.infrastructure.util.WxAuthUpdateUtils;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since 2022/8/16 - 18:54
 */
@RequiredArgsConstructor
@Repository
public class AccountRecordRepository {
    private final AccountRecordMapper accountRecordMapper;

    public boolean insertBatch(List<AccountRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return false;
        }
        return Lists.partition(records, 50)
                .stream()
                .map(accountRecordMapper::insertBatch)
                .mapToInt(i -> i)
                .sum() > 0;
    }

    public boolean insert(AccountRecord record) {
        if (Objects.isNull(record)) {
            return false;
        }
        return accountRecordMapper.insert(record) > 0;
    }

    public List<AccountRecord> listRecordByOrder(Query query) {
        if (query.getStartTime() == null) {
            query.setStartTime(WxAuthUpdateUtils.getLocalDateTimeModifyMonthsDate(LocalDateTime.now(), -1));
        }
        Wrapper<AccountRecord> queryWrapper = Wrappers.<AccountRecord>lambdaQuery()
                .eq(AccountRecord::getUserId, query.getUserId())
                .eq(AccountRecord::getMerchantId, query.getMerchantId())
                .eq(StringUtils.isNotBlank(query.getOrderNo()), AccountRecord::getOutTradeNo, query.getOrderNo())
                .in(CollectionUtils.isNotEmpty(query.getSubOrderNos()), AccountRecord::getSubOrderNo, query.getSubOrderNos())
                .ge(AccountRecord::getCreateTime, query.getStartTime())
                .le(query.getEndTime() != null, AccountRecord::getCreateTime, query.getEndTime())
                .orderByDesc(AccountRecord::getCreateTime);
        return accountRecordMapper.selectList(queryWrapper);
    }

    @Data
    public static class Query {
        private Long userId;
        private Long merchantId;
        private String orderNo;
        private List<String> subOrderNos;
        private Date startTime;
        private Date endTime;

    }

}
