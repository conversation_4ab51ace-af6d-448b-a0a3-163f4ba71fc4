package com.lyy.user.infrastructure.util;

import com.ctrip.framework.apollo.core.utils.StringUtils;

/**
 * <AUTHOR>
 * @create 2022/1/20 10:24
 */
public class LyyStringUtil {
    /**
     * 截取指定长度的字符串
     *
     * @param str
     * @param maxLength
     * @return
     */
    public static String substring(String str, Integer maxLength) {
        if (StringUtils.isBlank(str)) {
            return null;
        }

        str = str.trim();
        if (null == maxLength || maxLength < 0) {
            return str;
        }

        int length = str.length();
        maxLength = maxLength > length ? length : maxLength;
        return str.substring(0, maxLength);
    }
}
