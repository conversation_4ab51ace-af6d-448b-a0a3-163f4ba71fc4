package com.lyy.user.infrastructure.base;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @className: IBaseService
 * @date 2021/4/15
 */
public interface IBaseService<T> extends IService<T> {

    /**
     * 获取分库的字段key
     * @return
     */
    String getShardingFieldKey();

//    /**
//     * 根据 ID 查询
//     *
//0     * @param id 主键ID
//     */
//    @Override
//    default T getById(Serializable id) {
//        return getBaseMapper().selectById(id);
//    }

    /**
     * 根据分表与id获取对应的实体
     * @param shardingField 分表字段
     * @param id
     * @return
     */
    T getById(Serializable shardingField, Serializable id);

//    /**
//     * 根据 ID 删除
//     * @param shardingField 分表字段
//     * @param id 主键ID
//     */
//    default boolean removeByIdRetrunBool(Serializable shardingField,Serializable id) {
//        return SqlHelper.retBool(removeById(shardingField,id));
//    }
    /**
     * 根据 ID 删除
     * @param shardingField 分表字段
     * @param id 主键ID
     */
    int removeById(Serializable shardingField,Serializable id);
    /**
     * 查询（根据ID 批量查询）
     * @param shardingField 分表字段
     * @param idList 主键ID列表
     */
    List<T> listByIds(Serializable shardingField,Collection<? extends Serializable> idList);

    /**
     * 删除（根据ID 批量删除）
     * @param shardingField 分表字段
     * @param idList 主键ID列表
     */
    boolean removeByIds(Serializable shardingField,Collection<? extends Serializable> idList);

}
