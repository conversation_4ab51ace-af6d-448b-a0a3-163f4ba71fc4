package com.lyy.user.infrastructure.support.specification.account;

import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.infrastructure.support.specification.AbstractCompositeSpecification;
import java.util.Objects;

/**
 * 主卡判断
 *
 * <AUTHOR>
 * @since 2022/1/11
 */
public class MainAccountSpecification extends AbstractCompositeSpecification<Account> {

    @Override
    public boolean isSatisfiedBy(Account t) {
        return Objects.nonNull(t) && Objects.isNull(t.getParentAccountId());
    }

}
