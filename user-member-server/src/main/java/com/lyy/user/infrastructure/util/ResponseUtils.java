package com.lyy.user.infrastructure.util;

import static java.util.Optional.ofNullable;

import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.app.infrastructure.resp.RespBody;
import com.lyy.user.infrastructure.execption.BusinessException;
import java.util.function.Supplier;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @since 2022/4/12 - 14:59
 */
@Slf4j
public class ResponseUtils {

    public static <T> T getData(RespBody<T> response) {
        return getData(response, null);
    }

    public static <T> T getData(RespBody<T> response, T other) {
        return ofNullable(response)
                .filter(r -> {
                    boolean success = StringUtils.equals(GlobalErrorCode.OK.getCode(), r.getCode());
                    if (!success) {
                        log.warn("请求失败 response: {}", response);
                    }
                    log.debug("请求用户信息：{}", r);
                    return success;
                })
                .map(RespBody::getBody)
                .orElse(other);
    }

    private static <T> T getDataElseThrow(RespBody<T> response, Supplier<BusinessException> exceptionSupplier) {
        return ofNullable(response)
                .filter(r -> StringUtils.equals(GlobalErrorCode.OK.getCode(), r.getCode()))
                .map(RespBody::getBody)
                .orElseThrow(exceptionSupplier);
    }

}
