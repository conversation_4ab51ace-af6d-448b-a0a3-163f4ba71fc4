package com.lyy.user.infrastructure.constants;

import com.lyy.user.domain.benefit.entity.Benefit;
import com.lyy.user.domain.benefit.entity.BenefitConsumeRule;
import com.lyy.user.domain.benefit.entity.BenefitRule;
import com.lyy.user.domain.benefit.entity.BenefitScope;
import com.lyy.user.domain.correction.entity.UmBenefitBak;
import com.lyy.user.domain.correction.entity.UmBenefitConsumeRuleBak;
import com.lyy.user.domain.correction.entity.UmBenefitRuleBak;
import com.lyy.user.domain.correction.entity.UmBenefitScopeBak;
import com.lyy.user.domain.correction.entity.UmMemberGroupBak;
import com.lyy.user.domain.correction.entity.UmMemberLevelBak;
import com.lyy.user.domain.correction.entity.UmMemberLiftingBak;
import com.lyy.user.domain.correction.entity.UmMemberLiftingRuleBak;
import com.lyy.user.domain.correction.entity.UmMemberRangeBak;
import com.lyy.user.domain.correction.entity.UmMemberRuleBak;
import com.lyy.user.domain.correction.entity.UmTagUserBak;
import com.lyy.user.domain.member.entity.MemberGroup;
import com.lyy.user.domain.member.entity.MemberLevel;
import com.lyy.user.domain.member.entity.MemberLifting;
import com.lyy.user.domain.member.entity.MemberLiftingRule;
import com.lyy.user.domain.member.entity.MemberRange;
import com.lyy.user.domain.member.entity.MemberRule;
import com.lyy.user.domain.user.entity.TagUser;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/9/25
 */
@Getter
@Slf4j
@AllArgsConstructor
public enum ShardingGCEnum {

    UM_TAG_USER(0, "um_tag_user", "um_tag_user_bak", TagUser.class, UmTagUserBak.class),

    UM_BENEFIT(1, "um_benefit", "um_benefit_bak", Benefit.class, UmBenefitBak.class),

    UM_BENEFIT_RULE(2, "um_benefit_rule", "um_benefit_rule_bak", BenefitRule.class, UmBenefitRuleBak.class),

    UM_BENEFIT_SCOPE(3, "um_benefit_scope", "um_benefit_scope_bak", BenefitScope.class, UmBenefitScopeBak.class),

    UM_BENEFIT_CONSUME_RULE(4, "um_benefit_consume_rule", "um_benefit_consume_rule_bak", BenefitConsumeRule.class, UmBenefitConsumeRuleBak.class),

    UM_MEMBER_GROUP(5, "um_member_group", "um_member_group_bak", MemberGroup.class, UmMemberGroupBak.class),

    UM_MEMBER_LEVEL(6, "um_member_level", "um_member_level_bak", MemberLevel.class, UmMemberLevelBak.class),

    UM_MEMBER_LIFTING(7, "um_member_lifting", "um_member_lifting_bak", MemberLifting.class, UmMemberLiftingBak.class),

    UM_MEMBER_LIFTING_RULE(8, "um_member_lifting_rule", "um_member_lifting_rule_bak", MemberLiftingRule.class, UmMemberLiftingRuleBak.class),

    UM_MEMBER_RANGE(9, "um_member_range", "um_member_range_bak", MemberRange.class, UmMemberRangeBak.class),

    UM_MEMBER_RULE(10, "um_member_rule", "um_member_rule_bak", MemberRule.class, UmMemberRuleBak.class),
    ;

    private final Integer code;

    private final String tableName;

    private final String bakTableName;

    private final Class umClazz;

    private final Class backupClazz;

    public static ShardingGCEnum getData(Integer tableNo) {
        return Arrays.stream(ShardingGCEnum.values()).filter(it -> tableNo.equals(it.getCode())).findFirst().get();
    }

    public static List<String> getTableName(List<Integer> tableNos) {
        if (tableNos.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> result = new ArrayList<>(tableNos.size());
        tableNos.forEach(info -> {
            String tableName = getData(info).getTableName();
            result.add(tableName);
        });

        return result;
    }

    public static Class getSourceClass(String name) {
        return Arrays.stream(ShardingGCEnum.values()).filter(it -> name.equals(it.getTableName())).findFirst().get().getUmClazz();
    }

    public static Class getBakSourceClass(String name) {
        return Arrays.stream(ShardingGCEnum.values()).filter(it -> name.equals(it.getBakTableName())).findFirst().get().getBackupClazz();
    }


    public static Boolean validateInput(List<Integer> tableNos) {
        if (null == tableNos || tableNos.isEmpty()) {
            log.error("没有指定清除的表");
            return false;
        }
        return true;
    }
}


