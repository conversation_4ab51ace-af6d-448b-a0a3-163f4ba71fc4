package com.lyy.user.infrastructure.sharding.algorithm;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.shardingsphere.api.sharding.hint.HintShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.hint.HintShardingValue;

/**
 * <AUTHOR>
 * @date 2023/10/12
 */
@Deprecated
public class GcHintShardingAlgorithm implements HintShardingAlgorithm<String>  {

    @Override
    public Collection<String> doSharding(Collection<String> collection, HintShardingValue<String> hintShardingValue) {
        List<String> result = new ArrayList<>();
        for (String talbleName : collection) {
            for (String value : hintShardingValue.getValues()) {
                if (talbleName.equals(value)) {
                    result.add(talbleName);
                }
            }
        }

        return result;
    }
}
