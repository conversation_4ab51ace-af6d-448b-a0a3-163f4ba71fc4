package com.lyy.user.infrastructure.repository.account;

import static java.util.Optional.ofNullable;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.lyy.klock.annotation.Klock;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.AvailableBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueAccountBenefitTransferDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueAccountBenefitTransferQueryDTO;
import com.lyy.user.account.infrastructure.constant.AccountBenefitStatusEnum;
import com.lyy.user.account.infrastructure.constant.AccountRecordOperationTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountStatusEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.domain.account.dto.AccountInitDTO;
import com.lyy.user.domain.account.dto.AccountInitResultDTO;
import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.domain.account.entity.AccountBenefit;
import com.lyy.user.domain.account.entity.AccountRecord;
import com.lyy.user.domain.account.repository.AccountBenefitMapper;
import com.lyy.user.domain.account.repository.AccountMapper;
import com.lyy.user.domain.account.repository.AccountRecordMapper;
import com.lyy.user.domain.statistics.entity.SmallVenueStoredStatistics;
import com.lyy.user.domain.statistics.repository.SmallVenueStoredStatisticsMapper;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.domain.user.repository.MerchantUserMapper;
import com.lyy.user.infrastructure.repository.user.MerchantUserRepository;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2022/1/8
 */
@Repository
public class SmallVenueAccountRepository {

    @Resource
    private AccountMapper accountMapper;
    @Resource
    private AccountBenefitMapper accountBenefitMapper;
    @Resource(name = "smallVenueStoredStatisticsMapper")
    private SmallVenueStoredStatisticsMapper statisticsMapper;
    @Resource
    private AccountRecordMapper accountRecordMapper;

    @Resource
    private MerchantUserMapper merchantUserMapper;
    @Resource
    private MerchantUserRepository merchantUserRepository;

    public void update(Long merchantId, Account entity) {
        Wrapper<Account> wrapper = Wrappers.lambdaUpdate(Account.class)
                .eq(Account::getId, entity.getId())
                .eq(Account::getMerchantId, merchantId)
                .eq(Account::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        accountMapper.update(entity, wrapper);
    }

    public void updateAccountStatus(Long merchantId, Long accountId, AccountStatusEnum status) {
        Wrapper<Account> wrapper = Wrappers.lambdaUpdate(Account.class)
                .eq(Account::getId, accountId)
                .eq(Account::getMerchantId, merchantId)
                .eq(Account::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                .set(Account::getStatus, status.getStatus());
        accountMapper.update(null, wrapper);
    }

    /**
     * 注销会员卡
     */
    public void updateAccountStatusAndDefaultFlag(Long merchantId, Long accountId, AccountStatusEnum status, Boolean defaultFlag, Date now, Long updateBy) {
        Wrapper<Account> wrapper = Wrappers.lambdaUpdate(Account.class)
                .eq(Account::getId, accountId)
                .eq(Account::getMerchantId, merchantId)
                .eq(Account::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                .set(Account::getStatus, status.getStatus())
                .set(Account::getUpdateTime, now)
                .set(Account::getUpdateBy, updateBy)
                .set(Account::getCardNo, null)
                .set(Objects.nonNull(defaultFlag), Account::getDefaultFlag, defaultFlag);
        accountMapper.update(null, wrapper);
    }

    public void updateRecentlyAccountAsDefaultFlag(Long merchantId, Long userId, Long updateBy) {
        accountMapper.updateRecentlyAccountAsDefaultFlag(merchantId, userId, updateBy);
    }

    public void updateAccountDefaultFlag(Long merchantId, Long accountId, Long updateBy, Boolean defaultFlag) {
        Wrapper<Account> wrapper = Wrappers.lambdaUpdate(Account.class)
                .eq(Account::getId, accountId)
                .eq(Account::getMerchantId, merchantId)
                .eq(Account::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                .set(Account::getUpdateTime, new Date())
                .set(Account::getUpdateBy, updateBy)
                .set(Account::getDefaultFlag, defaultFlag);
        accountMapper.update(null, wrapper);
    }

    public Optional<Account> get(Long merchantId, Long userId, String cardNo) {
        LambdaQueryWrapper<Account> wrapper = Wrappers.<Account>lambdaQuery()
                .eq(Account::getMerchantId, merchantId)
                .eq(Account::getUserId, userId)
                .ne(Account::getStatus, AccountStatusEnum.CANCELLATION.getStatus())
                .eq(Account::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                .eq(StringUtils.isNotBlank(cardNo), Account::getCardNo, cardNo);
        Account account = accountMapper.selectOne(wrapper);
        return ofNullable(account);
    }

    public Optional<Account> get(Long merchantId, String cardNo) {
        LambdaQueryWrapper<Account> wrapper = Wrappers.<Account>lambdaQuery()
                .eq(Account::getMerchantId, merchantId)
                .eq(Account::getCardNo, cardNo)
                .eq(Account::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                .ne(Account::getStatus, AccountStatusEnum.CANCELLATION.getStatus());
        Account account = accountMapper.selectOne(wrapper);
        return Optional.ofNullable(account);
    }

    public Optional<Account> get(Long merchantId, Long accountId) {
        LambdaQueryWrapper<Account> wrapper = Wrappers.<Account>lambdaQuery()
                .eq(Account::getMerchantId, merchantId)
                .eq(Account::getId, accountId);
        Account account = accountMapper.selectOne(wrapper);
        return Optional.ofNullable(account);
    }

    public void saveAccountBenefit(AccountBenefit entity) {
        accountBenefitMapper.insert(entity);
    }

    public void updateAccountBenefit(Long merchantId, AccountBenefit entity) {
        Wrapper<AccountBenefit> wrapper = Wrappers.<AccountBenefit>lambdaUpdate()
                .eq(AccountBenefit::getId, entity.getId())
                .eq(AccountBenefit::getMerchantId, merchantId)
                .eq(AccountBenefit::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        accountBenefitMapper.update(entity, wrapper);
    }

    public void updateAccountBenefit(Long merchantId, Long accountBenefitId, BigDecimal change, Date updateTime, Long updateBy) {
        AccountBenefit entity = new AccountBenefit();
        entity.setId(accountBenefitId);
        entity.setUpdateTime(updateTime);
        entity.setUpdateBy(updateBy);
        this.updateAccountBenefit(merchantId, entity);
    }

    public Boolean changeBalance(Long merchantId, Long accountBenefitId, BigDecimal change, Date updateTime, Long updateBy) {
        return accountBenefitMapper.changeBalance(accountBenefitId, merchantId, change, updateBy, updateTime) > 0;
    }

    public void deleteAccountBenefit(Long merchantId, Long accountId) {
        Wrapper<AccountBenefit> wrapper = Wrappers.<AccountBenefit>lambdaQuery()
                .eq(AccountBenefit::getMerchantId, merchantId)
                .eq(AccountBenefit::getAccountId, accountId)
                .eq(AccountBenefit::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
        accountBenefitMapper.delete(wrapper);
    }

    public void updateAccountStatistics4Expired(Long merchantId, Long userId, Long classifyId, BigDecimal balance, boolean isFrequency, Date updateTime) {
        LambdaUpdateWrapper<SmallVenueStoredStatistics> wrapper = Wrappers.<SmallVenueStoredStatistics>lambdaUpdate()
                .eq(SmallVenueStoredStatistics::getMerchantId, merchantId)
                .eq(SmallVenueStoredStatistics::getUserId, userId)
                .eq(SmallVenueStoredStatistics::getMerchantBenefitClassifyId, classifyId)
                .set(SmallVenueStoredStatistics::getUpdated, updateTime)
                .setSql(Objects.nonNull(balance), "balance = coalesce(balance, 0) + " + balance)
                .setSql(Objects.nonNull(balance), "total_invalid = coalesce(total_invalid, 0) + " + balance.abs())
                .setSql(isFrequency, "remain_num =  coalesce(remain_num, 0) + " + (-1))
                .setSql(isFrequency, "total_num_invalid =  coalesce(total_num_invalid, 0) + " + 1);
        statisticsMapper.update(null, wrapper);
    }

    public void updateAccountStatistics(Long merchantId, SmallVenueStoredStatistics entity) {
        Wrapper<SmallVenueStoredStatistics> wrapper = Wrappers.<SmallVenueStoredStatistics>lambdaUpdate()
                .eq(SmallVenueStoredStatistics::getId, entity.getId())
                .eq(SmallVenueStoredStatistics::getMerchantId, merchantId);
        statisticsMapper.update(entity, wrapper);
    }

    public void saveAccountStatistics(SmallVenueStoredStatistics entity) {
        statisticsMapper.insert(entity);
    }

    public void saveAccountRecord(AccountRecord entity) {
        accountRecordMapper.insert(entity);
    }

    public List<AccountBenefit> selectBenefit(Long merchantId, Long accountId, Integer status) {
        LambdaQueryWrapper<AccountBenefit> wrapper = Wrappers.<AccountBenefit>lambdaQuery()
                .eq(AccountBenefit::getMerchantId, merchantId)
                .eq(AccountBenefit::getAccountId, accountId)
                .eq(AccountBenefit::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                .eq(Objects.nonNull(status), AccountBenefit::getStatus, status);
        return accountBenefitMapper.selectList(wrapper);
    }

    public List<AccountBenefit> selectBenefit(Long merchantId, List<Long> accountBenefitIds) {
        if (CollectionUtils.isEmpty(accountBenefitIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<AccountBenefit> wrapper = Wrappers.<AccountBenefit>lambdaQuery()
                .eq(AccountBenefit::getMerchantId, merchantId)
                .eq(AccountBenefit::getStatus, AccountBenefitStatusEnum.NORMAL.getStatus())
                .eq(AccountBenefit::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                .in(AccountBenefit::getId, accountBenefitIds);
        return accountBenefitMapper.selectList(wrapper);
    }

    public List<AccountBenefit> selectUnlimitedBenefit(Long merchantId, Long accountId, List<Long> classifyIds) {
        if (CollectionUtils.isEmpty(classifyIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<AccountBenefit> wrapper = Wrappers.<AccountBenefit>lambdaQuery()
                .eq(AccountBenefit::getMerchantId, merchantId)
                .eq(AccountBenefit::getAccountId, accountId)
                .eq(AccountBenefit::getStatus, AccountBenefitStatusEnum.NORMAL.getStatus())
                .eq(AccountBenefit::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                .eq(AccountBenefit::getExpiryDateCategory, ExpiryDateCategoryEnum.NO_LIMIT.getValue())
                .isNull(AccountBenefit::getUpTime)
                .isNull(AccountBenefit::getDownTime)
                .in(AccountBenefit::getMerchantBenefitClassifyId, classifyIds);
        return accountBenefitMapper.selectList(wrapper);
    }

    public List<AccountBenefit> selectBenefit(Long merchantId, Long userId, Long accountId, List<Long> classifyIds) {
        return this.selectBenefit(merchantId, userId, accountId, classifyIds, null, AccountBenefitStatusEnum.NORMAL);
    }

    public List<AccountBenefit> selectBenefit(Long merchantId, Long userId, Long accountId, List<Long> classifyIds, ExpiryDateCategoryEnum category) {
        return this.selectBenefit(merchantId, userId, accountId, classifyIds, category, AccountBenefitStatusEnum.NORMAL);
    }

    public List<AccountBenefit> selectBenefit(Long merchantId, Long userId, Long accountId, List<Long> classifyIds, ExpiryDateCategoryEnum category, AccountBenefitStatusEnum status) {
        if (CollectionUtils.isEmpty(classifyIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<AccountBenefit> wrapper = Wrappers.<AccountBenefit>lambdaQuery()
                .eq(AccountBenefit::getMerchantId, merchantId)
                .eq(AccountBenefit::getUserId, userId)
                .eq(AccountBenefit::getAccountId, accountId)
                .eq(AccountBenefit::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                .in(AccountBenefit::getMerchantBenefitClassifyId, classifyIds);

        if (Objects.nonNull(status)) {
            wrapper.eq(AccountBenefit::getStatus, status.getStatus());
        }
        if (Objects.nonNull(category)) {
            wrapper.eq(AccountBenefit::getExpiryDateCategory, category.getValue());
        }

        return accountBenefitMapper.selectList(wrapper);
    }
    public List<AccountBenefit> selectTransferOutBenefit(Long merchantId, Long userId, Long accountId, List<Long> classifyIds) {
        if (CollectionUtils.isEmpty(classifyIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<AccountBenefit> wrapper = Wrappers.<AccountBenefit>lambdaQuery()
                .eq(AccountBenefit::getMerchantId, merchantId)
                .eq(AccountBenefit::getUserId, userId)
                .eq(AccountBenefit::getAccountId, accountId)
                .eq(AccountBenefit::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                .eq(AccountBenefit::getStatus, AccountBenefitStatusEnum.NORMAL.getStatus())
                .gt(AccountBenefit::getBalance, 0)
                .in(AccountBenefit::getMerchantBenefitClassifyId, classifyIds);
        return accountBenefitMapper.selectList(wrapper);
    }

    public IPage<AccountBenefit> pageExpiredBenefit(Long merchantId, Integer pageIndex, Integer pageSize) {
        Page<AccountBenefit> page = new Page<>(pageIndex, pageSize);
        page.setSearchCount(false);
        return accountBenefitMapper.selectPage(page, getAccountBenefitQueryWrapper(merchantId));
    }

    private LambdaQueryWrapper<AccountBenefit> getAccountBenefitQueryWrapper(Long merchantId) {
        return Wrappers.<AccountBenefit>lambdaQuery()
                .select(AccountBenefit::getId, AccountBenefit::getUserId,
                        AccountBenefit::getAccountId, AccountBenefit::getMerchantBenefitClassifyId,
                        AccountBenefit::getMerchantUserId, AccountBenefit::getStoreId,
                        AccountBenefit::getMerchantId, AccountBenefit::getValueType,
                        AccountBenefit::getBalance)
                .eq(AccountBenefit::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                .eq(AccountBenefit::getStatus, AccountBenefitStatusEnum.NORMAL.getStatus())
                .eq(AccountBenefit::getExpiryDateCategory, ExpiryDateCategoryEnum.TIME_INTERVAL.getValue())
                .and(wrapper -> wrapper.lt(AccountBenefit::getDownTime, DateUtil.format(new Date(), "yyyy-MM-dd")))
                .and(wrapper -> wrapper.eq(AccountBenefit::getMerchantId, merchantId).or().exists("select 1"));
    }


    public List<SmallVenueStoredStatistics> selectStatistics(Long merchantId, Long userId, List<Long> classifyIds) {
        if (CollectionUtils.isEmpty(classifyIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<SmallVenueStoredStatistics> wrapper = Wrappers.<SmallVenueStoredStatistics>lambdaQuery()
                .eq(SmallVenueStoredStatistics::getMerchantId, merchantId)
                .eq(SmallVenueStoredStatistics::getUserId, userId)
                .in(SmallVenueStoredStatistics::getMerchantBenefitClassifyId, classifyIds);
        return statisticsMapper.selectList(wrapper);
    }


    @Deprecated
    public boolean batchSaveRecord(List<AccountRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return false;
        }
        return accountRecordMapper.insertBatch(records) > 0;
    }

    /**
     * 获取用户所有的权益
     *
     * @param queryDTO
     * @return
     */
    public List<AccountBenefit> findUserAllBenefit(AvailableBenefitQueryDTO queryDTO) {
        return accountBenefitMapper.findUserAllBenefit(queryDTO);
    }

    /**
     * 获取权益明细使用记录
     *
     * @param userId            用户id
     * @param merchantId        商户id
     * @param accountBenefitIds 账户权益ids
     * @param recordTypes       消费类型
     * @param merchantUserId    商户用户id
     */
    public List<AccountRecord> findByAccountBenefit(Long userId, Long merchantId, List<Long> accountBenefitIds,
                                                    List<Integer> recordTypes, Long merchantUserId) {
        return accountRecordMapper.findByAccountBenefit(userId, merchantId, accountBenefitIds, recordTypes, merchantUserId);
    }

    /**
     * 根据订单号获取权益变更名
     *
     * @param userId      用户id
     * @param merchantId  商户id
     * @param orderNo     订单号
     * @param subOrderNos 子订单
     * @return
     */
    public List<AccountRecord> findRecordByOrderNo(Long userId, Long merchantId, String orderNo, List<String> subOrderNos,Date startTime ,Date endTime) {
        MerchantUser merchantUser = merchantUserRepository.getByUserIdAndMerchantId(userId, merchantId);
        if (merchantUser == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AccountRecord> queryWrapper = Wrappers.lambdaQuery(AccountRecord.class)
                .eq(AccountRecord::getUserId, userId)
                .eq(AccountRecord::getMerchantId, merchantId)
                .eq(AccountRecord::getOperationType, 4)
                .eq(AccountRecord::getMerchantUserId, merchantUser.getId());
        if (StringUtils.isNotBlank(orderNo)) {
            queryWrapper.eq(AccountRecord::getOrderNo, orderNo);
        }
        if (CollectionUtils.isNotEmpty(subOrderNos)) {
            queryWrapper.in(AccountRecord::getSubOrderNo, subOrderNos);
        }
        if (startTime != null) {
            queryWrapper.ge(AccountRecord::getCreateTime, startTime );
        }
        if (endTime != null) {
            queryWrapper.le(AccountRecord::getCreateTime, endTime );
        }
        return accountRecordMapper.selectList(queryWrapper);
    }

    public List<Account> listAccountWithDefault(Long merchantId, Long userId, String cardNo) {
        return accountMapper.listAccountWithDefault(merchantId, userId, cardNo);
    }

    public List<Account> listSubAccount(Long merchantId, Long parentAccountId) {
        LambdaQueryWrapper<Account> wrapper = Wrappers.<Account>lambdaQuery()
                .eq(Account::getMerchantId, merchantId)
                .eq(Account::getParentAccountId, parentAccountId)
                .eq(Account::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                .ne(Account::getStatus, AccountStatusEnum.CANCELLATION.getStatus());
        return accountMapper.selectList(wrapper);
    }

    public List<Account> selectAccount(Long merchantId, Long userId, List<String> cards) {
        return this.selectAccount(merchantId, userId, cards, AccountStatusEnum.NORMAL);
    }

    public List<Account> selectAccount(Long merchantId, Long userId, List<String> cards, AccountStatusEnum status) {
        if (CollectionUtils.isEmpty(cards)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<Account> wrapper = Wrappers.<Account>lambdaQuery()
                .eq(Account::getMerchantId, merchantId)
                .eq(Account::getUserId, userId)
                .eq(Account::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                .in(Account::getCardNo, cards)
                .orderByDesc(Account::getCreateTime);
        if (Objects.nonNull(status)) {
            wrapper.eq(Account::getStatus, status.getStatus());
        }
        return accountMapper.selectList(wrapper);
    }

    public List<Account> selectAccountCard4ExpiredBenefit(Long merchantId, List<Long> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Lists.newArrayList();
        }
        return accountMapper.selectList(
                Wrappers.<Account>lambdaQuery()
                        .select(Account::getId, Account::getCardNo)
                        .eq(Account::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                        .ne(Account::getStatus, AccountStatusEnum.CANCELLATION.getStatus())
                        .in(Account::getId, accountIds)
                        .isNotNull(Account::getCardNo)
                        .and(wrapper -> wrapper.eq(Account::getMerchantId, merchantId).or().exists("select 1"))
        );
    }

    public IPage<Account> pageExpiredAccount(long merchantId, int pageIndex, int pageSize) {
        Page<Account> page = new Page<>(pageIndex, pageSize);
        page.setSearchCount(false);
        return accountMapper.selectPage(page,
                Wrappers.<Account>lambdaQuery()
                        .select(Account::getId, Account::getMerchantId, Account::getDownTime)
                        .eq(Account::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                        .eq(Account::getStatus, AccountStatusEnum.NORMAL.getStatus())
                        .isNotNull(Account::getDownTime)
                        .and(wrapper -> wrapper.lt(Account::getDownTime, new Date()))
                        .and(wrapper -> wrapper.eq(Account::getMerchantId, merchantId).or().exists("select 1"))
        );
    }

    /**
     * 账号初始化
     *
     * @param initDTO
     * @return
     */
    @Klock(keys = "#initDTO.merchantId + ':' + #initDTO.classify + ':' + #initDTO.merchantUserId +'_'+ #initDTO.cardNo", waitTime = 5)
    @Transactional(propagation = Propagation.SUPPORTS,rollbackFor = Exception.class)
    public AccountInitResultDTO initAccount(AccountInitDTO initDTO) {
        AccountInitResultDTO resultDTO = new AccountInitResultDTO();
        LambdaQueryWrapper<Account> queryWrapper = Wrappers.lambdaQuery(Account.class)
                .eq(Account::getMerchantId, initDTO.getMerchantId())
                .eq(Account::getClassify, initDTO.getClassify())
                .eq(Account::getMerchantUserId, initDTO.getMerchantUserId());
        if (StringUtils.isNotBlank(initDTO.getCardNo())) {
            queryWrapper.eq(Account::getCardNo, initDTO.getCardNo());
        }
        if (Objects.equals(initDTO.getClassify(), BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())) {
            queryWrapper.ne(Account::getStatus, AccountStatusEnum.CANCELLATION.getStatus());
        }
        Account account = accountMapper.selectOne(queryWrapper);
        if (account == null) {
            Date now = new Date();
            //新建账号
            account = new Account();
            account.setMerchantUserId(initDTO.getMerchantUserId());
            account.setUserId(initDTO.getUserId());
            account.setMerchantId(initDTO.getMerchantId());
            account.setStatus(AccountStatusEnum.NORMAL.getStatus());
            account.setDescription(initDTO.getDescription());
            account.setCreateTime(now);
            account.setUpdateTime(now);
            account.setClassify(initDTO.getClassify());
            account.setTotal(ofNullable(initDTO.getTotal()).orElse(BigDecimal.ZERO));
            account.setBalance(ofNullable(initDTO.getBalance()).orElse(BigDecimal.ZERO));
            account.setCreateBy(ofNullable(initDTO.getOperatorId()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
            account.setUpdateBy(ofNullable(initDTO.getOperatorId()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
            account.setCardNo(initDTO.getCardNo());
            account.setStoreId(initDTO.getStoreId());
            account.setDeposit(initDTO.getDeposit());
            account.setDefaultFlag(initDTO.getDefaultFlag());
            account.setDownTime(initDTO.getDownTime());
            if (initDTO.getDefaultFlag() != null && initDTO.getDefaultFlag()) {
                //将其他账号设置为非默认状态
                accountMapper.updateAccountDefaultFlag(initDTO.getMerchantId(), initDTO.getMerchantUserId(),
                        ofNullable(initDTO.getOperatorId()).orElse(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID));
            }
            if(StringUtils.isBlank(initDTO.getCardNo())
                    && Objects.equals(initDTO.getClassify(), BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())) {
                account.setDefaultFlag(true);
            }

            accountMapper.insert(account);
            resultDTO.setIsCreate(true);
        } else {
            resultDTO.setIsCreate(false);
        }
        resultDTO.setAccount(account);
        return resultDTO;
    }

    public List<AccountRecord> listRecordWithBenefit(String orderNo, Long merchantId, Long userId, boolean isRefund, Date date) {
        LocalDate time = null;
        if (Objects.nonNull(date)) {
           time = date.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
        } else {
            time = LocalDate.now();
        }

        return accountRecordMapper.selectList(
                Wrappers.<AccountRecord>lambdaQuery()
                        .select(AccountRecord::getId, AccountRecord::getAccountInitialBalance,
                                AccountRecord::getAccountInitialNum, AccountRecord::getActualBenefit,
                                AccountRecord::getRecordType, AccountRecord::getMode,
                                AccountRecord::getMerchantBenefitClassifyId, AccountRecord::getMerchantBenefitClassifyName,
                                AccountRecord::getAccountBenefitId)
                        .eq(AccountRecord::getMerchantId, merchantId)
                        .eq(AccountRecord::getUserId, userId)
                        .eq(AccountRecord::getOperationType, AccountRecordOperationTypeEnum.BENEFIT_MODIFY.getCode())
                        .eq(!isRefund, AccountRecord::getOrderNo, orderNo)
                        .eq(isRefund, AccountRecord::getRefundNo, orderNo)
                        .ge(AccountRecord::getCreateTime, time)
                        .lt(AccountRecord::getCreateTime, time.plusDays(1))
        );
    }

    public Account getDefaultAccount(Long merchantId, Long userId) {
        return accountMapper.getDefaultAccount(merchantId, userId);
    }

    public void updateAccountBenefit(AccountBenefit entity, BigDecimal total, BigDecimal balance, Long operatorId) {
        accountBenefitMapper.increaseBalanceAndTotal(entity.getId(), entity.getMerchantId(), operatorId, total, balance);
    }

    public void updateAccountStatistics(Long merchantId, Long userId, Long classifyId, BigDecimal balance, boolean isFrequency, Date updateTime) {
        LambdaUpdateWrapper<SmallVenueStoredStatistics> wrapper = Wrappers.<SmallVenueStoredStatistics>lambdaUpdate()
                .eq(SmallVenueStoredStatistics::getMerchantId, merchantId)
                .eq(SmallVenueStoredStatistics::getUserId, userId)
                .eq(SmallVenueStoredStatistics::getMerchantBenefitClassifyId, classifyId)
                .set(SmallVenueStoredStatistics::getUpdated, updateTime)
                .setSql(Objects.nonNull(balance), "balance = coalesce(balance, 0) + " + balance)
                .setSql(Objects.nonNull(balance), "total = coalesce(total, 0) + " + balance)
                .setSql(isFrequency, "remain_num =  coalesce(remain_num, 0) + " + 1)
                .setSql(isFrequency, "total_num =  coalesce(total_num, 0) + " + 1);
        statisticsMapper.update(null, wrapper);
    }


    public int updateAccount(Long merchantId, Long oldUserId, Long oldMerchantUserId,
                              Long newUserId, Long newMerchantUserId, String cardNo) {
        LambdaUpdateWrapper<Account> wrapper = Wrappers.<Account>lambdaUpdate()
                .eq(Account::getMerchantId, merchantId)
                .eq(Account::getUserId, oldUserId)
                .eq(Account::getMerchantUserId, oldMerchantUserId)
                ;
        if (newUserId != null) {
            wrapper.set(Account::getUserId, newUserId);
        }
        if (newMerchantUserId != null) {
            wrapper.set(Account::getMerchantUserId, newMerchantUserId);
        }
        if (StringUtils.isNotBlank(cardNo)) {
            wrapper.set(Account::getCardNo, cardNo);
        }
        return accountMapper.update(null, wrapper);
    }

    public int updateAccountBebefitIdInfo(Long merchantId, Long oldUserId, Long oldMerchantUserId,
                                          Long newUserId, Long newMerchantUserId, Long accountId) {
        return accountBenefitMapper.updateAccountBenefitIds(merchantId, oldUserId, oldMerchantUserId, newUserId, newMerchantUserId, accountId);
    }

    public List<SmallVenueAccountBenefitTransferDTO> selectTotalTransferAccountBenefit(SmallVenueAccountBenefitTransferQueryDTO smallVenueAccountBenefitTransferQueryDTO) {
        return accountBenefitMapper.selectTotalTransferAccountBenefit(smallVenueAccountBenefitTransferQueryDTO);
    }

    public List<SmallVenueAccountBenefitTransferDTO> selectTransferAccountBenefit(SmallVenueAccountBenefitTransferQueryDTO smallVenueAccountBenefitTransferQueryDTO) {
        return accountBenefitMapper.selectTransferAccountBenefit(smallVenueAccountBenefitTransferQueryDTO);
    }

    public Integer updateVenueAccountById(Account account) {
        return accountMapper.updateVenueAccountById(account);
    }
}
