package com.lyy.user.infrastructure.listener;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lyy.user.account.infrastructure.account.dto.BenefitRollbackDTO;
import com.lyy.user.application.account.AccountService;
import com.lyy.user.domain.account.dto.BenefitRollbackDelayedMessage;
import com.rabbitmq.client.Channel;
import java.io.IOException;
import java.util.concurrent.Executor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: 权益回退延时处理
 * @author: qgw
 * @date on 2021/4/15.
 * @Version: 1.0
 */
@Component
@Slf4j
public class BenefitRollbackDelayedListener {

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private BenefitRollbackDelayedProducer rollbackDelayedProducer;

    @Autowired
    private AccountService accountService;

    @Value("${user.member.rabbit.benefit.delay.time:1}")
    private Integer delay;

    @Value("${user.member.rabbit.benefit.delay.count:3}")
    private Integer totalFailCount;


    @Autowired
    private Executor benefitRollbackCallbackExecutor;


    @RabbitListener(queues = "${user.member.rabbit.benefit-rollback-queue}",
            containerFactory = "benefitRollbackDelayedRabbitListenerContainerFactory"
            , autoStartup = "${user.member.mq.autoStartup:true}")
    public void handler(Message message, Channel channel) {
        benefitRollbackCallbackExecutor.execute(() -> {
            try {
                log.info("权益回退处理延时队列监听:{}", message);
                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                mapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);

                BenefitRollbackDelayedMessage benefitRollbackDelayedMessage = mapper.readValue(message.getBody(), BenefitRollbackDelayedMessage.class);

                Integer failCount = benefitRollbackDelayedMessage.getFailCount();
                if (failCount >= totalFailCount) {
                    //查询3次都失败
                    log.warn("回退失败， 权益回退缓存找不到");
                } else {
                    BenefitRollbackDTO benefitRollbackDTO = benefitRollbackDelayedMessage.getBenefitRollbackDTO();
                    boolean result = accountService.benefitRollbackDelayQueue(benefitRollbackDTO);
                    log.debug("权益回退处理延时队列监听,orderNo:{},结果:{},现重试次数:{}", benefitRollbackDTO.getOrderNo(), result, failCount);
                    if (!result) {
                        benefitRollbackDelayedMessage.setFailCount(failCount + 1);
                        rollbackDelayedProducer.send(benefitRollbackDelayedMessage, delay * 1000);
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                try {
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                } catch (IOException e) {
                    log.error("#===> 权益回退处理延时队列确认消息消费异常");
                    log.error(e.getMessage(), e);
                }
            }
        });
    }

}
