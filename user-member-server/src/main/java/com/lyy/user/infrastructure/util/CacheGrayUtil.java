package com.lyy.user.infrastructure.util;

import java.util.List;
import java.util.Objects;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class CacheGrayUtil {

    /**
     * 全局开关
     */
    @Value("${member.benefit.cache.full.gray:false}")
    private Boolean fullGray;

    /**
     * 灰度商户列表
     */
    @Value("${member.benefit.cache.distributor.list:}")
    private List<Long> distributorList;


    /**
     * 会员组全局开关
     */
    @Value("${member.group.cache.full.gray:false}")
    private Boolean memberGroupFullGray;

    /**
     * 会员组灰度商户列表
     */
    @Value("${member.group.cache.distributor.list:}")
    private List<Long> memberGroupdistributorList;


    private static CacheGrayUtil cacheGrayUtil;

    @PostConstruct
    public void init() {
        cacheGrayUtil = this;
    }

    /**
     * 判断是否权益缓存灰度
     *
     */
    public static Boolean benefitCacheGray(Long merchantId, String method) {
        if (log.isDebugEnabled()) {
            log.debug("benefit-cache全局开关:{}", cacheGrayUtil.fullGray);
        }
        Boolean flag = null;
        if (Objects.equals(cacheGrayUtil.fullGray, Boolean.TRUE)) {
            flag =  Boolean.TRUE;
        } else if (merchantId == null || CollectionUtils.isEmpty(cacheGrayUtil.distributorList)) {
            flag =  Boolean.FALSE;
        } else {
            flag =  cacheGrayUtil.distributorList.contains(merchantId);
        }
        log.debug("benefit-cache商户灰度开关:{},商户:{},method:{}", flag, merchantId, method);
        return flag;
    }

    /**
     * 判断是否会员组缓存灰度
     *
     */
    public static boolean memberGroupCacheGray(Long merchantId) {
        if (log.isDebugEnabled()) {
            log.debug("member-group-cache 全局开关:{}", cacheGrayUtil.memberGroupFullGray);
        }
        if (Objects.equals(cacheGrayUtil.memberGroupFullGray, Boolean.TRUE)) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(cacheGrayUtil.memberGroupdistributorList)
                && cacheGrayUtil.memberGroupdistributorList.contains(merchantId)) {
            log.info("member-group-cache 命中商户灰度:{}", merchantId);
            return true;
        }
        return false;
    }

}
