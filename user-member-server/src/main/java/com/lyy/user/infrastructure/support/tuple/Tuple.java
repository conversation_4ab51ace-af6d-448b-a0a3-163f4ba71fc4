package com.lyy.user.infrastructure.support.tuple;

import org.codehaus.groovy.runtime.typehandling.DefaultTypeTransformation;

import java.util.AbstractList;
import java.util.List;

/**
 * 元组 <br>
 * copy from {@link groovy.lang.Tuple}
 */
public class <PERSON><PERSON> extends AbstractList {
    private final Object[] contents;
    private int hashCode;

    public Tuple(Object[] contents) {
        if (contents == null){ throw new NullPointerException();}
        this.contents = contents;
    }
    @Override
    public Object get(int index) {
        return contents[index];
    }
    @Override
    public int size() {
        return contents.length;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}
        if (o == null || !(o instanceof Tuple)) {return false;}

        Tuple that = (Tuple) o;
        if (size() != that.size()) {return false;}
        for (int i = 0; i < contents.length; i++) {
            if (!DefaultTypeTransformation.compareEqual(contents[i], that.contents[i])) {
                return false;
            }
        }
        return true;
    }

    @Override
    public int hashCode() {
        if (hashCode == 0) {
            for (int i = 0; i < contents.length; i++) {
                Object value = contents[i];
                int hash = (value != null) ? value.hashCode() : 0xbabe;
                hashCode ^= hash;
            }
            if (hashCode == 0) {
                hashCode = 0xbabe;
            }
        }
        return hashCode;
    }

    @Override
    public List subList(int fromIndex, int toIndex) {
        int size = toIndex - fromIndex;
        Object[] newContent = new Object[size];
        System.arraycopy(contents, fromIndex, newContent, 0, size);
        return new Tuple(newContent);
    }
}
