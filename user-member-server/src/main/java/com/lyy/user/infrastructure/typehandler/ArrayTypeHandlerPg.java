package com.lyy.user.infrastructure.typehandler;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeException;
import org.postgresql.jdbc.PgArray;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * java Object[] to PostgreSQL Array (PgArray)
 */
@Slf4j
public class ArrayTypeHandlerPg extends BaseTypeHandler<Object[]> {


    private static final String TYPE_NAME_VARCHAR = "varchar";
    private static final String TYPE_NAME_INTEGER = "integer";
    private static final String TYPE_NAME_BOOLEAN = "boolean";
    private static final String TYPE_NAME_NUMERIC = "numeric";

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object[] parameter, JdbcType jdbcType)
            throws SQLException {
        String typename = null;
        if (parameter instanceof Integer[]) {
            typename = TYPE_NAME_INTEGER;
        } else if (parameter instanceof String[]) {
            typename = TYPE_NAME_VARCHAR;
        } else if (parameter instanceof Boolean[]) {
            typename = TYPE_NAME_BOOLEAN;
        } else if (parameter instanceof Double[]) {
            typename = TYPE_NAME_NUMERIC;
        } else if (parameter instanceof Long[]) {
            typename = TYPE_NAME_NUMERIC;
        }

        if (typename == null) {
            throw new TypeException("ArrayTypeHandler parameter typename error, your type is " + parameter.getClass().getName());
        }

        ps.setObject(i, parameter);
    }

    @Override
    public Object[] getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getArray(rs.getObject(columnName));
    }


    @Override
    public Object[] getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getArray(rs.getObject(columnIndex));
    }


    @Override
    public Object[] getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getArray(cs.getObject(columnIndex));
    }

    private Object[] getArray(Object obj) {
        if (obj == null) {
            return null;
        }
        // 判断数据类型
        if (obj instanceof PgArray) {
            try {
                PgArray array = (PgArray) obj;
                return (Object[]) array.getArray();
            } catch (SQLException e) {
                log.error("pgArray data format failed, {}", e.getMessage());
            }
        }
        return null;

    }

}
