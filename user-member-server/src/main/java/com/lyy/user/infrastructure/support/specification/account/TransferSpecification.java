package com.lyy.user.infrastructure.support.specification.account;

import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.infrastructure.support.specification.AbstractCompositeSpecification;
import com.lyy.user.infrastructure.support.specification.Specification;
import com.lyy.user.infrastructure.support.tuple.AccountTransferTuple;

/**
 * <AUTHOR>
 * @since 2022/1/11
 */
public class TransferSpecification extends AbstractCompositeSpecification<AccountTransferTuple> {

    @Override
    public boolean isSatisfiedBy(AccountTransferTuple tuple) {
        Specification<Account> specification = new MainAccountSpecification().and(new AccountValidSpecification());
        return specification.isSatisfiedBy(tuple.getOutAccount()) && specification.isSatisfiedBy(tuple.getInAccount());
    }
}
