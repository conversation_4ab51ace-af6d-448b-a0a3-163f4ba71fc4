package com.lyy.user.infrastructure.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;

/**
 * <AUTHOR>
 * @date 2023/10/9
 */
public class GcNodeUtils {

    private static final Logger logger = LoggerFactory.getLogger(GcNodeUtils.class);
    public static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final ObjectMapper MAPPER = new ObjectMapper();

    static {
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        MAPPER.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
        MAPPER.configure(DeserializationFeature.ACCEPT_EMPTY_ARRAY_AS_NULL_OBJECT, true);
        MAPPER.setDateFormat(SIMPLE_DATE_FORMAT);
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S")));
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern("HH:mm:ss")));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S")));
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ofPattern("HH:mm:ss")));
        MAPPER.registerModule(javaTimeModule);

    }

    private GcNodeUtils() {
    }


    public static ObjectNode createNode() {
        return MAPPER.createObjectNode();
    }

    public static void putNode(ObjectNode objectNode, String k, String v) {
        objectNode.put(k, v);
    }

    public static ArrayNode createArrNode() {
        return MAPPER.createArrayNode();
    }

    public static <T>  List<T> convertNode(ArrayNode arrayNode, Class<T> clazz) {
        try {
            return MAPPER.readerForListOf(clazz).readValue(arrayNode);
        } catch (IOException e) {
            logger.error("Jackson convert fail, err: {}", e.getLocalizedMessage());
            throw new RuntimeException(e);
        }
    }

    public static <T> List<T> nodeToJson(Object obj, Class<T> t) {
        if (Objects.isNull(obj)) {
            return null;
        }
        try {
            String jsonStr = MAPPER.writeValueAsString(obj);
            JsonNode jsonNode = MAPPER.readTree(jsonStr);
            return MAPPER.readerForListOf(t).readValue(jsonNode);
        } catch (IOException e) {
            logger.error("序列化失败:{}, err: ", obj, e);
        }
        return null;
    }

    @Deprecated
    public static <T> List<T> nodeToJson(List obj, Class<T> t, Integer ds, String timeFormat) {
        if (CollectionUtils.isEmpty(obj)) {
            return null;
        }
        try {
            ArrayNode arrayNode = createArrNode();
            for (Object ob : obj) {
                String jsonStr = MAPPER.writeValueAsString(ob);
                JsonNode jsonNode = MAPPER.readTree(jsonStr);
                ObjectNode objectNode = (ObjectNode) jsonNode;
                objectNode.put("dsNum", ds);
                objectNode.put("snapshotTime", timeFormat);
                arrayNode.add(objectNode);
            }

            return MAPPER.readerForListOf(t).readValue(arrayNode);
        } catch (IOException e) {
            logger.error("序列化失败:{}, err: ", obj, e);
        }
        return null;
    }

    public static JsonNode objToNode(Object obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        try {
            String jsonStr = MAPPER.writeValueAsString(obj);
            return MAPPER.readTree(jsonStr);
        } catch (IOException e) {
            logger.error("序列化失败:{}, err: ", obj, e);
        }
        return null;
    }

    public static List<Long> jsonToIds(List obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        ArrayList<Long> result = new ArrayList<>();

        try {
            for (Object ob : obj) {
                String jsonStr = MAPPER.writeValueAsString(ob);
                JsonNode jsonNode = MAPPER.readTree(jsonStr);
                if (!jsonNode.isEmpty()) {
                    JsonNode idNode = jsonNode.get("id");
                    long id = idNode.asLong();
                    result.add(id);
                }
            }

            return result;
        } catch (IOException e) {
            logger.error("序列化失败:{}, err: ", obj, e);
        }
        return Collections.emptyList();
    }

    @Deprecated
    public static List<MapSqlParameterSource> objToMap(List param) {
        List<MapSqlParameterSource> result = new ArrayList<>(param.size());
        for (Object ob : param) {
            try {
                String json = MAPPER.writeValueAsString(ob);
                Map map = MAPPER.readValue(json, Map.class);
                MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource(map);
                result.add(mapSqlParameterSource);
            } catch (JsonProcessingException e) {
                logger.error("序列化失败:", e);
            }
        }
        return result;
    }

}
