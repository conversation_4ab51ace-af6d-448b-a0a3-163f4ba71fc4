package com.lyy.user.infrastructure.support.specification.account;

import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitPredicate;
import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.infrastructure.support.specification.AbstractCompositeSpecification;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022/1/11
 */
public class AccountPredicateSpecification extends AbstractCompositeSpecification<Account> {

    private final BenefitPredicate predicate;
    private final AccountValidSpecification validSpecification;

    public AccountPredicateSpecification(BenefitPredicate predicate) {
        this.predicate = predicate;
        validSpecification  = new AccountValidSpecification();
    }

    @Override
    public boolean isSatisfiedBy(Account t) {
        if (Objects.nonNull(predicate) && Objects.nonNull(predicate.getAccountStatus()) && !predicate.getAccountStatus()) {
            return false;
        }
        return !validSpecification.isSatisfiedBy(t);
    }

}
