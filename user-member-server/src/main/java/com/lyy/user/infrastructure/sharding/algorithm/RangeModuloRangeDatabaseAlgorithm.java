package com.lyy.user.infrastructure.sharding.algorithm;

import com.google.common.collect.Range;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.infrastructure.execption.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.sharding.standard.RangeShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.RangeShardingValue;

import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.Set;


/**
 * <AUTHOR>
 * <p>
 * 范围分库
 */
@Slf4j
public final class RangeModuloRangeDatabaseAlgorithm implements RangeShardingAlgorithm<Integer> {

    private final static Integer PRECISE_SHARDING_INDEX_127 = 127;
    private final static Integer PRECISE_SHARDING_INDEX_96 = 96;
    private final static Integer PRECISE_SHARDING_INDEX_95 = 95;
    private final static Integer PRECISE_SHARDING_INDEX_64 = 64;
    private final static Integer PRECISE_SHARDING_INDEX_63 = 63;
    private final static Integer PRECISE_SHARDING_INDEX_32 = 32;
    private final static Integer PRECISE_SHARDING_INDEX_31 = 31;

    @Override
    public Collection<String> doSharding(Collection<String> databaseNames, RangeShardingValue<Integer> rangeShardingValue) {
        Set<String> result = new LinkedHashSet<>();
        log.debug("RangeModuloRangeDatabaseAlgorithm databaseName: {}", databaseNames);
        if (Range.closed(0, PRECISE_SHARDING_INDEX_31).encloses(rangeShardingValue.getValueRange())) {
            for (String each : databaseNames) {
                if (each.endsWith("0")) {
                    result.add(each);
                }
            }
        } else if (Range.closed(PRECISE_SHARDING_INDEX_32, PRECISE_SHARDING_INDEX_63).encloses(rangeShardingValue.getValueRange())) {
            for (String each : databaseNames) {
                if (each.endsWith("1")) {
                    result.add(each);
                }
            }
        } else if (Range.closed(PRECISE_SHARDING_INDEX_64, PRECISE_SHARDING_INDEX_95).encloses(rangeShardingValue.getValueRange())) {
            for (String each : databaseNames) {
                if (each.endsWith("2")) {
                    result.add(each);
                }
            }
        } else if (Range.closed(PRECISE_SHARDING_INDEX_96, PRECISE_SHARDING_INDEX_127).encloses(rangeShardingValue.getValueRange())) {
            for (String each : databaseNames) {
                if (each.endsWith("3")) {
                    result.add(each);
                }
            }
        } else {
            throw new BusinessException(GlobalErrorCode.DATABASE_SHARDING_ROUTE);
        }

        return result;
    }
}
