package com.lyy.user.infrastructure.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品分类枚举类
 *     此类为商品服务冗余枚举类，最新数据参考商品服务ClassifyEnum
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum CommodityClassifyCodeEnum {

    /**
     * 按实际收费
     */
    TIME("TIME","按时间收费"),
    /**
     * 按电量收费
     */
    ELEC("ELEC","按电量收费"),
    /**
     * 按动态功率计费
     */
    DYNAMIC("DYNAMIC","按功率动态计费"),
    /**
     * 按功率收费
     */
    POWER("POWER","按功率收费"),
    RECHARGE("RECHARGE","充值套餐"),
    VOLUME("VOLUME","按体积容量收费"),
    PERIOD("PERIOD","按时间段收费"),
    MODE("MODE","按模式收费"),
    NUM("NUM","按脉冲(次数)收费");



    private final String code;

    private final String name;

    public static String getName(String code){
        for(CommodityClassifyCodeEnum classifyCodeEnum:CommodityClassifyCodeEnum.values()){
            if(classifyCodeEnum.getCode().equalsIgnoreCase(code)){
                return classifyCodeEnum.getName();
            }
        }
        return null;
    }
}
