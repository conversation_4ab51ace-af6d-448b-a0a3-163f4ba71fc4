package com.lyy.user.infrastructure.support.specification.account;

import com.lyy.user.account.infrastructure.constant.AccountStatusEnum;
import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.infrastructure.support.specification.AbstractCompositeSpecification;
import java.util.Date;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

/**
 * 会员卡账户状态有效
 *
 * <AUTHOR>
 * @since 2022/1/11
 */
@Slf4j
public class AccountValidSpecification extends AbstractCompositeSpecification<Account> {

    private final Date now;

    public AccountValidSpecification() {
        this.now = new Date();
    }

    @Override
    public boolean isSatisfiedBy(Account t) {
        if (!Objects.equals(t.getStatus(), AccountStatusEnum.NORMAL.getStatus())) {
            log.warn("[小场地]--会员卡已过期，状态: {}", t.getStatus());
            return false;
        }
        if (Objects.nonNull(t.getDownTime()) && t.getDownTime().before(now)) {
            log.warn("[小场地]--会员卡已过期，时间: {}", t.getDownTime());
            return false;
        }
        return true;
    }

}
