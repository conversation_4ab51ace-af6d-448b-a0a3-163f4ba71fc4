package com.lyy.user.infrastructure.constants;

import cn.lyy.base.communal.constant.TradeTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * 类描述：支付类型标签枚举
 * <p>
 *
 *
 * <AUTHOR>
 * @since 2021/6/15 14:34
 */
@Getter
@AllArgsConstructor
@Slf4j
public enum TradeTypeGroupEnum {


    /**
     * 交易类型
     */
    WECHAT(1, "微信支付", Arrays.asList(
            TradeTypeEnum.WECHAT_TRADE.getId(),
            TradeTypeEnum.WECHAT_MINI_PROGRAM_TRADE.getId(),
            TradeTypeEnum.WECHAT_APP.getId(),
            TradeTypeEnum.WECHAT_FACE_TRADE.getId()
    ),"微信"),
    ALIPAY(2, "支付宝支付",Arrays.asList(
            TradeTypeEnum.ALIPAY_TRADE.getId(),
            TradeTypeEnum.ALIPAY_FACE_TRADE.getId()
    ),"支付宝"),
    UNION(3, "云闪付支付",Arrays.asList(
            TradeTypeEnum.UNION_TRADE.getId()
    ),"云闪付"),
    JD(4, "京东支付",Arrays.asList(
            TradeTypeEnum.JD_TRADE.getId()
    ),"京东"),
    REVERSE_SCAN(5, "反扫付款码",Arrays.asList(
            TradeTypeEnum.REVERSE_SCAN.getId()
    ),""),

   WALLET(6, "其他支付",Arrays.asList(
            TradeTypeEnum.PURSE.getId(),
            TradeTypeEnum.BOOST.getId(),
            TradeTypeEnum.GRABPAY.getId(),
            TradeTypeEnum.Maybank.getId(),
           TradeTypeEnum.DIRECT_TRADE.getId()
   ),""),


    ;


    private final Integer groupPayType;

    private final String description;
    /**
     * 分组内包含的支付类型
     */
    private final List<Integer> tradeTypes;

    /**
     * 对应支付标签的名称
     */
    private final String tagName;



    public static String findGroupPayType(Integer tradeType) {
        for (TradeTypeGroupEnum value : TradeTypeGroupEnum.values()) {
            if (value.getTradeTypes().contains(tradeType)) {
                return value.getTagName();
            }
        }
        log.warn("找不到对应支付类型标签:{}", tradeType);
        return null;
    }

}

