package com.lyy.user.infrastructure.mapstruct;

import com.lyy.user.account.infrastructure.account.dto.smallvenue.AccountRecordInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitIncrementDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.CardTransferDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.UserAccountBenefit;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.UserAccountRecord;
import com.lyy.user.domain.account.entity.AccountBenefit;
import com.lyy.user.domain.account.entity.AccountRecord;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

/**
 * <AUTHOR>
 * @since 2022/1/8
 */
@Mapper(componentModel = "spring")
public interface AccountMapStruct {

    @Mapping(target = "accountBenefitId", source = "id")
    UserAccountBenefit AccountBenefit2UserAccountBenefit(AccountBenefit benefits);

    List<UserAccountBenefit> AccountBenefit2UserAccountBenefit(List<AccountBenefit> benefits);

    @Mapping(target = "consumeType", source = "consumerType")
    @Mapping(target = "createName", source = "operatorName")
    void assignAccountRecord(AccountRecordInfoDTO infoDTO, @MappingTarget AccountRecord record);

    @Mapping(target = "orderNo", source = "orderNo")
    @Mapping(target = "outTradeNo", source = "orderNo")
    @Mapping(target = "createdby", source = "operatorId")
    @Mapping(target = "description", source = "record.description")
    @Mapping(target = "storeName", source = "record.storeName")
    @Mapping(target = "operationChannel", source = "record.operationChannel")
    @Mapping(target = "consumeType", source = "record.consumerType")
    @Mapping(target = "operationEquipmentName", source = "record.operationEquipmentName")
    @Mapping(target = "equipmentName", source = "record.equipmentName")
    @Mapping(target = "equipmentValue", source = "record.equipmentValue")
    @Mapping(target = "terminalName", source = "record.terminalName")
    @Mapping(target = "createName", source = "record.operatorName")
    void assignAccountRecord(CardTransferDTO dto, @MappingTarget AccountRecord record);

    @Mapping(target = "orderNo", source = "orderNo")
    @Mapping(target = "outTradeNo", source = "orderNo")
    @Mapping(target = "createdby", source = "operatorId")
    @Mapping(target = "description", ignore = true)
    @Mapping(target = "recordType", source = "record.recordType")
    @Mapping(target = "storeName", source = "record.storeName")
    @Mapping(target = "operationChannel", source = "record.operationChannel")
    @Mapping(target = "consumeType", source = "record.consumerType")
    @Mapping(target = "operationEquipmentName", source = "record.operationEquipmentName")
    @Mapping(target = "equipmentName", source = "record.equipmentName")
    @Mapping(target = "equipmentValue", source = "record.equipmentValue")
    @Mapping(target = "terminalName", source = "record.terminalName")
    @Mapping(target = "createName", source = "record.operatorName")
    @Mapping(target = "commodityName", source = "record.commodityName")
    @Mapping(target = "goodsId", source = "record.goodsId")
    @Mapping(target = "goodsType", source = "record.goodsType")
    @Mapping(target = "goodsClassify", source = "record.goodsClassify")
    @Mapping(target = "goodsClassifyName", source = "record.goodsClassifyName")
    @Mapping(target = "goodsNum", source = "record.goodsNum")
    void assignAccountRecord(BenefitIncrementDTO incrementDTO, @MappingTarget AccountRecord record);

    @Mapping(target = "orderNo", ignore = true)
    UserAccountRecord AccountRecord2UserAccountRecord(AccountRecord accountRecord);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "accountId", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "updateBy", ignore = true)
    @Mapping(target = "total", ignore = true)
    @Mapping(target = "balance", ignore = true)
    void setAccountBenefitValue(AccountBenefit accountBenefit, @MappingTarget AccountBenefit newBenefit);
}
