package com.lyy.user.infrastructure.config;

import cn.lyy.base.utils.SnowflakeIdWorkerUtils;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CustomIdGenerator implements IdentifierGenerator {

    @Override
    public Number nextId(Object entity) {
        log.debug("自定义雪花算法生成IdGenerator");
        return SnowflakeIdWorkerUtils.INSTANCE.getSnowflakeIdWorker().nextId();
    }
}
