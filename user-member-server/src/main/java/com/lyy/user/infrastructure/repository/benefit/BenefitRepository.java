package com.lyy.user.infrastructure.repository.benefit;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.lyy.user.domain.benefit.entity.Benefit;
import com.lyy.user.domain.benefit.entity.BenefitConsumeRule;
import com.lyy.user.domain.benefit.entity.BenefitScope;
import com.lyy.user.domain.benefit.repository.BenefitConsumeRuleMapper;
import com.lyy.user.domain.benefit.repository.BenefitMapper;
import com.lyy.user.domain.benefit.repository.BenefitScopeMapper;
import com.lyy.user.infrastructure.util.CacheGrayUtil;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since 2022/8/16 - 20:04
 */
@RequiredArgsConstructor
@Repository
public class BenefitRepository {

    private final BenefitScopeMapper benefitScopeMapper;

    private final BenefitMapper benefitMapper;

    private final BenefitConsumeRuleMapper benefitConsumeRuleMapper;

    private final BenefitCacheRepository benefitCacheRepository;

    private final static Integer MAX_ABLE_CACHE_SIZE = 100;

    public List<Benefit> listBenefit(Long merchantId, List<Long> benefitIds) {
        if (CollectionUtils.isEmpty(benefitIds) || Objects.isNull(merchantId)) {
            return null;
        }
        return benefitMapper.selectList(new LambdaQueryWrapper<Benefit>().in(Benefit::getId, benefitIds).eq(Benefit::getMerchantId, merchantId));
    }

    public boolean insertBatch(List<BenefitScope> records) {
        if (CollectionUtils.isEmpty(records)) {
            return false;
        }
        boolean result = Lists.partition(records, 200)
                .stream()
                .map(benefitScopeMapper::insertBatch)
                .mapToInt(i -> i)
                .sum() > 0;
        if (result) {
            records.forEach(benefitScope -> benefitCacheRepository.clearBenefitScopeCache(benefitScope.getMerchantId(), benefitScope.getBenefitId()));
        }
        return result;
    }

    public List<BenefitScope> listScopeByBenefitIds(Long merchantId, List<Long> benefitIds) {
        if (CollectionUtils.isEmpty(benefitIds)) {
            return Lists.newArrayList();
        }
        if (!CacheGrayUtil.benefitCacheGray(merchantId, "BenefitRepository#listScopeByBenefitIds")) {
            return benefitScopeMapper.selectList(new QueryWrapper<BenefitScope>().in("benefit_id", benefitIds));
        }
        if (benefitIds.size() > MAX_ABLE_CACHE_SIZE) {
            return benefitScopeMapper.selectList(new LambdaQueryWrapper<BenefitScope>().in(BenefitScope::getBenefitId, benefitIds)
                .eq(BenefitScope::getMerchantId, merchantId));
        }
        return benefitIds.stream().map(benefitId -> benefitCacheRepository.getScopeByBenefitId(merchantId, benefitId))
            .flatMap(Collection::stream).collect(Collectors.toList());
    }


    public List<BenefitConsumeRule> listBenefitConsumeRule(Long merchantId, List<Integer> classifyList, Boolean filterActive) {
        if (!CacheGrayUtil.benefitCacheGray(merchantId, "BenefitRepository#listBenefitConsumeRule")) {
            return benefitConsumeRuleMapper.selectList(new QueryWrapper<BenefitConsumeRule>()
                .eq("merchant_id", merchantId)
                .in("benefit_classify", classifyList)
                .eq(Objects.equals(filterActive, Boolean.TRUE), "is_active", true));
        }
        List<BenefitConsumeRule> benefitConsumeRules = benefitCacheRepository.listBenefitConsumeRule(merchantId);
        return benefitConsumeRules.stream().filter(benefitConsumeRule -> classifyList.contains(benefitConsumeRule.getBenefitClassify()))
            .filter(benefitConsumeRule -> !Objects.equals(filterActive, Boolean.TRUE) || Objects.equals(benefitConsumeRule.getIsActive(), Boolean.TRUE))
            .collect(Collectors.toList());
    }

    public BenefitConsumeRule getBenefitConsumeRule(Long merchantId, Integer classify) {
        if (classify == null) {
            return null;
        }
        if (!CacheGrayUtil.benefitCacheGray(merchantId, "BenefitRepository#getBenefitConsumeRule")) {
            return benefitConsumeRuleMapper.getByMerchantIdAndClassify(merchantId, classify);
        }
        List<BenefitConsumeRule> benefitConsumeRules = benefitCacheRepository.listBenefitConsumeRule(merchantId);
        return benefitConsumeRules.stream().filter(benefitConsumeRule -> Objects.equals(benefitConsumeRule.getBenefitClassify(), classify))
            .filter(benefitConsumeRule -> Objects.equals(benefitConsumeRule.getIsActive(), Boolean.TRUE))
            .findFirst().orElse(null);
    }
}
