package com.lyy.user.infrastructure.listener;

import com.lyy.user.domain.account.dto.BenefitRollbackDelayedMessage;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: qgw
 * @date on 2021-05-27.
 * @Version: 1.0
 */
@Slf4j
@Component("benefitRollbackDelayedProducer")
@ConditionalOnProperty(name = {"user.member.rabbit.benefit-rollback-delayed.producer.enable"}, havingValue = "true")
public class BenefitRollbackDelayedProducer implements RabbitTemplate.ReturnCallback {
    @Autowired
    @Qualifier("benefitRollbackDelayedTemplate")
    private RabbitTemplate benefitRollbackDelayedTemplate;

    /**
     * 权益回退延时exchange
     */
    @Value("${user.member.rabbit.benefit-rollback-exchange}")
    private String benefitRollbackDelayedExchange;

    /**
     * 权益回退延时route
     */
    @Value("${user.member.rabbit.benefit-rollback-route}")
    private String benefitRollbackDelayedRoute;

    /**
     *
     * @param rollbackDelayedMessage
     * @param delay 毫秒
     */
    public void send(BenefitRollbackDelayedMessage rollbackDelayedMessage, Integer delay) {
        log.info("权益退款-发送：{},delay:{}", rollbackDelayedMessage, delay);
        CorrelationData correlationId = new CorrelationData(UUID.randomUUID().toString());
        benefitRollbackDelayedTemplate.convertAndSend(benefitRollbackDelayedExchange, benefitRollbackDelayedRoute, rollbackDelayedMessage, a -> {
            a.getMessageProperties().setDelay(delay);
            return a;
        }, correlationId);
    }

    @Override
    public void returnedMessage(Message message, int replyCode, String replyText, String exchange, String routingKey) {

    }
}
