package com.lyy.user.infrastructure.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 升降策略枚举
 * <AUTHOR>
 * @className: MemberGroupLiftingStrategyEnum
 * @date 2021/3/29
 */
@Getter
@AllArgsConstructor
public enum MemberGroupLiftingStrategyEnum {
    //升降策略
    LIFTING_STRATEGY_NOT((short)0,"无策略"),
    LIFTING_STRATEGY_UPGRADE((short)1,"升级策略"),
    LIFTING_STRATEGY_DEMOTING((short)2,"降级策略"),
    LIFTING_STRATEGY_UPGRADE_AND_DEMOTING((short)3,"有升有降策略"),
    ;

    private final Short value;
    private final String name;

}
