package com.lyy.user.infrastructure.base;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <AUTHOR>
 * @className: MerchantBaseServiceImpl
 * @date 2021/4/15
 */
public class MerchantBaseServiceImpl <M extends BaseMapper<T>, T>  extends BaseServiceImpl<M,T> implements IBaseService<T>{
    /**
     * 获取分库的字段key
     *
     * @return
     */
    @Override
    public String getShardingFieldKey() {
        return "merchant_id";
    }
}
