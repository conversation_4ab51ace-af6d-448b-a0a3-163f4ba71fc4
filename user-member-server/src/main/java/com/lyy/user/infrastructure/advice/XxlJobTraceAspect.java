package com.lyy.user.infrastructure.advice;

import brave.Span;
import brave.Tracer;
import brave.Tracing;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

import static org.springframework.cloud.sleuth.util.SpanNameUtil.toLowerHyphen;

/**
 * <AUTHOR>
 * @since 2023/3/27 - 17:17
 */
@Slf4j
@Aspect
public class XxlJobTraceAspect {

    private final Tracing tracing;

    public XxlJobTraceAspect(Tracing tracing) {
        this.tracing = tracing;
    }

    private static final String CLASS_KEY = "class";

    private static final String METHOD_KEY = "method";


    @Around("execution (* com.xxl.job.core.handler.IJobHandler.execute(..))")
    public Object traceBackgroundThread(final ProceedingJoinPoint pjp) throws Throwable {

        Tracer tracer = this.tracing.tracer();
        String spanName = toLowerHyphen(pjp.getSignature().getName());
        Span span = tracer.newTrace().name(spanName);
        try (Tracer.SpanInScope ws = tracer.withSpanInScope(span.start())) {
            span.tag(CLASS_KEY, pjp.getTarget().getClass().getSimpleName());
            span.tag(METHOD_KEY, pjp.getSignature().getName());
            return pjp.proceed();
        } catch (Throwable ex) {
            String message = ex.getMessage() == null ? ex.getClass().getSimpleName() : ex.getMessage();
            span.tag("error", message);
            throw ex;
        } finally {
            span.finish();
        }
    }

}
