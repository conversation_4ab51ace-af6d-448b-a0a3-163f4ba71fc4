package com.lyy.user.infrastructure.listener;

import com.rabbitmq.client.AMQP;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessageProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * 权益变更消息重试
 */
public abstract class AbstractBenefitAdjustRetry {


    protected int getRetryTimes(Message message) {
        // 重消息头中获取重试次数
        return Integer.parseInt(String.valueOf(message.getMessageProperties().getHeaders().getOrDefault("retryTimes","1")));
    }


    protected AMQP.BasicProperties convertBasicProperties(MessageProperties messageProperties, int retryTimes) {
        Map<String, Object> headers = new HashMap<>(2);
        headers.put("retryTimes", retryTimes);
        return new AMQP.BasicProperties(
                messageProperties.getContentType(),
                messageProperties.getContentEncoding(),
                headers,
                MessageDeliveryMode.toInt(messageProperties.getReceivedDeliveryMode()),
                messageProperties.getPriority(),
                messageProperties.getCorrelationId(),
                messageProperties.getReplyTo(),
                messageProperties.getExpiration(),
                messageProperties.getMessageId(),
                messageProperties.getTimestamp(),
                messageProperties.getType(),
                messageProperties.getUserId(),
                messageProperties.getAppId(),
                messageProperties.getClusterId()
        );
    }
}
