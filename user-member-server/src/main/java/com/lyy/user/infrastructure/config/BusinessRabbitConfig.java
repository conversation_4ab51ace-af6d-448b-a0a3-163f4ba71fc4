package com.lyy.user.infrastructure.config;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 设备启动Rabbitmq配置
 */
@Slf4j
@Configuration
@Getter
public class BusinessRabbitConfig {

    @Value("${spring.rabbitmq.pay.host}")
    private String host;
    @Value("${spring.rabbitmq.pay.port}")
    private int port;
    @Value("${spring.rabbitmq.pay.username}")
    private String username;
    @Value("${spring.rabbitmq.pay.password}")
    private String password;
    @Value("${spring.rabbitmq.pay.listener.concurrency:5}")
    public int concurrency;
    @Value("${spring.rabbitmq.pay.listener.max-concurrency:10}")
    public int maxConcurrency;
    @Value("${spring.rabbitmq.pay.listener.prefetch:50}")
    public int prefetch;

    /**
     * 权益回退延时队列
     */
    @Value("${user.member.rabbit.benefit-rollback-queue}")
    private String benefitRollbackDelayedQueue;

    /**
     * 权益回退延时exchange
     */
    @Value("${user.member.rabbit.benefit-rollback-exchange}")
    private String benefitRollbackDelayedExchange;

    /**
     * 权益回退延时route
     */
    @Value("${user.member.rabbit.benefit-rollback-route}")
    private String benefitRollbackDelayedRoute;

    /**
     * 权益变更队列
     */
    @Value("${user.member.rabbit.benefit-adjust-queue}")
    private String benefitAdjustQueue;

    /**
     * 权益消耗队列
     */
    @Value("${user.member.rabbit.benefit-consume-queue:cn.lyy.usermember.benefit-consume-queue}")
    private String benefitConsumeQueue;

    @Value("${user.member.rabbit.benefit-adjust-exchange}")
    private String benefitAdjustExchange;

    @Value("${user.member.rabbit.benefit-adjust-rote}")
    private String benefitAdjustRote;

    @Value("${user.member.rabbit.benefit-consume-rote:cn.lyy.usermember.benefit-consume-rote}")
    private String benefitConsumeRote;


    @Value("${user.member.rabbit.benefit-adjust-retry-queue}")
    private String benefitAdjustRetryQueue;

    @Value("${user.member.rabbit.benefit-adjust-retry-exchange}")
    private String benefitAdjustRetryExchange;


    /**
     * 权益调整队列-由新会员服务发送信息
     */
    @Getter
    @Value("${user.member.rabbit.benefit-adjust-new-queue:cn.lyy.usermember.benefit-adjust-new-queue}")
    private String benefitAdjustNewQueue;
    /**
     * 权益调整交换机-由新会员服务发送信息
     */
    @Getter
    @Value("${user.member.rabbit.benefit-adjust-new-exchange:cn.lyy.usermember.benefit-adjust-new-exchange}")
    private String benefitAdjustNewExchange;
    /**
     * 权益调整路由-由新会员服务发送信息
     */
    @Getter
    @Value("${user.member.rabbit.benefit-adjust-new-rote:cn.lyy.usermember.benefit-adjust-new-rote}")
    private String benefitAdjustNewRote;


    @Bean("businessConnectionFactory")
    public ConnectionFactory businessConnectionFactory() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory(host, port);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setVirtualHost("/");
        connectionFactory.setPublisherConfirms(true);
        return connectionFactory;
    }

    @Bean("businessRabbitAdmin")
    public RabbitAdmin rabbitAdmin(@Qualifier("businessConnectionFactory") ConnectionFactory connectionFactory) {
        return new RabbitAdmin(connectionFactory);
    }


    /**
     * 权益回退延时消息监听工厂
     *
     * @param connectionFactory
     * @return
     */
    @Bean(name = "benefitRollbackDelayedRabbitListenerContainerFactory")
    public SimpleRabbitListenerContainerFactory benefitRollbackDelayedRabbitListenerContainerFactory(@Qualifier("businessConnectionFactory") ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        return factory;
    }

    @Bean("benefitRollbackDelayedTemplate")
    public RabbitTemplate rabbitTemplate(@Qualifier("businessConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(new Jackson2JsonMessageConverter());
        return template;
    }

    /**
     * 初始化延迟队列
     *
     * @return
     */
    @Bean
    public Queue benefitRollbackDelayedQueue() {
        return new Queue(benefitRollbackDelayedQueue, true);
    }

    /**
     * 定义一个延迟交换机
     *
     * @return
     */
    @Bean
    public CustomExchange benefitRollbackDelayedExchange() {
        Map<String, Object> args = new HashMap<>(1);
        args.put("x-delayed-type", "direct");
        return new CustomExchange(benefitRollbackDelayedExchange, "x-delayed-message", true, false, args);
    }

    /**
     * 绑定队列到这个延迟交换机上
     *
     * @return
     */
    @Bean
    public Binding bindingNotify() {
        return BindingBuilder.bind(benefitRollbackDelayedQueue()).to(benefitRollbackDelayedExchange())
                .with(benefitRollbackDelayedRoute).noargs();
    }

    @Bean
    public Queue benefitAdjustQueue(){
        return new Queue(benefitAdjustQueue,true);
    }

    @Bean
    public Queue benefitConsumeQueue(){
        return new Queue(benefitConsumeQueue,true);
    }

    @Bean
    public TopicExchange benefitAdjustExchange(){
        return new TopicExchange(benefitAdjustExchange);
    }

    @Bean
    public Binding benefitAdjustBinding(){
        return BindingBuilder.bind(benefitAdjustQueue())
                .to(benefitAdjustExchange()).with(benefitAdjustRote);
    }

    @Bean
    public Binding benefitConsumeBinding(){
        return BindingBuilder.bind(benefitConsumeQueue())
                .to(benefitAdjustExchange()).with(benefitConsumeRote);
    }

    @Bean
    public TopicExchange benefitAdjustRetryExchange() {
        return new TopicExchange(benefitAdjustRetryExchange);
    }

    /**
     * 重试队列
     * @return
     */
    @Bean
    public Queue benefitAdjustRetryQueue() {
        Map<String, Object> args = new HashMap<>(2);
        //绑定原来的交换机
        args.put("x-dead-letter-exchange", benefitAdjustExchange);
        args.put("x-message-ttl", 5 * 1000);
        return new Queue(benefitAdjustRetryQueue, true, false, false, args);
    }

    @Bean
    public Binding benefitAdjustRetryBinding() {
        return BindingBuilder.bind(benefitAdjustRetryQueue())
                .to(benefitAdjustRetryExchange()).with(benefitAdjustRote);
    }

    /**
     * 新会员相关对接队列，交换机及重试相关队列
     * @return
     */
    @Bean
    public Queue benefitAdjustNewQueue(){
        return new Queue(benefitAdjustNewQueue,true);
    }

    @Bean
    public TopicExchange benefitAdjustNewExchange(){
        return new TopicExchange(benefitAdjustNewExchange);
    }

    @Bean
    public Binding benefitAdjustNewBinding(){
        return BindingBuilder.bind(benefitAdjustNewQueue())
                .to(benefitAdjustNewExchange()).with(benefitAdjustNewRote);
    }


}
