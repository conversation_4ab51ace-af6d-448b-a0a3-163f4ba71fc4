package com.lyy.user.infrastructure.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员规则的有效期类型
 * <AUTHOR>
 * @className: MemberRuleExpiryDateCategoryEnum
 * @date 2021/3/29
 */
@AllArgsConstructor
@Getter
public enum MemberRuleExpiryDateCategoryEnum {
    //
    EXPIRY_DATE_CATEGORY_INDEFINITE_DURATION("0","无限期"),
    EXPIRY_DATE_CATEGORY_DATE("1","日期区间可用"),
    EXPIRY_DATE_CATEGORY_DATE_OF_TIME("2","单日时间区间可用"),
    ;


    private final String value;
    private final String name;

}
