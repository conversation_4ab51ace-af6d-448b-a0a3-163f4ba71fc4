package com.lyy.user.infrastructure.listener;

import cn.lyy.base.utils.converter.CommonConverterTools;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lyy.user.account.infrastructure.account.dto.ConsumeDTO;
import com.lyy.user.account.infrastructure.account.dto.ConsumeMessageDTO;
import com.lyy.user.application.account.AccountService;
import com.lyy.user.application.statistics.StatisticsService;
import com.lyy.user.infrastructure.config.BusinessRabbitConfig;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 权益消耗mq 消息消费
 * <AUTHOR>
 * @create 2021/8/9 10:25
 */
@Component
@Slf4j
public class BenefitConsumeListener extends AbstractBenefitAdjustRetry {
    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private AccountService accountService;

    @Autowired
    private StatisticsService statisticsService;

    @Value("${user.member.rabbit.benefit.delay.count:3}")
    private Integer totalFailCount;

    @Autowired
    private BusinessRabbitConfig rabbitConfig;

    @RabbitListener(queues = "${user.member.rabbit.benefit-consume-queue:cn.lyy.usermember.benefit-consume-queue}",
            containerFactory = "benefitRollbackDelayedRabbitListenerContainerFactory",
            autoStartup = "${user.member.mq.autoStartup:true}")
    public void handler(Message message, Channel channel){
        try{
            log.debug("权益消耗监听:{}",message);
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            mapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);

            //反序列化实例
            ConsumeMessageDTO consumeMessageDTO = mapper.readValue(message.getBody(), ConsumeMessageDTO.class);
            int retryTimes = getRetryTimes(message);
            if(retryTimes > totalFailCount){
                log.error("权益变更监听,失败次数为:{}",retryTimes);
            }else {
                try{
                    benefitConsume(consumeMessageDTO);
                }catch (Exception e){
                    //重试
                    channel.basicPublish(rabbitConfig.getBenefitAdjustRetryExchange(),
                            rabbitConfig.getBenefitConsumeRote(),
                            convertBasicProperties(message.getMessageProperties(), retryTimes + 1),
                            message.getBody());
                }
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }finally {
            try {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } catch (IOException e) {
                log.error("#===> 权益回退处理延时队列确认消息消费异常");
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 权益变更处理
     * @param consumeMessageDTO
     */
//    @Transactional(rollbackFor = Exception.class)
    public void benefitConsume(ConsumeMessageDTO consumeMessageDTO){
        ConsumeDTO consumeDTO = CommonConverterTools.convert(ConsumeDTO.class,consumeMessageDTO);
        accountService.benefitConsume(consumeDTO);

        if(Boolean.TRUE.equals(consumeMessageDTO.getStaticsFlag())){
            //TODO 后期待完成，目前用不上
//            UserStatisticsUpdateDTO updateDTO = new UserStatisticsUpdateDTO();
//            updateDTO.setMerchantId(consumeDTO.getMerchantId());
//            updateDTO.setUserId(consumeDTO.getUserId());
//            updateDTO.setTotalCoins(adjustMessage.getTotalCoins());
//            updateDTO.setBalanceCoins(adjustMessage.getBalanceCoins());
//            updateDTO.setUpdateTime(adjustMessage.getUpdateTime());
//            statisticsService.updateStatistics(updateDTO);
        }

    }
}
