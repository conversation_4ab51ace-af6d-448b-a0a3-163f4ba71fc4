package com.lyy.user.infrastructure.mq.listen;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lyy.idempotent.core.exception.action.CompletedActionException;
import com.lyy.idempotent.core.support.IdempotentTemplateBuilder;
import com.lyy.rocketmq.process.MessageBusiness;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsUpdateDTO;
import com.lyy.user.application.statistics.StatisticsService;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 会员统计更新监听
 */
@Slf4j
@Component
public class UserStatisticsUpdateListener implements MessageBusiness {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private IdempotentTemplateBuilder idempotentTemplateBuilder;

    @Resource
    private StatisticsService statisticsService;

    @Override
    public Action doBusiness(Message message, ConsumeContext consumeContext) {
        try {
            UserStatisticsUpdateDTO dto = objectMapper.readValue(message.getBody(), UserStatisticsUpdateDTO.class);
            log.info("会员统计更新消息:{}", dto);
            if (Objects.isNull(dto)) {
                return Action.CommitMessage;
            }
            statisticsService.updateStatisticsWithIdempotent(dto, false);
            return Action.CommitMessage;
        } catch (CompletedActionException completedActionException) {
            log.warn("[{}]已经执行完成，忽略错误继续执行", completedActionException.getAction().getActionId());
            return Action.CommitMessage;
        } catch (Exception e) {
            log.error("会员统计更新异常:{}", e.getMessage(), e);
            return Action.ReconsumeLater;
        }
    }
}
