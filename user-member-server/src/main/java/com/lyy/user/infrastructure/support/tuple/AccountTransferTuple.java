package com.lyy.user.infrastructure.support.tuple;

import com.lyy.user.domain.account.entity.Account;

/**
 * <AUTHOR>
 * @since 2022/1/12
 */
public class AccountTransferTuple extends Tuple {
    public AccountTransferTuple(Account out, Account in) {
        super(new Object[]{out, in});
    }

    public Account getOutAccount() {
        return (Account) get(0);
    }

    public Account getInAccount() {
        return (Account) get(1);
    }
}
