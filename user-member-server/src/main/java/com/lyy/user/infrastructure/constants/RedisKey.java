package com.lyy.user.infrastructure.constants;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/2 14:02
 */
public interface RedisKey {

    /**
     * 账户权益更新锁
     */
    String ACCOUNT_BENEFIT_UPDATE_LOCK = "usermember:account:benefitupdate:";

    /**
     * 账户权益更新锁-用户组成
     */
    String ACCOUNT_BENEFIT_USER_UPDATE_LOCK = "usermember:account:user:benefitupdate:";

    /**
     * 用户统计更新锁
     */
    String STATISTICS_UPDATE_LOCK = "usermember:statistics:update:";


    /**
     * 会员组降级处理锁
     */
    String MEMBER_LIFTING_JUDGE_FALSE_LOCK="usermember:member:judge:false:";

    String MEMBER_TAG_CREATE_LOCK="usermember:tag:create:";

    String MEMBER_TAG_UPDATE_LOCK="usermember:tag:update:";

    /**
     * 全选用户绑定锁
     */
    String TAG_USER_All_BINDING_LOCK="usermember:tag:user:binding:";
    /**
     * 全选用户解绑锁
     */
    String TAG_USER_ALL_UNBINDING_LOCK="usermember:tag:user:unbinding:";

    /**
     * 消耗抵扣缓存
     */
    String MEMBER_CONSUME_CACHE = "usermember:account:consumecahe:";

    /**
     * 商家券同步处理锁
     */
    String ACCOUNT_COUPON_SYNCHRONIZATION_LOCK = "usermember:account:coupon:synchronization:";


    /**
     * 支付充值退款回调退余额余币 更新锁
     */
    String ACCOUNT_BENEFIT_PAY_REFUND_ROLLBACK_LOCK = "usermember:account:benefit:pay.refund:";

    /**
     * 商户批量派发权益锁
     */
    String MERCHANT_PAYOUT_BENEFIT_LOCK = "usermember:merchant:payout:benefit:";

    /**
     * 商户会员组存在标记
     */
    String MEMBER_GROUP_FLAG = "usermember:group:flag:";
}
