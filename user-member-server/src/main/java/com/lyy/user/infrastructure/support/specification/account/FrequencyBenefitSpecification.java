package com.lyy.user.infrastructure.support.specification.account;

import com.lyy.user.account.infrastructure.constant.AccountBenefitNumTypeEnum;
import com.lyy.user.infrastructure.support.specification.AbstractCompositeSpecification;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022/1/18
 */
public class FrequencyBenefitSpecification extends AbstractCompositeSpecification<Integer> {
    @Override
    public boolean isSatisfiedBy(Integer numType) {
        return Objects.equals(numType, AccountBenefitNumTypeEnum.FREQUENCY.getCode());
    }
}
