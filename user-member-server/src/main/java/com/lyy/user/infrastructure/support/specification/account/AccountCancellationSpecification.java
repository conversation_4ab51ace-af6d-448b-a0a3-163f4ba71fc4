package com.lyy.user.infrastructure.support.specification.account;

import static com.lyy.error.member.infrastructure.AccountErrorCode.ACCOUNT_CANCELLATION_ERROR;

import com.lyy.user.account.infrastructure.constant.AccountStatusEnum;
import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.repository.account.SmallVenueAccountRepository;
import com.lyy.user.infrastructure.support.specification.AbstractCompositeSpecification;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

/**
 * 注销校验
 *
 * <AUTHOR>
 * @since 2022/1/13
 */
@Slf4j
public class AccountCancellationSpecification extends AbstractCompositeSpecification<Account> {

    private final SmallVenueAccountRepository accountRepository;

    public AccountCancellationSpecification(SmallVenueAccountRepository accountRepository) {
        this.accountRepository = accountRepository;
    }

    @Override
    public boolean isSatisfiedBy(Account account) {
        if (Objects.equals(account.getStatus(), AccountStatusEnum.CANCELLATION.getStatus())) {
            log.warn("[小场地会员账户]--当前账号已注销, {}, account: {}", account.getStatus(), account);
            throw new BusinessException(ACCOUNT_CANCELLATION_ERROR.getCode(), "账户已注销");
        }
        if (Objects.isNull(account.getParentAccountId())) {
            List<Account> subAccounts = accountRepository.listSubAccount(account.getMerchantId(), account.getId());
            for (Account subAccount : subAccounts) {
                if (!Objects.equals(subAccount.getStatus(), AccountStatusEnum.CANCELLATION.getStatus())) {
                    log.warn("[小场地会员账户]--存在未注销的子账户: {}", subAccount);
                    throw new BusinessException(ACCOUNT_CANCELLATION_ERROR.getCode(), "存在未注销的子账户");
                }
            }
        }
        return true;
    }
}
