package com.lyy.user.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.LocalDateTimeTypeHandler;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * <AUTHOR>
 * @create 2021/4/21 10:11
 */
@Slf4j
public class MyLocalDateTimeTypeHandler extends LocalDateTimeTypeHandler {

    @Override
    public LocalDateTime getResult(ResultSet rs, String columnName) throws SQLException {
        Object object = rs.getObject(columnName);
        log.info("object:{}",object);
        //在这里强行转换，将sql的时间转换为LocalDateTime
        if(object instanceof java.sql.Timestamp){
//可以根据自己的需要进行转化
            return LocalDateTime
                    .ofInstant(((Timestamp)object).toInstant(), ZoneOffset.ofHours(0));
        }
        return super.getResult(rs, columnName);
    }
}