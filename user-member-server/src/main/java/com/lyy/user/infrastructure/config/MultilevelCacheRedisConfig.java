package com.lyy.user.infrastructure.config;

import cn.lyy.cache.serializer.KryoRedisSerializer;
import cn.lyy.cache.serializer.StringRedisSerializer;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.lettuce.core.TimeoutOptions;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisNode;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.Set;

import static com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils.split;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;


/**
 * <AUTHOR>
 * <p>
 * 多级缓存redis配置
 */
@Configuration
public class MultilevelCacheRedisConfig {

    @Value("${cache.redis.cluster.nodes}")
    private String nodes;

    @Value("${cache.redis.cluster.max-redirects}")
    private Integer maxRedirects;

    @Value("${cache.redis.password}")
    private String password;

    @Value("${cache.redis.pool.max-idle:10}")
    private Integer maxIdle;

    @Value("${cache.redis.pool.min-idle:5}")
    private Integer minIdle;

    @Value("${cache.redis.pool.max-total:10}")
    private Integer maxTotal;

    @Value("${cache.redis.pool.max-wait:1000}")
    private Integer maxWait;

    public RedisConnectionFactory cacheRedisConnectionFactory() {
        // 集群
        RedisClusterConfiguration configuration = new RedisClusterConfiguration();
        Set<String> hostAndPortSet = StringUtils.commaDelimitedListToSet(nodes);
        hostAndPortSet.forEach(hostAndPort -> {
            configuration.addClusterNode(readHostAndPortFromString(hostAndPort));
        });
        configuration.setMaxRedirects(maxRedirects);
        configuration.setPassword(password);
        // 连接池
        GenericObjectPoolConfig<Object> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMinIdle(minIdle);
        poolConfig.setMaxTotal(maxTotal);
        poolConfig.setMaxWaitMillis(maxWait);
        ClusterTopologyRefreshOptions topologyRefreshOptions = ClusterTopologyRefreshOptions.builder()
                //开启自适应刷新
                .enableAllAdaptiveRefreshTriggers()
                // 自适应刷新超时时间(默认30秒)
                .adaptiveRefreshTriggersTimeout(Duration.ofSeconds(25))
                // 开周期刷新
                .enablePeriodicRefresh(Duration.ofSeconds(30))
                .build();

        ClusterClientOptions clusterClientOptions = ClusterClientOptions.builder()
                //redis命令超时时间,超时后才会使用新的拓扑信息重新建立连接
                .timeoutOptions(TimeoutOptions.enabled(Duration.ofSeconds(10)))
                .topologyRefreshOptions(topologyRefreshOptions)
                .build();
        LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder builder = LettucePoolingClientConfiguration.builder();
        builder.poolConfig(poolConfig);
        builder.clientOptions(clusterClientOptions);
        builder.build();
        LettuceConnectionFactory connectionFactory = new LettuceConnectionFactory(configuration, builder.build());
        connectionFactory.afterPropertiesSet();
        return connectionFactory;
    }

    private RedisNode readHostAndPortFromString(String hostAndPort) {
        String[] args = ofNullable(split(hostAndPort, ":"))
                .orElseThrow(() -> new IllegalArgumentException("Host And Port need to be separated by  ':'."));
        of(args).filter(s -> s.length == 2)
                .orElseThrow(() -> new IllegalArgumentException("Host and Port String needs to specified as host:port"));
        return new RedisNode(args[0], Integer.parseInt(args[1]));
    }

    @Bean("cacheRedisTemplate")
    public RedisTemplate<String, Object> cacheRedisTemplate() {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(cacheRedisConnectionFactory());

        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<Object>(Object.class);
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        jackson2JsonRedisSerializer.setObjectMapper(om);

        KryoRedisSerializer<Object> kryoRedisSerializer = new KryoRedisSerializer<>(Object.class);

        // 设置值（value）的序列化采用KryoRedisSerializer。
        redisTemplate.setValueSerializer(kryoRedisSerializer);
        redisTemplate.setHashValueSerializer(kryoRedisSerializer);
        // 设置键（key）的序列化采用StringRedisSerializer。
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());

        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }
}
