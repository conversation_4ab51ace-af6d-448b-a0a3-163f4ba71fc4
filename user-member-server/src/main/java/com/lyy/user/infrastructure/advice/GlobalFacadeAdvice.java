package com.lyy.user.infrastructure.advice;

import static java.util.Optional.ofNullable;

import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.idempotent.core.exception.action.CompletedActionException;
import com.lyy.klock.handler.KlockTimeoutException;
import com.lyy.user.account.infrastructure.resp.ErrorBody;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.execption.StateCodeException;
import java.lang.reflect.UndeclaredThrowableException;
import java.net.ConnectException;
import java.nio.charset.Charset;
import java.sql.SQLDataException;
import java.sql.SQLException;
import java.sql.SQLSyntaxErrorException;
import java.sql.SQLTransientConnectionException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.util.ContentCachingRequestWrapper;

/**
 * <AUTHOR>
 * 全局Facade通知处理
 * 主要做两个事情 1. 全局异常拦截并处理  2.DTO参数绑定
 */
@Slf4j
@RestControllerAdvice
public class GlobalFacadeAdvice {

    /**
     * INTERNAL_SERVER_ERROR RespCode
     */
    private static final GlobalErrorCode INTERNAL_SERVER_ERROR = GlobalErrorCode.INTERNAL_SERVER_ERROR;
    /**
     * BAD_REQUEST RepsCode
     */
    private static final GlobalErrorCode BAD_REQUEST = GlobalErrorCode.BAD_REQUEST;

    /**
     * 未声明的未知异常
     *
     * @param e UndeclaredThrowableException
     * @return INTERNAL_SERVER_ERROR RespBody
     */
    @ExceptionHandler(value = UndeclaredThrowableException.class)
    public RespBody<?> throwable(UndeclaredThrowableException e) {
        log.error(e.getMessage(), e);
        log.error("{}",  ErrorBody.build(e.getCause()));
        return RespBody.error(INTERNAL_SERVER_ERROR);
    }

    /**
     * 空指针异常
     *
     * @param e NullPointerException
     * @return NULL_POINTER_EXCEPTION RespBody
     */
    @ExceptionHandler(value = NullPointerException.class)
    public RespBody<?> nullPointerException(NullPointerException e) {
        log.error(e.getMessage(), e);
        ErrorBody errorBody = ErrorBody.build(e);
        StackTraceElement ste = e.getStackTrace()[0];
        Map<String, Object> metadata = errorBody.getMetadata();
        metadata.put("position", ste.getClassName() + "#" + ste.getMethodName() + ":" + ste.getLineNumber());
        metadata.put("fileName", ste.getFileName());
        errorBody.setMetadata(metadata);
        log.error("{}",  ErrorBody.build(e.getCause()));
        return RespBody.error(GlobalErrorCode.NULL_POINTER_EXCEPTION);
    }

    /**
     * 锁超时异常
     */
    @ExceptionHandler(value = KlockTimeoutException.class)
    public RespBody<?> lockTimeoutException(KlockTimeoutException e) {
        return RespBody.error(BAD_REQUEST, "请求过于频繁，请稍后重试");
    }


    /**
     * 异常级别
     */
    @ExceptionHandler(value = {Exception.class})
    public RespBody<?> exception(HttpServletRequest req, Exception e) {
        printInputParam(req);
        log.error(e.getMessage(), e);
        return RespBody.error(INTERNAL_SERVER_ERROR, "服务内部运行异常");
    }

    /**
     * http 异常
     */
    @ExceptionHandler(value = HttpStatusCodeException.class)
    public RespBody<?> httpStatusCodeException(HttpStatusCodeException e) {
        ErrorBody errorBody = ErrorBody.build(e);
        HttpStatus statusCode = e.getStatusCode();
        Map<String, Object> metadata = errorBody.getMetadata();
        metadata.put("statusCode", statusCode.value());
        metadata.put("RespBody", e.getResponseBodyAsString());
        errorBody.setMetadata(metadata);
        log.error("{}", errorBody);
        return RespBody.error(String.valueOf(statusCode.value()), statusCode.getReasonPhrase());
    }

    /**
     * 不支持的请求媒介类型
     */
    @ExceptionHandler(value = HttpMediaTypeNotSupportedException.class)
    public RespBody<?> httpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e, HttpServletRequest request) {
        ErrorBody errorBody = ErrorBody.build(e);
        List<MediaType> supportedMediaTypes = e.getSupportedMediaTypes();
        List<String> support = new ArrayList<>();
        for (MediaType mediaType : supportedMediaTypes) {
            support.add(mediaType.getType());
        }
        Map<String, Object> metadata = errorBody.getMetadata();
        MediaType contentType = e.getContentType();
        if (contentType != null) {
            metadata.put("contentType", contentType.getSubtype());
        }
        metadata.put("support", support);
        errorBody.setMetadata(metadata);

        log.error("errorBody:{}, url:{}", errorBody, ofNullable(request).map(HttpServletRequest::getRequestURI).orElse(""));
        return RespBody.error(GlobalErrorCode.UNSUPPORTED_MEDIA_TYPE);
    }

    /**
     * 请求方法不支持
     */
    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    public RespBody<?> httpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        ErrorBody errorBody = ErrorBody.build(e);
        Map<String, Object> metadata = errorBody.getMetadata();
        metadata.put("method", e.getMethod());
        metadata.put("supportedMethods", e.getSupportedMethods());
        log.error("errorBody:{}, url:{}", errorBody, ofNullable(request).map(HttpServletRequest::getRequestURI).orElse(""));
        return RespBody.error(GlobalErrorCode.METHOD_NOT_ALLOWED, errorBody);
    }

    /**
     * 请求参数(Parameter)缺失
     */
    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    public RespBody<?> missingServletRequestParameterException(MissingServletRequestParameterException e, HttpServletRequest request) {
        ErrorBody errorBody = ErrorBody.build(e);
        errorBody.getMetadata().put("parameterName", e.getParameterName());
        log.error("errorBody:{}, url:{}", errorBody, ofNullable(request).map(HttpServletRequest::getRequestURI).orElse(""));
        return RespBody.error(BAD_REQUEST);
    }

    /**
     * 数据读取异常
     */
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    public RespBody<?> httpMessageNotReadableException(HttpMessageNotReadableException e) {
        ErrorBody errorBody = ErrorBody.build(e);
        if (e.getCause() instanceof InvalidFormatException) {
            InvalidFormatException fe = (InvalidFormatException) e.getCause();
            errorBody.getMetadata().put("requiredType", fe.getTargetType().getName());
            errorBody.getMetadata().put("targetValue", fe.getValue());
        }
        log.error("{}", errorBody);
        return RespBody.error(BAD_REQUEST);
    }

    /**
     * 请求参数(RequestPart)缺失
     */
    @ExceptionHandler(value = MissingServletRequestPartException.class)
    public RespBody<?> missingServletRequestPartException(MissingServletRequestPartException e, HttpServletRequest request) {
        ErrorBody errorBody = ErrorBody.build(e);
        errorBody.getMetadata().put("partName", e.getRequestPartName());
        log.error("errorBody:{}, url:{}", errorBody, ofNullable(request).map(HttpServletRequest::getRequestURI).orElse(""));
        return RespBody.error(BAD_REQUEST);
    }

    /**
     * 参数转换异常
     */
    @ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
    public RespBody<?> methodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e,HttpServletRequest request) {
        ErrorBody errorBody = ErrorBody.build(e);
        Map<String, Object> metadata = errorBody.getMetadata();
        Class<?> requiredType = e.getRequiredType();
        if (requiredType != null) {
            metadata.put("requiredType", requiredType.getName());
        }
        metadata.put(e.getName(), e.getValue());
        log.error("errorBody:{}, url:{}, paramMap: {}", errorBody, ofNullable(request).map(HttpServletRequest::getRequestURI).orElse(""),
                ofNullable(request).map(HttpServletRequest::getParameterMap).orElse(null));
        return RespBody.error(BAD_REQUEST);
    }

    /**
     * 参数验证异常
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public RespBody<?> methodArgumentNotValidException(MethodArgumentNotValidException e) {
        ErrorBody errorBody = ErrorBody.build(e);
        if (e.getBindingResult().getFieldError() != null) {
            FieldError fieldError = e.getBindingResult().getFieldError();
            if (fieldError != null) {
                errorBody.getMetadata().put(fieldError.getField(), fieldError.getDefaultMessage());
                errorBody.setMessage(fieldError.getDefaultMessage());
            }
        }
        log.warn("参数校验异常：{}", errorBody);
        return RespBody.error(BAD_REQUEST, errorBody.getMessage());
    }

    /**
     * 参数验证异常
     */
    @ExceptionHandler(value = BindException.class)
    public RespBody<?> bindException(BindException e) {
        log.error(e.getMessage(), e);
        ErrorBody errorBody = ErrorBody.build(e);
        BindingResult bindingResult = e.getBindingResult();
        List<ObjectError> list = bindingResult.getAllErrors();
        List<Map<String, Object>> dataList = new ArrayList<>(list.size());
        Map<String, Object> fieldError = new TreeMap<>();
        String defaultMessage = null;
        for (ObjectError error : list) {
            if (error instanceof FieldError) {
                FieldError field = (FieldError) error;
                defaultMessage = field.getDefaultMessage();
                fieldError.put(field.getField(), field.getRejectedValue());
                dataList.add(fieldError);
            }
        }
        errorBody.getMetadata().put("errorList", dataList);
        int length = 100;
        // 提示大于100个字符就取参数错误提示
        if (defaultMessage == null || defaultMessage.length() > length) {
            defaultMessage = BAD_REQUEST.getMessage();
        }
        log.error("{}", errorBody);
        return RespBody.error(BAD_REQUEST, defaultMessage);
    }

    /**
     * 无效请求头异常
     */
    @ExceptionHandler(value = MissingRequestHeaderException.class)
    public RespBody<?> missingRequestHeaderException(MissingRequestHeaderException e, HttpServletRequest request) {
        ErrorBody errorBody = ErrorBody.build(e);
        errorBody.getMetadata().put("headerName", e.getHeaderName());
        log.error("errorBody:{}, url:{}", errorBody, ofNullable(request).map(HttpServletRequest::getRequestURI).orElse(""));
        return RespBody.error(GlobalErrorCode.MISSING_REQ_HEADER);
    }

    /**
     * SQL 异常
     */
    @ExceptionHandler(value = SQLException.class)
    public RespBody<?> sqlException(SQLException e) {
        log.error(e.getMessage(), e);
        return RespBody.error(GlobalErrorCode.SQL_EXCEPTION);
    }

    /**
     * 服务连接异常
     */
    @ExceptionHandler(value = ConnectException.class)
    public RespBody<?> connectException(ConnectException e) {
        log.error("{}", ErrorBody.build(e));
        return RespBody.build(GlobalErrorCode.CONNECT_EXCEPTION, null);
    }

    /**
     * 数据库SQL语法错误
     */
    @ExceptionHandler(value = {SQLSyntaxErrorException.class, SQLDataException.class})
    public RespBody<?> sqlSyntaxErrorException(Exception e) {
        log.error(e.getMessage(), e);
        return RespBody.build(GlobalErrorCode.SQL_EXCEPTION,e.getMessage());
    }


    /**
     * 服务器数据库发生异常
     */
    @ExceptionHandler(value = {SQLTransientConnectionException.class})
    public RespBody<?> sqlTransientConnectionException(Exception e) {
        log.error(e.getMessage(), e);
        return RespBody.build(GlobalErrorCode.DATABASE_EXCEPTION, null);
    }

    /**
     * 类型转换异常
     */
    @ExceptionHandler(value = {ClassCastException.class})
    public RespBody<?> classCastException(ClassCastException e) {
        log.error(e.getMessage(), e);
        return RespBody.build(INTERNAL_SERVER_ERROR, null);
    }

    /**
     * 自定义 状态码异常
     */
    @ExceptionHandler(value = StateCodeException.class)
    public RespBody<?> stateCodeException(StateCodeException e) {
        log.warn(e.getMessage(), e);
        return RespBody.build(e.getStateCode(), e.getMessage(), null);
    }


    /**
     * 自定义 业务异常
     */
    @ExceptionHandler(value = BusinessException.class)
    public RespBody<?> businessException(HttpServletRequest req, BusinessException e) {
        printInputParam(req);
        // 堆栈异常 可以不输出，待系统稳定之后屏蔽此输出
        log.warn(e.getMessage(), e);
        return RespBody.build(e.getStateCode(), e.getMessage(), null);
    }

    /**
     * 幂等异常(这里返回正常的响应码)
     */
    @ExceptionHandler(value = CompletedActionException.class)
    public RespBody<?> actionException(CompletedActionException e) {
        log.warn(e.getMessage(), e);
        return RespBody.build(GlobalErrorCode.OK.getCode(), e.getMessage(), null);
    }

    /**
     * 打印入参
     * @param req
     */
    private void printInputParam(HttpServletRequest req) {

        try {
            if (RequestMethod.POST.toString().equalsIgnoreCase(req.getMethod())) {
                String body = null;
                if (req instanceof ContentCachingRequestWrapper) {
                    ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper) req;
                    body = StringUtils.toEncodedString(wrapper.getContentAsByteArray(), Charset.forName(wrapper.getCharacterEncoding()));
                }
                log.info("请求路径：{}，请求body参数：{}", req.getRequestURI(), body);
                printParameterMap(req);
            } else if (RequestMethod.GET.toString().equalsIgnoreCase(req.getMethod())) {
                log.info("请求路径：{}", req.getRequestURI());
                printParameterMap(req);
            }
        }catch (Exception e){
            log.error("body转换失败",e);
        }
    }

    private void printParameterMap(HttpServletRequest req) {
        Map<String, String[]> reqMap = req.getParameterMap();
        for(Map.Entry entry:reqMap.entrySet()){
            log.info("key:{},value:{}",entry.getKey(), Arrays.toString((String[])entry.getValue()));
        }
    }

}
