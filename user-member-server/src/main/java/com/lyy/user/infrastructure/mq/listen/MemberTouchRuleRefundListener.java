package com.lyy.user.infrastructure.mq.listen;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lyy.idempotent.core.exception.action.CompletedActionException;
import com.lyy.idempotent.core.support.IdempotentTemplate;
import com.lyy.idempotent.core.support.IdempotentTemplateBuilder;
import com.lyy.rocketmq.process.MessageBusiness;
import com.lyy.user.account.infrastructure.member.dto.MemberTouchRuleDTO;
import com.lyy.user.application.member.IMemberService;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 用户触发会员规则退回监听
*/
@Slf4j
@Component
public class MemberTouchRuleRefundListener implements MessageBusiness {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private IdempotentTemplateBuilder idempotentTemplateBuilder;

    @Autowired
    private IMemberService memberService;

    @Override
    public Action doBusiness(Message message, ConsumeContext consumeContext) {
        try {
            MemberTouchRuleDTO dto = objectMapper.readValue(message.getBody(), MemberTouchRuleDTO.class);
            log.info("用户触发会员规则消息:{}", dto);
            if (Objects.isNull(dto)) {
                return Action.CommitMessage;
            }
            IdempotentTemplate idempotentTemplate = idempotentTemplateBuilder.build();
            idempotentTemplate
                // 构建业务key
                .key(dto.getOutTradeNo())
                // 关闭严格模式
                .strict(false)
                .scene("MemberController#removeMemberOfRule")
                // 执行业务方法
                .execute(() -> memberService.removeMemberOfRule(dto));
            return Action.CommitMessage;
        } catch (CompletedActionException completedActionException) {
            log.warn("[{}]已经执行完成，忽略错误继续执行", completedActionException.getAction().getActionId());
            return Action.CommitMessage;
        } catch (Exception e) {
            log.error("用户触发会员规则退回异常:{}", e.getMessage(), e);
            return Action.ReconsumeLater;
        }
    }
}
