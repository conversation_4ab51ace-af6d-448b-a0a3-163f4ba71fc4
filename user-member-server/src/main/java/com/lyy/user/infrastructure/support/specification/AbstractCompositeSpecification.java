package com.lyy.user.infrastructure.support.specification;

/**
 * <AUTHOR>
 * @since 2022/1/11
 */
public abstract class AbstractCompositeSpecification<T> implements Specification<T> {

    @Override
    public Specification<T> and(Specification<T> other) {
        return new AndSpecification<T>(this, other);
    }

    @Override
    public Specification<T> not(Specification<T> other) {
        return new NotSpecification<T>(this);
    }

    @Override
    public Specification<T> or(Specification<T> other) {
        return new OrSpecification<T>(this, other);
    }

}

