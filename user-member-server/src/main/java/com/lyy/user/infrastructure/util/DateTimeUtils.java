package com.lyy.user.infrastructure.util;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Period;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.Date;

import static java.util.Optional.ofNullable;

/**
 * @author: Seraphim
 * @date: 2018/8/6 16:33
 * @description: 时间格式化工具类
 * 优先使用LocalDateTime来处理时间
 */
public class DateTimeUtils {
    /**
     * @param: []
     * @return: java.lang.String
     * @auther: Seraphim
     * @date: 2018/8/10 23:21
     * @description: 获得当前时间的毫秒数
     */
    public static String getCurrentMillisecond() {
        SimpleDateFormat outFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        return outFormat.format(new Date());
    }

    /**
     * @param: [localDateTime, format]
     * @return: java.lang.String
     * @auther: Seraphim
     * @date: 2019/3/28 19:30
     * @description: LocalDateTime格式化
     */
    public static String format(LocalDateTime localDateTime, String format) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(format);
        return dtf.format(localDateTime);
    }

    public static String format(Date date, String format) {
        return new SimpleDateFormat(format).format(date);
    }

    public static LocalDateTime parse(String datetime, String formatter) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(formatter);
        return LocalDateTime.parse(datetime, df);
    }

    /**
     * @param: [time, number, field]
     * @return: java.time.LocalDateTime
     * @auther: Seraphim
     * @date: 2019/8/16 17:55
     * @description: 日期加上一个数, 根据field不同加不同值, field为ChronoUnit.*
     */
    public static LocalDateTime plus(LocalDateTime time, long number, TemporalUnit field) {
        return time.plus(number, field);
    }

    /**
     * @param: [time, number, field]
     * @return: java.time.LocalDateTime
     * @auther: Seraphim
     * @date: 2019/8/16 17:59
     * @description: 日期减去一个数, 根据field不同减不同值, field参数为ChronoUnit.*
     */
    public static LocalDateTime mins(LocalDateTime time, long number, TemporalUnit field) {
        return time.minus(number, field);
    }

    /**
     * @param: [startTime, endTime, field]
     * @return: long
     * @auther: Seraphim
     * @date: 2019/8/16 18:01
     * @description: 获取两个日期的差  field参数为ChronoUnit.*
     */
    public static long betweenTwoTime(LocalDateTime startTime, LocalDateTime endTime, ChronoUnit field) {
        Period period = Period.between(LocalDate.from(startTime), LocalDate.from(endTime));
        if (field == ChronoUnit.YEARS) {
            return period.getYears();
        }
        if (field == ChronoUnit.MONTHS) {
            return period.getYears() * 12 + period.getMonths();
        }
        return field.between(startTime, endTime);
    }

    /**
     * @param: [time]
     * @return: java.time.LocalDateTime
     * @auther: Seraphim
     * @date: 2019/8/16 18:02
     * @description: 获取一天的开始时间
     */
    public static LocalDateTime getDayStart(LocalDateTime time) {
        return time.withHour(0).withMinute(0).withSecond(0).withNano(0);
    }


    /**
     * 获取当前月的第一天开始时间
     *
     * @return
     */
    public static LocalDateTime getMonthStart() {
        return LocalDateTime.now().withDayOfMonth(1).withHour(0)
                .withMinute(0).withSecond(0).withNano(0);
    }

    public static LocalTime parseLocalTimeOrElse(String time) {
        return parseLocalTimeOrElse(time, "23:59:59");
    }

    public static LocalTime parseLocalTimeOrElse(String time, String defaultTime) {
        return LocalTime.parse(ofNullable(time).orElse(defaultTime), DateTimeFormatter.ofPattern("HH:mm:ss"));
    }

    public static LocalDateTime parseLocalDateTimeOrElse(String time) {
        return parseLocalDateTimeOrElse(time, "9999-01-01 00:00:00");
    }

    public static LocalDateTime parseLocalDateTimeOrElse(String time, String defaultTime) {
        return LocalDateTime.parse(ofNullable(time).orElse(defaultTime), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    public static Date localDateTime2Date(LocalDateTime localDateTime) {
        if (null == localDateTime) {
            return null;
        }
        Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }
}
