package com.lyy.user.infrastructure.support.specification.account;

import com.google.common.collect.Lists;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import com.lyy.user.infrastructure.support.specification.AbstractCompositeSpecification;

import java.util.List;

/**
 * 增加权益时需要调整总数的类型
 *
 * <AUTHOR>
 * @since 2022/1/18
 */
public class IncrementChangingTotalSpecification extends AbstractCompositeSpecification<Integer> {
    public static final List<Integer> TYPES = Lists.newArrayList(
            AccountRecordTypeEnum.SV_RECHARGE.getCode(),
            AccountRecordTypeEnum.SV_PRESENTATION.getCode(),
            AccountRecordTypeEnum.SV_GIFT_RECYCLING.getCode()
    );

    @Override
    public boolean isSatisfiedBy(Integer recordType) {
        return TYPES.contains(recordType);
    }
}