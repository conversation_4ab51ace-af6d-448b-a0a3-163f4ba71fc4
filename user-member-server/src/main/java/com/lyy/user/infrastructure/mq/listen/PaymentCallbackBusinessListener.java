package com.lyy.user.infrastructure.mq.listen;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lyy.idempotent.core.exception.action.CompletedActionException;
import com.lyy.rocketmq.process.MessageBusiness;
import com.lyy.user.account.infrastructure.statistics.dto.PaymentCallbackDataDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsUpdateDTO;
import com.lyy.user.application.member.IMemberService;
import com.lyy.user.application.statistics.StatisticsService;
import com.lyy.user.application.user.ITagUserService;
import java.io.IOException;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/7/11 - 14:53
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PaymentCallbackBusinessListener  implements MessageBusiness {

    private final StatisticsService statisticsService;
    private final IMemberService iMemberService;
    private final ITagUserService tagUserService;
    private final ObjectMapper objectMapper;

    @Override
    public Action doBusiness(Message message, ConsumeContext consumeContext) {
        PaymentCallbackDataDTO dto = null;
        try {
            dto = objectMapper.readValue(message.getBody(), PaymentCallbackDataDTO.class);
            if (Objects.isNull(dto)) {
                log.warn("支付回调会员业务处理失败, 消息内容不存在: {}", message.getKey());
                return Action.CommitMessage;
            }
        } catch (IOException e) {
            log.error("支付回调会员业务处理失败, 消息序列化失败: {}", message.getKey(), e);
            return Action.CommitMessage;
        }
        try {
            log.info("支付回调会员业务处理: {}", dto.getOutTradeNo());
            if (Objects.nonNull(dto.getMemberTouchRule())) {
                try {
                    if (Objects.equals(dto.getMemberTouchRule().getUserId(), 0L)) {
                        log.warn("支付回调会员成长值业务处理失败, 用户id为空: {}, dto: {}", dto.getOutTradeNo(), dto);
                        return Action.CommitMessage;
                    }
                    iMemberService.updateMemberOfRuleWithIdempotent(dto.getMemberTouchRule());
                }  catch (CompletedActionException completedActionException) {
                    log.warn("支付回调会员业务-会员成长值-[{}]已经执行完成，忽略错误继续执行", completedActionException.getAction().getActionId());
                }
            }
            if (CollectionUtils.isNotEmpty(dto.getStatistics())) {
                for (UserStatisticsUpdateDTO param : dto.getStatistics()) {
                    try {
                        if (Objects.equals(param.getUserId(), 0L)) {
                            log.warn("支付回调会员统计业务处理失败, 用户id为空: {}, dto: {}", dto.getOutTradeNo(), dto);
                            return Action.CommitMessage;
                        }
                        statisticsService.updateStatisticsWithIdempotent(param, true);
                    } catch (CompletedActionException completedActionException) {
                        log.warn("支付回调会员业务-会员统计-[{}]已经执行完成，忽略错误继续执行", completedActionException.getAction().getActionId());
                    }
                }
            }
            if (Objects.nonNull(dto.getTagUser())) {
                try {
                    tagUserService.taggingUserWithIdempotent(dto.getOutTradeNo(), dto.getTagUser());
                } catch (CompletedActionException completedActionException) {
                    log.warn("支付回调会员业务-打标签-[{}]已经执行完成，忽略错误继续执行", completedActionException.getAction().getActionId());
                }
            }
            return Action.CommitMessage;
        } catch (Exception e) {
            log.error("支付回调会员业务处理失败: {}", dto, e);
            return Action.ReconsumeLater;
        }
    }
}
