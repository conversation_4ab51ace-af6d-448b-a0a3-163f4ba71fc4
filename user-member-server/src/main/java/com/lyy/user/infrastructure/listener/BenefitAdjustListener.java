package com.lyy.user.infrastructure.listener;

import cn.lyy.base.utils.converter.CommonConverterTools;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lyy.klock.annotation.Klock;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.account.dto.BenefitAdjustMessage;
import com.lyy.user.application.account.AccountService;
import com.lyy.user.application.statistics.StatisticsService;
import com.lyy.user.infrastructure.config.BusinessRabbitConfig;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * @ClassName: BenefitAdjustListener
 * @description: 权益变更监听
 * @author: pengkun
 * @date: 2021/07/01
 **/
@Component
@Slf4j
public class BenefitAdjustListener extends AbstractBenefitAdjustRetry {

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private AccountService accountService;

    @Autowired
    private StatisticsService statisticsService;

    @Value("${user.member.rabbit.benefit.delay.count:3}")
    private Integer totalFailCount;

    @Autowired
    private BusinessRabbitConfig rabbitConfig;

    @RabbitListener(queues = "${user.member.rabbit.benefit-adjust-queue}",
            containerFactory = "benefitRollbackDelayedRabbitListenerContainerFactory",
            autoStartup = "${user.member.mq.autoStartup:true}")
    public void handler(Message message, Channel channel){
        try{
            log.debug("权益变更监听:{}",message);
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            mapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);

            //反序列化实例
            BenefitAdjustMessage adjustMessage = mapper.readValue(message.getBody(),BenefitAdjustMessage.class);
            int retryTimes = getRetryTimes(message);
            if(retryTimes > totalFailCount){
                log.error("权益变更监听,失败次数为:{}",retryTimes);
            }else {
                try{
                    BenefitAdjustListener listener = (BenefitAdjustListener)AopContext.currentProxy();
                    listener.benefitAdjust(adjustMessage);
                }catch (Exception e){
                    log.error("调整权益异常,准备重试,msg:{}",e.getMessage());
                    //重试
                    channel.basicPublish(rabbitConfig.getBenefitAdjustRetryExchange(),
                            rabbitConfig.getBenefitAdjustRote(),
                            convertBasicProperties(message.getMessageProperties(), retryTimes + 1),
                            message.getBody());
                }
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }finally {
            try {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } catch (IOException e) {
                log.error("#===> 权益回退处理延时队列确认消息消费异常");
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 权益变更处理
     * @param adjustMessage
     */
//    @Transactional(rollbackFor = Exception.class)
    @Klock(keys = "'user-member:klock:mqBenefitAdjust-'+#adjustMessage.merchantId+'_'+#adjustMessage.userId+'_'+#adjustMessage.classify", waitTime = 5)
    public void benefitAdjust(BenefitAdjustMessage adjustMessage){
        AccountBenefitAdjustDTO adjustDTO = CommonConverterTools.convert(AccountBenefitAdjustDTO.class,adjustMessage);
        if(adjustDTO == null){
            return;
        }
        accountService.benefitAdd(adjustDTO,true);
    }
}
