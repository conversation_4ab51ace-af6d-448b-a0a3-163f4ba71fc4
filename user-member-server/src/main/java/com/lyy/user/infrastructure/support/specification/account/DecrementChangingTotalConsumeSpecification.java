package com.lyy.user.infrastructure.support.specification.account;

import com.google.common.collect.Lists;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import com.lyy.user.infrastructure.support.specification.AbstractCompositeSpecification;

import java.util.List;

/**
 * 减扣权益时需要增加消费记录的类型
 *
 * <AUTHOR>
 * @since 2022/1/18
 */
public class DecrementChangingTotalConsumeSpecification extends AbstractCompositeSpecification<Integer> {

    public static final List<Integer> TYPES = Lists.newArrayList(
            AccountRecordTypeEnum.SV_EQUIPMENT_CONSUMPTION.getCode(),
            AccountRecordTypeEnum.SV_STORED_VALUE_DEDUCTION.getCode(),
            AccountRecordTypeEnum.SV_WITHDRAW_COINS.getCode(),
            AccountRecordTypeEnum.SV_EXCHANGE.getCode()
    );

    @Override
    public boolean isSatisfiedBy(Integer recordType) {
        return TYPES.contains(recordType);
    }
}