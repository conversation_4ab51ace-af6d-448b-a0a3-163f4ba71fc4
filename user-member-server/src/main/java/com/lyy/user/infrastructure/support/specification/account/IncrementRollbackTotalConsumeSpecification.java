package com.lyy.user.infrastructure.support.specification.account;

import com.google.common.collect.Lists;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import com.lyy.user.infrastructure.support.specification.AbstractCompositeSpecification;

import java.util.List;

/**
 * 新增权益时需要回滚消费记录的类型
 *
 * <AUTHOR>
 * @since 2022/1/18
 */
public class IncrementRollbackTotalConsumeSpecification extends AbstractCompositeSpecification<Integer> {

    static final List<Integer> TYPES = Lists.newArrayList(
            AccountRecordTypeEnum.SV_EQUIPMENT_CONSUMPTION_REFUND.getCode(),
            AccountRecordTypeEnum.SV_STORED_VALUE_DEDUCTION_REFUND.getCode(),
            AccountRecordTypeEnum.SV_EXCHANGE_REFUND.getCode()
    );

    @Override
    public boolean isSatisfiedBy(Integer recordType) {
        return TYPES.contains(recordType);
    }
}