package com.lyy.user.infrastructure.mq;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lyy.user.account.infrastructure.mq.dto.BenefitMsgDTO;
import com.lyy.user.infrastructure.config.BusinessRabbitConfig;
import java.util.Objects;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 权益变更消息发送到会员服务去
 * <AUTHOR>
 * @className: OldMemberBenefitSender
 * @date 2021/7/2
 */
@Slf4j
@Component
public class OldMemberBenefitSender {

    @Autowired
    private BusinessRabbitConfig businessRabbitConfig;
    @Autowired
    private AmqpTemplate amqpTemplate;

    private ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 发送权益变动，发送到旧会员中去同步
     * @param benefitMsgDTO
     * @return
     */
    public boolean benefitMsgSender(BenefitMsgDTO benefitMsgDTO){
        if(Objects.isNull(benefitMsgDTO)){
            return false;
        }
        try {
            //rabbitMessageSender 发送消息工具类无法正常使用，只能这边先手动序列化
            String messageId = UUID.randomUUID().toString().replaceAll("-","");
            Message message= MessageBuilder.withBody(this.objectMapper.writeValueAsBytes(benefitMsgDTO))
                    .setContentType("application/json")
                    .setMessageId(messageId)
                    .setHeader("retryTimes", 1)
                    .setDeliveryMode(MessageDeliveryMode.PERSISTENT)
                    .build();
            amqpTemplate.convertAndSend(businessRabbitConfig.getBenefitAdjustNewExchange(),businessRabbitConfig.getBenefitAdjustNewRote(),message);
            if(log.isInfoEnabled()){
                log.debug("发送权益变动到旧会员服务-->exchange:{} ,routeKey:{} ,msg:{} ",
                        businessRabbitConfig.getBenefitAdjustNewExchange(),businessRabbitConfig.getBenefitAdjustNewRote(),benefitMsgDTO);
            }
            return true;
        } catch (Exception e) {
            log.debug("发送权益变动到旧会员服务失败-->{} ",benefitMsgDTO);
            log.error(e.getMessage(),e);
        }
        return false;
    }

}
