package com.lyy.user.infrastructure.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static java.util.Optional.ofNullable;

/**
 * <AUTHOR> {<EMAIL>}
 * @date 2020/7/31 13:51
 * <p>
 * 微信用户信息更新判断
 **/
@Slf4j
public class WxAuthUpdateUtils {

    /**
     * 更新数据日期以及星期限制
     *
     * @param update  最后更新时间
     * @param between 间隔天数
     * @param weeks   限制星期
     * @return
     */
    public static boolean dateBetween(LocalDateTime update, int between, List<Integer> weeks) {
        boolean isWeek = false;
        if(!CollectionUtils.isEmpty(weeks)) {
            isWeek = isWeek(new Date(), weeks);
        }
        LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
        return DateTimeUtils.betweenTwoTime(ofNullable(update).orElse(now), now, ChronoUnit.DAYS) > between && !isWeek;
    }


    /**
     * 判断当前日期是否在现在星期范围内
     *
     * @param dt    日期
     * @param weeks 限制星期范围
     * @return
     */
    private static boolean isWeek(Date dt, List<Integer> weeks) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dt);
        int dayForWeek = 0;
        if (calendar.get(Calendar.DAY_OF_WEEK) == 1) {
            dayForWeek = 7;
        } else {
            dayForWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        }
        return weeks.contains(dayForWeek);
    }

    /**
     * 获取当前时间的调整过的date
     * @param date
     * @param days
     * @return
     */
    public static Date getLocalDateTimeModifyDaysDate(LocalDateTime date ,int  days) {
        LocalDateTime localDateTime = date.plusDays(days);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取当前时间的调整月份的date
     * @param date
     * @return
     */
    public static Date getLocalDateTimeModifyMonthsDate(LocalDateTime date ,int  months) {
        LocalDateTime localDateTime = date.plusMonths(months);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
}
