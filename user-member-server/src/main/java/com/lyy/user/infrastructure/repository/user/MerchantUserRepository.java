package com.lyy.user.infrastructure.repository.user;

import cn.lyy.cache.annotation.Cacheable;
import cn.lyy.cache.annotation.Level2Cache;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MerchantBenefitIncrementDTO;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.domain.user.repository.MerchantUserMapper;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since 2022/1/10
 */
@Repository
public class MerchantUserRepository {

    @Resource
    private MerchantUserMapper merchantUserMapper;


    @Cacheable(value = "multilevel:cache:member:merchantUser", key = "#merchantId  + '-' + #merchantUserId ",
            level2Cache = @Level2Cache(useCacheCenter = false, expireTime = 3, timeUnit = TimeUnit.HOURS, preloadTime = 1, forceRefresh = true))
    public MerchantUser getByIdAndMerchantId(Long merchantUserId, Long merchantId) {
        MerchantUser merchantUser = merchantUserMapper.selectByIdAndMerchantId(merchantUserId, merchantId);
        resetProvinceCity(merchantUser);
        return merchantUser;
    }

    @Cacheable(value = "multilevel:cache:member:user", key = "#userId  + '-' + #merchantId ",
            level2Cache = @Level2Cache(useCacheCenter = false, expireTime = 3, timeUnit = TimeUnit.HOURS, preloadTime = 1, forceRefresh = true))
    public MerchantUser getByUserIdAndMerchantId(Long userId, Long merchantId) {
        MerchantUser merchantUser;
        merchantUser = merchantUserMapper.selectByUserIdAndMerchantId(userId, merchantId);
        resetProvinceCity(merchantUser);
        return merchantUser;
    }

    private void resetProvinceCity(MerchantUser merchantUser) {
        if(merchantUser != null && StringUtils.isNotBlank(merchantUser.getProvinceCity()) && merchantUser.getProvinceCity().contains("null")){
            merchantUser.setProvinceCity("");
        }
    }

    public Optional<MerchantUser> selectBy(Long merchantId, Long userId) {
        MerchantUser merchantUser = merchantUserMapper.selectByUserIdAndMerchantId(userId, merchantId);
        return Optional.ofNullable(merchantUser);
    }

    public List<Long> findSmallVenueMobileUserIds(MerchantBenefitIncrementDTO selectDTO, boolean hasTag,
            Long[] groupTagIds,
            Long[] equipmentTypeTagIds,
            Long[] otherTagIds,
            Long[] sexTagIds,
            boolean hasMemberLevel,
            boolean hasMerchantBenefitClassifyId,
            String numericKeyword,
            String strKeyWord) {
        return merchantUserMapper.findSmallVenueMobileUserIds(selectDTO, hasTag, groupTagIds,equipmentTypeTagIds,otherTagIds,sexTagIds, hasMemberLevel,
                hasMerchantBenefitClassifyId, numericKeyword, strKeyWord);
    }

}
