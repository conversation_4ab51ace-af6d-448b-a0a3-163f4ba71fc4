package com.lyy.user.infrastructure.support.specification;

/**
 * <AUTHOR>
 * @since 2022/1/11
 */
public class AndSpecification<T> extends AbstractCompositeSpecification<T> {

    private final Specification<T> b;
    private final Specification<T> a;

    public AndSpecification(Specification<T> a, Specification<T> b) {
        this.a = a;
        this.b = b;
    }

    @Override
    public boolean isSatisfiedBy(T t) {
        return a.isSatisfiedBy(t) && b.isSatisfiedBy(t);
    }
}

