package com.lyy.user.infrastructure.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JSONUtil {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    static {
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    private JSONUtil() {
    }

    public static String toJSONString(Object obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        try {
            return MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("序列化失败:{}", obj);
        }
        return null;
    }

    public static <T> T parseObject(String json, Class<T> tClass) {
        try {
            return MAPPER.readValue(json, tClass);
        } catch (JsonProcessingException e) {
            log.error("错误信息", e);
            log.error("序列化失败:{}", json);
        }
        return null;
    }

    public static <T> T parseObject(byte[] bytes, Class<T> tClass) {
        try {
            return MAPPER.readValue(bytes, tClass);
        } catch (IOException e) {
            String json = new String(bytes, StandardCharsets.UTF_8);
            log.error("错误信息", e);
            log.error("序列化失败:{}", json);
        }
        return null;
    }

    public static <T> T parseObject(String json, TypeReference<T> tTypeReference) {
        try {
            return MAPPER.readValue(json, tTypeReference);
        } catch (JsonProcessingException e) {
            log.error("错误信息", e);
            log.error("序列化失败:{}", json);
        }
        return null;
    }

    public static <T> T parseObject(byte[] bytes, TypeReference<T> tTypeReference) {
        try {
            return MAPPER.readValue(bytes, tTypeReference);
        } catch (IOException e) {
            String json = new String(bytes, StandardCharsets.UTF_8);
            log.error("错误信息", e);
            log.error("序列化失败:{}", json);
        }
        return null;
    }

    public static byte[] toBytes(Object obj) {
        try {
            return MAPPER.writeValueAsBytes(obj);
        } catch (JsonProcessingException e) {
            log.error("序列化失败:{}", obj);
        }
        return null;
    }

}
