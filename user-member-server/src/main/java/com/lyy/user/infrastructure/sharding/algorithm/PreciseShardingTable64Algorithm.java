package com.lyy.user.infrastructure.sharding.algorithm;

import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.infrastructure.execption.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;

import java.util.Collection;

/**
 * <AUTHOR>
 * <p>
 * 精准分表
 * <p>
 * 分表基数为64
 */
@Slf4j
public class PreciseShardingTable64Algorithm implements PreciseShardingAlgorithm<Long> {

    private final static Integer SHARDING_TABLE_NUM = 127;

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<Long> preciseShardingValue) {

        for (String name : availableTargetNames) {
            if (name.endsWith(String.valueOf(preciseShardingValue.getValue() & SHARDING_TABLE_NUM))) {
                log.debug("===> Table ->PreciseShardingTableAlgorithm: {},{}", preciseShardingValue, name);
                return name;
            }
        }
        throw new BusinessException(GlobalErrorCode.DATABASE_SHARDING_ROUTE);
    }
}
