package com.lyy.user.infrastructure.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.jackson.JsonComponent;

import java.io.IOException;

/**
 * json序列化
 * <AUTHOR>
 * @className: JsonComponent
 * @date 2021/5/28
 */
//@JsonComponent
@Slf4j
public class MyJsonComponentConfig {
    private static final String LONG_NEW_STRING_SUFFIX = "-str";

    public static class AddLongStrJsonSerializer extends JsonSerializer<Long>{

        @Override
        public void serialize(Long aLong, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
//            log.debug("自定义处理序列化long类型-->{}",aLong);
            if (aLong != null){
                if(aLong > Integer.MAX_VALUE || aLong < Integer.MIN_VALUE){
                    //超出int范围可能引起前端显示数据异常
                    jsonGenerator.writeNumber(aLong);
                    String name = jsonGenerator.getOutputContext().getCurrentName();
                    String newName = name+ LONG_NEW_STRING_SUFFIX;
                    try {
                        jsonGenerator.writeFieldName(newName);
                        jsonGenerator.writeString(aLong.toString());
                        log.debug("{} Long数据为 {} 超长，需要增加新属性 {},用于前端显示",name,aLong,newName);
                    }catch (Exception e){
                        log.debug("{} Long数据为 {} 超长，需要增加新属性 {} 失败",name,aLong,newName);
                    }
                }else{
                    jsonGenerator.writeNumber(aLong);
                }
            }

        }
    }
}
