package com.lyy.user.infrastructure.sharding.algorithm;

import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.infrastructure.execption.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;

import java.util.Collection;

/**
 * <AUTHOR>
 * <p>
 * 精准分库 基数128, 采用4库
 * 0-31 库1
 * 32-63 库2
 * 64-95 库3
 * 96-127 库4
 */
@Slf4j
public class PreciseShardingDatabase128Algorithm implements PreciseShardingAlgorithm<Long> {

    private final static Integer PRECISE_SHARDING_INDEX = 127;
    private final static Integer PRECISE_SHARDING_31 = 31;
    private final static Integer PRECISE_SHARDING_63 = 63;
    private final static Integer PRECISE_SHARDING_95 = 95;

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<Long> preciseShardingValue) {
        for (String name : availableTargetNames) {
            if (name.endsWith(String.valueOf(getIndex(preciseShardingValue.getValue())))) {
                log.debug("===> DataBase -> PreciseShardingDatabaseAlgorithm: {},{}", preciseShardingValue, name);
                return name;
            }
        }
        throw new BusinessException(GlobalErrorCode.DATABASE_SHARDING_ROUTE);
    }

    private int getIndex(Long value) {
        long range = value & PRECISE_SHARDING_INDEX;
        if (range >= 0 && range <= PRECISE_SHARDING_31) {
            return 0;
        } else if (range > PRECISE_SHARDING_31 && range <= PRECISE_SHARDING_63) {
            return 1;
        } else if (range > PRECISE_SHARDING_63 && range <= PRECISE_SHARDING_95) {
            return 2;
        } else {
            return 3;
        }
    }
}
