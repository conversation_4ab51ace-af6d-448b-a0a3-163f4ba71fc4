package com.lyy.user.infrastructure.config;

import cn.lyy.base.utils.RedisClient;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * <p>
 * apollo 配置
 */
@Slf4j
@Component
@EnableApolloConfig
public class ApolloConfig implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @ApolloConfigChangeListener("application.yml")
    private void handlerListener(ConfigChangeEvent event) {
        event.changedKeys().forEach(key -> log.debug("##### Change Keys: {}", event.getChange(key)));
        applicationContext.publishEvent(new EnvironmentChangeEvent(event.changedKeys()));
    }

    @Bean
    public RedisClient redisClient() {
        RedisClient redisClient = new RedisClient();
        redisClient.init();
        return redisClient;
    }
}
