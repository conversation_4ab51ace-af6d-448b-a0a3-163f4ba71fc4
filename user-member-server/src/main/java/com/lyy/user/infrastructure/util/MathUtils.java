package com.lyy.user.infrastructure.util;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/1/19
 */
public class MathUtils {

    public static Integer add(Integer a, Integer b) {
        a = getOrElse(a);
        b = getOrElse(b);
        return a + b;
    }

    public static Integer subtract(Integer a, Integer b) {
        a = getOrElse(a);
        b = getOrElse(b);
        return a - b;
    }

    private static Integer getOrElse(Integer a) {
        return Optional.ofNullable(a).orElse(0);
    }

    public static BigDecimal add(BigDecimal a, BigDecimal b) {
        a = getOrElse(a);
        b = getOrElse(b);
        return a.add(b);
    }

    private static BigDecimal getOrElse(BigDecimal a) {
        return Optional.ofNullable(a).orElse(BigDecimal.ZERO);
    }

    public static BigDecimal subtract(BigDecimal a, BigDecimal b) {
        a = getOrElse(a);
        b = getOrElse(b);
        return a.subtract(b);
    }

}
