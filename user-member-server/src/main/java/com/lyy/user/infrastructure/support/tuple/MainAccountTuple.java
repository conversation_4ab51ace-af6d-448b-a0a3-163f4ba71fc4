package com.lyy.user.infrastructure.support.tuple;

import com.lyy.user.domain.account.entity.Account;

/**
 * <AUTHOR>
 * @since 2022/1/22 - 11:28
 */
public class MainAccountTuple extends Tuple {

    public MainAccountTuple(Account account, Account mainAccount) {
        super(new Object[]{account, mainAccount});
    }

    public Account getAccount() {
        return (Account) get(0);
    }

    public Account getMainAccount() {
        return (Account) get(1);
    }

}
