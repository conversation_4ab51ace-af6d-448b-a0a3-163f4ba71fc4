package com.lyy.user.infrastructure.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品类型枚举类
 *     此类为商品服务冗余枚举类，最新数据参考商品服务CategoryEnum
 * <AUTHOR>
 * @create 2021-04-21
 */
@AllArgsConstructor
@Getter
public enum CommodityCategoryEnum {

    //
    PHYSICAL_COMMODITY(10101,"physical","实物商品"),
    /**
     * 储值套餐
     */
    SET_MEAL(10102,"set_meal","储值套餐"),
    /**
     * 设备服务
     */
    DEVICE_SERVICE(10103,"device_service","设备服务"),
    MEMBER_AUTH(10104,"member_auth","会员权益"),
    PLATFORM_AUTH(10105,"platform_auth","平台权益"),
    VIRTUAL_COMBINE(10106,"virtual_combine","虚拟商品组合"),
    VIRTUAL_PHYSICAL_COMBINE(10107,"virtual_physical_combine","虚拟和实物商品组合"),
    PER_CARD(10108,"per_card","期限有效次卡"),
    IC_SET_MEAL(10109,"ic_set_meal","IC卡充值套餐"),
    SERVICE_CHARGE(10110,"service_charge","服务费"),
    ;

    private int id;

    private String code;

    private String name;
}
