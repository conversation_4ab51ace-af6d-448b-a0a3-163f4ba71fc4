package com.lyy.user.infrastructure.util;

import java.util.Random;

/**
 * <AUTHOR>
 * @date 2019/12/27 11:56
 **/
public class RandomUtil {

    public final static Random RANDOM = new Random();

    public static String getRandom12forNumber() {
        long num = Math.abs(RANDOM.nextLong() % 1000000000000L);
        StringBuilder s = new StringBuilder(String.valueOf(num));

        for (int i = 0; i < 12 - s.length(); ++i) {
            s.insert(0, "0");
        }

        return "0".equals(s.substring(0, 1)) ? getRandom12forNumber() : s.toString();
    }

    public static String getRandom() {
        long num = Math.abs(RANDOM.nextLong() % 100000000L);
        StringBuilder s = new StringBuilder(String.valueOf(num));

        for (int i = 0; i < 8 - s.length(); ++i) {
            s.insert(0, "0");
        }

        return "0".equals(s.substring(0, 1)) ? getRandom() : s.toString();
    }

    public static String inItLoginPassWord() {
        String password = "YFK" + getRandom();
        return password;
    }

}
