package com.lyy.user.infrastructure.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员组相关常量
 * <AUTHOR>
 * @className: MemberGroupEnums
 * @date 2021/3/29
 */
@Getter
@AllArgsConstructor
public enum MemberGroupEnums {
    //开通方式
    OPEN_METHOD_AUTO((short)0,"自动开通"),
    OPEN_METHOD_MANUAL((short)1,"手动开通"),

    //升降策略
    LIFTING_STRATEGY_NOT((short)0,"无策略"),
    LIFTING_STRATEGY_UPGRADE((short)1,"升级策略"),
    LIFTING_STRATEGY_DEMOTING((short)2,"降级策略"),

    //规则策略
    RULE_STRATEGY_ADD((short)0,"叠加规则策略"),
    RULE_STRATEGY_COVER((short)1,"覆盖规则策略"),

    ;
    private final Short value;
    private final String name;
}
