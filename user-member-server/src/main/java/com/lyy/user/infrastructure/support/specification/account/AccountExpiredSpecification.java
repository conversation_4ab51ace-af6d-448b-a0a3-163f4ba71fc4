package com.lyy.user.infrastructure.support.specification.account;

import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.infrastructure.support.specification.AbstractCompositeSpecification;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;

/**
 * 会员卡账户过期
 *
 * <AUTHOR>
 * @since 2022/1/11
 */
public class AccountExpiredSpecification extends AbstractCompositeSpecification<Account> {

    @Override
    public boolean isSatisfiedBy(Account t) {
        return Objects.nonNull(t.getDownTime())
                && t.getDownTime()
                .toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime()
                .isBefore(LocalDateTime.now());
    }

}
