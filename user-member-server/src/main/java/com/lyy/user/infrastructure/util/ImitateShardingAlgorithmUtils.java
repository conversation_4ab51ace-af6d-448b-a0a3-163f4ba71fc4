package com.lyy.user.infrastructure.util;

/**
 * <AUTHOR>
 * @date 2023/10/7
 */
public class ImitateShardingAlgorithmUtils {

    private final static Integer PRECISE_SHARDING_INDEX = 127;
    private final static Integer PRECISE_SHARDING_31 = 31;
    private final static Integer PRECISE_SHARDING_63 = 63;
    private final static Integer PRECISE_SHARDING_95 = 95;


    public static int findIndex(Long value) {
        long range = value & PRECISE_SHARDING_INDEX;
        if (range >= 0 && range <= PRECISE_SHARDING_31) {
            return 0;
        } else if (range > PRECISE_SHARDING_31 && range <= PRECISE_SHARDING_63) {
            return 1;
        } else if (range > PRECISE_SHARDING_63 && range <= PRECISE_SHARDING_95) {
            return 2;
        } else {
            return 3;
        }
    }
}
