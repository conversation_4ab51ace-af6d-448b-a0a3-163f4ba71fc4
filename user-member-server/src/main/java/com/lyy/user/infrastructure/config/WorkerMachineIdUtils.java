package com.lyy.user.infrastructure.config;


import cn.lyy.base.utils.SnowflakeIdWorker;
import cn.lyy.base.utils.SnowflakeIdWorkerUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.PreDestroy;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

/**
 * <AUTHOR> {<EMAIL>}
 * @date 2020/8/25 08:53
 **/
@Slf4j
@Configuration
@Getter
@Setter
public class WorkerMachineIdUtils {

    /**
     * 数据中心节点
     */
    @Value("${snowflake.dataCenterId:1}")
    private long dataCenterId;

    /**
     * 机器节点redis缓存前缀
     */
    @Value("${snowflake.worker.prefix:snowflake_worker}")
    private String workerPrefix;

    private long workerId;

    private String localIp;

    public static final String REDIS_SPLIT = "_";

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * hash机器IP初始化一个机器ID
     */
    @Bean
    public SnowflakeIdWorker initMachineId() {
        try {
            localIp = getInnerIp();
        } catch (SocketException e) {
            localIp = "127.0.0.1";
        }
        //这里取128,为后续机器Ip调整做准备。
        Long ip_ = Long.parseLong(localIp.replaceAll("\\.", ""));
        int hashCode = ip_.hashCode();
        hashCode = hashCode < 0 ? hashCode & Integer.MAX_VALUE : hashCode;
        workerId = hashCode % 32;
        //创建一个机器ID
        dataCenterId = System.currentTimeMillis() % 32;
        createMachineId();
        log.info("[snowflake]初始化 machineId :{}, dataCenterId: {}", workerId, dataCenterId);
        SnowflakeIdWorker idWorker = new SnowflakeIdWorker(workerId, dataCenterId);
        SnowflakeIdWorkerUtils.INSTANCE.setSnowflakeIdWorker(idWorker);
        return idWorker;
    }

    /**
     * 容器销毁前清除注册记录
     */
    @PreDestroy
    public void destroyMachineId() {
        String key = workerPrefix + REDIS_SPLIT + dataCenterId + REDIS_SPLIT + workerId;
        log.info("[snowflake]清除snowflake的key:{}", key);
        redisTemplate.delete(key);
    }

    /**
     * 主方法：获取一个机器id
     *
     * @return
     */
    public void createMachineId() {
        try {
            //向redis注册，并设置超时时间
            Boolean aBoolean = registerMachine(workerId, localIp);
            //注册成功
            if (aBoolean) {
                //启动一个线程更新超时时间
                return;
            }
            //检查是否被注册满了.不能注册，就直接返回
            if (checkIfCanRegister()) {
                return;
            }
            log.info("[snowflake]createMachineId->ip:{},machineId:{}", localIp, workerId);
            //递归调用
            createMachineId();
        } catch (Exception e) {
            getRandomMachineId();
        }
    }

    /**
     * 检查是否被注册满了
     *
     * @return
     */
    private Boolean checkIfCanRegister() {
        Boolean flag = true;
        //判断0~31这个区间段的机器IP是否被占满
        for (int i = 0; i <= 31; i++) {
            long did = System.currentTimeMillis() % 32;
            String key = workerPrefix + REDIS_SPLIT + did + REDIS_SPLIT + i;
            flag = redisTemplate.hasKey(key);
            //如果不存在。说明还可以继续注册。直接返回i
            if (!flag) {
                workerId = i;
                dataCenterId = did;
                log.info("[snowflake]重新生成key:{}, localIp: {}", key, localIp);
                redisTemplate.opsForValue().set(key, localIp);
                break;
            }
        }
        return !flag;
    }

    /**
     * 获取1~127随机数
     */
    public void getRandomMachineId() {
        workerId = System.currentTimeMillis() % 32;
    }


    /**
     * 1.注册机器
     * 2.设置超时时间
     *
     * @param machineId 取值为0~127
     * @return
     */
    private Boolean registerMachine(long machineId, String localIp) {
        String key = workerPrefix + REDIS_SPLIT + dataCenterId + REDIS_SPLIT + machineId;
        log.info("[snowflake]key:{}, localIp : {}", key, localIp);
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            return false;
        }
        redisTemplate.opsForValue().set(key, localIp);
        return true;
    }

    public String getInnerIp() throws SocketException {
        // 本地IP，如果没有配置外网IP则返回它
        String localip = null;
        // 外网IP
        String netip = null;
        Enumeration<NetworkInterface> netInterfaces = NetworkInterface.getNetworkInterfaces();
        InetAddress ip = null;
        // 是否找到外网IP
        boolean finded = false;
        while (netInterfaces.hasMoreElements() && !finded) {
            NetworkInterface ni = netInterfaces.nextElement();
            Enumeration<InetAddress> address = ni.getInetAddresses();
            while (address.hasMoreElements()) {
                ip = address.nextElement();
                // 外网IP
                if (!ip.isSiteLocalAddress()
                        && !ip.isLoopbackAddress()
                        && ip.getHostAddress().indexOf(":") == -1) {
                    netip = ip.getHostAddress();
                    finded = true;
                    break;
                } else if (ip.isSiteLocalAddress()
                        && !ip.isLoopbackAddress()
                        && ip.getHostAddress().indexOf(":") == -1) {
                    // 内网IP
                    localip = ip.getHostAddress();
                }
            }
        }
        if (netip != null && !"".equals(netip)) {
            return netip;
        } else {
            return localip;
        }
    }

}