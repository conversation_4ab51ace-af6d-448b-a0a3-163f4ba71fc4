package com.lyy.user.infrastructure.repository.account;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitScopeQueryDTO;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.domain.account.dto.AccountBenefitScopeDO;
import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.domain.account.repository.AccountBenefitMapper;
import com.lyy.user.domain.account.repository.AccountMapper;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since 2022/4/26 - 14:01
 */
@Repository
public class AccountRepository {

    @Resource
    private AccountMapper accountMapper;

    @Resource
    private AccountBenefitMapper accountBenefitMapper;

    public List<AccountBenefitScopeDO> listBenefitWithScope(AccountBenefitScopeQueryDTO query) {
        return accountBenefitMapper.listBenefitWithScope(query);
    }

    public int updateAccountAmount(Long merchantId, Long userId, Integer classify, BigDecimal amount, AdjustTypeEnum adjustType) {
        return updateAccountAmount(merchantId, userId, classify, amount, adjustType, false);
    }

    public int updateAccountAmount(Long merchantId, Long userId, Integer classify, BigDecimal amount, AdjustTypeEnum adjustType, boolean isAddTotal) {
        LambdaUpdateWrapper<Account> wrapper = new UpdateWrapper<Account>()
                .lambda()
                .eq(Account::getMerchantId, merchantId)
                .eq(Account::getUserId, userId)
                .eq(Account::getClassify, classify)
                .set(Account::getUpdateTime, new Date())
                .setSql(isAddTotal && AdjustTypeEnum.INCREMENT.equals(adjustType), "total = total + " + amount.abs())
                .setSql(AdjustTypeEnum.INCREMENT.equals(adjustType), "balance = balance + " + amount.abs())
//                .setSql(AdjustTypeEnum.DECREMENT.equals(adjustType),"balance = balance - " + amount.abs())
                .setSql(AdjustTypeEnum.DECREMENT.equals(adjustType), "balance = greatest(balance - " + amount.abs() + ",0) ")

                .setSql(AdjustTypeEnum.EMPTY.equals(adjustType), "balance = 0 ");
        return accountMapper.update(null,wrapper);
    }

    public Account getMerchantCouponAccount(Long merchantId, Long userId) {
        if (userId == null || merchantId == null) {
            return null;
        }
        return accountMapper.selectOne(
                Wrappers.<Account>lambdaQuery()
                        .eq(Account::getMerchantId, merchantId)
                        .eq(Account::getUserId, userId)
                        .eq(Account::getClassify, BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON.getCode())
        );
    }
}
