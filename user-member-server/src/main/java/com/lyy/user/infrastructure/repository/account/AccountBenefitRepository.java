package com.lyy.user.infrastructure.repository.account;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.lyy.user.account.infrastructure.constant.AccountBenefitStatusEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import com.lyy.user.domain.account.entity.AccountBenefit;
import com.lyy.user.domain.account.repository.AccountBenefitMapper;
import com.lyy.user.domain.benefit.entity.BenefitConsumeRule;
import com.lyy.user.domain.benefit.entity.BenefitScope;
import com.lyy.user.infrastructure.repository.benefit.BenefitRepository;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since 2022/8/16 - 19:58
 */
@RequiredArgsConstructor
@Repository
public class AccountBenefitRepository {

    private final AccountBenefitMapper accountBenefitMapper;

    private final BenefitRepository benefitRepository;

    public boolean insertBatch(List<AccountBenefit> records) {
        if (CollectionUtils.isEmpty(records)) {
            return false;
        }
        return Lists.partition(records, 50)
                .stream()
                .map(accountBenefitMapper::insertBatch)
                .mapToInt(i -> i)
                .sum() > 0;
    }

    public IPage<AccountBenefit> pageExpiredBenefit(Long merchantId, Integer pageIndex, Integer pageSize) {
        Page<AccountBenefit> page = new Page<>(pageIndex, pageSize);
        page.setSearchCount(false);
        return accountBenefitMapper.selectPage(page, getAccountBenefitQueryWrapper(merchantId));
    }

    private LambdaQueryWrapper<AccountBenefit> getAccountBenefitQueryWrapper(Long merchantId) {
        return Wrappers.<AccountBenefit>lambdaQuery()
                .select(AccountBenefit::getId, AccountBenefit::getUserId,
                        AccountBenefit::getAccountId, AccountBenefit::getMerchantBenefitClassifyId,
                        AccountBenefit::getMerchantUserId, AccountBenefit::getStoreId,
                        AccountBenefit::getMerchantId, AccountBenefit::getValueType,
                        AccountBenefit::getBalance)
                .ne(AccountBenefit::getClassify, BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode())
                .ne(AccountBenefit::getStatus, AccountBenefitStatusEnum.EXPIRED.getStatus())
                .eq(AccountBenefit::getExpiryDateCategory, ExpiryDateCategoryEnum.TIME_INTERVAL.getValue())
                .and(wrapper -> wrapper.lt(AccountBenefit::getDownTime, DateUtil.format(new Date(), "yyyy-MM-dd")))
                .and(wrapper -> wrapper.eq(AccountBenefit::getMerchantId, merchantId).or().exists("select 1"));
    }


    public List<AccountBenefit> listBenefit(Query query) {
        return accountBenefitMapper.selectList(
                Wrappers.<AccountBenefit>lambdaQuery()
                        .eq(AccountBenefit::getUserId, query.getUserId())
                        .eq(AccountBenefit::getMerchantId, query.getMerchantId())
                        .in(CollectionUtils.isNotEmpty(query.getAccountBenefitIds()), AccountBenefit::getId, query.getAccountBenefitIds())
        );
    }

    public List<BenefitConsumeRule> listBenefitConsumeRule(Long merchantId, List<Integer> classifies) {
        if (CollectionUtils.isEmpty(classifies)) {
            return Lists.newArrayList();
        }
        return benefitRepository.listBenefitConsumeRule(merchantId, classifies, Boolean.FALSE);
    }

    public List<BenefitScope> listBenefitScope(Long merchantId,List<Long> benefitIds) {
        if (CollectionUtils.isEmpty(benefitIds)) {
            return Lists.newArrayList();
        }
        return benefitRepository.listScopeByBenefitIds(merchantId, benefitIds);
    }

    public List<AccountBenefit> listAccountBenefit(Long merchantId, List<Long> accountBenefitIds) {
        if (CollectionUtils.isEmpty(accountBenefitIds)) {
            return Lists.newArrayList();
        }
        return accountBenefitMapper.selectList(
            Wrappers.<AccountBenefit>lambdaQuery()
                .eq(AccountBenefit::getMerchantId, merchantId)
                .in(AccountBenefit::getId, accountBenefitIds)
        );
    }

    @Data
    public static class Query {
        private Long userId;
        private Long merchantId;
        private List<Long> accountBenefitIds;

    }

}
