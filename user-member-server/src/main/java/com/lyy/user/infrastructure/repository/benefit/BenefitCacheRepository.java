package com.lyy.user.infrastructure.repository.benefit;

import cn.lyy.cache.annotation.CacheEvict;
import cn.lyy.cache.annotation.Cacheable;
import cn.lyy.cache.annotation.Level2Cache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lyy.user.domain.benefit.entity.BenefitConsumeRule;
import com.lyy.user.domain.benefit.entity.BenefitScope;
import com.lyy.user.domain.benefit.repository.BenefitConsumeRuleMapper;
import com.lyy.user.domain.benefit.repository.BenefitScopeMapper;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@RequiredArgsConstructor
@Component
@Slf4j
public class BenefitCacheRepository {

    public final BenefitScopeMapper benefitScopeMapper;

    public final BenefitConsumeRuleMapper benefitConsumeRuleMapper;

    @Cacheable(value = "multilevel:cache:member:benefitScope", key = "#merchantId  + '-' + #benefitId",
        level2Cache = @Level2Cache(useCacheCenter = false, expireTime = 8, timeUnit = TimeUnit.HOURS, preloadTime = 1, forceRefresh = true))
    public List<BenefitScope> getScopeByBenefitId(Long merchantId, Long benefitId) {
        return benefitScopeMapper.selectList(new LambdaQueryWrapper<BenefitScope>().eq(BenefitScope::getBenefitId, benefitId)
            .eq(BenefitScope::getMerchantId, merchantId));
    }

    @CacheEvict(value = "multilevel:cache:member:benefitScope", key = "#merchantId  + '-' + #benefitId")
    public void clearBenefitScopeCache(Long merchantId, Long benefitId) {
        log.info("清除权益范围缓存: key={}, merchantId:{},benefitId={}", "multilevel:cache:member:benefitScope", merchantId, benefitId);
    }

    @Cacheable(value = "multilevel:cache:member:benefitConsumeRule", key = "#merchantId",
        level2Cache = @Level2Cache(useCacheCenter = false, expireTime = 8, timeUnit = TimeUnit.HOURS, preloadTime = 1, forceRefresh = true))
    public List<BenefitConsumeRule> listBenefitConsumeRule(Long merchantId) {
        return benefitConsumeRuleMapper.selectList(new LambdaQueryWrapper<BenefitConsumeRule>().eq(BenefitConsumeRule::getMerchantId, merchantId));
    }

    @CacheEvict(value = "multilevel:cache:member:benefitConsumeRule", key = "#merchantId")
    public void clearBenefitConsumeRuleCache(Long merchantId) {
        log.info("清除商户权益消耗规则缓存: key={}, merchantId:{}", "multilevel:cache:member:benefitConsumeRule", merchantId);
    }

}
