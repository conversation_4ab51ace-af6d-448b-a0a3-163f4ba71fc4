<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.member.repository.MemberLiftingRuleMapper">


    <select id="selectByIdForUpdate" parameterType="java.lang.Long" resultType="com.lyy.user.domain.member.entity.MemberLiftingRule">
        select * from um_member_lifting_rule where  id = #{id} for update
    </select>

    <select id="touchLiftingRule" resultType="com.lyy.user.domain.member.dto.MemberLiftingRuleTouchResultDTO">
        select mlr.*,ml.member_group_id
        from um_member_lifting_rule mlr
        left join um_member_lifting ml on ml.id = mlr.member_lifting_id
        where ml.merchant_id = #{merchantId}  and mlr.merchant_id = #{merchantId}
        and (ml.active is null or ml.active = true)
        and (mlr.active is null or mlr.active = true)
         and ml.member_group_id in
            <foreach collection="memberGroupIdList" item="memberGroupId" open="(" close=")" separator=",">
                #{memberGroupId}
            </foreach>
        and category = #{category}
    </select>
</mapper>
