<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.report.repository.SmallVenueMemberReportMapper">

    <resultMap id="SmallVenueMemberReportDtoMap"
               type="com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberReportDto">
        <id column="user_tags" typeHandler="com.lyy.user.infrastructure.typehandler.ArrayTypeHandlerPg"
            property="userTagIds" javaType="java.lang.Object" jdbcType="ARRAY"/>
    </resultMap>

    <select id="queryUserMemberReport" resultMap="SmallVenueMemberReportDtoMap">
        select
        umu.id as merchantUserId
        , umu.user_id as userId
        , umu.telephone as phone
        , umu.name as name
        , umu.user_type as userType
        , umu.name as levelName
        , umu.gender as gender
        , umu.birthday as birthday
        , umu.province_id as provinceId
        , umu.city_id as cityId
        , umu.region_id as regionId
        , umu.address as adress
        , umu.create_time as initTime
        , umut.user_tags
        <include refid="memberDetail"/>
        order by umu.create_time desc
        limit #{smallVenueMemberReportQueryDto.pageSize} offset #{startIndex}
    </select>

    <select id="queryTagName" resultType="java.lang.String">
        select name from um_tag_user
        where merchant_id = #{merchantId} and state = 0
        and id in
        <foreach collection="tagIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryUserMemberReportTotalMemberNum" resultType="java.lang.Long">
        select count(*)
        <include refid="memberDetail"></include>
    </select>
    <select id="queryUserMemberStoreValueReport"
            resultType="com.lyy.user.account.infrastructure.report.dto.SmallVenueStoreValueSimpleDto">
        SELECT merchant_user_id as merchantUserId
        , COUNT(*) AS benefitTotalNum
        , sum(balance) AS num
        , merchant_benefit_classify_id as storeValueId
        , value_type as valueType
        from um_account_benefit
        <where>
            user_id in
            <foreach collection="userIdList" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
            and merchant_id = #{merchantId}
            and classify = 99
            and status = 1
            and store_id in
            <foreach collection="storeIds" item="storeId" open="(" close=")" separator=",">
                #{storeId}
            </foreach>
            and balance > 0
        </where>
        group by merchant_user_id,merchant_id,classify,merchant_benefit_classify_id, value_type
    </select>
    <select id="totalQueryMemberStoreValueReport"
            resultType="com.lyy.user.account.infrastructure.report.dto.SmallVenueStoreValueSimpleDto">
        SELECT
        COUNT(*) AS benefitTotalNum
        , sum(uab.balance) AS num
        , uab.merchant_benefit_classify_id as storeValueId
        , uab.value_type as valueType
        from
        um_merchant_user umu
        left join um_merchant_user_tag umut on umut.merchant_id = umu.merchant_id and umut.business_user_id = umu.id
        left join um_account_benefit uab on umu.user_id = uab.user_id and uab.merchant_id = umu.merchant_id and uab.classify = 99
        <where>
            umu.merchant_id = #{smallVenueMemberReportQueryDto.merchantId}
            <if test="smallVenueMemberReportQueryDto.merchantUserId != null">
                and umu.id = #{smallVenueMemberReportQueryDto.merchantUserId}
            </if>
            <if test="smallVenueMemberReportQueryDto.userId != null">
                and umu.user_id = #{smallVenueMemberReportQueryDto.userId}
            </if>
            <if test="smallVenueMemberReportQueryDto.userType != null and smallVenueMemberReportQueryDto.userType.size>0 ">
                and umu.user_type in
                <foreach collection="smallVenueMemberReportQueryDto.userType" item="type" open="(" close=")"
                         separator=",">
                    #{type}
                </foreach>
            </if>
            and uab.status=1
            and uab.store_id in
            <foreach collection="smallVenueMemberReportQueryDto.storeValueCurrencyStoreIds" item="storeId" open="("
                     close=")" separator=",">
                #{storeId}
            </foreach>
            <if test="userTagList != null and userTagList.length > 0">
                and umut.user_tags @> array
                [#{userTagList,typeHandler=com.lyy.user.infrastructure.typehandler.ArrayTypeHandlerPg}]
            </if>
            <if test="smallVenueMemberReportQueryDto.name != null and smallVenueMemberReportQueryDto.name !='' ">
                and umu.name like concat('%',#{smallVenueMemberReportQueryDto.name},'%')
            </if>
            <if test="smallVenueMemberReportQueryDto.phone != null and smallVenueMemberReportQueryDto.phone !='' ">
                and umu.telephone like concat(#{smallVenueMemberReportQueryDto.phone},'%')
            </if>
            and uab.balance > 0
        </where>
        group by uab.merchant_id,uab.classify, uab.merchant_benefit_classify_id, uab.value_type
    </select>
    <select id="queryUserMemberReportTotalCardNum" resultType="java.lang.Long">
        select count (* )
        <include refid="memberCardDetail"></include>
    </select>
    <select id="queryUserMemberCardReport"
            resultType="com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberCardReportDto">
        select umu.id as merchantUserId,
        umu.user_id as userId,
        umu.telephone as phone,
        umu.name as name,
        ua.id as accountId,
        ua.parent_account_id as parentAccountId,
        ua.card_no as cardNo,
        ua.status as status,
        ua.create_time as initTime,
        ua.down_time as downTime,
        ua.deposit as deposit,
        umut.user_tags
        <include refid="memberCardDetail"/>
        order by ua.create_time desc
        limit #{smallVenueMemberCardReportQueryDto.pageSize} offset #{startIndex}
    </select>
    <select id="queryUserMemberCardStoreValueReport"
            resultType="com.lyy.user.account.infrastructure.report.dto.SmallVenueStoreValueSimpleDto">
        SELECT account_id as accountId
        , COUNT(*) AS benefitTotalNum
        , sum(balance) AS num
        , merchant_benefit_classify_id as storeValueId
        , value_type as valueType
        from um_account_benefit
        <where>
            account_id in
            <foreach collection="accountIdList" item="accountId" open="(" close=")" separator=",">
                #{accountId}
            </foreach>
            and merchant_id = #{merchantId}
            and classify = 99
            and status = 1
            and store_id in
            <foreach collection="storeIds" item="storeId" open="(" close=")" separator=",">
                #{storeId}
            </foreach>
            and balance > 0
        </where>
        group by account_id,classify,merchant_benefit_classify_id, value_type
    </select>
    <select id="totalQueryMemberCardStoreValueReport"
            resultType="com.lyy.user.account.infrastructure.report.dto.SmallVenueStoreValueSimpleDto">
        SELECT
        COUNT(*) AS benefitTotalNum
        , sum(uab.balance) AS num
        , uab.merchant_benefit_classify_id as storeValueId
        , uab.value_type as valueType
        from
        um_merchant_user umu
        left join um_merchant_user_tag umut on umut.merchant_id = umu.merchant_id and umut.business_user_id = umu.id
        left join um_account ua on ua.merchant_user_id = umu.id and ua.merchant_id = umu.merchant_id and ua.classify = 99
        left join um_account_benefit uab on uab.account_id = ua.id
        <where>
            umu.merchant_id = #{smallVenueMemberCardReportQueryDto.merchantId}
            and uab.status = 1
            <if test="smallVenueMemberCardReportQueryDto.merchantUserId != null">
                and umu.id = #{smallVenueMemberCardReportQueryDto.merchantUserId}
            </if>
            <if test="smallVenueMemberCardReportQueryDto.userId != null">
                and umu.user_id = #{smallVenueMemberCardReportQueryDto.userId}
            </if>
            <if test="smallVenueMemberCardReportQueryDto.accountId != null">
                and ua.id = #{smallVenueMemberCardReportQueryDto.accountId}
            </if>
            and uab.store_id in
            <foreach collection="smallVenueMemberCardReportQueryDto.storeValueCurrencyStoreIds" item="storeId" open="("
                     close=")" separator=",">
                #{storeId}
            </foreach>
            and ua.card_no != ''
            <if test="userTagList != null and userTagList.length > 0">
                and umut.user_tags @> array
                [#{userTagList,typeHandler=com.lyy.user.infrastructure.typehandler.ArrayTypeHandlerPg}]
            </if>
            <if test="smallVenueMemberCardReportQueryDto.name != null and smallVenueMemberCardReportQueryDto.name !='' ">
                and umu.name like concat('%',#{smallVenueMemberCardReportQueryDto.name},'%')
            </if>
            <if test="smallVenueMemberCardReportQueryDto.phone != null and smallVenueMemberCardReportQueryDto.phone !='' ">
                and umu.telephone like concat(#{smallVenueMemberCardReportQueryDto.phone},'%')
            </if>
            <if test="smallVenueMemberCardReportQueryDto.cardNo != null and smallVenueMemberCardReportQueryDto.cardNo !='' ">
                and ua.card_no like concat('%',#{smallVenueMemberCardReportQueryDto.cardNo},'%')
            </if>
            and uab.balance > 0
        </where>
        group by uab.merchant_id,uab.classify, merchant_benefit_classify_id, value_type
    </select>

    <select id="totalQueryMemberStoreValueRecordTotalReport" resultType="java.lang.Long">
        select count(*)
        <include refid="memberStoreValue"/>
    </select>
    <select id="totalQueryMemberStoreValueRecordReport"
            resultType="com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberStoreValueRecordDto">
        select
        uar.account_id as accountId,
        umu.name as name ,
        umu.telephone as phone,
        uar.card_no as cardNo,
        uar.record_type as recordType,
        umu.id as merchantUserId,
        umu.user_id as userId,
        uar.merchant_benefit_classify_id as storeValueId,
        uar.merchant_benefit_classify_name as storeValueName,
        uar.initial_benefit as initialBenefit,
        uar.actual_benefit as actualBenefit,
        uar.consume_type as operationChannel,
        uar.equipment_name as assetEquipmentName,
        uar.terminal_name as terminalName,
        uar.mode as mode,
        uar.create_name as createName,
        uar.create_time as createTime,
        uar.order_no as orderNo,
        uar.description as description,
        uar.merchant_id as merchantId,
        uar.store_id as storeId,
        uar.store_name as storeName,
        uar.sub_order_no as subOrderNo,
        uar.equipment_value as equipmentValue,
        uar.equipment_name as equipmentName,
        uar.account_benefit_id as accountBenefitId,
        uar.goods_id as goodsId,
        uar.account_initial_balance as accountInitialBalance,
        uar.account_initial_num as accountInitialNum,
        uar.merchant_benefit_classify_id as merchantBenefitClassifyId
        <include refid="memberStoreValue"/>
        order by uar.create_time desc, uar.id desc
        limit #{smallVenueMemberStoreValueRecordQueryDto.pageSize} offset #{startIndex}
    </select>

    <select id="totalQueryMemberStoreValueRecordChangeReport" resultType="java.math.BigDecimal">
        select
        sum(uar.actual_benefit)
        <include refid="memberStoreValue"/>
        and uar.mode =#{mode}
    </select>


    <sql id="memberStoreValue">
        from
        um_merchant_user umu
        left join um_merchant_user_tag umut on umut.merchant_id = umu.merchant_id and umut.business_user_id = umu.id
        left join um_account_record uar on uar.merchant_user_id = umu.id
        and uar.create_time >= #{smallVenueMemberStoreValueRecordQueryDto.startTime}
        and uar.create_time <![CDATA[ <= ]]> #{smallVenueMemberStoreValueRecordQueryDto.endTime}
        and uar.operation_type = #{smallVenueMemberStoreValueRecordQueryDto.operationType}
        <if test="smallVenueMemberStoreValueRecordQueryDto.storeId != null">
            and uar.store_id =#{smallVenueMemberStoreValueRecordQueryDto.storeId}
        </if>
        and uar.benefit_classify =99
        <where>
            umu.merchant_id = #{smallVenueMemberStoreValueRecordQueryDto.merchantId}
            and uar.merchant_id =#{smallVenueMemberStoreValueRecordQueryDto.merchantId}
            <if test="smallVenueMemberStoreValueRecordQueryDto.merchantUserId != null">
                and umu.id = #{smallVenueMemberStoreValueRecordQueryDto.merchantUserId}
            </if>
            <if test="smallVenueMemberStoreValueRecordQueryDto.userId != null">
                and umu.user_id = #{smallVenueMemberStoreValueRecordQueryDto.userId}
            </if>
            <if test="userTagList != null and userTagList.length > 0">
                <choose>
                    <when test="smallVenueMemberStoreValueRecordQueryDto.summaryFlag != null and smallVenueMemberStoreValueRecordQueryDto.summaryFlag == true">
                        <![CDATA[
                        and ( umut.user_tags && array
                        [#{userTagList,typeHandler=com.lyy.user.infrastructure.typehandler.ArrayTypeHandlerPg}] ) = true
                        ]]>
                    </when>
                    <otherwise>
                        and umut.user_tags @> array
                        [#{userTagList,typeHandler=com.lyy.user.infrastructure.typehandler.ArrayTypeHandlerPg}]
                    </otherwise>
                </choose>

            </if>
            <if test="smallVenueMemberStoreValueRecordQueryDto.orderNo != null and smallVenueMemberStoreValueRecordQueryDto.orderNo !='' ">
                and uar.order_no = #{smallVenueMemberStoreValueRecordQueryDto.orderNo}
            </if>
            <if test="smallVenueMemberStoreValueRecordQueryDto.recordTypeIds != null and smallVenueMemberStoreValueRecordQueryDto.recordTypeIds.size>0 ">
                and uar.record_type in
                <foreach collection="smallVenueMemberStoreValueRecordQueryDto.recordTypeIds" item="item" open="("
                         close=")"
                         separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="smallVenueMemberStoreValueRecordQueryDto.excludeRecordTypes != null and smallVenueMemberStoreValueRecordQueryDto.excludeRecordTypes.size>0 ">
                and uar.record_type not in
                <foreach collection="smallVenueMemberStoreValueRecordQueryDto.excludeRecordTypes" item="item" open="("
                         close=")"
                         separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="smallVenueMemberStoreValueRecordQueryDto.storeValeIds != null and smallVenueMemberStoreValueRecordQueryDto.storeValeIds.size>0 ">
                and uar.merchant_benefit_classify_id in
                <foreach collection="smallVenueMemberStoreValueRecordQueryDto.storeValeIds" item="item" open="("
                         close=")"
                         separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="smallVenueMemberStoreValueRecordQueryDto.operationChannelIds != null and smallVenueMemberStoreValueRecordQueryDto.operationChannelIds.size>0 ">
                and uar.consume_type in
                <foreach collection="smallVenueMemberStoreValueRecordQueryDto.operationChannelIds" item="item" open="("
                         close=")"
                         separator=",">
                    #{item}
                </foreach>
            </if>
            and uar.id is not null
            <if test="smallVenueMemberStoreValueRecordQueryDto.assetEquipmentName != null and smallVenueMemberStoreValueRecordQueryDto.assetEquipmentName !='' ">
                and uar.equipment_name like concat('%',#{smallVenueMemberStoreValueRecordQueryDto.assetEquipmentName},'%')
            </if>
            <if test="smallVenueMemberStoreValueRecordQueryDto.name != null and smallVenueMemberStoreValueRecordQueryDto.name !='' ">
                and umu.name like concat('%',#{smallVenueMemberStoreValueRecordQueryDto.name},'%')
            </if>
            <if test="smallVenueMemberStoreValueRecordQueryDto.phone != null and smallVenueMemberStoreValueRecordQueryDto.phone !='' ">
                and umu.telephone like concat(#{smallVenueMemberStoreValueRecordQueryDto.phone},'%')
            </if>
            <if test="smallVenueMemberStoreValueRecordQueryDto.cardNo != null and smallVenueMemberStoreValueRecordQueryDto.cardNo !='' ">
                and uar.card_no like concat('%',#{smallVenueMemberStoreValueRecordQueryDto.cardNo},'%')
            </if>
            <if test="smallVenueMemberStoreValueRecordQueryDto.storeIdList != null and smallVenueMemberStoreValueRecordQueryDto.storeIdList.size>0 ">
                and uar.store_id in
                <foreach collection="smallVenueMemberStoreValueRecordQueryDto.storeIdList" item="item" open="("
                        close=")"
                        separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>

    <sql id="memberCardDetail">
        from
        um_merchant_user umu
        left join um_merchant_user_tag umut on umut.merchant_id = umu.merchant_id and umut.business_user_id = umu.id
        left join um_account ua on ua.merchant_user_id = umu.id and ua.merchant_id =umu.merchant_id and ua.classify = 99
        <where>
            umu.merchant_id = #{smallVenueMemberCardReportQueryDto.merchantId}
            <if test="smallVenueMemberCardReportQueryDto.merchantUserId != null">
                and umu.id = #{smallVenueMemberCardReportQueryDto.merchantUserId}
            </if>
            <if test="smallVenueMemberCardReportQueryDto.accountId != null">
                and ua.id = #{smallVenueMemberCardReportQueryDto.accountId}
            </if>
            <if test="smallVenueMemberCardReportQueryDto.userId != null">
                and umu.user_id = #{smallVenueMemberCardReportQueryDto.userId}
            </if>
            <if test="userTagList != null and userTagList.length > 0">
                and umut.user_tags @> array
                [#{userTagList,typeHandler=com.lyy.user.infrastructure.typehandler.ArrayTypeHandlerPg}]
            </if>
            and ua.card_no != ''
            <if test="smallVenueMemberCardReportQueryDto.name != null and smallVenueMemberCardReportQueryDto.name !='' ">
                and umu.name like concat('%',#{smallVenueMemberCardReportQueryDto.name},'%')
            </if>
            <if test="smallVenueMemberCardReportQueryDto.phone != null and smallVenueMemberCardReportQueryDto.phone !='' ">
                and umu.telephone like concat(#{smallVenueMemberCardReportQueryDto.phone},'%')
            </if>
            <if test="smallVenueMemberCardReportQueryDto.cardNo != null and smallVenueMemberCardReportQueryDto.cardNo !='' ">
                and ua.card_no like concat('%',#{smallVenueMemberCardReportQueryDto.cardNo},'%')
            </if>
        </where>
    </sql>


    <sql id="memberDetail">
        from
        um_merchant_user umu
        left join um_merchant_user_tag umut on umut.merchant_id = umu.merchant_id and umut.business_user_id = umu.id
        <where>
            umu.merchant_id = #{smallVenueMemberReportQueryDto.merchantId}
            <if test="smallVenueMemberReportQueryDto.merchantUserId != null">
                and umu.id = #{smallVenueMemberReportQueryDto.merchantUserId}
            </if>
            <if test="smallVenueMemberReportQueryDto.userId != null">
                and umu.user_id = #{smallVenueMemberReportQueryDto.userId}
            </if>
            <if test="userTagList != null and userTagList.length > 0">
                and umut.user_tags @> array [#{userTagList,typeHandler=com.lyy.user.infrastructure.typehandler.ArrayTypeHandlerPg}]
            </if>
            <if test="smallVenueMemberReportQueryDto.userType != null and smallVenueMemberReportQueryDto.userType.size>0 ">
                and umu.user_type in
                <foreach collection="smallVenueMemberReportQueryDto.userType" item="type" open="(" close=")"
                         separator=",">
                    #{type}
                </foreach>
            </if>
            <if test="smallVenueMemberReportQueryDto.name != null and smallVenueMemberReportQueryDto.name !='' ">
                and umu.name like concat('%',#{smallVenueMemberReportQueryDto.name},'%')
            </if>
            <if test="smallVenueMemberReportQueryDto.phone != null and smallVenueMemberReportQueryDto.phone !='' ">
                and umu.telephone like concat(#{smallVenueMemberReportQueryDto.phone},'%')
            </if>
        </where>
    </sql>


    <select id="queryUserMemberReportV2" resultMap="SmallVenueMemberReportDtoMap">
        select
        umu.id as merchantUserId
        , umu.user_id as userId
        , umu.telephone as phone
        , umu.name as name
        , umu.user_type as userType
        , umu.name as levelName
        , umu.gender as gender
        , umu.birthday as birthday
        , umu.province_id as provinceId
        , umu.city_id as cityId
        , umu.region_id as regionId
        , umu.address as adress
        , umu.create_time as initTime
        , umut.user_tags
        , uml.name as memberLevelName
        from
        um_merchant_user umu
        left join um_merchant_user_tag umut on umut.merchant_id = umu.merchant_id and umut.business_user_id = umu.id
        left join um_member umm on
        umm.merchant_user_id = umu.id
        left join um_member_level uml on
        uml.id = umm.member_level_id
        <where>
            umu.merchant_id = #{smallVenueMemberReportQueryDto.merchantId}
            <if test="smallVenueMemberReportQueryDto.merchantUserId != null">
                and umu.id = #{smallVenueMemberReportQueryDto.merchantUserId}
            </if>
            <if test="smallVenueMemberReportQueryDto.userId != null">
                and umu.user_id = #{smallVenueMemberReportQueryDto.userId}
            </if>
            <if test="userTagList != null and userTagList.length > 0">
                and umut.user_tags @> array
                [#{userTagList,typeHandler=com.lyy.user.infrastructure.typehandler.ArrayTypeHandlerPg}]
            </if>
            <if test="smallVenueMemberReportQueryDto.userType != null and smallVenueMemberReportQueryDto.userType.size>0 ">
                and umu.user_type in
                <foreach collection="smallVenueMemberReportQueryDto.userType" item="type" open="(" close=")"
                         separator=",">
                    #{type}
                </foreach>
            </if>
            <if test="smallVenueMemberReportQueryDto.name != null and smallVenueMemberReportQueryDto.name !='' ">
                and umu.name like concat('%',#{smallVenueMemberReportQueryDto.name},'%')
            </if>
            <if test="smallVenueMemberReportQueryDto.phone != null and smallVenueMemberReportQueryDto.phone !='' ">
                and umu.telephone like concat(#{smallVenueMemberReportQueryDto.phone},'%')
            </if>
        </where>
        order by umu.create_time desc
        limit #{smallVenueMemberReportQueryDto.pageSize} offset #{startIndex}
    </select>
</mapper>