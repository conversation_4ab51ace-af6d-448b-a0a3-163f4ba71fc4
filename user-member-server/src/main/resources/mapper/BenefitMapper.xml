<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.benefit.repository.BenefitMapper">
    <resultMap id="baseResultMap" type="com.lyy.user.domain.benefit.entity.Benefit">

        <result column="merchant_id" property="merchantId"></result>
        <result column="is_active" property="active"></result>
    </resultMap>

    <insert id="insertBatch">
        insert into um_benefit(id,merchant_id,title,sub_title,
        code,classify,benefit_count,expiry_date_category,
        up_time,down_time,show_date_category,show_up_time,show_down_time,
        create_time,update_time,is_active)
        values
        <foreach collection="list" item="e" separator=",">
            (#{e.id},#{e.merchantId},#{e.title},#{e.subTitle},
            #{e.code},#{e.classify},#{e.benefitCount},#{e.expiryDateCategory},
            #{e.upTime},#{e.downTime},#{e.showDateCategory},#{e.showUpTime},#{e.showDownTime},
            #{e.createTime},#{e.updateTime},#{e.active})
        </foreach>
    </insert>

    <update id="updateBenefit">
        update um_benefit set create_time = now(),update_time = now()
        where merchant_id = #{merchantId} and id = #{benefitId}
    </update>

    <select id="queryBenefitList" resultMap="baseResultMap">
        select ub.* from um_account ua
        inner join um_account_benefit uab on ua.id = uab.account_id
        inner join um_benefit ub on uab.benefit_id = ub.id
        <where>
            ua.user_id = #{lyyUserId}
            and ua.merchant_id = #{merchantId}
            and ub.is_active = true
        </where>

    </select>

    <select id="queryBenefitListByMerchant" resultMap="baseResultMap">
        select distinct ub.* from um_benefit ub
        inner join um_benefit_rule ubr on ub.id  = ubr.benefit_id
        <where>
            ubr.generate_type  = #{generateType} and ub.merchant_id  = #{merchantId} and ub.is_active = true
        </where>
    </select>

    <select id="queryBenefitListByScope" resultMap="baseResultMap">
        select distinct ub.* from um_benefit ub
        left join um_benefit_scope ubs on ub.id  = ubs.benefit_id and ub.merchant_id = ubs.merchant_id
        <where>
          <if test="merchantId != null">
              ub.merchant_id  = #{merchantId}
          </if>
          <if test="classify != null">
              and ub.classify  = #{classify}
          </if>
          <if test="applicable != null">
              and ubs.applicable = #{applicable}
          </if>

          <choose>
              <when test="associatedId != null">
                  and ubs.associated_id =  #{associatedId}
              </when>
              <otherwise>
                  and ubs.benefit_id is null
              </otherwise>
          </choose>

          <if test="expiryDateCategory != null">
              and ub.expiry_date_category =  #{expiryDateCategory}
          </if>
          <if test="upTime != null and upTime != '' ">
              and ub.up_time =  #{upTime}
          </if>
          <if test="downTime != null and downTime !='' ">
              and ub.down_time =  #{downTime}
          </if>
          and ub.is_active = true
        </where>
        order by ub.create_time desc
    </select>

    <select id="queryBenefitListByGroupIdList" resultMap="baseResultMap">
        select distinct ub.* from um_benefit ub
        left join um_benefit_scope ubs on ub.id  = ubs.benefit_id
        <where>
            <if test="merchantId != null">
                ub.merchant_id  = #{merchantId}
            </if>
            <if test="classify != null">
                and ub.classify  = #{classify}
            </if>
            <if test="applicable != null">
                and ubs.applicable = #{applicable}
            </if>
            <if test="associatedIdList != null and associatedIdList.size > 0">
                and ubs.associated_id in
                <foreach collection="associatedIdList" item="associatedId" open="(" close=")" separator=",">
                    #{associatedId}
                </foreach>
            </if>
            and ub.is_active = true
        </where>
    </select>

    <select id="selectCountBenefitScope" resultType="java.lang.Long">

        select count(*) from um_benefit ub
        left join um_benefit_scope ubs on ub.id  = ubs.benefit_id
         WHERE ub.merchant_id  = #{merchantId} and ubs.merchant_id  = #{merchantId}
          and ub.classify  = #{benefitClassifyCode}  and ubs.applicable  =  #{applicable} and ubs.associated_id   = #{associatedId}
    </select>

    <select id="selectBenefitScopeList" resultType="com.lyy.user.domain.benefit.entity.BenefitScope" >
        select distinct ubs.* from um_benefit ub
        left join um_benefit_scope ubs on ub.id  = ubs.benefit_id
         WHERE ub.merchant_id  = #{merchantId} and ubs.merchant_id  = #{merchantId}
          and ub.classify  = #{benefitClassifyCode}  and ubs.applicable  =  #{applicable} and ubs.associated_id   = #{associatedId}
    </select>

    <select id="getBenefitFromAccountRecord" resultType="com.lyy.user.domain.benefit.dto.BenefitRecordCountDTO" >
        select
        b.*,
        sum(ar.actual_benefit) as actual_benefit
        from um_account_record ar
        left join um_benefit b on  ar.merchant_id = b.merchant_id and ar.benefit_id = b.id
        where ar.merchant_id = #{accountBenefitAdjust.merchantId}
        and ar.user_id = #{accountBenefitAdjust.userId}
        and ar.mode= #{mode}
        and ar.benefit_classify in
        <foreach collection="classifyList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="accountBenefitAdjust.outTradeNo != null and accountBenefitAdjust.outTradeNo != ''">
            and ar.out_trade_no = #{accountBenefitAdjust.outTradeNo}
        </if>
        <if test="accountBenefitAdjust.orderNo != null and accountBenefitAdjust.orderNo != ''">
            and ar.order_no = #{accountBenefitAdjust.orderNo}
        </if>
        group by b.id
    </select>

    <select id="getBenefitId" resultType="java.lang.Long">
        select bc.benefit_id
            from um_benefit_scope bc
            where bc.benefit_id in
                  (select ub.id
                   from um_benefit ub
                        join um_benefit_scope ubs on ub.id = ubs.benefit_id
                   where ub.merchant_id = #{merchantId}
                     and ub.classify = #{benefitClassifyCode}
                     and ub.is_active = true
                     and ubs.merchant_id = #{merchantId}
                     and ubs.applicable = #{applicable}
                     and ubs.associated_id = #{associatedId})
              and bc.merchant_id = #{merchantId}
            group by bc.benefit_id
            having count(benefit_id) = 1
            limit 1
    </select>

    <update id="updateBenefitByIds">
        update um_benefit set update_time = now()
        where merchant_id = #{merchantId} and id in
        <foreach collection="benefitIdList" item="benefitId" open="(" close=")" separator=",">
            #{benefitId}
        </foreach>
    </update>

    <select id="findBenefitIdByExcludeClassify" resultType="java.lang.Long">
       select id from um_benefit where merchant_id=#{dto.merchantId}
       and classify not in
       <foreach collection="dto.excludeClassifys" item="classify" open="(" close=")" separator=",">
            #{classify}
       </foreach>
       and id in
       <foreach collection="dto.benefitIds" item="benefitId" open="(" close=")" separator=",">
            #{benefitId}
       </foreach>
    </select>

    <select id="findBenefitIdByIncludeClassify" resultType="java.lang.Long">
        select id from um_benefit where merchant_id=#{dto.merchantId}
        and classify in
        <foreach collection="dto.includeClassifys" item="classify" open="(" close=")" separator=",">
            #{classify}
        </foreach>
        and id in
        <foreach collection="dto.benefitIds" item="benefitId" open="(" close=")" separator=",">
            #{benefitId}
        </foreach>
    </select>

    <select id="findShardExpireNoScopeBenefit" resultMap="baseResultMap">
        select ub.*
        from um_benefit ub
        left join um_benefit_scope ubs on ub.id = ubs.benefit_id
        where
            (ub.merchant_id = #{merchantId} or exists(select 1))
            and ubs.benefit_id is null
            and ub.expiry_date_category = 1
            and ub.down_time <![CDATA[ <= ]]> #{date}
            and ub.id <![CDATA[ > ]]> #{minId}
        order by ub.id
        limit #{limit}
    </select>

    <select id="findMerchantExpireNoScopeBenefit" resultMap="baseResultMap">
        select ub.*
        from um_benefit ub
                 left join um_benefit_scope ubs on ub.id = ubs.benefit_id
        where
          ub.merchant_id = #{merchantId}
          and ubs.benefit_id is null
          and ub.expiry_date_category = 1
          and ub.down_time <![CDATA[ <= ]]> #{date}
          and ub.id <![CDATA[ > ]]> #{minId}
        order by ub.id
            limit #{limit}
    </select>
</mapper>