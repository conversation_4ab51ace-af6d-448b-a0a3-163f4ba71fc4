<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.account.repository.AccountBenefitTmpMapper">
    <insert id="insertBatch">
        insert into um_account_benefit_tmp(id,account_id,merchant_id,user_id,merchant_user_id,
        benefit_id,total,balance,classify,status,expiry_date_category,
        up_time,down_time,create_time,create_by,update_time,update_by,
        resource,resource_id,merchant_benefit_classify_id,use_rule_id,
        store_id,value_type)
        values
        <foreach collection="list" item="e" separator=",">
            (#{e.id},#{e.accountId},#{e.merchantId},#{e.userId},#{e.merchantUserId},
            #{e.benefitId},#{e.total},#{e.balance},#{e.classify},#{e.status},#{e.expiryDateCategory},
            #{e.upTime},#{e.downTime},#{e.createTime},#{e.createBy},#{e.updateTime},#{e.updateBy},
            #{e.resource},#{e.resourceId},#{e.merchantBenefitClassifyId},#{e.useRuleId},
            #{e.storeId},#{e.valueType})
        </foreach>
    </insert>
</mapper>