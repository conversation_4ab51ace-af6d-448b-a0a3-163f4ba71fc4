<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.member.repository.MemberLevelMapper">


    <select id="selectByIdForUpdate" parameterType="java.lang.Long"
            resultType="com.lyy.user.domain.member.entity.MemberLevel">
                    select * from um_member_level where  id = #{id} for update
                </select>
    <select id="findLevelOfMemberGrowValue"
            resultType="com.lyy.user.domain.member.entity.MemberLevel">
                select ml.*
                from um_member m
                left join um_member_level ml on m.member_group_id = ml.member_group_id
                <if test="upgrade">
                    and (ml.active is null or ml.active = true)
                </if>
                left join um_member_level ml2 on m.member_level_id = ml2.id
                where m.id = #{memberId}
                and m.merchant_id = #{merchantId}
        <choose>
            <when test="upgrade">
                and ml.grow_value &gt; ml2.grow_value
                and ml.grow_value &lt;= m.grow_value
                order by ml.grow_value asc
            </when>
            <otherwise>
                and ml.grow_value &gt; m.grow_value
                and ml.grow_value &lt;= ml2.grow_value
                order by ml.grow_value desc
            </otherwise>
        </choose>
    </select>

    <select id="findLevelOfMemberGrowValueDemoting"
            resultType="com.lyy.user.domain.member.entity.MemberLevel">
        select ml.*
        from um_member m
        left join um_member_level ml on m.member_group_id = ml.member_group_id and m.member_level_id != ml.id
        left join um_member_level ml2 on m.member_level_id = ml2.id
        where m.id = #{memberId}
        and ml.grow_value &gt;= m.grow_value and ml.grow_value &lt;= ml2.grow_value
        order by ml.grow_value asc
    </select>

    <select id="findNextLevel"
            resultType="com.lyy.user.domain.member.entity.MemberLevel">
        select ml.*
        from um_member_level ml
        where merchant_id = #{merchantId}
        and (ml.active is null or ml.active = true)
        and exists (
            select 1
            from um_member_level ml1
            where ml.merchant_id = ml1.merchant_id
            and ml.member_group_id = ml1.member_group_id
            and ml1.id =#{memberLevelId}
            <choose>
                <when test="isNext">
                    and ml.grow_value &gt; ml1.grow_value
                </when>
                <otherwise>
                    and ml.grow_value &lt; ml1.grow_value
                </otherwise>
            </choose>
        )
        <choose>
            <when test="isNext">
                order by grow_value asc
            </when>
            <otherwise>
                order by grow_value desc
            </otherwise>
        </choose>
        limit #{size}
    </select>

    <select id="findLastLevel"
            resultType="com.lyy.user.domain.member.entity.MemberLevel">
        select ml.*
        from um_member_level ml
        where merchant_id = #{merchantId}
        and exists (
            select 1
            from um_member_level ml1
            where ml.merchant_id = ml1.merchant_id
            and ml.member_group_id = ml1.member_group_id
            and ml1.id =#{memberLevelId}
            and ml.grow_value &lt; ml1.grow_value
        )
        order by grow_value desc
    </select>


    <select id="batchSelectMemberLevelName" resultType="com.lyy.user.account.infrastructure.member.dto.MemberLevelInfoNameDTO">
        select id "memberLevelId",name "memberLevelName" from um_member_level where id in
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="selectByIdAndMerchant" resultType="com.lyy.user.domain.member.entity.MemberLevel">
        select * from um_member_level uml where uml.merchant_id = #{merchantId} and uml.id = #{id}
    </select>
</mapper>
