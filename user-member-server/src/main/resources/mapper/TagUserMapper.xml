<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.user.repository.TagUserMapper">
  <resultMap id="BaseResultMap" type="com.lyy.user.domain.user.entity.TagUser">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="NUMERIC" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="merchant_id" jdbcType="NUMERIC" property="merchantId" />
    <result column="category" jdbcType="CHAR" property="category" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="is_active" jdbcType="BOOLEAN" property="active" />
    <result column="tag_type" jdbcType="SMALLINT" property="tagType" />
    <result column="state" jdbcType="SMALLINT" property="state" />
    <result column="business_type" jdbcType="SMALLINT" property="businessType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="createdby" jdbcType="NUMERIC" property="createdby" />
    <result column="updatedby" jdbcType="NUMERIC" property="updatedby" />
  </resultMap>

  <resultMap id="MerchantUserTagDTO" type="com.lyy.user.domain.user.dto.MerchantUserTagDTO" extends="BaseResultMap">
    <result column="business_user_id" jdbcType="NUMERIC" property="merchantUserId"/>
  </resultMap>

    <update id="changeTagStatus" parameterType="com.lyy.user.domain.user.entity.TagUser">
        update um_tag_user
        set updatedby   = #{operatorId},
        <if test="active!=null">
            is_active = #{active},
        </if>
        <if test="state!=null">
            state = #{state},
        </if>
        update_time = now()
        where merchant_id =#{merchantId}
         and id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="selectListByMerchantUser" resultMap="BaseResultMap">
        select utu.* from um_tag_user utu
        join (
            select unnest(user_tags) as id
            from um_merchant_user_tag
            where business_user_id = #{merchantUserId} and merchant_id = #{merchantId} and tag_type = #{tagType}
        ) a on utu.id = a.id
        <where>
            utu.merchant_id = #{merchantId}
            and utu.tag_type = #{tagType}
            and utu.is_active = true
            and utu.state =0
            <if test="businessTypeList != null and businessTypeList.size > 0">
                and utu.business_type in
                <foreach collection="businessTypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectNewListByMerchantUser" resultMap="MerchantUserTagDTO">
        select utu.*,a.business_user_id from um_tag_user utu
        join (
        select unnest(user_tags) as id, business_user_id
        from um_merchant_user_tag
        where  merchant_id = #{merchantId} and tag_type = #{tagType}
        <if test="merchantUserIds != null and merchantUserIds.size >0">
            and business_user_id in
            <foreach collection="merchantUserIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ) a on utu.id = a.id
        <where>
            utu.merchant_id = #{merchantId}
            and utu.tag_type = #{tagType}
            and utu.is_active = true
            and utu.state =0
            <if test="businessTypeList != null and businessTypeList.size > 0">
                and utu.business_type in
                <foreach collection="businessTypeList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updateTagUser" parameterType="com.lyy.user.domain.user.entity.TagUser">
        update um_tag_user
        set updatedby   = #{tagUser.updatedby},
        name = #{tagUser.name},
        <if test="tagUser.active!=null">
            is_active = #{tagUser.active},
        </if>
        <if test="tagUser.state!=null">
            state = #{tagUser.state},
        </if>
        <if test="tagUser.category!=null">
            category = #{tagUser.category},
        </if>
        <if test="tagUser.code!=null and tagUser.code!=''">
            code = #{tagUser.state},
        </if>
        <if test="tagUser.description!=null and tagUser.description!=''">
            description = #{tagUser.description},
        </if>
        <if test="tagUser.tagType!=null and tagUser.tagType!=''">
            tag_type = #{tagUser.tagType},
        </if>
        <if test="tagUser.businessType!=null">
            business_type = #{tagUser.businessType},
        </if>
        update_time = now()
        where  id =#{tagUser.id}
        and merchant_id =#{tagUser.merchantId}

    </update>

    <delete id="deleteByTagId" >
        delete  from um_tag_user
        where merchant_id = #{merchantId}
          and id =#{tagId}
    </delete>

    <select id="selectBatchIds" resultType="com.lyy.user.domain.user.entity.TagUser">
        select * from um_tag_user
        where merchant_id = #{merchantId}
        and id in
        <foreach collection="tagId" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <sql id="listTagByUserSql">
        from um_tag_user t
        where t.id in    (
            select unnest(u.user_tags)
            from um_merchant_user_tag u
            where u.merchant_id = #{dto.merchantId}
            and u.business_user_id = #{dto.merchantUserId}
        )
        and t.merchant_id = #{dto.merchantId}
        and t.state =0
        <if test="dto.tagType != null">
            and t.tag_type = #{dto.tagType}
        </if>
        <if test="dto.active != null">
            and t.is_active = #{dto.active}
        </if>
        <if test="dto.category != null">
            and t.category = #{dto.category}
        </if>
        <if test="dto.exactName != null and dto.exactName != ''">
            and t.name = #{dto.exactName}
        </if>
        <if test="dto.businessType != null">
            and t.business_type = #{dto.businessType}
        </if>
    </sql>
    <select id="countListTagByUser" resultType="java.lang.Long">
        select count (t.id) as num
        <include refid="listTagByUserSql"/>
    </select>

    <select id="listTagByUser" resultType="com.lyy.user.account.infrastructure.user.dto.tag.TagUserListDTO">
        select
            t.*
        <include refid="listTagByUserSql"/>
        order by t.create_time desc
        limit #{pageSize} offset #{start}
    </select>
    <sql id="tagUserInfoCondition">
        from um_merchant_user s
        where s.merchant_id = #{dto.merchantId}
        <if test="dto.active != null">
            and s.is_active = #{dto.active}
        </if>
        and s.id in (
            select business_user_id
            from um_merchant_user_tag u
            where u.merchant_id = #{dto.merchantId}
            and u.user_tags @> array [#{tagIds}]
        )
        <if test="dto.name != null and dto.name != ''">
            and (
            s.name like  concat('%',#{dto.name},'%')
            <if test="queryIdFlag">
                or   cast(s.id as VARCHAR) like concat('%',#{dto.name},'%')
                or s.telephone like  concat('%',#{dto.name},'%')
            </if>
            )
        </if>
    </sql>

    <select id="countListTagUserInfoByTagIds" resultType="java.lang.Long">
        select
        count(0) num
        <include refid="tagUserInfoCondition"></include>
    </select>



    <select id="listTagUserInfoByTagIds" resultType="com.lyy.user.account.infrastructure.user.dto.tag.TagUserInfoDTO">
        select
        s.id businessUserId,s.telephone,s.name,s.gender,s.head_img
        <include refid="tagUserInfoCondition"></include>
        order by s.create_time desc
        limit #{pageSize} offset #{start}
    </select>

  <select id="listTagUserInfoByTagIdsV2" resultType="com.lyy.user.account.infrastructure.user.dto.tag.TagUserInfoDTO">
      select
      s.id businessUserId,s.telephone,s.name,s.gender,s.head_img
      from um_merchant_user s
      where s.merchant_id = #{dto.merchantId}
      <if test="dto.active != null">
          and s.is_active = #{dto.active}
      </if>
      and s.id in (
      select business_user_id
      from um_merchant_user_tag u
      where u.merchant_id = #{dto.merchantId}
      and u.user_tags @> array [#{tagIds}]
      <if test="notHandleUserIds != null and notHandleUserIds.size > 0">
          and u.business_user_id not in
          <foreach collection="notHandleUserIds" item="item" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      )
      <if test="dto.name != null and dto.name != ''">
          and (
          s.name like  concat('%',#{dto.name},'%')
          <if test="queryIdFlag">
              or   cast(s.id as VARCHAR) like concat('%',#{dto.name},'%')
              or s.telephone like  concat('%',#{dto.name},'%')
          </if>
          )
      </if>
      order by s.create_time desc
      limit #{pageSize} offset #{start}
    </select>
</mapper>