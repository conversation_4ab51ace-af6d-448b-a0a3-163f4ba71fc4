<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.benefit.repository.BenefitConsumeRuleMapper">
  <resultMap id="BaseResultMap" type="com.lyy.user.domain.benefit.entity.BenefitConsumeRule">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="NUMERIC" property="id" />
    <result column="merchant_id" jdbcType="NUMERIC" property="merchantId" />
    <result column="benefit_classify" jdbcType="NUMERIC" property="benefitClassify" />
    <result column="weight" jdbcType="NUMERIC" property="weight" />
    <result column="expire_priority" jdbcType="NUMERIC" property="expirePriority" />
    <result column="is_default" jdbcType="NUMERIC" property="isDefault" />
    <result column="is_active" jdbcType="NUMERIC" property="isActive" />
    <result column="create_by" jdbcType="NUMERIC" property="createBy" />
    <result column="update_by" jdbcType="NUMERIC" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <update id="updateBenefitConsumeRule">
    update um_benefit_consume_rule
    <set>
        <if test="record.weight != null">
          weight = #{record.weight},
        </if>
        <if test="record.expirePriority != null">
          expire_priority = #{record.expirePriority},
        </if>
        <if test="record.isDefault != null">
          is_default = #{record.isDefault},
        </if>
        <if test="record.updateTime != null">
          update_time = #{record.updateTime},
        </if>
        <if test="record.updateBy != null">
          update_by = #{record.updateBy},
        </if>
    </set>
    where merchant_id = #{record.merchantId}
    AND benefit_classify = #{record.benefitClassify}
    AND id = #{record.id}
  </update>
  <select id="getByMerchantIdAndClassify" resultMap="BaseResultMap">
    select * from um_benefit_consume_rule where merchant_id = #{merchantId}
    and benefit_classify = #{classify} and is_active = true limit 1
  </select>
</mapper>