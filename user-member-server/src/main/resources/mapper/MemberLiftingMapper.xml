<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.member.repository.MemberLiftingMapper">


                <select id="selectByIdForUpdate" parameterType="java.lang.Long" resultType="com.lyy.user.domain.member.entity.MemberLifting">
                    select * from um_member_lifting where  id = #{id} for update
                </select>

</mapper>
