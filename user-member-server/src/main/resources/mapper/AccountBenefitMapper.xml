<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.account.repository.AccountBenefitMapper">
    <resultMap id="BaseResultMap" type="com.lyy.user.domain.account.entity.AccountBenefit">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="NUMERIC" property="id" />
        <result column="account_id" jdbcType="NUMERIC" property="accountId" />
        <result column="user_id" jdbcType="NUMERIC" property="userId" />
        <result column="merchant_id" jdbcType="NUMERIC" property="merchantId" />
        <result column="benefit_id" jdbcType="NUMERIC" property="benefitId" />
        <result column="total" jdbcType="NUMERIC" property="total" />
        <result column="balance" jdbcType="NUMERIC" property="balance" />
        <result column="classify" jdbcType="SMALLINT" property="classify" />
        <result column="status" jdbcType="SMALLINT" property="status" />
        <result column="expiry_date_category" jdbcType="VARCHAR" property="expiryDateCategory" />
        <result column="up_time" jdbcType="VARCHAR" property="upTime" />
        <result column="down_time" jdbcType="VARCHAR" property="downTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_by" jdbcType="NUMERIC" property="createBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_by" jdbcType="NUMERIC" property="updateBy" />
        <result column="resource" jdbcType="SMALLINT" property="resource" />
        <result column="resource_id" jdbcType="NUMERIC" property="resourceId" />
        <result column="merchant_user_id" jdbcType="NUMERIC" property="merchantUserId"/>
        <result column="merchant_benefit_classify_id" jdbcType="NUMERIC" property="merchantBenefitClassifyId"/>
        <result column="use_rule_id" jdbcType="NUMERIC" property="useRuleId"/>
        <result column="store_id" jdbcType="NUMERIC" property="storeId"/>
        <result column="actual_value" jdbcType="NUMERIC" property="actualValue"/>
    </resultMap>
    <sql id="ALL_COLUMN">
        id,account_id,user_id,benefit_id,total,balance,classify,status,
        expiry_date_category,up_time,down_time,create_time,create_by,
        update_time,update_by,resource,resource_id,merchant_id,merchant_user_id
    </sql>
    <insert id="insertBatch">
        insert into um_account_benefit(id,account_id,merchant_id,user_id,merchant_user_id,
                                       benefit_id,total,balance,classify,status,expiry_date_category,
                                       up_time,down_time,create_time,create_by,update_time,update_by,
                                       resource,resource_id,merchant_benefit_classify_id,use_rule_id,
                                       store_id,value_type)
        values
        <foreach collection="list" item="e" separator=",">
            (#{e.id},#{e.accountId},#{e.merchantId},#{e.userId},#{e.merchantUserId},
             #{e.benefitId},#{e.total},#{e.balance},#{e.classify},#{e.status},#{e.expiryDateCategory},
             #{e.upTime},#{e.downTime},#{e.createTime},#{e.createBy},#{e.updateTime},#{e.updateBy},
             #{e.resource},#{e.resourceId},#{e.merchantBenefitClassifyId},#{e.useRuleId},
             #{e.storeId},#{e.valueType})
        </foreach>
    </insert>

    <update id="increaseBalanceAndTotal">
        update um_account_benefit
        <set>
            <if test="balance != null">
                balance = balance + #{balance},
            </if>
            <if test="total != null">
                total = total + #{total},
            </if>
            update_time = now(), update_by = #{operator}
        </set>
        where id = #{id} and merchant_id = #{merchantId}
    </update>

    <update id="changeBalance">
        update um_account_benefit
        set balance = balance + #{change},
        update_time = #{updateTime}, update_by = #{operator}
        where id = #{id} and merchant_id = #{merchantId}
        and balance + #{change} >= 0
    </update>

    <select id="queryForConsume" resultType="com.lyy.user.domain.account.dto.AccountBenefitWithScopeDTO">
        select ab.total as total, ab.id as accountBenefitId, ab.classify, ab.balance as amount,
               ab.benefit_id as benefitId, ab.status as status,
               ab.create_time, ab.up_time, ab.down_time, ab.expiry_date_category expiryDateCategory
        from um_account_benefit ab
        <where>
        <if test="!isAll">
            and ab.status = 1
            <if test="serviceType == null">
                and ab.balance != 0
            </if>
        </if>
        and ab.merchant_id = #{merchantId}
        and ab.merchant_user_id = #{merchantUserId}
        <if test="classify != null and classify.size > 0">
            and ab.classify in
            <foreach collection="classify" item="cid" open="(" close=")" separator=",">
                #{cid}
            </foreach>
        </if>
         <if test="excludeClassify != null and excludeClassify.size > 0">
            and ab.classify not in
            <foreach collection="excludeClassify" item="cid" open="(" close=")" separator=",">
                #{cid}
            </foreach>
        </if>
        <if test="benefitId != null and benefitId.size > 0">
            and ab.benefit_id in
            <foreach collection="benefitId" item="bid" open="(" close=")" separator=",">
                #{bid}
            </foreach>
        </if>
        </where>
    </select>
    <select id="selectInvalidateRecord" resultType="com.lyy.user.domain.account.entity.AccountBenefit">
        select id, merchant_user_id as merchantUserId, merchant_id as merchantId, benefit_id as benefitId,
               account_id as accountId, balance, total, classify,user_id as userId
        from um_account_benefit
        where expiry_date_category = 1 and down_time::timestamp &lt; now() and (merchant_id = #{merchantId} or 1 = 1)
    </select>

    <sql id="BENEFIT_RECORD_QUERY_CONDITION">
        <if test="dto.classify != null and dto.classify.size > 0">
            and ab.classify in
            <foreach collection="dto.classify" item="cid" open="(" close=")" separator=",">
                #{cid}
            </foreach>
        </if>
        <if test="dto.benefitIds != null and dto.benefitIds.size > 0">
            and ab.id in
            <foreach collection="dto.benefitIds" item="bid" open="(" close=")" separator=",">
                #{bid}
            </foreach>
        </if>
        <if test="dto.queryType != null and dto.queryType == 1">
            and ab.status = 1
            and ab.balance != 0
        </if>
        <if test="dto.queryType != null and dto.queryType == 2">
            and ab.status = 2
            and ab.balance != 0
        </if>
        <if test="dto.queryType != null and dto.queryType == 3">
            and ab.status = 1
            and ab.total > ab.balance
        </if>
        <if test="dto.startTime != null ">
            and COALESCE(ab.update_time ,ab.create_time) <![CDATA[ >= ]]> #{dto.startTime}
        </if>
        <if test="dto.endTime != null ">
            and COALESCE(ab.update_time ,ab.create_time) <![CDATA[ <= ]]> #{dto.endTime}
        </if>
        <if test="dto.notHandleBenefitIdList!=null and dto.notHandleBenefitIdList.size()>0" >
            and ab.id  not in
            <foreach collection="dto.notHandleBenefitIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dto.excludeClassify!=null and dto.excludeClassify.size()>0" >
            and ab.classify not in
            <foreach collection="dto.excludeClassify" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

    </sql>
    <sql id="BENEFIT_SCOPE_QUERY_CONDITION">
        <if test="dto.equipmentTypeIds != null and dto.equipmentTypeIds.size > 0">
            <!-- applicable = 1：不存在设备类型限制（通用） or 存在设备类型限制，且满足查询条件 -->
            and (
                not exists(
                    select s.id
                    from um_benefit_scope s
                    where s.benefit_id = ab.benefit_id
                    and s.applicable = 1
                )
                or exists(
                    select s.id
                    from um_benefit_scope s
                    where s.benefit_id = ab.benefit_id
                    and s.applicable = 1
                    and s.associated_id in
                        <foreach collection="dto.equipmentTypeIds" item="typeId" open="(" close=")" separator=",">
                            #{typeId}
                        </foreach>
                )
            )
        </if>
        <if test="dto.storeIds != null and dto.storeIds.size > 0">
            <!-- applicable = 2：不存在场地限制（通用） or 存在场地限制，且满足查询条件 -->
            and (
                not exists (
                    select s.id
                    from um_benefit_scope s
                    where s.benefit_id = ab.benefit_id
                    and s.applicable = 2
                )
                or exists (
                    select s.id
                    from um_benefit_scope s
                    where s.benefit_id = ab.benefit_id
                    and s.applicable = 2
                    and s.associated_id in
                        <foreach collection="dto.storeIds" item="storeId" open="(" close=")" separator=",">
                            #{storeId}
                        </foreach>
                )
            )
        </if>
    </sql>

    <sql id="BENEFIT_RECORD_SQL_COLUMN">
        ab.id,
        ab.total,
        ab.balance,
        ab.expiry_date_category expiryDateCategory,
        ab.up_time upTime,
        ab.down_time downTime,
        ab.status,
        ab.classify,
        ab.create_time,
        ab.merchant_benefit_classify_id merchantBenefitClassifyId,
        ab.use_rule_id useRuleId,
        ab.store_id storeId,
        ab.value_type valueType
    </sql>

    <sql id="BENEFIT_RECORD_SQL_WHERE">
        <where>
            and ab.merchant_id = #{dto.merchantId}
            and ab.merchant_user_id = #{dto.merchantUserId}
            <include refid="BENEFIT_SCOPE_QUERY_CONDITION"/>
            <include refid="BENEFIT_RECORD_QUERY_CONDITION"/>
        </where>
    </sql>

    <select id="allBenefitRecord" resultType="com.lyy.user.account.infrastructure.account.dto.AccountBenefitDTO">
        select
        <include refid="BENEFIT_RECORD_SQL_COLUMN"/>
        from um_account_benefit ab
        <include refid="BENEFIT_RECORD_SQL_WHERE"></include>
    </select>
    <select id="listBenefitRecord" resultType="com.lyy.user.account.infrastructure.account.dto.AccountBenefitDTO">
        select
        <include refid="BENEFIT_RECORD_SQL_COLUMN"/>
        from um_account_benefit ab
        <include refid="BENEFIT_RECORD_SQL_WHERE"></include>
        order by
            case when
                ab.create_time is null
            then
                ab.update_time
            else
                ab.create_time
            end
        desc
        <if test="queryAll==null">
            limit #{pageSize} offset #{start}
        </if>

    </select>


    <select id="countBenefitRecord" resultType="java.lang.Long">
        select count(distinct ab.id)
        from um_account_benefit ab
        <include refid="BENEFIT_RECORD_SQL_WHERE"></include>
    </select>

    <select id="getAccountBenefit" resultType="com.lyy.user.domain.account.entity.AccountBenefit">
        select <include refid="ALL_COLUMN"/> from um_account_benefit
        where merchant_id = #{merchantId}
        AND user_id = #{userId}
        AND status = 1
        <if test="merchantUserId != null">
            AND merchant_user_id = #{merchantUserId}
        </if>
        <if test="benefitId != null">
            AND benefit_id = #{benefitId}
        </if>
        <if test="accountId != null">
            AND account_id = #{accountId}
        </if>
        order by create_time desc limit 1
    </select>

    <select id="findUserAllBenefit" resultType="com.lyy.user.domain.account.entity.AccountBenefit">
        select uab.id,uab.merchant_id,uab.merchant_user_id,uab.user_id,uab.merchant_benefit_classify_id,
        uab.use_rule_id,uab.expiry_date_category,uab.up_time,uab.down_time,uab.balance,uab.store_id,
        uab.value_type,uab.benefit_id,uab.total,uab.classify,uab.status,uab.account_id,
        uab.update_time, uab.create_time
        from um_account_benefit uab
                 join um_account ua on uab.account_id = ua.id
            and uab.merchant_id = ua.merchant_id
            and uab.user_id = ua.user_id
            and ua.merchant_id = #{dto.merchantId}
        where uab.merchant_id = #{dto.merchantId}
        and uab.user_id = #{dto.userId}
        and uab.status = 1
        and ua.status = 1
        and uab.classify = #{dto.classify}
        <if test="dto.cardNo != null and dto.cardNo != '' ">
            and ua.card_no = #{dto.cardNo}
        </if>
        <if test="dto.accountId != null ">
            and ua.id = #{dto.accountId}
        </if>
        <if test="dto.merchantBenefitIds != null and dto.merchantBenefitIds.size > 0 ">
            and uab.merchant_benefit_classify_id in
            <foreach collection="dto.merchantBenefitIds" item="merchantBenefitId" open="(" close=")" separator=",">
                #{merchantBenefitId}
            </foreach>
        </if>
        <if test="dto.accountBenefitIds != null and dto.accountBenefitIds.size > 0 ">
            and uab.id in
            <foreach collection="dto.accountBenefitIds" item="accountBenefitId" open="(" close=")" separator=",">
                #{accountBenefitId}
            </foreach>
        </if>
        <if test="dto.ignoreEmptyBenefit != null and dto.ignoreEmptyBenefit ">
            and uab.balance > 0
        </if>
    </select>

    <select id="smallVenueBenefitDetail" resultType="com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueBenefitDetailDTO">
         select
            uab.id "accountBenefitId",
            uab.down_time "downTime",
            uab.up_time "upTime",
            uab.create_time "createTime",
            uab.store_id "storeId",
            uab.total,
            uab.balance,
            uab.expiry_date_category expiryDateCategory,
            uab.use_rule_id "useRuleId",
            uab.merchant_benefit_classify_id "merchantBenefitClassifyId",
            ua.card_no "cardNo",
            ua.merchant_user_id "merchantUserId"
        from
            um_account_benefit uab
            left join um_account ua on uab.account_id =ua.id
        where
            uab.merchant_id =#{selectDTO.merchantId}
            and ua.merchant_id =#{selectDTO.merchantId}
            and uab.user_id =#{selectDTO.userId}
            and uab.account_id = #{selectDTO.accountId}
            <if test="selectDTO.storeId!= null">
                and uab.store_id =#{selectDTO.storeId}
            </if>
<!--            <if test="selectDTO.merchantBenefitClassifyId!= null">-->
<!--                and merchant_benefit_classify_id =#{selectDTO.merchantBenefitClassifyId}-->
<!--            </if>-->
            <if test="selectDTO.merchantBenefitClassifyIds!= null and selectDTO.merchantBenefitClassifyIds.size > 0">
                and uab.merchant_benefit_classify_id in
                <foreach collection="selectDTO.merchantBenefitClassifyIds" item="merchantBenefitClassifyId" open="(" close=")" separator=",">
                    #{merchantBenefitClassifyId}
                </foreach>
            </if>
            <if test="selectDTO.startTime != null and selectDTO.endTime != null">
                AND uab.create_time BETWEEN #{selectDTO.startTime} AND #{selectDTO.endTime}
            </if>
             <if test="selectDTO.accountBenefitIds != null and selectDTO.accountBenefitIds.size > 0 ">
                 and uab.id in
                <foreach collection="selectDTO.accountBenefitIds" item="accountBenefitId" open="(" close=")" separator=",">
                  #{accountBenefitId}
                 </foreach>
            </if>
    </select>


    <select id="benefitStatisticsGroupByStoreId" resultType="com.lyy.user.account.infrastructure.account.dto.smallvenue.UserBenefitStatisticsDTO">
        select
            account_id "accountId",
            store_id "storeId",
            merchant_benefit_classify_id "merchantBenefitClassifyId",
            balance
        from
            um_account_benefit
        where
            merchant_id =#{merchantId}
            and user_id =#{userId}
            and account_id =#{accountId}
    </select>

    <select id="listBenefitWithScope" resultType="com.lyy.user.domain.account.dto.AccountBenefitScopeDO">
        select ab.merchant_id, ab.id as accountBenefitId, ab.benefit_id, ab.balance,ab.classify,bs.applicable, bs.associated_id
        from um_account_benefit ab
                 left join um_benefit_scope bs on ab.benefit_id = bs.benefit_id and bs.applicable = #{query.applicable}
        where ab.merchant_id = #{query.merchantId}
          and ab.user_id = #{query.userId}
        <if test="query.classify != null and query.classify.size > 0">
            and ab.classify in
            <foreach collection="query.classify" item="cid" open="(" close=")" separator=",">
                #{cid}
            </foreach>
        </if>
    </select>

    <update id="updateAccountBenefitByAccountId">
        update  um_account_benefit set account_id=#{newAccountId} where merchant_id =#{merchantId} and account_id=#{oldAccountId}
    </update>


    <update id="updateAccountBenefitIdInfo">
        update um_account_benefit
        set user_id=#{userId},merchant_user_id=#{merchantUserId}
        where merchant_id=#{merchantId} and account_id=#{accountId}
    </update>

    <update id="batchUpdateAccountBenefitBalance">
        update um_account_benefit set balance=#{balance},update_time=now(),update_by=#{operatorId}
        where merchant_id=#{merchantId} and id in
        <foreach collection="accountBenefitIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="findBenefitByAccountIds"
            resultType="com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountBenefitInfo">
        select
        sum(balance) "balance",count(*) "num",account_id "accountId" ,merchant_benefit_classify_id "merchantBenefitClassifyId"
        from
        um_account_benefit
        where merchant_id=#{merchantId} and
        account_id in
        <foreach collection="accountBenefitIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by account_id ,merchant_benefit_classify_id
    </select>

    <update id="updateAccountBenefitIds">
        update um_account_benefit
        <set>
            <if test="userId != null">
                user_id=#{userId},
            </if>
            <if test="merchantUserId != null">
                merchant_user_id=#{merchantUserId},
            </if>
            <if test="accountId != null">
                account_id=#{accountId}
            </if>
        </set>
        where merchant_id=#{merchantId} and user_id=#{oldUserId} and merchant_user_id=#{oldMerchantUserId}
    </update>

    <select id="selectTotalTransferAccountBenefit" resultType="com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueAccountBenefitTransferDTO">
        select uab.user_id as userId,umu.name as name,SUM(uab.balance) as balance,uab.classify as classify
        from um_account_benefit uab left join um_merchant_user umu on umu.user_id = uab.user_id and umu.merchant_id =
        #{query.merchantId}
        where uab.merchant_id = #{query.merchantId} and uab.user_id = #{query.userId}
        and uab.merchant_user_id = #{query.merchantUserId} and uab.benefit_id in
        (select distinct benefit_id from um_benefit_scope where uab.merchant_id = #{query.merchantId}
          <if test="query.applicable != null">
              and applicable = #{query.applicable}
          </if>
          <if test="query.storeIds != null">
            and associated_id in
            <foreach collection="query.storeIds" item="storeId" open="(" close=")" separator=",">
                #{storeId}
            </foreach>
          </if>
        )
        and uab.balance > 0
        and uab.expiry_date_category in (0,2)
        <if test="query.classifys != null">
              and uab.classify in
              <foreach collection="query.classifys" item="classify" open="(" close=")" separator=",">
                  #{classify}
              </foreach>
        </if>
        group by uab.user_id,umu.name,uab.classify
        union all
        select uab.user_id,umu.name,SUM(uab.balance),uab.classify
        from um_account_benefit uab left join um_merchant_user umu on umu.user_id = uab.user_id and umu.merchant_id = #{query.merchantId}
        where uab.merchant_id = #{query.merchantId} and uab.user_id = #{query.userId}
          and uab.merchant_user_id = #{query.merchantUserId} and  uab.benefit_id in (select distinct benefit_id from um_benefit_scope where
            uab.merchant_id = #{query.merchantId}
            <if test="query.applicable != null">
                and applicable = #{query.applicable}
            </if>
            <if test="query.storeIds != null">
                and associated_id in
                <foreach collection="query.storeIds" item="storeId" open="(" close=")" separator=",">
                    #{storeId}
                </foreach>
            </if>
          )
          and uab.balance > 0
          and uab.expiry_date_category = 1
          and to_timestamp(uab.down_time, 'YYYY-MM-DD HH24:MI:SS') >= CURRENT_DATE
            <if test="query.classifys != null">
                and uab.classify in
                <foreach collection="query.classifys" item="classify" open="(" close=")" separator=",">
                    #{classify}
                </foreach>
            </if>
        group by uab.user_id,umu.name,uab.classify;
    </select>

    <select id="selectTransferAccountBenefit" resultType="com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueAccountBenefitTransferDTO">
        select uab.id as id, classify, balance
        from um_account_benefit uab left join um_merchant_user umu on umu.user_id = uab.user_id and umu.merchant_id =
        #{query.merchantId}
        where uab.merchant_id = #{query.merchantId} and uab.user_id = #{query.userId}
        and uab.merchant_user_id = #{query.merchantUserId} and uab.benefit_id in
        (select distinct benefit_id from um_benefit_scope where uab.merchant_id = #{query.merchantId}
        <if test="query.applicable != null">
            and applicable = #{query.applicable}
        </if>
        <if test="query.storeIds != null">
            and associated_id in
            <foreach collection="query.storeIds" item="storeId" open="(" close=")" separator=",">
                #{storeId}
            </foreach>
        </if>
        )
        and uab.balance > 0
        and uab.expiry_date_category in (0,2)
        <if test="query.classifys != null">
            and uab.classify in
            <foreach collection="query.classifys" item="classify" open="(" close=")" separator=",">
                #{classify}
            </foreach>
        </if>
        union all
        select uab.id as id, classify, balance
        from um_account_benefit uab left join um_merchant_user umu on umu.user_id = uab.user_id and umu.merchant_id = #{query.merchantId}
        where uab.merchant_id = #{query.merchantId} and uab.user_id = #{query.userId}
        and uab.merchant_user_id = #{query.merchantUserId} and  uab.benefit_id in (select distinct benefit_id from um_benefit_scope where
        uab.merchant_id = #{query.merchantId}
        <if test="query.applicable != null">
            and applicable = #{query.applicable}
        </if>
        <if test="query.storeIds != null">
            and associated_id in
            <foreach collection="query.storeIds" item="storeId" open="(" close=")" separator=",">
                #{storeId}
            </foreach>
        </if>
        )
        and uab.balance > 0
        and uab.expiry_date_category = 1
        and to_timestamp(uab.down_time, 'YYYY-MM-DD HH24:MI:SS') >= CURRENT_DATE
        <if test="query.classifys != null">
            and uab.classify in
            <foreach collection="query.classifys" item="classify" open="(" close=")" separator=",">
                #{classify}
            </foreach>
        </if>
    </select>

    <select id="queryAccountBenefitList"
            resultType="com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueBenefitDetailDTO">
        select
        uab.id "accountBenefitId",
        uab.down_time "downTime",
        uab.up_time "upTime",
        uab.create_time "createTime",
        uab.store_id "storeId",
        uab.total,
        uab.balance,
        uab.expiry_date_category expiryDateCategory,
        uab.use_rule_id "useRuleId"
        from
        um_account_benefit uab
        where
        uab.merchant_id =#{selectDTO.merchantId}
        and uab.user_id =#{selectDTO.userId}
        and uab.store_id =#{selectDTO.storeId}
        <if test="selectDTO.startTime != null and selectDTO.endTime != null">
            AND uab.create_time BETWEEN #{selectDTO.startTime} AND #{selectDTO.endTime}
        </if>
        <if test="selectDTO.accountId != null and selectDTO.accountId != null">
            AND account_id =#{selectDTO.accountId}
        </if>
        <if test="selectDTO.merchantBenefitClassifyId != null and selectDTO.merchantBenefitClassifyId != null">
            AND merchant_benefit_classify_id =#{selectDTO.merchantBenefitClassifyId}
        </if>
        <if test="selectDTO.accountBenefitIds != null and selectDTO.accountBenefitIds.size > 0 ">
            AND uab.id IN
            <foreach collection="selectDTO.accountBenefitIds" item="accountBenefitId" open="(" close=")" separator=",">
                #{accountBenefitId}
            </foreach>
        </if>
    </select>
</mapper>