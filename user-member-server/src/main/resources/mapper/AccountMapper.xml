<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.account.repository.AccountMapper">
    <resultMap id="BaseResultMap" type="com.lyy.user.domain.account.entity.Account">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="NUMERIC" property="id" />
        <result column="user_id" jdbcType="NUMERIC" property="userId" />
        <result column="merchant_id" jdbcType="NUMERIC" property="merchantId" />
        <result column="total" jdbcType="NUMERIC" property="total" />
        <result column="balance" jdbcType="NUMERIC" property="balance" />
        <result column="classify" jdbcType="SMALLINT" property="classify" />
        <result column="status" jdbcType="SMALLINT" property="status" />
        <result column="description" jdbcType="VARCHAR" property="description" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_by" jdbcType="NUMERIC" property="createBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_by" jdbcType="NUMERIC" property="updateBy" />
        <result column="card_no" jdbcType="VARCHAR" property="cardNo"/>
        <result column="parentAccountId" jdbcType="NUMERIC" property="parentAccountId"/>
        <result column="store_id" jdbcType="NUMERIC" property="storeId"/>
        <result column="down_time" jdbcType="TIMESTAMP" property="downTime"/>
        <result column="deposit" jdbcType="NUMERIC" property="deposit"/>
        <result column="default_flag" jdbcType="BOOLEAN" property="defaultFlag"/>
    </resultMap>

    <sql id="memberAccountBaseColumn">
        id,
        user_id,
        merchant_id,
        status,
        description,
        merchant_user_id,
        card_no,
        parent_account_id,
        store_id,
        down_time,
        deposit,
        default_flag
    </sql>

    <update id="increaseBalanceAndTotal">
        update um_account
        <set>
            <if test="balance != null">
                balance = balance + #{balance},
            </if>
            <if test="total != null">
                total = total + #{total},
            </if>
            update_time = now(), update_by = #{operator}
        </set>
        where id = #{id} and merchant_id = #{merchantId}
    </update>

    <select id="findAccount" resultType="com.lyy.user.domain.account.entity.Account" >
            select *
            from um_account
            where classify = #{classify}
            and status = 1
            and balance > 0
            and (merchant_id = #{merchantId} or 1=1)
            order by user_id asc
    </select>

    <select id="findAccountByMerchantUserIds" resultType="com.lyy.user.domain.account.entity.Account">
        select merchant_user_id,classify,balance
        from  um_account
        where merchant_id = #{merchantId}
        <if test="merchantUserIds !=null and merchantUserIds.size()>0" >
            and merchant_user_id in
            <foreach collection="merchantUserIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="classifies !=null and classifies.size()>0" >
            and classify in
            <foreach collection="classifies" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="findCardStatus" resultType="com.lyy.user.account.infrastructure.account.dto.AccountStatusDTO">
        select status from um_account where card_no=#{cardNo} and merchant_id = #{merchantId}
    </select>


    <select id="getAccountInfo" resultType="com.lyy.user.account.infrastructure.account.dto.SmallVenueAccountInfoDTO">
         select
            umu.user_id "userId",
            umu.merchant_id "merchantId",
            umu.id "merchantUserId",
            ua.card_no "cardNo",
            ua.id "accountId"
        from
            um_merchant_user umu
            left join um_account ua on
            ua.merchant_user_id = umu.id
            and ua.merchant_id = #{merchantId}
        where
            umu.merchant_id=#{merchantId}
            and (ua.card_no=#{keyword}
            or ua.merchant_user_id :: VARCHAR =#{keyword}
            or umu.telephone =#{keyword})
        limit 1
    </select>

    <select id="selectUserAllCard" resultType="com.lyy.user.account.infrastructure.account.dto.SmallVenueAllCardDTO">
        select id "accountId",card_no "cardNo",status,classify,deposit
        from um_account
        where merchant_id=#{merchantId}
          and user_id=#{userId}
        <if test="hasSupplementaryCard == null || !hasSupplementaryCard">
            and parent_account_id is null
        </if>
    </select>
    <select id="selectCountCardNumber" resultType="com.lyy.user.domain.account.dto.SmallVenueCountCardNoDTO">
        select merchant_user_id, count(*)  cardNumber
        from um_account
        where merchant_id = #{merchantId}
          <!-- 卡号为null的话，说明是虚拟卡，会员中台虚拟一张卡号为null的卡来存放储值，这时不应该把这个当成会员的会员卡  -->
        and card_no is not null
        <if test="merchantUserIds !=null and merchantUserIds.size()>0" >
            and merchant_user_id in
            <foreach collection="merchantUserIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cardStatus !=null and cardStatus.size()>0" >
            and status in
            <foreach collection="cardStatus" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="hasSupplementaryCard == null || !hasSupplementaryCard">
            and parent_account_id is null
        </if>
        group by merchant_user_id
    </select>

    <select id="selectParentAccountStatus" resultType="java.lang.Integer">
        select status from um_account where merchant_id=#{merchantId} and id=#{accountId} and user_id=#{userId} and parent_account_id is null
    </select>

    <select id="selectSupplementaryCardCount" resultType="java.lang.Integer">
        select count(*) from um_account where merchant_id=#{merchantId} and parent_account_id=#{accountId} and user_id=#{userId} and status!=5 and classify = 99
    </select>

    <update id="updateCardStatus">
        update um_account set status=#{status},update_by=#{updateBy},update_time=now()
        where merchant_id=#{merchantId} and card_no=#{cardNo} and classify = 99
    </update>

    <select id="selectAccountByCardNo" resultType="com.lyy.user.domain.account.entity.Account">
        select * from um_account where merchant_id=#{merchantId} and card_no=#{cardNo} and classify = 99
    </select>
    <select id="selectAccountByCardNos" resultType="com.lyy.user.domain.account.entity.Account">
        select * from um_account where merchant_id=#{merchantId}
                                   and card_no in (
                                       <foreach collection="cardNos" item="item" separator=",">
                                           #{item}
                                       </foreach>
                ) and classify = 99
    </select>
    <select id="listAccountWithDefault" resultType="com.lyy.user.domain.account.entity.Account">
        SELECT  <include refid="memberAccountBaseColumn"/>
        FROM um_account
        WHERE user_id = #{userId}
          AND merchant_id = #{merchantId}
          AND status != 5
          AND classify = 99
          AND (default_flag = TRUE
                <if test="cardNo != null">
                    OR card_no = #{cardNo}
                </if>
              )
    </select>

    <update id="updateAccountCardNo">
         update um_account set card_no=#{newCardNo},update_by=#{updateBy},update_time=now()
         where merchant_id=#{merchantId} and card_no=#{oldCardNo} and classify = 99
    </update>

    <update id="updateAccountDefaultFlag">
        update um_account set default_flag = false,update_by=#{updateBy},update_time=now()
        where  merchant_id=#{merchantId} and merchant_user_id = #{merchantUserId} and classify = 99
    </update>

    <update id="renewalCard">
        update um_account
        set down_time = #{renewalTime}, update_by = #{updateBy}, update_time = now()
        where  merchant_id = #{merchantId} and card_no = #{cardNo} and classify = 99
    </update>
    <update id="updateRecentlyAccountAsDefaultFlag">
        update um_account
        set default_flag = true, update_by = #{updateBy}, update_time = now()
        where merchant_id = #{merchantId}
          and id = (
            select id
            from um_account
            where merchant_id = #{merchantId}
              and user_id = #{userId}
              and status = 1
              and classify = 99
              and parent_account_id is null
            order by create_time desc
            limit 1
        )
    </update>

    <update id="updateAccountIdInfo">
        update um_account
        set user_id=#{userId},merchant_user_id=#{merchantUserId}
        <if test="defaultFlag!=null">
            ,default_flag=#{defaultFlag}
        </if>
        where merchant_id = #{merchantId} and card_no=#{cardNo}
    </update>

    <update id="updateStatusAfterReissue">
        update um_account set status=1,update_by=#{updateBy},update_time=now()
        where merchant_id=#{merchantId} and card_no=#{cardNo} and status=3 and classify = 99
    </update>


    <select id="getAccountInfoByCardNo" resultType="com.lyy.user.account.infrastructure.account.dto.SmallVenueAccountInfoDTO">
         select
            umu.user_id "userId",
            umu.merchant_id "merchantId",
            umu.id "merchantUserId",
            ua.card_no "cardNo",
            ua.id "accountId"
        from
            um_merchant_user umu
            left join um_account ua on
            cast (ua.merchant_user_id as bigint) = umu.id
            and ua.merchant_id = #{merchantId}
        where
            umu.merchant_id=#{merchantId}
            and ua.card_no=#{keyword}
        limit 1
    </select>

    <select id="getAccountInfoByTelephone"
            resultType="com.lyy.user.account.infrastructure.account.dto.SmallVenueAccountInfoDTO">
         select
            umu.user_id "userId",
            umu.merchant_id "merchantId",
            umu.id "merchantUserId",
            ua.card_no "cardNo",
            ua.id "accountId"
        from
            um_merchant_user umu
            left join um_account ua on
            ua.merchant_user_id = umu.id
            and ua.merchant_id = #{merchantId}
            join um_platform_user_phone upup on upup.merchant_id = umu.merchant_id
            and upup.user_id = umu.user_id and upup.is_active = true
            and upup.merchant_id = #{merchantId} and system_flag = 1
        where
            umu.merchant_id= #{merchantId}
            and umu.telephone = #{keyword}
            and upup.telephone = #{keyword}
        limit 1
    </select>

    <select id="getAccountInfoByMerchantUserId"
            resultType="com.lyy.user.account.infrastructure.account.dto.SmallVenueAccountInfoDTO">
         select
            umu.user_id "userId",
            umu.merchant_id "merchantId",
            umu.id "merchantUserId",
            ua.card_no "cardNo",
            ua.id "accountId"
        from
            um_merchant_user umu
            left join um_account ua on
            ua.merchant_user_id = umu.id
            and ua.merchant_id = #{merchantId}
        where
            umu.merchant_id=#{merchantId}
            and umu.id :: VARCHAR =#{keyword}
        limit 1
    </select>

    <select id="mobileTerminalAccountList" resultType="com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountListDTO">
        select
        ua.id "accountId",
        ua.user_id "userId",
        ua.card_no "cardNo",
        ua.down_time "downTime",
        ua.store_id "storeId",
        ua.merchant_user_id "merchantUserId",
        umu.name,
        umu.telephone,
        umu.head_img "headImg"
        from
        um_account ua
        inner join um_merchant_user umu on
        ua.merchant_user_id = umu.id
        where
        ua.merchant_id =#{selectDTO.merchantId}
        and umu.merchant_id =#{selectDTO.merchantId}
        and ua.card_no !='' and parent_account_id is null and status !=5
        <if test="selectDTO.storeIds != null and selectDTO.storeIds.size > 0">
            AND ua.store_id in
            <foreach collection="selectDTO.storeIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="selectDTO.keyword!=null and selectDTO.keyword!=''">
            AND (umu.name like concat('%',#{selectDTO.keyword},'%')
            OR umu.telephone like concat('%',#{selectDTO.keyword},'%')
            OR ua.merchant_user_id ::VARCHAR like concat('%',#{selectDTO.keyword},'%')
            OR ua.card_no like concat('%',#{selectDTO.keyword},'%'))
        </if>
    </select>

    <select id="batchSelectSupplementaryCardCount" resultType="com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalCardCountInfo">
        select count(*),parent_account_id "accountId" from um_account where  merchant_id=#{merchantId}
        and parent_account_id in
        <foreach collection="accountIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and status!=5 and classify = 99 group by parent_account_id
    </select>


    <select id="mobileTerminalAccountDetails" resultType="com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountDetailsDTO">
        select
        ua.id "accountId",
        ua.card_no "cardNo",
        ua.down_time "downTime",
        ua.store_id "storeId",
        ua.user_id "userId",
        umu.name,
        umu.telephone,
        umu.head_img "headImg"
        from
        um_account ua
        inner join um_merchant_user umu on
        ua.merchant_user_id = umu.id
        where
        ua.merchant_id =#{merchantId}
        and umu.merchant_id =#{merchantId}
        and ua.id=#{accountId}
    </select>


    <select id="findAccountSupplementaryCardList" resultType="com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalSupplementaryCardDTO">
        select card_no "cardNo",store_id "storeId",down_time "downTime"
        from um_account
        where merchant_id=#{merchantId} and parent_account_id=#{accountId}  and status!=5 and classify = 99
    </select>

    <select id="getDefaultAccount" resultType="com.lyy.user.domain.account.entity.Account">
        SELECT  <include refid="memberAccountBaseColumn"/>
        FROM um_account
        WHERE user_id = #{userId}
        AND merchant_id = #{merchantId}
        AND classify = 99
        AND default_flag = TRUE
    </select>

    <update id="updateVenueAccountById">
        update um_account
        set card_no=#{cardNo}
        <if test="userId!=null">
            ,user_id=#{userId}
        </if>
        <if test="merchantUserId!=null">
            ,merchant_user_id=#{merchantUserId}
        </if>
        <if test="defaultFlag!=null">
            ,default_flag=#{defaultFlag}
        </if>
        <if test="status!=null">
            ,status=#{status}
        </if>
        <if test="description!=null">
            ,description=#{description}
        </if>
        <if test="parentAccountId!=null">
            ,parent_account_id=#{parentAccountId}
        </if>
        <if test="downTime!=null">
            ,down_time=#{downTime}
        </if>
        <if test="deposit!=null">
            ,deposit=#{deposit}
        </if>
        <if test="defaultFlag!=null">
            ,default_flag=#{defaultFlag}
        </if>
        <if test="updateTime!=null">
            ,update_time=#{updateTime}
        </if>
        where merchant_id = #{merchantId} and id=#{id} and classify = 99
    </update>

    <select id="selectDistinctMerchantUserIdByMerchantIdAndCardNo" resultType="java.lang.Long">
        select
            distinct merchant_user_id
        from
            um_account
        where
            merchant_id = #{merchantId}
            and classify = 99
            and status != 5
            and card_no like  concat('%', #{keyword},'%')
    </select>

    <select id="countSmallVenueUserMemberList" resultType="java.lang.Long">
        select
            count ( distinct merchant_user_id )
        from
            um_account
        where
            merchant_id = #{merchantId}
            and classify = 99
            and status != 5
            and card_no like  concat('%', #{keyword},'%')
    </select>

    <select id="getAccountInfoByCardNos" resultType="com.lyy.user.account.infrastructure.account.dto.SmallVenueAccountInfoDTO">
        select
            umu.user_id "userId",
            umu.merchant_id "merchantId",
            umu.id "merchantUserId",
            ua.card_no "cardNo",
            ua.id "accountId"
        from
            um_merchant_user umu
                left join um_account ua on
                        cast (ua.merchant_user_id as bigint) = umu.id
                    and ua.merchant_id = #{merchantId}
        where
            umu.merchant_id=#{merchantId}
          and ua.card_no in (
              <foreach collection="cardNos" separator="," item="item">
                  #{item}
              </foreach>
            )
    </select>
</mapper>