<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.member.repository.MemberGroupMapper">


    <select id="selectByIdForUpdate" parameterType="java.lang.Long" resultType="com.lyy.user.domain.member.entity.MemberGroup">
        select * from um_member_group where  id = #{id} for update
    </select>
    <select id="getMemberGroupOfRange" parameterType="com.lyy.user.account.infrastructure.member.dto.MemberGroupRangCheckDTO"
            resultType="com.lyy.user.domain.member.entity.MemberGroup">
        select mg.*
        from um_member_group mg
        left join um_member_range mr on mg.id = mr.member_group_id and mr.merchant_id = #{merchantId}
            and (mr.active is null or mr.active = true)
        where mg.start_date &lt;= now()
        and mg.end_date &gt;= now()
        and mg.is_active = true
        and mg.is_del = false
        and mg.merchant_id = #{merchantId}
        <if test="equipmentTypeId != null or equipmentGroupId != null or equipmentId != null or commodityId != null">
            and( 1 = 2
            <if test="equipmentTypeId != null">
                or (applicable = 1 and associated_id = #{equipmentTypeId})
            </if>
            <if test="equipmentGroupId != null">
                or (applicable = 2 and associated_id = #{equipmentGroupId})
            </if>
            <if test="equipmentId != null">
                or (applicable = 3 and associated_id = #{equipmentId})
            </if>
            <if test="commodityId != null">
                or (applicable = 4 and associated_id = #{commodityId})
            </if>
            )
        </if>

    </select>

    <select id="countMemberByGroup" resultType="java.lang.Integer">
        select count(1)
        from um_member me
        join um_member_level ml on ml.id = me.member_level_id and  ml.member_group_id = me.member_group_id
        join um_merchant_user mu on mu.id = me.merchant_user_id
        where me.merchant_id = #{merchantId}
        and ml.merchant_id = #{merchantId}
        and mu.merchant_id = #{merchantId}
        and  me.member_group_id = #{memberGroupId}
        and ml.grow_value > 0
        <if test="isEffective">
          and me.is_del = false
          and (me.member_end_time is null or me.member_end_time > now())
        </if>
    </select>

    <select id="getMemberGroupById" resultType="com.lyy.user.domain.member.entity.MemberGroup">
        select * from um_member_group where id = #{id} and merchant_id = #{merchantId}
    </select>

    <select id="countEffectiveMemberByGroup" resultType="java.lang.Integer">
        select count(1)
        from um_member me
        join um_member_level ml on ml.id = me.member_level_id and  ml.member_group_id = me.member_group_id
        join um_merchant_user mu on mu.id = me.merchant_user_id
        where me.merchant_id = #{merchantId}
        and ml.merchant_id = #{merchantId}
        and mu.merchant_id = #{merchantId}
        and  me.member_group_id = #{memberGroupId}
        and ml.grow_value > 0
        and me.is_del = false
        and (me.member_end_time is null or me.member_end_time &gt;= current_date - 1)
    </select>

    <select id="existMemberGroup" resultType="java.lang.Integer">
        SELECT 1 FROM um_member_group
        WHERE merchant_id = #{merchantId} LIMIT 1
    </select>
</mapper>
