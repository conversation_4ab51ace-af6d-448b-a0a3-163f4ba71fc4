<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.user.repository.MerchantUserTagMapper">
    <resultMap id="BaseResultMap" type="com.lyy.user.domain.user.entity.MerchantUserTag">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="NUMERIC" property="id"/>
        <result column="business_user_id" jdbcType="NUMERIC" property="businessUserId"/>
        <result column="merchant_id" jdbcType="NUMERIC" property="merchantId"/>
        <result column="user_tags" jdbcType="NUMERIC" property="userTags"
                typeHandler="com.lyy.user.infrastructure.typehandler.ArrayTypeHandlerPg"/>
        <result column="is_active" jdbcType="BOOLEAN" property="active"/>
        <result column="tag_type" jdbcType="SMALLINT" property="tagType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="createdby" jdbcType="NUMERIC" property="createdby"/>
        <result column="updatedby" jdbcType="NUMERIC" property="updatedby"/>
    </resultMap>



    <update id="deleteByTagId">
        update um_merchant_user_tag set user_tags = array_remove(user_tags, #{tagId}), update_time = now()
        where merchant_id = #{merchantId}
        <if test="notHandleUserIds!=null and notHandleUserIds.size()>0">
            and business_user_id not in
            <foreach collection="notHandleUserIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="deleteByTagIdAndUserIds">
        update um_merchant_user_tag set user_tags = array_remove(user_tags, #{tagId}), update_time = now()
        where merchant_id = #{merchantId}
        and business_user_id in
        <foreach collection="userIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="countUserMember" resultType="java.lang.Integer">
        select count(*) members
        from um_merchant_user_tag
        where merchant_id = #{merchantId}
          and user_tags @> array[#{tagId}]
          and is_active = #{active}
          and tag_type = #{tagType}
    </select>

    <select id="selectListByBusinessUserId"  resultMap="BaseResultMap">
        select iss.*
        from um_merchant_user_tag as iss
        where iss.merchant_id = #{merchantId}
        and iss.business_user_id in
        <foreach collection="userIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and iss.tag_type = #{tagType}

    </select>

    <select id="selectTagListByUserId" resultType="com.lyy.user.account.infrastructure.user.dto.userInfo.TagInfoDTO">
        select id as tagId,name as tagName,business_type businessType from um_tag_user
        where id in(
        select
            unnest (user_tags)
        from
            um_merchant_user_tag
        where
            merchant_id =#{merchantId}
            and business_user_id =#{businessUserId}
            and tag_type =#{tagType} )
    </select>
    <select id="getByBusinessUserId"  resultMap="BaseResultMap">
        select iss.*
        from um_merchant_user_tag as iss
        where iss.merchant_id = #{merchantId}
        and iss.business_user_id = #{businessUserId}
    </select>
</mapper>