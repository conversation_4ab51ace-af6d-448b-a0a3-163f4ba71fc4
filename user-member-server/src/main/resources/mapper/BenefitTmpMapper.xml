<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.benefit.repository.BenefitTmpMapper">
    <insert id="insertBatch">
        insert into um_benefit_tmp(id,merchant_id,title,sub_title,
        code,classify,benefit_count,expiry_date_category,
        up_time,down_time,show_date_category,show_up_time,show_down_time,
        create_time,update_time,is_active)
        values
        <foreach collection="list" item="e" separator=",">
            (#{e.id},#{e.merchantId},#{e.title},#{e.subTitle},
            #{e.code},#{e.classify},#{e.benefitCount},#{e.expiryDateCategory},
            #{e.upTime},#{e.downTime},#{e.showDateCategory},#{e.showUpTime},#{e.showDownTime},
            #{e.createTime},#{e.updateTime},#{e.active})
        </foreach>
    </insert>
</mapper>