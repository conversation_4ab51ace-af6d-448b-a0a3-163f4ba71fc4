<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.member.repository.MemberRangeMapper">


    <select id="selectByIdForUpdate" parameterType="java.lang.Long" resultType="com.lyy.user.domain.member.entity.MemberRange">
        select * from um_member_range where  id = #{id} for update
    </select>


    <select id="findAssociatedList" parameterType="java.lang.Long" resultType="com.lyy.user.account.infrastructure.member.dto.MemberRangeAssociatedDTO">
        select applicable
        ,regexp_split_to_array(concat(string_agg(cast(associated_id as VARCHAR) , ',')) ,',') as associated
        from um_member_range
        where member_group_id= #{memberGroupId}
        group by applicable
    </select>

    <delete id="deleteNotInAssociated" >
        update um_member_range mr1 set active = false
        where member_group_id= #{memberGroupId}
        and not exists  (
            select *
            from um_member_range mr2
            where mr2.member_group_id= #{memberGroupId}
            and mr1.id = mr2.id
            and (mr2.active is null or mr2.active = true)
            <if test="memberRangeAssociatedList != null and memberRangeAssociatedList.size() > 0 " >
                and
                <foreach collection="memberRangeAssociatedList" item="memberRangeAssociated" separator="or" open="(" close=")" >
                    mr2.applicable = #{memberRangeAssociated.applicable} and mr2.associated_id in
                  <foreach collection="memberRangeAssociated.associatedIdList" item="associatedId" open="(" close=")" separator=",">
                    #{associatedId}
                  </foreach>
                </foreach>
            </if>
        )
    </delete>

    <insert id="addAssociated" useGeneratedKeys="false" >
        insert into "um_member_range" ("id", "member_group_id", "applicable", "associated_id")
        select
        nextval('um_member_range_seq') as "id",
        100 as "member_group_id",
        new_info.*
        from (
                select
                '1' as "applicable",
                10 as "associated_id"
                union
                select
                '1' as "applicable",
                21 as "associated_id"
        ) as new_info
        where not exists(
            select *
            from um_member_range mr2
            where mr2.member_group_id= 100
            and new_info.applicable = mr2.applicable
            and new_info.associated_id = mr2.associated_id
        )
    </insert>
</mapper>
