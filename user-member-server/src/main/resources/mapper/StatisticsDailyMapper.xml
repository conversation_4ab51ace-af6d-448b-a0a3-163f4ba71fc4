<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.statistics.repository.StatisticsDailyMapper">
    <resultMap id="BaseResultMap" type="com.lyy.user.domain.statistics.entity.StatisticsDaily">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="merchant_user_id" jdbcType="NUMERIC" property="merchantUserId" />
        <result column="merchant_id" jdbcType="NUMERIC" property="merchantId" />
        <result column="user_id" jdbcType="NUMERIC" property="userId" />
        <result column="statistics_date" jdbcType="DATE" property="statisticsDate" />
        <result column="start_times" jdbcType="NUMERIC" property="startTimes" />
        <result column="pay_times" jdbcType="NUMERIC" property="payTimes" />
        <result column="pay_amount" jdbcType="NUMERIC" property="payAmount" />
        <result column="pay_for_service_times" jdbcType="NUMERIC" property="payForServiceTimes" />
        <result column="coins_consumption" jdbcType="NUMERIC" property="coinsConsumption" />
        <result column="amount_consumption" jdbcType="NUMERIC" property="amountConsumption" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <resultMap id="rechargeRankMap" type="com.lyy.user.account.infrastructure.statistics.dto.MerchantUserRankRecordDTO">
        <result column="lyy_user_id" property="lyyUserId" />
        <result column="type" property="type" />
        <result column="name" property="name" />
        <result column="head_img" property="headImg" />
        <result column="total_recharge" property="totalRecharge" />
        <result column="rank" property="rank" />
        <result column="recharge" property="recharge" />
    </resultMap>

    <resultMap id="MerchantUserStatisticsMap" type="com.lyy.user.account.infrastructure.statistics.dto.MerchantUserStatisticsListDTO">
        <result column="id" property="id" />
        <result column="name" property="nickName" />
        <result column="head_img" property="headImg" />
        <result column="gender" property="gender" />
        <result column="user_id" property="userId" />
        <result column="merchant_id" property="merchantId" />
        <result column="user_type" property="userType" />
        <result column="telephone" property="telephone" />
        <result column="pay_amount"  property="totalRechargeMoney" />
        <result column="pay_for_service_amount"  property="totalPayServiceMoney" />
        <result column="amount_consumption"  property="totalConsumeBalance" />
        <result column="coins_consumption"  property="totalConsumeCoin" />
        <result column="balance_amount" jdbcType="NUMERIC" property="balanceAmount" />
        <result column="balance_coins" jdbcType="NUMERIC" property="balanceCoins" />
    </resultMap>
    <update id="updateStatistics">
        update um_statistics_daily
        <set>
            update_time = #{updateTime},
            <if test="startTimes != null  and startTimes > 0">
                start_times = coalesce(start_times,0) + #{startTimes},
            </if>
            <if test="payTimes != null and payTimes > 0">
                pay_times = coalesce(pay_times,0) + #{payTimes},
            </if>
            <if test="payAmount != null">
                pay_amount = greatest(coalesce(pay_amount,0) + #{payAmount},0),
            </if>
            <if test="payForServiceTimes != null and payForServiceTimes > 0">
                pay_for_service_times = coalesce(pay_for_service_times,0) + #{payForServiceTimes},
            </if>
            <if test="payForServiceAmount != null">
                pay_for_service_amount = greatest(coalesce(pay_for_service_amount,0) + #{payForServiceAmount},0),
            </if>
            <if test="coinsConsumption != null">
                coins_consumption = greatest(coalesce(coins_consumption,0) + #{coinsConsumption},0),
            </if>
            <if test="amountConsumption != null">
                amount_consumption = greatest(coalesce(amount_consumption,0) + #{amountConsumption},0),
            </if>
        </set>
        where merchant_user_id = #{merchantUserId}
        and merchant_id = #{merchantId}
        and statistics_date = #{updateTime}::date
    </update>
    <select id="queryStatisticsUserList" resultMap="MerchantUserStatisticsMap">
        select umu.id,umu."name",umu.head_img,umu.gender,umu.user_id ,umu.merchant_id,umu.telephone, umu.user_type, coalesce(usd.pay_amount,0) as pay_amount,coalesce(usd.pay_for_service_amount,0)  as pay_for_service_amount,
        coalesce(usd.amount_consumption,0) as amount_consumption ,coalesce(usd.coins_consumption,0) as coins_consumption
        from um_statistics_daily usd inner join um_merchant_user umu on usd.merchant_user_id  = umu.id
        where umu.merchant_id  = #{merchantId}  and usd.merchant_id = #{merchantId} and usd.statistics_date =  #{statisticsDate}
        <if test="userId != null and userId != ''">
            and umu.user_id = #{userId}
        </if>
        <if test="telephone != null and telephone != ''">
            and umu.telephone = #{telephone}
        </if>
    </select>


    <select id="selectDateUseNum" resultType="java.math.BigDecimal">
        select amount_consumption from um_statistics_daily where merchant_id = #{merchantId} and user_id=#{userId} and statistics_date =#{statisticsDate} limit 1
    </select>

    <select id="getRechargeRank" resultMap="rechargeRankMap">
        SELECT u.user_id AS lyy_user_id,
        SUM(round(COALESCE(pay_amount,0)*3.6/100, 0)) AS total_recharge,
        row_number() over(ORDER BY COALESCE(sum(pay_amount),0) DESC, u.user_id ) AS rank,
        SUM(COALESCE(pay_amount,0)) AS recharge
        FROM um_statistics_daily d join um_merchant_user u
        ON d.merchant_user_id = u.id
        WHERE d.statistics_date >= #{startDate}
        AND d.merchant_id = #{distributorId}
        AND u.merchant_id = #{distributorId}
        <choose>
            <when test="lyyUserId != null">
                and u.user_id = #{lyyUserId}
                GROUP BY u.user_id
                limit 1
            </when>
            <otherwise>
                GROUP BY u.user_id
                limit #{limit}
            </otherwise>
        </choose>
    </select>

    <insert id="upsertStatistics">
        INSERT INTO um_statistics_daily AS t (
            merchant_user_id,
            merchant_id,
            user_id,
            statistics_date,
            start_times,
            pay_times,
            pay_amount,
            pay_for_service_times,
            pay_for_service_amount,
            coins_consumption,
            amount_consumption,
            create_time,
            update_time
        ) VALUES (
            #{param.merchantUserId},
            #{param.merchantId},
            #{param.userId},
            #{statisticsDate},
            #{param.startTimes},
            #{param.payTimes},
            #{param.payAmount},
            #{param.payForServiceTimes},
            #{param.payForServiceAmount},
            #{param.coinsConsumption},
            #{param.amountConsumption},
            #{param.updateTime},
            #{param.updateTime}
        )
        ON CONFLICT (merchant_user_id, merchant_id, statistics_date) DO UPDATE SET
            <if test="param.startTimes != null and param.startTimes > 0">
                start_times = COALESCE(t.start_times, 0) + EXCLUDED.start_times,
            </if>
            <if test="param.payTimes != null and param.payTimes > 0">
                pay_times = COALESCE(t.pay_times, 0) + EXCLUDED.pay_times,
            </if>
            <if test="param.payAmount != null">
                pay_amount = greatest(COALESCE(t.pay_amount, 0) + EXCLUDED.pay_amount, 0),
            </if>
            <if test="param.payForServiceTimes != null and param.payForServiceTimes > 0">
                pay_for_service_times = COALESCE(t.pay_for_service_times, 0) + EXCLUDED.pay_for_service_times,
            </if>
            <if test="param.payForServiceAmount != null">
                pay_for_service_amount = greatest(COALESCE(t.pay_for_service_amount, 0) + EXCLUDED.pay_for_service_amount, 0),
            </if>
            <if test="param.coinsConsumption != null">
                coins_consumption = greatest(COALESCE(t.coins_consumption, 0) + EXCLUDED.coins_consumption, 0),
            </if>
            <if test="param.amountConsumption != null">
                amount_consumption = greatest(COALESCE(t.amount_consumption, 0) + EXCLUDED.amount_consumption, 0),
            </if>
            update_time = EXCLUDED.update_time
    </insert>
</mapper>