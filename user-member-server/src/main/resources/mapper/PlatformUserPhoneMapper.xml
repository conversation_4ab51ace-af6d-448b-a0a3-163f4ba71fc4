<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.user.repository.PlatformUserPhoneMapper">
  <resultMap id="BaseResultMap" type="com.lyy.user.domain.user.entity.PlatformUserPhone">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="NUMERIC" property="id" />
    <result column="user_id" jdbcType="NUMERIC" property="userId" />
    <result column="merchant_id" jdbcType="NUMERIC" property="merchantId" />
    <result column="telephone" jdbcType="VARCHAR" property="telephone" />
    <result column="is_active" jdbcType="BOOLEAN" property="active" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="createdby" jdbcType="NUMERIC" property="createdby" />
    <result column="updatedby" jdbcType="NUMERIC" property="updatedby" />
    <result column="merchant_user_id" jdbcType="NUMERIC" property="merchantUserId" />
    <result column="system_flag" jdbcType="NUMERIC" property="systemFlag"/>
  </resultMap>
    <update id="updateTelephoneByIds" >
        update um_platform_user_phone
        set is_active = #{active} ,
        updatedby = #{dto.operatorId},
        update_time = now()
        where
              merchant_id= #{dto.merchantId}
             and  id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="dto.merchantUserId != null">
            and merchant_user_id = #{dto.merchantUserId}
        </if>
    </update>
    <update id="updateByMerchantIdAndUserId">
        update um_platform_user_phone
        set is_active = #{active},
        updatedby = #{operatorId},
        update_time = now()
        where merchant_id = #{merchantId}
        and user_id = #{userId}
    </update>
    <select id="selectVenue" resultType="com.lyy.user.domain.user.entity.PlatformUserPhone">
        select * from um_platform_user_phone
        where is_active = true
        and system_flag = 1
        and merchant_id = #{merchantId}
        and telephone = #{telephone}
        order by createdby desc
        limit 1
    </select>
</mapper>