<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.member.repository.MemberRuleMapper">

    <select id="findAllByMemberLevel" resultType="com.lyy.user.domain.member.entity.MemberRule">
        select mr.*
        from um_member_rule mr
        join um_member_level ml on ml.merchant_id = mr.merchant_id and ml.id = mr.member_level_id
        where mr.merchant_id = #{merchantId}
        and exists (
        select 1
        from um_member_level ml1
        where ml.merchant_id = ml1.merchant_id
        and ml.member_group_id = ml1.member_group_id
        and ml1.id =#{memberLevelId}
        and ml.grow_value &lt;= ml1.grow_value
        )
    </select>
</mapper>
