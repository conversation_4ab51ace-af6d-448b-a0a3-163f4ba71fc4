<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.statistics.repository.SmallVenueStoredStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.lyy.user.domain.statistics.entity.SmallVenueStoredStatistics">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="NUMERIC" property="id" />
    <result column="user_id" jdbcType="NUMERIC" property="userId" />
    <result column="merchant_id" jdbcType="NUMERIC" property="merchantId" />
    <result column="merchant_user_id" jdbcType="BIGINT" property="merchantUserId" />
    <result column="merchant_benefit_classify_id" jdbcType="NUMERIC" property="merchantBenefitClassifyId" />
    <result column="total" jdbcType="NUMERIC" property="total" />
    <result column="balance" jdbcType="NUMERIC" property="balance" />
    <result column="total_consume" jdbcType="NUMERIC" property="totalConsume" />
    <result column="total_invalid" jdbcType="NUMERIC" property="totalInvalid" />
    <result column="remain_num" jdbcType="NUMERIC" property="remainNum" />
    <result column="total_num" jdbcType="NUMERIC" property="totalNum" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="updated" jdbcType="TIMESTAMP" property="updated" />
    <result column="create_by" jdbcType="NUMERIC" property="createdby" />
    <result column="update_by" jdbcType="NUMERIC" property="updatedby" />
  </resultMap>

  <select id="selectStoredStatisticsByUserId" resultMap="BaseResultMap">
   select * from um_small_venue_stored_statistics where user_id=#{userId} and merchant_user_id=#{merchantUserId} and merchant_id=#{merchantId}
  </select>

  <select id="selectStoredStatisticsMaxBalanceByMerchantUserId" resultMap="BaseResultMap">
      select *
      from um_small_venue_stored_statistics
      where merchant_user_id = #{merchantUserId}
        and merchant_id = #{merchantId}
        order by balance desc
       limit #{rowNumber}
  </select>

<!--    row_number 函数 shardingsphere 不能使用,暂时保留代码 -->
    <select id="selectStoredStatisticsByMerchantUserIdsAndRowNumber"
            resultMap="BaseResultMap">
        select t.merchant_user_id  , t.merchant_benefit_classify_id  , t.balance, t.remain_num
        from (
        select merchant_id, merchant_user_id,
        merchant_benefit_classify_id,
        balance,
        remain_num
       , row_number() over (partition by merchant_user_id order by balance desc ) as row
        from um_small_venue_stored_statistics
        where merchant_id = #{merchantId}
        <if test="merchantUserIds != null and merchantUserIds.size() > 0">
            and merchant_user_id in
            <foreach collection="merchantUserIds" item="merchantUserId" open="(" close=")"  separator=",">
                #{merchantUserId}
            </foreach>
        </if>
        ) t
        where row  <![CDATA[ <= ]]>  #{rowNumber}
    </select>


    <select id="selectStatisticsByMerchantBenefitClassifyIds" resultType="com.lyy.user.account.infrastructure.statistics.dto.StoredStatisticsNumInfoDTO">
    select
	merchant_benefit_classify_id "merchantBenefitClassifyId" ,
	remain_num "num"
    from
        um_small_venue_stored_statistics
    where
        merchant_id = #{merchantId}
        and user_id = #{userId}
        and merchant_benefit_classify_id in
    <foreach collection="merchantBenefitClassifyIds" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>

  <update id="updateStatisticsIdInfo">
    update um_small_venue_stored_statistics
    set user_id=#{userId},merchant_user_id=#{merchantUserId}
    where merchant_id = #{merchantId} and user_id=#{oldUserId} and merchant_user_id=#{oldMerchantUserId}
  </update>

    <update id="updateStatisticsIdInfoByMerchantBenefitClassifyIds">
        update um_small_venue_stored_statistics
        set user_id = #{userId}, merchant_user_id = #{merchantUserId}
        where merchant_id = #{merchantId} and user_id = #{oldUserId} and merchant_user_id = #{oldMerchantUserId}
        and merchant_benefit_classify_id in
        <foreach collection="merchantBenefitClassifyIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="updateStatisticsIdInfoByMerchantBenefitClassifyId">
        update um_small_venue_stored_statistics
        set updated = now()
        <if test="oldMerchantBenefitClassify.total != null">
            ,total = COALESCE(total, 0) + #{oldMerchantBenefitClassify.total}
        </if>
        <if test="oldMerchantBenefitClassify.balance != null">
            ,balance = COALESCE(balance, 0) + #{oldMerchantBenefitClassify.balance}
        </if>
        <if test="oldMerchantBenefitClassify.totalConsume != null">
            ,total_consume = COALESCE(total_consume, 0) + #{oldMerchantBenefitClassify.totalConsume}
        </if>
        <if test="oldMerchantBenefitClassify.totalInvalid != null">
            ,total_invalid = COALESCE(total_invalid, 0) + #{oldMerchantBenefitClassify.totalInvalid}
        </if>
        <if test="oldMerchantBenefitClassify.remainNum != null">
            ,remain_num = COALESCE(remain_num, 0) + #{oldMerchantBenefitClassify.remainNum}
        </if>
        <if test="oldMerchantBenefitClassify.totalNum != null">
            ,total_num = COALESCE(total_num, 0) + #{oldMerchantBenefitClassify.totalNum}
        </if>
        <if test="oldMerchantBenefitClassify.totalNumConsume != null">
            ,total_num_consume = COALESCE(total_num_consume, 0) + #{oldMerchantBenefitClassify.totalNumConsume}
        </if>
        <if test="oldMerchantBenefitClassify.totalNumInvalid != null">
            ,total_num_invalid = COALESCE(total_num_invalid, 0) + #{oldMerchantBenefitClassify.totalNumInvalid}
        </if>
        where merchant_id = #{merchantId} and user_id = #{userId} and merchant_user_id = #{merchantUserId}
        and merchant_benefit_classify_id = #{merchantBenefitClassifyId}
    </update>

</mapper>