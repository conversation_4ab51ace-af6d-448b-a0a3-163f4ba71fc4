<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.account.repository.AccountRecordMapper">
  <resultMap id="BaseResultMap" type="com.lyy.user.domain.account.entity.AccountRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="NUMERIC" property="id" />
    <result column="account_id" jdbcType="NUMERIC" property="accountId" />
    <result column="user_id" jdbcType="NUMERIC" property="userId" />
    <result column="merchant_user_id" jdbcType="NUMERIC" property="merchantUserId" />
    <result column="merchant_id" jdbcType="NUMERIC" property="merchantId" />
    <result column="store_id" jdbcType="NUMERIC" property="storeId" />
    <result column="equipment_id" jdbcType="NUMERIC" property="equipmentId" />
    <result column="benefit_classify" jdbcType="SMALLINT" property="benefitClassify" />
    <result column="benefit_id" jdbcType="NUMERIC" property="benefitId" />
    <result column="initial_benefit" jdbcType="NUMERIC" property="initialBenefit" />
    <result column="original_benefit" jdbcType="NUMERIC" property="originalBenefit" />
    <result column="actual_benefit" jdbcType="NUMERIC" property="actualBenefit" />
    <result column="out_trade_no" jdbcType="VARCHAR" property="outTradeNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="sub_order_no" jdbcType="VARCHAR" property="subOrderNo" />
    <result column="mode" jdbcType="SMALLINT" property="mode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="resource" jdbcType="VARCHAR" property="resource" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="trade_type" jdbcType="SMALLINT" property="tradeType" />
    <result column="trade_amount" jdbcType="NUMERIC" property="tradeAmount" />
    <result column="commodity_name" jdbcType="VARCHAR" property="commodityName" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="equipment_type_name" jdbcType="VARCHAR" property="equipmentTypeName" />
    <result column="equipment_value" jdbcType="VARCHAR" property="equipmentValue" />
    <result column="equipment_type_id" jdbcType="NUMERIC" property="equipmentTypeId" />
    <result column="sort" jdbcType="NUMERIC" property="sort"/>
    <result column="account_benefit_id" jdbcType="NUMERIC" property="accountBenefitId"/>
    <result column="createdby" jdbcType="NUMERIC" property="createdby"/>
    <result column="create_name" jdbcType="VARCHAR" property="createName"/>
    <result column="card_no" jdbcType="VARCHAR" property="cardNo"/>
    <result column="refund_no" jdbcType="VARCHAR" property="refundNo"/>
    <result column="goods_id" jdbcType="NUMERIC" property="goodsId"/>
    <result column="goods_type" jdbcType="NUMERIC" property="goodsType"/>
    <result column="goods_classify" jdbcType="NUMERIC" property="goodsClassify"/>
    <result column="goods_classify_name" jdbcType="VARCHAR" property="goodsClassifyName"/>
    <result column="goods_num" jdbcType="NUMERIC" property="goodsNum"/>
    <result column="discounts_amount" jdbcType="NUMERIC" property="discountsAmount"/>
    <result column="operation_equipment_name" jdbcType="VARCHAR" property="operationEquipmentName"/>
    <result column="equipment_name" jdbcType="VARCHAR" property="equipmentName"/>
    <result column="terminal_name" jdbcType="VARCHAR" property="terminalName"/>
    <result column="merchant_benefit_classify_id" jdbcType="NUMERIC" property="merchantBenefitClassifyId"/>
    <result column="merchant_benefit_classify_name" jdbcType="VARCHAR" property="merchantBenefitClassifyName"/>
    <result column="operation_type" jdbcType="NUMERIC" property="operationType"/>
    <result column="operation_channel" jdbcType="NUMERIC" property="operationChannel"/>
    <result column="consume_type" jdbcType="NUMERIC" property="consumeType"/>
  </resultMap>

  <sql id="ALL_COLUMN">
    id,account_id,user_id,merchant_user_id,merchant_id,store_id,equipment_id,benefit_classify,
    benefit_id,initial_benefit,original_benefit,actual_benefit,out_trade_no,order_no,mode,create_time,
    resource,description,trade_type,trade_amount,commodity_name,store_name,equipment_type_name,equipment_value,
    equipment_type_id,sort,account_benefit_id
  </sql>

    <insert id="insertBatch">
        INSERT INTO um_account_record(id,account_id,user_id,merchant_user_id,merchant_id,
                                      store_id,equipment_id,benefit_classify,benefit_id,
                                      initial_benefit,original_benefit,actual_benefit,
                                      out_trade_no,order_no,sub_order_no,mode,create_time,
                                      resource,description,trade_type,trade_amount,commodity_name,
                                      equipment_type_name,equipment_value,group_number,store_name,
                                      equipment_type_id,record_type,sort,account_benefit_id,createdby,
                                      create_name,card_no,refund_no,goods_id,goods_type,goods_classify,
                                      goods_classify_name,goods_num,discounts_amount,operation_equipment_name,
                                      equipment_name,terminal_name,merchant_benefit_classify_id,merchant_benefit_classify_name,
                                      operation_type,operation_channel,consume_type,actual_value,goods_price,goods_type_name,
                                      account_initial_balance,account_initial_num,goods_type_id)
        VALUES
        <foreach collection="list" item="e" separator=",">
            (#{e.id}, #{e.accountId}, #{e.userId}, #{e.merchantUserId}, #{e.merchantId},
             #{e.storeId}, #{e.equipmentId}, #{e.benefitClassify}, #{e.benefitId},
             #{e.initialBenefit}, #{e.originalBenefit}, #{e.actualBenefit},
             #{e.outTradeNo}, #{e.orderNo}, #{e.subOrderNo}, #{e.mode}, #{e.createTime},
             #{e.resource}, #{e.description}, #{e.tradeType}, #{e.tradeAmount}, #{e.commodityName},
             #{e.equipmentTypeName}, #{e.equipmentValue}, #{e.groupNumber}, #{e.storeName},
             #{e.equipmentTypeId}, #{e.recordType}, #{e.sort}, #{e.accountBenefitId}, #{e.createdby},
             #{e.createName}, #{e.cardNo}, #{e.refundNo}, #{e.goodsId}, #{e.goodsType}, #{e.goodsClassify},
             #{e.goodsClassifyName}, #{e.goodsNum}, #{e.discountsAmount}, #{e.operationEquipmentName},
             #{e.equipmentName}, #{e.terminalName}, #{e.merchantBenefitClassifyId}, #{e.merchantBenefitClassifyName},
             #{e.operationType}, #{e.operationChannel}, #{e.consumeType}, #{e.actualValue}, #{e.goodsPrice}, #{e.goodsTypeName},
             #{e.accountInitialBalance}, #{e.accountInitialNum}, #{e.goodsTypeId})
        </foreach>
    </insert>

  <select id="findAllByOrderNo" resultMap="BaseResultMap">
    select <include refid="ALL_COLUMN"/>
    from um_account_record
    <where>
      <if test="merchantId != null">
        AND merchant_id = #{merchantId}
      </if>
      <if test="userId != null">
        AND user_id = #{userId}
      </if>
      <if test="merchantUserId != null">
        AND merchant_user_id = #{merchantUserId}
      </if>
      <if test="orderNo != null and orderNo != '' ">
        AND order_no = #{orderNo}
      </if>
      <if test="classify != null and classify.size > 0">
        AND benefit_classify in
        <foreach collection="classify" item="cid" open="(" close=")" separator=",">
          #{cid}
        </foreach>
      </if>
      <if test="mode != null">
        AND mode = #{mode}
      </if>
        <if test="startTime != null">
            and create_time <![CDATA[ >= ]]>  #{startTime}
        </if>
        <if test="endTime != null">
            and create_time <![CDATA[ <= ]]> #{endTime}
        </if>
    </where>
  </select>

  <select id="findByAccountBenefit" resultType="com.lyy.user.domain.account.entity.AccountRecord">
    select account_benefit_id,actual_benefit,mode,record_type from um_account_record
    where  create_time >= current_date
      <if test="merchantId != null">
        AND merchant_id = #{merchantId}
      </if>
      <if test="userId != null">
        AND user_id = #{userId}
      </if>
      <if test="merchantUserId != null">
          AND merchant_user_id = #{merchantUserId}
      </if>
      <if test="accountBenefitIds != null and accountBenefitIds.size > 0">
        AND account_benefit_id in
        <foreach collection="accountBenefitIds" item="id" open="(" close=")" separator=",">
          #{id}
        </foreach>
      </if>
      <if test="recordTypes != null and recordTypes.size > 0">
        AND record_type in
        <foreach collection="recordTypes" item="id" open="(" close=")" separator=",">
          #{id}
        </foreach>
      </if>
  </select>

  <select id="smallVenueRecord" resultType="com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueRecordListDTO">
       select
            order_no "orderNo",
            refund_no "refundNo",
            card_no "cardNo",
            commodity_name "commodityName" ,
            goods_id "goodsId",
            goods_price "goodsPrice",
            goods_classify "goodsClassify",
            goods_classify_name "goodsClassifyName",
            goods_type_id "goodsTypeId",
            goods_type_name  "goodsTypeName",
            goods_num "goodsNum",
            discounts_amount "discountsAmount",
            actual_benefit "actualBenefit",
            store_name "storeName",
            operation_channel "operationChannel",
            operation_equipment_name "operationEquipmentName",
            create_name "operatorName",
            create_time "createTime",
            merchant_benefit_classify_name "merchantBenefitClassifyName",
            merchant_benefit_classify_id "merchantBenefitClassifyId",
            original_benefit "originalBenefit",
            account_initial_balance "accountInitialBalance",
            equipment_name "equipmentName",
            equipment_value "equipmentValue",
            terminal_name "terminalName",
            description,
            consume_type "consumeType",
            record_type  "recordType",
            mode,
            trade_type "tradeType",
            trade_amount "tradeAmount"
       from
	       um_account_record
	   where
	       merchant_id = #{selectDTO.merchantId} and operation_type=#{selectDTO.operationType}
          <if test="selectDTO.goodsTypes != null and selectDTO.goodsTypes.size > 0">
              AND goods_type_id in
              <foreach collection="selectDTO.goodsTypes" item="goodType" open="(" close=")" separator=",">
                  #{goodType}
              </foreach>
          </if>
          <if test="selectDTO.goodsClassify != null">
              AND goods_classify = #{selectDTO.goodsClassify}
          </if>
          <if test="selectDTO.orderNo != null and selectDTO.orderNo != '' ">
              AND order_no like concat('%',#{selectDTO.orderNo},'%')
          </if>
          <if test="selectDTO.commodityName != null and selectDTO.commodityName !='' ">
              AND commodity_name like concat('%',#{selectDTO.commodityName},'%')
          </if>
          <if test="selectDTO.equipmentName != null and selectDTO.equipmentName !='' ">
              AND equipment_name like concat('%',#{selectDTO.equipmentName},'%')
          </if>
          <if test="selectDTO.operationChannels != null and selectDTO.operationChannels.size > 0">
              AND operation_channel in
              <foreach collection="selectDTO.operationChannels" item="operationChannel" open="(" close=")" separator=",">
                  #{operationChannel}
              </foreach>
          </if>
          <if test="selectDTO.startTime != null">
              and create_time <![CDATA[ >= ]]> #{selectDTO.startTime}
          </if>
          <if test="selectDTO.endTime != null">
              and create_time <![CDATA[ <= ]]> #{selectDTO.endTime}
          </if>
          <if test="selectDTO.storeIds != null and selectDTO.storeIds.size > 0">
              AND store_id in
              <foreach collection="selectDTO.storeIds" item="id" open="(" close=")" separator=",">
                  #{id}
              </foreach>
          </if>
      <if test="selectDTO.merchantBenefitClassifyIds != null">
          AND merchant_benefit_classify_id = #{selectDTO.merchantBenefitClassifyIds}
      </if>
      <if test="selectDTO.merchantBenefitClassifyIds == null
                    and selectDTO.merchantBenefitClassifyIdList != null and selectDTO.merchantBenefitClassifyIdList.size > 0">
          AND merchant_benefit_classify_id in
          <foreach collection="selectDTO.merchantBenefitClassifyIdList" item="id" open="(" close=")" separator=",">
              #{id}
          </foreach>
      </if>
      <if test="selectDTO.description != null and selectDTO.description !='' ">
          AND description like concat('%',#{selectDTO.description},'%')
      </if>
      <if test="selectDTO.consumeType != null">
          AND consume_type = #{selectDTO.consumeType}
      </if>
      <if test="selectDTO.recordType != null and selectDTO.recordType.size > 0">
          AND record_type in
          <foreach collection="selectDTO.recordType" item="type" open="(" close=")" separator=",">
              #{type}
          </foreach>
      </if>
      <if test="selectDTO.excludeRecordTypes != null and selectDTO.excludeRecordTypes.size > 0">
          AND record_type not in
          <foreach collection="selectDTO.excludeRecordTypes" item="type" open="(" close=")" separator=",">
              #{type}
          </foreach>
      </if>
      <if test="selectDTO.merchantUserId!=null">
           and merchant_user_id=#{selectDTO.merchantUserId}
      </if>
      order by create_time desc
  </select>

    <select id="accountBenefitTodayUseNum" resultType="java.math.BigDecimal">
        select
	     coalesce (sum(case when mode = 1 then actual_benefit when mode = 2 then -actual_benefit else 0 end),0) "todayUseNum"
        from
	      um_account_record
        where
            merchant_id = #{selectDTO.merchantId}
            and user_id =#{selectDTO.userId}
            and account_id = #{selectDTO.accountId}
            and store_id =#{selectDTO.storeId}
            and merchant_benefit_classify_id =#{selectDTO.merchantBenefitClassifyId}
            and to_char(now(), 'YYYYMMDD')= to_char(create_time, 'YYYYMMDD')
    </select>

  <select id="countRecord" resultType="java.lang.Long">
    select count(1) from um_account_record
    where merchant_id = #{accountRecordCountDTO.merchantId}
    and create_time>=current_date-1 and create_time <![CDATA[ < ]]> current_date
    <if test="accountRecordCountDTO.recordTypeList != null">
      <foreach collection="accountRecordCountDTO.recordTypeList" item="recordType" open=" AND record_type in (" close=")" separator=",">
        #{recordType}
      </foreach>
    </if>
    <if test="accountRecordCountDTO.equipmentTypeIdList != null">
      <foreach collection="accountRecordCountDTO.equipmentTypeIdList" item="equipmentTypeId" open=" AND equipment_type_id in (" close=")" separator=",">
        #{equipmentTypeId}
      </foreach>
    </if>
  </select>


    <select id="mobileTerminalRecord" resultType="com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueRecordListDTO">
        select
        order_no "orderNo",
        refund_no "refundNo",
        card_no "cardNo",
        commodity_name "commodityName" ,
        goods_id "goodsId",
        goods_price "goodsPrice",
        goods_classify "goodsClassify",
        goods_classify_name "goodsClassifyName",
        goods_type_id "goodsTypeId",
        goods_type_name  "goodsTypeName",
        goods_num "goodsNum",
        discounts_amount "discountsAmount",
        actual_benefit "actualBenefit",
        store_name "storeName",
        operation_channel "operationChannel",
        operation_equipment_name "operationEquipmentName",
        create_name "operatorName",
        create_time "createTime",
        merchant_benefit_classify_id "merchantBenefitClassifyId",
        merchant_benefit_classify_name "merchantBenefitClassifyName",
        original_benefit "originalBenefit",
        equipment_name "equipmentName",
        equipment_value "equipmentValue",
        terminal_name "terminalName",
        description,
        consume_type "consumeType",
        record_type  "recordType",
        mode,
        trade_type "tradeType",
        trade_amount "tradeAmount"
        from
        um_account_record
        where
        merchant_id = #{selectDTO.merchantId} and operation_type=#{selectDTO.operationType}
        <if test="selectDTO.goodsTypeIds != null and selectDTO.goodsTypeIds.size > 0">
            AND goods_type_id in
            <foreach collection="selectDTO.goodsTypeIds" item="goodTypeId" open="(" close=")" separator=",">
                #{goodTypeId}
            </foreach>
        </if>
        <if test="selectDTO.goodsClassify != null">
            AND goods_classify = #{selectDTO.goodsClassify}
        </if>
        <if test="selectDTO.keyword != null and selectDTO.keyword != '' ">
            AND (order_no like concat('%',#{selectDTO.keyword},'%')
            OR commodity_name like concat('%',#{selectDTO.keyword},'%'))
        </if>
        <if test="selectDTO.cardNo != null and selectDTO.cardNo != '' ">
            AND card_no=#{selectDTO.cardNo}
        </if>
        <if test="selectDTO.operationChannels != null and selectDTO.operationChannels.size > 0">
            AND operation_channel in
            <foreach collection="selectDTO.operationChannels" item="operationChannel" open="(" close=")" separator=",">
                #{operationChannel}
            </foreach>
        </if>
        <if test="selectDTO.storeIds != null and selectDTO.storeIds.size > 0">
            AND store_id in
            <foreach collection="selectDTO.storeIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="selectDTO.recordType != null and selectDTO.recordType.size > 0">
            AND record_type in
            <foreach collection="selectDTO.recordType" item="type" open="(" close=")" separator=",">
                #{type}
            </foreach>
        </if>
        <if test="selectDTO.excludeRecordTypes != null and selectDTO.excludeRecordTypes.size > 0">
            AND record_type not in
            <foreach collection="selectDTO.excludeRecordTypes" item="type" open="(" close=")" separator=",">
                #{type}
            </foreach>
        </if>
        <if test="selectDTO.merchantBenefitClassifyIds != null and selectDTO.merchantBenefitClassifyIds.size > 0">
            AND merchant_benefit_classify_id in
            <foreach collection="selectDTO.merchantBenefitClassifyIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="selectDTO.merchantUserId!=null">
            and merchant_user_id=#{selectDTO.merchantUserId}
        </if>
        <if test="selectDTO.startTime != null">
            and create_time <![CDATA[ >= ]]> #{selectDTO.startTime}
        </if>
        <if test="selectDTO.endTime != null">
            and create_time <![CDATA[ <= ]]> #{selectDTO.endTime}
        </if>
        order by create_time desc
    </select>


    <select id="listGrantCoinsRecord" resultType="com.lyy.user.account.infrastructure.account.dto.AccountAdjustRecordDTO">
        SELECT
            uar.MODE,
        uar.benefit_classify as benefitClassify,
        uar.createdby,
        uar.create_name as createName,
        uar.create_time as createDate,
        uar.actual_benefit as ammout,
        umu.ID as userId,
        umu.NAME as userNick,
        umu.telephone as userPhone
        FROM um_account_record uar
        left JOIN um_account_benefit uab on uar.benefit_id = uab.benefit_id and   uab.merchant_id = #{param.merchantId}
        left JOIN um_merchant_user umu on uar.merchant_user_id = umu.ID and umu.merchant_id = #{param.merchantId}
        WHERE   uar.merchant_id = #{param.merchantId} and uar.record_type IN ( 110, 111, 112, 113, 114 )
        <if test="param.benefitClassify == null">
            AND uar.benefit_classify IN (1,2,3, 4,5, 6,16,17,18,19, 21,24,25,100)
        </if>
        <if test="param.benefitClassify != null and param.benefitClassify==1">
            AND uar.benefit_classify IN (1,3,5,17,19,25,100)
        </if>
        <if test="param.benefitClassify != null and param.benefitClassify==2">
            AND uar.benefit_classify IN (2,4,6,16,18,21,24)
        </if>
        <if test="param.mode != null">
            AND uar.MODE = #{param.mode}
        </if>
        <if test="param.createDate != null">
            AND uar.create_time :: DATE = #{param.createDate}
        </if>
        <if test="numericKeyword !=null and param.keyword != null">
            AND  (umu.telephone = #{param.keyword} or umu.ID = #{param.keyword}::numeric or umu.NAME = #{param.keyword} or uar.createdby = #{param.keyword}::numeric)
        </if>
        <if test="stringKeyword !=null and param.keyword != null">
            AND  (umu.telephone = #{param.keyword} or umu.NAME = #{param.keyword} )
        </if>
    </select>

    <select id="countGrantCoinsRecord"  resultType="java.lang.Long">
        SELECT count(r.id)
        FROM um_account_record uar
        left JOIN um_account_benefit uab on uar.benefit_id = uab.benefit_id and   uab.merchant_id = #{param.merchantId}
        left JOIN um_merchant_user umu on uar.merchant_user_id = umu.ID and umu.merchant_id = #{param.merchantId}
        WHERE   uar.merchant_id = #{param.merchantId} and uar.record_type IN ( 110, 111, 112, 113, 114 )
        <if test="param.benefitClassify == null">
            AND uar.benefit_classify IN (1,2,3, 4,5, 6,16,17,18,19, 21,24,25,100)
        </if>
        <if test="param.benefitClassify != null and param.benefitClassify==1">
            AND uar.benefit_classify IN (1,3,5,17,19,25,100)
        </if>
        <if test="param.benefitClassify != null and param.benefitClassify==2">
            AND uar.benefit_classify IN (2,4,6,16,18,21,24)
        </if>
        <if test="param.mode != null">
            AND uar.MODE = #{param.mode}
        </if>
        <if test="param.createDate != null">
            AND uar.create_time :: DATE = #{param.createDate}
        </if>
        <if test="numericKeyword !=null and param.keyword != null">
            AND  (umu.telephone = #{param.keyword} or umu.ID = #{param.keyword}::numeric or umu.NAME = #{param.keyword} or uar.createdby = #{param.keyword}::numeric)
        </if>
        <if test="stringKeyword !=null and param.keyword != null">
            AND  (umu.telephone = #{param.keyword} or umu.NAME = #{param.keyword} )
        </if>
    </select>
</mapper>