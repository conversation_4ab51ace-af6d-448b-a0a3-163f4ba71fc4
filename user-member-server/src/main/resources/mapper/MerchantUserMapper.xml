<!--<?xml version="1.0" encoding="UTF-8"?>-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.user.repository.MerchantUserMapper">
  <resultMap id="BaseResultMap" type="com.lyy.user.domain.user.entity.MerchantUser">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="NUMERIC" property="id" />
    <result column="user_id" jdbcType="NUMERIC" property="userId" />
    <result column="merchant_id" jdbcType="NUMERIC" property="merchantId" />
    <result column="telephone" jdbcType="VARCHAR" property="telephone" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="is_active" jdbcType="BOOLEAN" property="active" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="createdby" jdbcType="NUMERIC" property="createdby" />
    <result column="updatedby" jdbcType="NUMERIC" property="updatedby" />
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="gender" jdbcType="VARCHAR" property="gender"/>
    <result column="head_img" jdbcType="VARCHAR" property="headImg"/>
    <result column="birthday" jdbcType="VARCHAR" property="birthday"/>
    <result column="user_type" jdbcType="VARCHAR" property="userType"/>
    <result column="city_id" jdbcType="NUMERIC" property="cityId"/>
    <result column="province_city" jdbcType="VARCHAR" property="provinceCity"/>
    <result column="province_id" jdbcType="NUMERIC" property="provinceId"/>
    <result column="region_id" jdbcType="NUMERIC" property="regionId"/>
    <result column="address" jdbcType="VARCHAR" property="address"/>
    <result column="pass_word" jdbcType="VARCHAR" property="passWord"/>
  </resultMap>

  <resultMap id="MerchantUserListDTOMap" type="com.lyy.user.account.infrastructure.user.dto.MerchantUserListDTO">

    <id column="id" jdbcType="NUMERIC" property="id" />
    <result column="user_id" jdbcType="NUMERIC" property="userId" />
    <result column="merchant_id" jdbcType="NUMERIC" property="merchantId" />
    <result column="telephone" jdbcType="VARCHAR" property="telephone" />
    <result column="name" jdbcType="VARCHAR" property="nickName"/>
    <result column="gender" jdbcType="VARCHAR" property="gender"/>
    <result column="head_img" jdbcType="VARCHAR" property="headImg"/>
<!--    <result column="birthday" jdbcType="VARCHAR" property="birthday"/>-->
<!--    <result column="user_type" jdbcType="VARCHAR" property="userType"/>-->
<!--    <result column="city_id" jdbcType="NUMERIC" property="cityId"/>-->
<!--    <result column="province_city" jdbcType="VARCHAR" property="provinceCity"/>-->

    <result column="last_consume_time" jdbcType="TIMESTAMP" property="lastConsumeTime" />
    <result column="total_pay_consume" jdbcType="NUMERIC" property="totalConsume" />
    <result column="balance_amount" jdbcType="NUMERIC" property="totalBalance" />
    <result column="balance_coins" jdbcType="NUMERIC" property="totalCoin" />
  </resultMap>

  <sql id="Base_Column_List">
    id,user_id,merchant_id,telephone,description,is_active,create_time,update_time,createdby,updatedby,
    name,gender,head_img,birthday,user_type,province_city,city_id,province_id,region_id
  </sql>

  <sql id="Query_Column_List">
    id,user_id,merchant_id,telephone,description,is_active,name,gender,head_img,birthday,user_type,province_city,city_id,province_id,region_id
  </sql>

  <sql id = "Merchant_User_List_Query">
    select mu.id,mu.name,mu.user_id,mu.merchant_id,mu.gender,mu.telephone,mu.head_img,(COALESCE(us.pay_amount,0) + COALESCE(us.pay_for_service_amount,0)) as total_pay_consume,us.balance_amount,
    us.balance_coins,us.recent_consumption_time as last_consume_time
    from um_merchant_user mu
    <if test="hasTag == true">
      left join um_merchant_user_tag utmu on mu.id = utmu.business_user_id and utmu.tag_type = '1'
    </if>
    left join um_statistics us on mu.id = us.merchant_user_id
    where mu.is_active = true
    <if test="merchantId != null">
      and mu.merchant_id = #{merchantId} and us.merchant_id = #{merchantId}
      <if test="hasTag == true">
        and utmu.merchant_id = #{merchantId}
      </if>
    </if>
    <if test="totalConsumeMoneyMin != null">
      and (us.pay_amount + us.pay_for_service_amount) &gt;= #{totalConsumeMoneyMin}
    </if>
    <if test="totalConsumeMoneyMax != null">
      and (us.pay_amount + us.pay_for_service_amount) &lt;= #{totalConsumeMoneyMax}
    </if>

    <if test="totalBalanceMin != null">
      and us.balance_amount &gt;= #{totalBalanceMin}
    </if>
    <if test="totalBalanceMax != null">
      and us.balance_amount &lt;= #{totalBalanceMax}
    </if>

    <if test="totalCoinMin != null">
      and us.balance_coins &gt;= #{totalCoinMin}
    </if>
    <if test="totalCoinMax != null">
      and us.balance_coins &lt;= #{totalCoinMax}
    </if>

    <if test="groupTagIds != null and groupTagIds.length > 0">
      and utmu.user_tags &amp;&amp; array[#{groupTagIds}]
    </if>
    <if test="equipmentTypeTagIds != null and equipmentTypeTagIds.length > 0">
      and utmu.user_tags &amp;&amp; array[#{equipmentTypeTagIds}]
    </if>
    <if test="otherTagIds != null and otherTagIds.length > 0">
      and utmu.user_tags &amp;&amp; array[#{otherTagIds}]
    </if>
    <if test="sexTagIds != null and sexTagIds.length > 0">
      and utmu.user_tags &amp;&amp; array[#{sexTagIds}]
    </if>
  </sql>

  <update id="updateMerchantUserByIdAndMerchantId">
    update um_merchant_user
    <set>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="telephone != null and telephone != '' ">
        telephone = #{telephone},
      </if>
      <if test="description != null and description != '' ">
        description = #{description},
      </if>
      <if test="active != null">
        is_active = #{active},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="updatedby != null">
        updatedby = #{updatedby},
      </if>
      <if test="name !=null and name != '' ">
        name = #{name},
      </if>
      <if test="gender != null and gender != '' ">
        gender = #{gender},
      </if>
      <if test="headImg != null and headImg != '' ">
        head_img = #{headImg},
      </if>
      <if test="birthday != null and birthday != '' ">
        birthday = #{birthday},
      </if>
      <if test="userType != null and userType != '' ">
        user_type = #{userType},
      </if>
      <if test="cityId != null">
        city_id = #{cityId},
      </if>
      <if test="provinceCity != null and provinceCity != '' ">
        province_city = #{provinceCity},
      </if>
      <if test="provinceId != null ">
        province_id = #{provinceId},
      </if>
      <if test="regionId != null">
        region_id =#{regionId},
      </if>
      <if test="createdby != null">
        createdby = #{createdby},
      </if>
    </set>
    where id = #{id}
    and merchant_id = #{merchantId}
  </update>

  <select id="selectByUserIdAndMerchantId" resultType="com.lyy.user.domain.user.entity.MerchantUser">
    select <include refid="Query_Column_List"/>
    from um_merchant_user
    where merchant_id = #{merchantId}
    and user_id = #{userId}
    and is_active = true
  </select>
  <select id="selectAllByTelephoneAndMerchantId" resultMap="BaseResultMap">
    select
    <include refid="Query_Column_List"/>
    from um_merchant_user
    where
    telephone = #{telephone}
    AND merchant_id = #{merchantId}
    and is_active = true
    order by create_time desc
  </select>

  <select id="selectByIdAndMerchantId" resultType="com.lyy.user.domain.user.entity.MerchantUser">
    select
    <include refid="Query_Column_List"/>
    from um_merchant_user
    where
    id = #{merchantUserId}
    AND is_active = true
    AND merchant_id = #{merchantId}
  </select>


  <select id="countUserListByMerchantOrder" resultType="java.lang.Long">

    select count(distinct mu.id) from um_merchant_user mu
    <if test="tagTypeNum > 0">
    left join um_tag_merchant_user utmu on mu.id = utmu.business_user_id and utmu.tag_type = '1'
    left join um_tag_user utu on utmu.tag_id = utu.id
    </if>
    left join um_statistics us on mu.id = us.merchant_user_id
    <where>
      mu.is_active = true
      <if test="merchantId != null">
        and mu.merchant_id = #{merchantId} and us.merchant_id = #{merchantId}
        <if test="tagTypeNum > 0">
          and utmu.merchant_id = #{merchantId} and utu.merchant_id = #{merchantId}
        </if>
      </if>
      <choose>
        <when test="numericKeyword !=null">
          and (mu.telephone like concat(#{numericKeyword},'%')  or mu.id = #{numericKeyword}::numeric or mu.name like
          concat('%',#{numericKeyword},'%'))
        </when>
        <when test="strKeyWord != null and strKeyWord != ''">
          and mu.name like concat('%',#{strKeyWord},'%')
        </when>
      </choose>

      <if test="totalConsumeMoneyMin != null">
        and (us.pay_amount + us.pay_for_service_amount) &gt;= #{totalConsumeMoneyMin}
      </if>
      <if test="totalConsumeMoneyMax != null">
        and (us.pay_amount + us.pay_for_service_amount) &lt;= #{totalConsumeMoneyMax}
      </if>

      <if test="totalBalanceMin != null">
        and us.balance_amount &gt;= #{totalBalanceMin}
      </if>
      <if test="totalBalanceMax != null">
        and us.balance_amount &lt;= #{totalBalanceMax}
      </if>

      <if test="totalCoinMin != null">
        and us.balance_coins &gt;= #{totalCoinMin}
      </if>
      <if test="totalCoinMax != null">
        and us.balance_coins &lt;= #{totalCoinMax}
      </if>

      <if test="tagIdList != null and tagIdList.size()>0">
        and utmu.tag_id in
        <foreach collection="tagIdList" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
        <if test="tagTypeNum > 1">
          group by mu.id having count(mu.id) >= #{tagTypeNum}
        </if>

      </if>
    </where>

  </select>

  <select id="countUserList" resultType="java.lang.Long">
    select count(*)
    from um_merchant_user mu
    <if test="hasTag == true">
      left join um_merchant_user_tag utmu on mu.id = utmu.business_user_id and utmu.tag_type = '1'
    </if>
    left join um_statistics us on mu.id = us.merchant_user_id
    <where>
      mu.is_active = true
      <if test="merchantId != null">
        and mu.merchant_id = #{merchantId} and us.merchant_id = #{merchantId}
        <if test="hasTag == true">
          and utmu.merchant_id = #{merchantId}
        </if>
      </if>
      <choose>
        <when test="numericKeyword !=null">
          and (mu.telephone like concat(#{numericKeyword},'%')  or mu.user_id = #{numericKeyword}::numeric or mu.name like
          concat('%',#{numericKeyword},'%'))
        </when>
        <when test="strKeyWord != null and strKeyWord != ''">
          and mu.name like concat('%',#{strKeyWord},'%')
        </when>
      </choose>

      <if test="totalConsumeMoneyMin != null">
        and (us.pay_amount + us.pay_for_service_amount) &gt;= #{totalConsumeMoneyMin}
      </if>
      <if test="totalConsumeMoneyMax != null">
        and (us.pay_amount + us.pay_for_service_amount) &lt;= #{totalConsumeMoneyMax}
      </if>

      <if test="totalBalanceMin != null">
        and us.balance_amount &gt;= #{totalBalanceMin}
      </if>
      <if test="totalBalanceMax != null">
        and us.balance_amount &lt;= #{totalBalanceMax}
      </if>

      <if test="totalCoinMin != null">
        and us.balance_coins &gt;= #{totalCoinMin}
      </if>
      <if test="totalCoinMax != null">
        and us.balance_coins &lt;= #{totalCoinMax}
      </if>

      <if test="groupTagIds != null and groupTagIds.length > 0">
        and utmu.user_tags &amp;&amp; array[#{groupTagIds}]
      </if>
      <if test="equipmentTypeTagIds != null and equipmentTypeTagIds.length > 0">
        and utmu.user_tags &amp;&amp; array[#{equipmentTypeTagIds}]
      </if>
      <if test="otherTagIds != null and otherTagIds.length > 0">
        and utmu.user_tags &amp;&amp; array[#{otherTagIds}]
      </if>
      <if test="sexTagIds != null and sexTagIds.length > 0">
        and utmu.user_tags &amp;&amp; array[#{sexTagIds}]
      </if>
    </where>
  </select>

  <select id="selectUserListByMerchantOrder" resultMap="MerchantUserListDTOMap">
  select distinct mu.id,mu.name,mu.user_id,mu.merchant_id,mu.gender,mu.telephone,mu.head_img,(us.pay_amount + us.pay_for_service_amount) as total_pay_consume,us.balance_amount,
    us.balance_coins,us.recent_consumption_time as last_consume_time  from um_merchant_user mu
    <if test="tagTypeNum > 0">
  left join um_tag_merchant_user utmu on mu.id = utmu.business_user_id and utmu.tag_type = '1'
  left join um_tag_user utu on utmu.tag_id = utu.id
    </if>
  left join um_statistics us on mu.id = us.merchant_user_id
    <where>
      mu.is_active = true
      <if test="merchantId != null">
         and mu.merchant_id = #{merchantId} and us.merchant_id = #{merchantId}
        <if test="tagTypeNum > 0">
          and utmu.merchant_id = #{merchantId} and utu.merchant_id = #{merchantId}
        </if>
      </if>
      <choose>
        <when test="numericKeyword !=null">
          and (mu.telephone like concat(#{numericKeyword},'%')  or mu.id = #{numericKeyword}::numeric or mu.name like
          concat('%',#{numericKeyword},'%'))
        </when>
        <when test="strKeyWord != null and strKeyWord != ''">
          and mu.name like concat('%',#{strKeyWord},'%')
        </when>
      </choose>

      <if test="totalConsumeMoneyMin != null">
        and (us.pay_amount + us.pay_for_service_amount) &gt;= #{totalConsumeMoneyMin}
      </if>
      <if test="totalConsumeMoneyMax != null">
        and (us.pay_amount + us.pay_for_service_amount) &lt;= #{totalConsumeMoneyMax}
      </if>

      <if test="totalBalanceMin != null">
        and us.balance_amount &gt;= #{totalBalanceMin}
      </if>
      <if test="totalBalanceMax != null">
        and us.balance_amount &lt;= #{totalBalanceMax}
      </if>

      <if test="totalCoinMin != null">
        and us.balance_coins &gt;= #{totalCoinMin}
      </if>
      <if test="totalCoinMax != null">
        and us.balance_coins &lt;= #{totalCoinMax}
      </if>

      <if test="tagIdList != null and tagIdList.size()>0">
        and utmu.tag_id in
        <foreach collection="tagIdList" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
        <if test="tagTypeNum > 1">
          group by  mu.id,mu.name,mu.user_id,mu.merchant_id,mu.gender,mu.telephone,mu.head_img,total_pay_consume,us.balance_amount,
          us.balance_coins,last_consume_time having count(mu.id) >= #{tagTypeNum}
        </if>
      </if>

      <if test="orderItemList != null  and orderItemList.size > 0">
        order by
        <foreach collection="orderItemList" index="index" item="item" separator=",">
          <choose>
            <when test="item.asc == true">
              ${item.column} asc
            </when>
            <otherwise>
              ${item.column} desc nulls last
            </otherwise>
          </choose>
        </foreach>
      </if>
      limit #{pageSize} offset #{start}
  </where>
  </select>

  <select id="selectUserList" resultMap="MerchantUserListDTOMap">
    select mu.id,mu.name,mu.user_id,mu.merchant_id,mu.gender,mu.telephone,mu.head_img,(COALESCE(us.pay_amount,0) + COALESCE(us.pay_for_service_amount,0)) as total_pay_consume,us.balance_amount,
    us.balance_coins,us.recent_consumption_time as last_consume_time
    from um_merchant_user mu
    <if test="hasTag == true">
      left join um_merchant_user_tag utmu on mu.id = utmu.business_user_id and utmu.tag_type = '1'
    </if>
    left join um_statistics us on mu.id = us.merchant_user_id
    <where>
      mu.is_active = true
      <if test="merchantId != null">
        and mu.merchant_id = #{merchantId} and us.merchant_id = #{merchantId}
        <if test="hasTag == true">
          and utmu.merchant_id = #{merchantId}
        </if>
      </if>
      <choose>
        <when test='numericKeyword !=null and keywordType == null'>
          and (mu.telephone like concat(#{keyword},'%')  or mu.user_id = #{numericKeyword} or mu.name like
          concat('%',#{keyword},'%'))
        </when>
        <when test='strKeyWord != null and strKeyWord != "" and keywordType == null '>
          and mu.name like concat('%',#{strKeyWord},'%')
        </when>
        <when test='keyword != null and keyword != "" and keywordType != null and keywordType == 1' >
          and mu.name like concat('%',#{keyword},'%')
        </when>
        <when test='numericKeyword != null and keywordType != null and keywordType == 2' >
          and mu.user_id = #{numericKeyword}
        </when>
        <when test='keyword != null and keyword != "" and keywordType != null and keywordType == 3' >
          and mu.telephone like concat(#{keyword},'%')
        </when>
      </choose>

      <if test="totalConsumeMoneyMin != null">
        and (us.pay_amount + us.pay_for_service_amount) &gt;= #{totalConsumeMoneyMin}
      </if>
      <if test="totalConsumeMoneyMax != null">
        and (us.pay_amount + us.pay_for_service_amount) &lt;= #{totalConsumeMoneyMax}
      </if>

      <if test="totalBalanceMin != null">
        and us.balance_amount &gt;= #{totalBalanceMin}
      </if>
      <if test="totalBalanceMax != null">
        and us.balance_amount &lt;= #{totalBalanceMax}
      </if>

      <if test="totalCoinMin != null">
        and us.balance_coins &gt;= #{totalCoinMin}
      </if>
      <if test="totalCoinMax != null">
        and us.balance_coins &lt;= #{totalCoinMax}
      </if>

      <if test="groupTagIds != null and groupTagIds.length > 0">
        and utmu.user_tags &amp;&amp; array[#{groupTagIds}]
      </if>
      <if test="equipmentTypeTagIds != null and equipmentTypeTagIds.length > 0">
        and utmu.user_tags &amp;&amp; array[#{equipmentTypeTagIds}]
      </if>
      <if test="otherTagIds != null and otherTagIds.length > 0">
        and utmu.user_tags &amp;&amp; array[#{otherTagIds}]
      </if>
      <if test="sexTagIds != null and sexTagIds.length > 0">
        and utmu.user_tags &amp;&amp; array[#{sexTagIds}]
      </if>

      <if test="orderItemList != null  and orderItemList.size > 0">
        order by
        <foreach collection="orderItemList" index="index" item="item" separator=",">
          <choose>
            <when test="item.asc == true">
              ${item.column} asc
            </when>
            <otherwise>
              ${item.column} desc nulls last
            </otherwise>
          </choose>
        </foreach>
      </if>
    </where>
  </select>

  <select id="selectNoQueryConditionsList" resultMap="MerchantUserListDTOMap">
    select us.merchant_user_id as id,us.user_id,us.merchant_id,
           (COALESCE(us.pay_amount,0) + COALESCE(us.pay_for_service_amount,0)) as total_pay_consume,
           us.balance_amount,
           us.balance_coins,
           us.recent_consumption_time as last_consume_time
    from um_statistics us
    where us.merchant_id = #{merchantId}
    <if test="orderItemList != null  and orderItemList.size > 0">
      order by
      <foreach collection="orderItemList" index="index" item="item" separator=",">
        <choose>
          <when test="item.asc == true">
            ${item.column} asc
          </when>
          <otherwise>
            ${item.column} desc nulls last
          </otherwise>
        </choose>
      </foreach>
    </if>
  </select>

  <select id="selectByIds" resultType="com.lyy.user.domain.user.entity.MerchantUser">
        select * from um_merchant_user
        where merchant_id = #{merchantId}
        and id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectAllMerchantUserId" resultType="java.lang.Long">
      select id from um_merchant_user
      where merchant_id = #{merchantId} and is_active = true
    </select>

  <select id="getMax" resultType="java.lang.Long">
    select max(createdby) from um_merchant_user where merchant_id = #{merchantId} and user_id = #{userId}
  </select>


  <select id="searchByKeywords" resultType="com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserInfoAndAccountDTO">
  select
    umu.id "merchantUserId",
    umu.name "name",
	umu.user_id "userId",
	umu.head_img "headImg",
	umu.create_time "createTime",
	umu.user_type "userType" ,
	umu.birthday,
	umu.telephone,
	umu.gender "gender",
	umu.province_city "provinceCity" ,
	umu.address,
	umu.province_id "provinceId",
	umu.city_id "cityId",
	umu.region_id "regionId",
	ua.card_no "cardNo",
	ua.id "accountId",
	ua.parent_account_id "parentAccountId",
	ua.down_time "downTime",
	ua.store_id "storeId",
	ua.status "status",
	uab.balance "balance",
	ua.default_flag "defaultFlag",
	uab.merchant_benefit_classify_id "merchantBenefitClassifyId",
	uab.use_rule_id "useRuleId",
	uab.store_id "benefitStoreId",
	uab.up_time "benefitUpTime",
	uab.down_time "benefitDownTime"
  from
      um_merchant_user umu
  left join um_account ua on
      umu.user_id = ua.user_id
      and ua.classify=99
      and ua.merchant_id = #{merchantId}
  left join um_account_benefit uab on
      ua.id = uab.account_id and uab.merchant_id = #{merchantId}
  where
      umu.merchant_id = #{merchantId}
      and umu.is_active=true
      <choose>
          <when test="!(keyWordsType != null and @com.lyy.user.account.infrastructure.constant.UserMemberSysConstants@SMALL_VENUE_SEARCH_TYPE_USERID.equals(keyWordsType)) and userId == null">
              and ua.card_no = #{keywords}
          </when>
          <when test="keyWordsType != null and @com.lyy.user.account.infrastructure.constant.UserMemberSysConstants@SMALL_VENUE_SEARCH_TYPE_USERID.equals(keyWordsType)">
              and umu.user_id = #{keywords} :: BIGINT
          </when>
          <otherwise>
              <if test="userId != null">
                  and umu.user_id = #{userId}
              </if>
              union
              (select
                  umu.id "merchantUserId",
                  umu.name "name",
                  umu.user_id "userId",
                  umu.head_img "headImg",
                  umu.create_time "createTime",
                  umu.user_type "userType" ,
                  umu.birthday,
                  umu.telephone,
                  umu.gender "gender",
                  umu.province_city "provinceCity" ,
                  umu.address,
                  umu.province_id "provinceId",
                  umu.city_id "cityId",
                  umu.region_id "regionId",
                  ua.card_no "cardNo",
                  ua.id "accountId",
                  ua.parent_account_id "parentAccountId",
                  ua.down_time "downTime",
                  ua.store_id "storeId",
                  ua.status "status",
                  uab.balance "balance",
                  ua.default_flag "defaultFlag",
                  uab.merchant_benefit_classify_id "merchantBenefitClassifyId",
                  uab.use_rule_id "useRuleId",
                  uab.store_id "benefitStoreId",
                  uab.up_time "benefitUpTime",
                  uab.down_time "benefitDownTime"
                  from um_merchant_user umu
                  left join um_account ua on umu.user_id = ua.user_id
                      and ua.classify = 99 and ua.merchant_id = #{merchantId}
                  left join um_account_benefit uab on ua.id = uab.account_id and uab.merchant_id = #{merchantId}
                  where
                      umu.merchant_id = #{merchantId}
                      and umu.is_active = true
                      and ua.card_no = #{keywords}
              )
          </otherwise>
      </choose>
  </select>

  <select id="selectMerchantUserPassword" resultType="java.lang.String">
     select pass_word from um_merchant_user where merchant_id = #{merchantId} and user_id=#{userId}
  </select>

  <update id="updateMerchantUserPassword">
      update um_merchant_user set pass_word=#{newPassWord} where merchant_id = #{merchantId} and user_id=#{userId}
  </update>

  <select id="selectSmallVenueUserList" resultType="com.lyy.user.account.infrastructure.user.dto.SmallVenueUserDTO">
    select
    umu.name,
    umu.id ,
    umu.user_id "userId",
    umu.head_img "headImg" ,
    umu.telephone,
    umu.user_type "userType",
    umu.create_time "createTime"
    from
    um_merchant_user umu
    where
    umu.merchant_id =#{selectDTO.merchantId} and umu.is_active=true
    <if test="selectDTO.name != null and selectDTO.name !='' ">
      and umu.name like concat('%',#{selectDTO.name},'%')
    </if>
    <if test="selectDTO.telephone != null and selectDTO.telephone !='' ">
      and umu.telephone like concat('%',#{selectDTO.telephone},'%')
    </if>
    <if test="selectDTO.userId != null">
      and umu.user_id :: VARCHAR like concat('%',#{selectDTO.userId},'%')
    </if>
    <if test="selectDTO.userType != null and selectDTO.userType.size>0 ">
      and umu.user_type in
        <foreach collection="selectDTO.userType" item="type" open="(" close=")" separator=",">
            #{type}
        </foreach>
    </if>
    <if test="selectDTO.startTime != null">
      and umu.create_time <![CDATA[ >= ]]> #{selectDTO.startTime}
    </if>
    <if test="selectDTO.endTime != null">
      and umu.create_time <![CDATA[ <= ]]> #{selectDTO.endTime}
    </if>
  </select>

  <select id="countSmallVenueUserList" resultType="java.lang.Long">
    select count ( distinct umu.id )
    from
    um_merchant_user umu
    left join um_merchant_user_tag umut on
    umu.id = umut.business_user_id
    and umut.tag_type = 1 and umut.merchant_id =#{selectDTO.merchantId}
    where
    umu.merchant_id =#{selectDTO.merchantId} and umu.is_active=true
    <if test="tagIds != null and tagIds.length > 0">
      and umut.user_tags &amp;&amp; array[#{tagIds}]
    </if>
    <if test="selectDTO.name != null and selectDTO.name !='' ">
      and umu.name like concat('%',#{selectDTO.name},'%')
    </if>
    <if test="selectDTO.telephone != null and selectDTO.telephone !='' ">
      and umu.telephone like concat('%',#{selectDTO.telephone},'%')
    </if>
    <if test="selectDTO.userId != null">
      and umu.user_id :: VARCHAR like concat('%',#{selectDTO.userId},'%')
    </if>
    <if test="selectDTO.userType != null and selectDTO.userType.size>0 ">
      and umu.user_type in
      <foreach collection="selectDTO.userType" item="type" open="(" close=")" separator=",">
        #{type}
      </foreach>
    </if>
    <if test="selectDTO.startTime != null">
      and umu.create_time <![CDATA[ >= ]]> #{selectDTO.startTime}
    </if>
    <if test="selectDTO.endTime != null">
      and umu.create_time <![CDATA[ <= ]]> #{selectDTO.endTime}
    </if>
  </select>



  <sql id="merchantBenefitClassifyDTOCondition">
    <if test="selectDTO.merchantBenefitClassifyDTOList != null  and selectDTO.merchantBenefitClassifyDTOList.size > 0">
      <foreach collection="selectDTO.merchantBenefitClassifyDTOList" item="dto">
        and usvss.merchant_benefit_classify_id = ${dto.merchantBenefitClassifyId}
        <if test="dto.amountMin != null">
          and usvss.balance  <![CDATA[ >= ]]>  #{dto.amountMin}
        </if>
        <if test="dto.amountMax != null">
          and usvss.balance  <![CDATA[ <= ]]>  #{dto.amountMax}
        </if>
      </foreach>
    </if>
  </sql>

  <sql id="specificFilterInfoDTOCondition">
    <if test="selectDTO.specificFilterInfoDTO != null ">
<!--      <if test="selectDTO.specificFilterInfoDTO.totalConsumeMoneyMin != null">-->
<!--        and (us.pay_amount + us.pay_for_service_amount)  <![CDATA[ >= ]]>-->
<!--        #{selectDTO.specificFilterInfoDTO.totalConsumeMoneyMin}-->
<!--      </if>-->
<!--      <if test="selectDTO.specificFilterInfoDTO.totalConsumeMoneyMax != null">-->
<!--        and (us.pay_amount + us.pay_for_service_amount)  <![CDATA[ <= ]]>-->
<!--        #{selectDTO.specificFilterInfoDTO.totalConsumeMoneyMax}-->
<!--      </if>-->
      <if test="selectDTO.specificFilterInfoDTO.userTypeList != null and selectDTO.specificFilterInfoDTO.userTypeList.size>0 ">
        and umu.user_type in
        <foreach collection="selectDTO.specificFilterInfoDTO.userTypeList" item="type" open="(" close=")"
                 separator=",">
          #{type}
        </foreach>
      </if>
<!--      <if test="selectDTO.specificFilterInfoDTO.memberLevelIdList != null and selectDTO.specificFilterInfoDTO.memberLevelIdList.size>0 ">-->
<!--        and um.member_level_id in-->
<!--        <foreach collection="selectDTO.specificFilterInfoDTO.memberLevelIdList" item="memberLevelId" open="("-->
<!--                 close=")" separator=",">-->
<!--          #{memberLevelId}-->
<!--        </foreach>-->
<!--      </if>-->
    </if>
  </sql>

  <resultMap id="smallVenueMobileUserMap" type="com.lyy.user.account.infrastructure.user.dto.SmallVenueUserMobileVO">
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="user_id" jdbcType="NUMERIC" property="userId" />
    <result column="head_img" jdbcType="VARCHAR" property="headImg"/>
    <result column="telephone" jdbcType="VARCHAR" property="telephone" />
    <result column="user_type" jdbcType="VARCHAR" property="userType"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="total_pay_consume" jdbcType="NUMERIC" property="totalPayConsume" />
    <result column="amount_consumption" jdbcType="NUMERIC" property="amountConsumption" />
    <result column="recent_consumption_time" jdbcType="TIMESTAMP" property="recentConsumptionTime"/>
    <result column="member_level_id" jdbcType="NUMERIC" property="memberLevelId"/>
  </resultMap>

  <select id="smallVenueMobileUserList"
          resultMap="smallVenueMobileUserMap">
      select distinct
      umu.id ,
      umu.name,
      umu.user_id ,
      umu.head_img  ,
      umu.telephone,
      umu.user_type ,
      umu.create_time
      from
      um_merchant_user umu
<!--    <if test="selectDTO.keyword != null ">-->
<!--      left join um_account ua on umu.id = ua.merchant_user_id and ua.merchant_id = #{selectDTO.merchantId}-->
<!--    </if>-->
      <if test="hasTag == true">
          left join um_merchant_user_tag umut on
          umu.id = umut.business_user_id
          and umut.tag_type = 1 and umut.merchant_id =#{selectDTO.merchantId}
      </if>
      <where>
          and umu.merchant_id =#{selectDTO.merchantId} and umu.is_active=true
        <if test="selectDTO.keyword != null ">
          <choose>
            <when test="keyWordType != null and @com.lyy.user.account.infrastructure.constant.UserMemberSysConstants@SMALL_VENUE_SEARCH_TYPE_USERID.equals(keyWordType)">
              and umu.user_id = #{selectDTO.userId}
            </when>
            <when test="keyWordType != null and @com.lyy.user.account.infrastructure.constant.UserMemberSysConstants@SMALL_VENUE_SEARCH_TYPE_PHONENUM.equals(keyWordType)">
              and umu.telephone like  concat('%',#{selectDTO.keyword},'%')
            </when>
            <when test="keyWordType != null and @com.lyy.user.account.infrastructure.constant.UserMemberSysConstants@SMALL_VENUE_SEARCH_TYPE_USERMEMBERNAME.equals(keyWordType)">
              and umu.name like concat('%',#{selectDTO.keyword},'%')
            </when>
          </choose>
        </if>
        <if test="groupTagIds != null and groupTagIds.length > 0">
          and umut.user_tags &amp;&amp; array[#{groupTagIds}]
        </if>
        <if test="equipmentTypeTagIds != null and equipmentTypeTagIds.length > 0">
          and umut.user_tags &amp;&amp; array[#{equipmentTypeTagIds}]
        </if>
        <if test="otherTagIds != null and otherTagIds.length > 0">
          and umut.user_tags &amp;&amp; array[#{otherTagIds}]
        </if>
        <if test="sexTagIds != null and sexTagIds.length > 0">
          and umut.user_tags &amp;&amp; array[#{sexTagIds}]
        </if>
        <if test="merchantUserIdList != null and merchantUserIdList.size() > 0">
          and umu.id in
            <foreach collection="merchantUserIdList" item="merchantUserId" open="(" close=")" separator=",">
              ${merchantUserId}
            </foreach>
        </if>

        <!--      特定过滤条件-->
        <include refid="specificFilterInfoDTOCondition"/>
        order by umu.create_time desc
        <!--        商家自定义权益-->
<!--        <include refid="merchantBenefitClassifyDTOCondition"/>-->
        <!--        排序-->
<!--        <if test="orderItemList != null  and orderItemList.size > 0">-->
<!--          order by-->
<!--          <foreach collection="orderItemList" index="index" item="item" separator=",">-->
<!--            <choose>-->
<!--              <when test="item.asc == true">-->
<!--                ${item.column} asc-->
<!--              </when>-->
<!--              <otherwise>-->
<!--                ${item.column} desc nulls last-->
<!--              </otherwise>-->
<!--            </choose>-->
<!--          </foreach>-->
<!--        </if>-->
      </where>
  </select>

  <select id="smallVenueMobileMemberUserList" resultMap="smallVenueMobileUserMap">
    select distinct
    umu.id ,
    umu.name,
    umu.user_id ,
    umu.head_img  ,
    umu.telephone,
    umu.user_type ,
    umu.create_time
    from
    um_merchant_user umu
    <where>
      and umu.merchant_id =#{selectDTO.merchantId} and umu.is_active=true
        and umu.id in
        <foreach collection="merchantUserIdList" item="merchantUserId" open="(" close=")" separator=",">
          ${merchantUserId}
        </foreach>
    </where>
  </select>

  <select id="countSmallVenueMobileUserList" resultType="java.lang.Long">
    select count ( distinct umu.id )
    from
    um_merchant_user umu
    <if test="hasTag == true">
      left join um_merchant_user_tag umut on
      umu.id = umut.business_user_id
      and umut.tag_type = 1 and umut.merchant_id =#{selectDTO.merchantId}
    </if>
<!--    <if test="hasMerchantBenefitClassifyId == true">-->
<!--      left join um_small_venue_stored_statistics usvss on umu.id = usvss.merchant_user_id AND usvss.merchant_id =-->
<!--      #{selectDTO.merchantId}-->
<!--    </if>-->
    <where>
      and umu.merchant_id =#{selectDTO.merchantId} and umu.is_active=true
      <if test="selectDTO.keyword != null ">
        <choose>
          <when test="keyWordType != null and @com.lyy.user.account.infrastructure.constant.UserMemberSysConstants@SMALL_VENUE_SEARCH_TYPE_USERID.equals(keyWordType)">
            and umu.user_id = #{selectDTO.keyword} :: BIGINT
          </when>
          <when test="keyWordType != null and @com.lyy.user.account.infrastructure.constant.UserMemberSysConstants@SMALL_VENUE_SEARCH_TYPE_PHONENUM.equals(keyWordType)">
            and umu.telephone like  concat('%',#{selectDTO.keyword},'%')
          </when>
          <when test="keyWordType != null and @com.lyy.user.account.infrastructure.constant.UserMemberSysConstants@SMALL_VENUE_SEARCH_TYPE_USERMEMBERNAME.equals(keyWordType)">
            and umu.name like concat('%',#{selectDTO.keyword},'%')
          </when>
        </choose>
      </if>
      <if test="merchantUserIdList != null and merchantUserIdList.size() > 0">
        and umu.id in
        <foreach collection="merchantUserIdList" item="merchantUserId" open="(" close=")" separator=",">
          ${merchantUserId}
        </foreach>
      </if>
      <if test="groupTagIds != null and groupTagIds.length > 0">
        and umut.user_tags &amp;&amp; array[#{groupTagIds}]
      </if>
      <if test="equipmentTypeTagIds != null and equipmentTypeTagIds.length > 0">
        and umut.user_tags &amp;&amp; array[#{equipmentTypeTagIds}]
      </if>
      <if test="otherTagIds != null and otherTagIds.length > 0">
        and umut.user_tags &amp;&amp; array[#{otherTagIds}]
      </if>
      <if test="sexTagIds != null and sexTagIds.length > 0">
        and umut.user_tags &amp;&amp; array[#{sexTagIds}]
      </if>
      <!--      特定过滤条件-->
      <include refid="specificFilterInfoDTOCondition"/>
      <!--        商家自定义权益-->
<!--      <include refid="merchantBenefitClassifyDTOCondition"/>-->
    </where>
  </select>

  <select id="getByQuery" resultType="com.lyy.user.account.infrastructure.user.dto.MerchantUserDTO">
        select umu.* from um_merchant_user umu
        <where>
          <if test="dto.merchantId != null ">
            and umu.merchant_id = #{dto.merchantId}
          </if>
          <if test="dto.name != null and dto.name !='' ">
            and umu.name like concat('%',#{dto.name},'%')
          </if>
          <if test="dto.telephone != null and dto.telephone !='' ">
            and umu.telephone like concat('%',#{dto.telephone},'%')
          </if>
          <if test="dto.userId != null ">
            and umu.user_id = #{dto.userId}
          </if>
        </where>
        order by create_time desc limit 1
    </select>
  <select id="searchByPhone"
          resultType="com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserInfoAndAccountDTO">
    select
      umu.id "merchantUserId",
      umu.name "name",
      umu.user_id "userId",
      umu.head_img "headImg",
      umu.create_time "createTime",
      umu.user_type "userType" ,
      umu.birthday,
      umu.telephone,
      umu.gender "gender",
      umu.province_city "provinceCity" ,
      umu.address,
      umu.province_id "provinceId",
      umu.city_id "cityId",
      umu.region_id "regionId",
      ua.card_no "cardNo",
      ua.id "accountId",
      ua.parent_account_id "parentAccountId",
      ua.down_time "downTime",
      ua.store_id "storeId",
      ua.status "status",
      uab.balance "balance",
      ua.default_flag "defaultFlag",
      ua.deposit "deposit",
      uab.merchant_benefit_classify_id "merchantBenefitClassifyId",
      uab.use_rule_id "useRuleId",
      uab.store_id "benefitStoreId",
      uab.up_time "benefitUpTime",
      uab.down_time "benefitDownTime"
    from
        um_merchant_user umu
    left join um_account ua on
        umu.user_id = ua.user_id
        and ua.classify=99
        and ua.merchant_id = #{merchantId}
    left join um_account_benefit uab on
        ua.id = uab.account_id and uab.merchant_id = #{merchantId}
    join um_platform_user_phone upup on upup.merchant_id = umu.merchant_id and upup.user_id = umu.user_id
        and upup.is_active = true and upup.merchant_id = #{merchantId} and system_flag = 1
    where
        umu.merchant_id = #{merchantId}
        and umu.telephone = #{phone}
        and upup.telephone = #{phone}
  </select>

  <select id="findSmallVenueMobileUserIds" resultType="java.lang.Long">
    select umu.user_id
    from um_merchant_user umu
    <if test="hasTag == true">
      left join um_merchant_user_tag umut on
      umu.id = umut.business_user_id
      and umut.tag_type = 1 and umut.merchant_id =#{selectDTO.merchantId}
    </if>
    <if test="selectDTO.keyword != null and selectDTO.keyword != '' ">
      left join um_account ua on umu.id = ua.merchant_user_id and ua.merchant_id = #{selectDTO.merchantId}
    </if>
    left join um_statistics us on
    umu.id = us.merchant_user_id and us.merchant_id =#{selectDTO.merchantId}
    left join um_member um on
    umu.id=um.merchant_user_id and um.merchant_id =#{selectDTO.merchantId}
    <if test="hasMerchantBenefitClassifyId == true">
      left join um_small_venue_stored_statistics  usvss on umu.id = usvss.merchant_user_id  AND usvss.merchant_id = #{selectDTO.merchantId}
    </if>
    <where>
      and umu.merchant_id =#{selectDTO.merchantId} and umu.is_active=true
      <if test="selectDTO.keyword != null and selectDTO.keyword !='' ">
        and (
        <choose>
          <when test="numericKeyword !=null and numericKeyword != '' ">
            umu.telephone like  concat('%',#{numericKeyword},'%')
            <!--    or umu.user_id = #{numericKeyword}::numeric -->
            or umu.name like concat('%',#{numericKeyword},'%')
            or umu.id :: VARCHAR like concat('%',#{numericKeyword},'%')
          </when>
          <when test="strKeyWord != null and strKeyWord != ''">
            umu.name like concat('%',#{strKeyWord},'%')
          </when>
        </choose>
        or ua.card_no like concat('%',#{selectDTO.keyword},'%')
        )
      </if>
      <if test="groupTagIds != null and groupTagIds.length > 0">
        and umut.user_tags &amp;&amp; array[#{groupTagIds}]
      </if>
      <if test="equipmentTypeTagIds != null and equipmentTypeTagIds.length > 0">
        and umut.user_tags &amp;&amp; array[#{equipmentTypeTagIds}]
      </if>
      <if test="otherTagIds != null and otherTagIds.length > 0">
        and umut.user_tags &amp;&amp; array[#{otherTagIds}]
      </if>
      <if test="sexTagIds != null and sexTagIds.length > 0">
        and umut.user_tags &amp;&amp; array[#{sexTagIds}]
      </if>

      <!--      特定过滤条件-->
      <include refid="specificFilterInfoDTOCondition"/>
      <!--        商家自定义权益-->
      <include refid="merchantBenefitClassifyDTOCondition"/>
    </where>
  </select>
  <select id="countByMerchantId" resultType="java.lang.Long">
    select count(1) from um_merchant_user where merchant_id = #{merchantId}
  </select>

  <update id="updateMerchantUserInfo">
        update um_merchant_user
        <set>
            <if test="updateDTO.name != null">
              name = #{updateDTO.name},
            </if>
            <if test="updateDTO.birthday != null">
                birthday = #{updateDTO.birthday},
            </if>
            <if test="updateDTO.gender != null">
                gender= #{updateDTO.gender},
            </if>
            <if test="updateDTO.cityId != null">
                city_id = #{updateDTO.cityId},
            </if>
            <if test="updateDTO.provinceId != null">
                province_id = #{updateDTO.provinceId},
            </if>
            <if test="updateDTO.regionId != null">
                region_id = #{updateDTO.regionId},
            </if>
            <if test="updateDTO.address != null">
                address = #{updateDTO.address},
            </if>
            <if test="updateDTO.provinceCity != null">
              province_city = #{updateDTO.provinceCity},
            </if>
            <if test="updateDTO.headImg != null">
                head_img= #{updateDTO.headImg},
            </if>
            <if test="updateDTO.operatorId != null">
                updatedby = #{updateDTO.operatorId},
            </if>
        </set>
        where merchant_id = #{updateDTO.merchantId}
        AND id = #{updateDTO.id}
    </update>


  <update id="updateMerchantUserTelephone">
      update um_merchant_user set telephone=#{newTelephone} where merchant_id = #{merchantId} and user_id=#{userId}
        <if test="oldTelephone != null">
          and telephone=#{oldTelephone}
        </if>
  </update>


  <update id="mobileTerminalMerchantUserCancellation">
      update um_merchant_user set telephone='',is_active=false,update_time=now(),updatedby=#{operatorId}  where merchant_id = #{merchantId} and id=#{merchantUserId}
  </update>

</mapper>