<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.statistics.repository.StatisticsMapper">
    <resultMap id="BaseResultMap" type="com.lyy.user.domain.statistics.entity.Statistics">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="merchant_user_id" jdbcType="NUMERIC" property="merchantUserId" />
        <result column="merchant_id" jdbcType="NUMERIC" property="merchantId" />
        <result column="user_id" jdbcType="NUMERIC" property="userId" />
        <result column="start_times" jdbcType="NUMERIC" property="startTimes" />
        <result column="pay_times" jdbcType="NUMERIC" property="payTimes" />
        <result column="pay_amount" jdbcType="NUMERIC" property="payAmount" />
        <result column="pay_for_service_times" jdbcType="NUMERIC" property="payForServiceTimes" />
        <result column="pay_for_service_amount" jdbcType="NUMERIC" property="payForServiceAmount" />
        <result column="coins_consumption" jdbcType="NUMERIC" property="coinsConsumption" />
        <result column="amount_consumption" jdbcType="NUMERIC" property="amountConsumption" />
        <result column="total_coins" jdbcType="NUMERIC" property="totalCoins" />
        <result column="balance_coins" jdbcType="NUMERIC" property="balanceCoins" />
        <result column="balance_amount" jdbcType="NUMERIC" property="balanceAmount" />
        <result column="recent_consumption_time" jdbcType="TIMESTAMP" property="recentConsumptionTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="total_amount" jdbcType="NUMERIC" property="totalAmount" />
    </resultMap>
    <resultMap id="MerchantUserStatisticsMap" type="com.lyy.user.account.infrastructure.statistics.dto.MerchantUserStatisticsListDTO">
        <result column="id" property="id" />
        <result column="name" property="nickName" />
        <result column="head_img" property="headImg" />
        <result column="gender" property="gender" />
        <result column="user_id" property="userId" />
        <result column="merchant_id" property="merchantId" />
        <result column="telephone" property="telephone" />
        <result column="user_type" property="userType" />
        <result column="pay_amount"  property="totalRechargeMoney" />
        <result column="pay_for_service_amount"  property="totalPayServiceMoney" />
        <result column="amount_consumption"  property="totalConsumeBalance" />
        <result column="coins_consumption"  property="totalConsumeCoin" />
        <result column="balance_amount" jdbcType="NUMERIC" property="balanceAmount" />
        <result column="balance_coins" jdbcType="NUMERIC" property="balanceCoins" />
    </resultMap>
    <update id="updateStatistics">
        update um_statistics
        <set>
            update_time = #{updateTime},
            <if test="startTimes != null and startTimes > 0">
                start_times = coalesce(start_times,0) + #{startTimes},
            </if>
            <if test="payTimes != null and payTimes > 0">
                pay_times = coalesce(pay_times,0) + #{payTimes},
            </if>
            <if test="payAmount != null">
                pay_amount = coalesce(pay_amount,0) + #{payAmount},
            </if>
            <if test="payForServiceTimes != null and payForServiceTimes > 0">
                pay_for_service_times = coalesce(pay_for_service_times,0) + #{payForServiceTimes},
            </if>
            <if test="payForServiceAmount != null">
                pay_for_service_amount = coalesce(pay_for_service_amount,0) + #{payForServiceAmount},
            </if>
            <if test="coinsConsumption != null">
                coins_consumption = coalesce(coins_consumption,0) + #{coinsConsumption},
            </if>
            <if test="amountConsumption != null">
                amount_consumption = coalesce(amount_consumption,0) + #{amountConsumption},
            </if>
            <if test="totalCoins != null and totalCoins > 0 ">
                total_coins = coalesce(total_coins,0) + #{totalCoins},
            </if>
            <if test="balanceCoins != null">
                balance_coins = coalesce(balance_coins,0) + #{balanceCoins},
            </if>
            <if test="balanceAmount != null">
                balance_amount = coalesce(balance_amount,0) + #{balanceAmount},
            </if>
            <if test="recentConsumptionTime != null">
                recent_consumption_time = #{recentConsumptionTime},
            </if>
            <if test="totalAmount != null">
                total_amount = coalesce(total_amount,0) + #{totalAmount},
            </if>
        </set>
        where merchant_id = #{merchantId}
        and merchant_user_id = #{merchantUserId}
    </update>

    <update id="updateCleanBanlace">
        update um_statistics
        <set>
            update_time = #{updateTime},
            <if test="balanceCoins != null">
                balance_coins =  #{balanceCoins},
            </if>
            <if test="balanceAmount != null">
                balance_amount = #{balanceAmount},
            </if>

        </set>
        where merchant_id = #{merchantId}
        and merchant_user_id = #{merchantUserId}
    </update>
    <select id="queryStatisticsUserList" resultMap="MerchantUserStatisticsMap">
        select umu.id,umu."name",umu.head_img,umu.gender,umu.user_id ,umu.merchant_id,umu.telephone,  umu.user_type, coalesce(usd.pay_amount,0) as pay_amount,coalesce(usd.pay_for_service_amount,0)  as pay_for_service_amount,
        coalesce(usd.amount_consumption,0) as amount_consumption ,coalesce(usd.coins_consumption,0) as coins_consumption, coalesce(usd.balance_coins,0) as balance_coins, coalesce(usd.balance_amount,0) as balance_amount
        from um_statistics usd inner join um_merchant_user umu on usd.merchant_user_id  = umu.id
        where umu.merchant_id  = #{merchantId} and usd.merchant_id =  #{merchantId}
        <if test="userId != null and userId != ''">
            and umu.user_id = #{userId}
        </if>
        <if test="telephone != null and telephone != ''">
            and umu.telephone = #{telephone}
        </if>
    </select>

    <select id="selectStatisticsByUserId"
            resultType="com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserConsumeStatisticsDTO">
        select amount_consumption "amountConsumption",recent_consumption_time "recentConsumptionTime"
        from um_statistics where user_id=#{userId} and merchant_id  = #{merchantId} limit 1
    </select>

    <select id="querySmallVenueStatisticsList" resultType="com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserConsumeStatisticsDTO">
        select merchant_user_id "merchantUserId",amount_consumption "amountConsumption",recent_consumption_time "recentConsumptionTime"
        from um_statistics
        where merchant_id  = #{merchantId}
        and merchant_user_id in
        <foreach collection="merchantUserIdList" item="merchantUserId" separator="," open="(" close=")">
            #{merchantUserId}
        </foreach>
        order by recent_consumption_time desc nulls last
    </select>

    <select id="queryPageSmallVenueStatisticsList" resultType="com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserConsumeStatisticsDTO">
        select merchant_user_id "merchantUserId",amount_consumption "amountConsumption",recent_consumption_time "recentConsumptionTime"
        from um_statistics
        where merchant_id  = #{merchantId}
        order by recent_consumption_time desc nulls last
    </select>

    <select id="countSmallVenueStatisticsList" resultType="java.lang.Long">
        select count ( distinct merchant_user_id )
        from
        um_statistics
        where merchant_id  = #{merchantId}
    </select>

    <insert id="upsertStatistics">
        INSERT INTO um_statistics as target (
            merchant_user_id,
            merchant_id,
            user_id,
            start_times,
            pay_times,
            pay_amount,
            pay_for_service_times,
            pay_for_service_amount,
            coins_consumption,
            amount_consumption,
            total_coins,
            balance_coins,
            balance_amount,
            recent_consumption_time,
            create_time,
            update_time,
            total_amount
        ) VALUES (
            #{merchantUserId},
            #{merchantId},
            #{userId},
            #{startTimes},
            #{payTimes},
            #{payAmount},
            #{payForServiceTimes},
            #{payForServiceAmount},
            #{coinsConsumption},
            #{amountConsumption},
            #{totalCoins},
            #{balanceCoins},
            #{balanceAmount},
            #{recentConsumptionTime},
            #{updateTime},
            #{updateTime},
            #{totalAmount}
        )
        ON CONFLICT (merchant_user_id, merchant_id) DO UPDATE SET
            <if test="startTimes != null and startTimes > 0">
                start_times = COALESCE(target.start_times, 0) + EXCLUDED.start_times,
            </if>
            <if test="payTimes != null and payTimes > 0">
                pay_times = COALESCE(target.pay_times, 0) + EXCLUDED.pay_times,
            </if>
            <if test="payAmount != null">
                pay_amount = COALESCE(target.pay_amount, 0) + EXCLUDED.pay_amount,
            </if>
            <if test="payForServiceTimes != null and payForServiceTimes > 0">
                pay_for_service_times = COALESCE(target.pay_for_service_times, 0) + EXCLUDED.pay_for_service_times,
            </if>
            <if test="payForServiceAmount != null">
                pay_for_service_amount = COALESCE(target.pay_for_service_amount, 0) + EXCLUDED.pay_for_service_amount,
            </if>
            <if test="coinsConsumption != null">
                coins_consumption = COALESCE(target.coins_consumption, 0) + EXCLUDED.coins_consumption,
            </if>
            <if test="amountConsumption != null">
                amount_consumption = COALESCE(target.amount_consumption, 0) + EXCLUDED.amount_consumption,
            </if>
            <if test="totalCoins != null and totalCoins > 0 ">
                total_coins = coalesce(target.total_coins,0) + EXCLUDED.total_coins,
            </if>
            <if test="balanceCoins != null">
                balance_coins = COALESCE(target.balance_coins, 0) + EXCLUDED.balance_coins,
            </if>
            <if test="balanceAmount != null">
                balance_amount = COALESCE(target.balance_amount, 0) + EXCLUDED.balance_amount,
            </if>
            <if test="recentConsumptionTime != null">
                recent_consumption_time = EXCLUDED.recent_consumption_time,
            </if>
            <if test="totalAmount != null">
                total_amount = coalesce(target.total_amount,0) + EXCLUDED.total_amount,
            </if>
            update_time = EXCLUDED.update_time
    </insert>
</mapper>