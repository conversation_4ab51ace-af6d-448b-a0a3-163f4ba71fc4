<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.statistics.repository.MerchantStatisticsMapper">
    <resultMap id="BaseResultMap" type="com.lyy.user.domain.statistics.entity.MerchantStatistics">
        <!--
          WARNING - @mbg.generated
        -->

    </resultMap>
    <update id="updateStatistics">
        update um_merchant_statistics
        <set>
            updated = #{updateTime},
            <if test="userAmount != null">
                user_amount = greatest(coalesce(user_amount,0) + #{userAmount},0),
            </if>
            <if test="totalPayConsume != null">
                total_pay_consume = greatest(coalesce(total_pay_consume,0) + #{totalPayConsume},0),
            </if>
            <if test="balanceCoins != null">
                balance_coins = greatest(coalesce(balance_coins,0) + #{balanceCoins},0),
            </if>
            <if test="balanceAmount != null">
                balance_amount = greatest(coalesce(balance_amount,0) + #{balanceAmount},0),
            </if>
        </set>
        where merchant_id = #{merchantId}
    </update>



</mapper>