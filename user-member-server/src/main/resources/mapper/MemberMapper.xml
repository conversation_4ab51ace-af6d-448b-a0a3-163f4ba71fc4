<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.member.repository.MemberMapper">

    <update id="updateMemberByIdAndMerchantId">
       UPDATE um_member  SET  member_group_id=#{member.memberGroupId},
       member_level_id=#{member.memberLevelId},
       grow_value=#{member.growValue},
       member_start_time=#{member.memberStartTime},
       create_time=#{member.createTime},
       update_time=#{member.updateTime},
       is_del=#{member.del}
       WHERE id=#{id} and merchant_id = #{merchantId}
    </update>
    <update id="updateMemberEndTimeById">
        UPDATE um_member
            set member_end_time=#{memberEndTime}
        where id=#{id} and merchant_id = #{merchantId}
    </update>


    <select id="selectByIdForUpdate" parameterType="java.lang.Long" resultType="com.lyy.user.domain.member.entity.Member">
        select * from um_member where  id = #{id} for update
    </select>

    <select id="getNotRecordOfTime" resultType="com.lyy.user.domain.member.entity.Member">
        select um.*
        from um_member um
        where  um.merchant_id = #{memberLifting.merchantId}
        and um.member_group_id = #{memberLifting.memberGroupId}
        and um.is_del = false
        and (um.member_end_time is null or um.member_end_time &gt;= current_date - 1)
        and not exists (
            select *
            from um_member_grow_record mgr
            where  um.user_id = mgr.user_id
            and mgr.merchant_id = #{memberLifting.merchantId}
            and mgr.member_lifting_id = #{memberLifting.id}
            <if test="startTime != null ">
              and mgr.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null ">
                and mgr.create_time &lt; #{endTime}
            </if>
        )
        <if test="memberStartTime != null ">
            and um.member_start_time &lt;= #{memberStartTime}
        </if>
    </select>


    <select id="findPageMemberUser" resultType="com.lyy.user.account.infrastructure.member.dto.MemberUserPageDTO">
        select um.*,
        umg.name as member_group_name,
        uml.name as member_level_name
        from um_member um
        left join um_member_group umg on umg.id = um.member_group_id and umg.merchant_id = um.merchant_id
        left join um_member_level uml on uml.id = um.member_level_id and uml.merchant_id = um.merchant_id
        where um.merchant_id = #{merchantId}
        and umg.merchant_id = #{merchantId}
        and uml.merchant_id = #{merchantId}
        and um.user_id = #{userId}
        and um.is_del = false
    </select>

    <select id="findMemberByMemberGroup" resultType="com.lyy.user.account.infrastructure.member.dto.MemberInfoDTO">
        select m.*,
        s.recent_consumption_time as consumption_money_time,
        coalesce(s.pay_for_service_amount,0)  + coalesce(s.pay_amount,0) as consumption_money_sum
        from um_member m
        left join um_statistics s on s.merchant_id = m.merchant_id and s.merchant_user_id = m.merchant_user_id
        left join um_member_level ml on ml.merchant_id = m.merchant_id and ml.id = member_level_id
        where m.merchant_id = #{merchantId}
        and s.merchant_id = #{merchantId}
        and ml.merchant_id = #{merchantId}
        and m.member_group_id = #{memberGroupId}
        and (ml.active is null or ml.active = true)
        and m.is_del = false
        and ml.grow_value &gt; 0
--         and m.member_end_time > now()
        <if test="memberLevelId != null and memberLevelId > 0">
            and  m.member_level_id = #{memberLevelId}
        </if>
        order by m.grow_value desc,m.update_time desc

    </select>
    <select id="findValidMemberUser" resultType="com.lyy.user.account.infrastructure.member.dto.MemberUserPageDTO">
        select um.*,
        umg.name as member_group_name,
        uml.name as member_level_name
        from um_member um
        left join um_member_group umg on umg.id = um.member_group_id and umg.merchant_id = um.merchant_id
        left join um_member_level uml on uml.id = um.member_level_id and uml.merchant_id = um.merchant_id and (uml.active is null or uml.active = true)
        where um.merchant_id = #{merchantId}
        and umg.merchant_id = #{merchantId}
        and uml.merchant_id = #{merchantId}
        and um.user_id = #{userId}
        and um.member_end_time &gt; #{now}
        and um.is_del = false
    </select>

    <select id="selectMemberLevelName" resultType="com.lyy.user.domain.member.dto.MemberLevelInfoDTO">
        select
         uml.id "memberLevelId",
	     uml.name "memberLevelName",
	     um.grow_value "growValue"
        from
	     um_member um
        inner join um_member_level uml on
	    um.member_level_id = uml.id
        where
	      um.user_id =#{userId}
	    and um.merchant_id =#{merchantId}
	    limit 1
    </select>

    <select id="batchSelectMemberLevelInfo" resultType="com.lyy.user.domain.member.dto.MemberLevelInfoDTO">
        select
        uml.id "memberLevelId",
        uml.name "memberLevelName",
        um.merchant_user_id "merchantUserId"
        from
        um_member um
        inner join um_member_level uml on
        um.member_level_id = uml.id
        where
        um.merchant_id =#{merchantId}
        and uml.merchant_id =#{merchantId}
        and merchant_user_id in
        <foreach collection="merchantUserIdSet" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


</mapper>
