<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.user.repository.ThirdPlatformAccountRecordMapper">
  <resultMap id="BaseResultMap" type="com.lyy.user.domain.user.entity.ThirdPlatformAccountRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="NUMERIC" property="id" />
    <result column="merchant_id" jdbcType="NUMERIC" property="merchantId" />
    <result column="user_id" jdbcType="NUMERIC" property="userId" />
    <result column="equipment_id" jdbcType="NUMERIC" property="equipmentId" />
    <result column="equipment_value" jdbcType="VARCHAR" property="equipmentValue" />
    <result column="out_trade_no" jdbcType="VARCHAR" property="outTradeNo" />
    <result column="card_no" jdbcType="VARCHAR" property="cardNo" />
    <result column="external_user_id" jdbcType="VARCHAR" property="externalUserId" />
    <result column="external_system" jdbcType="NUMERIC" property="externalSystem" />
    <result column="event_type" jdbcType="NUMERIC" property="eventType" />
    <result column="amount" jdbcType="NUMERIC" property="amount" />
    <result column="balance" jdbcType="NUMERIC" property="balance" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="NUMERIC" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="NUMERIC" property="updateBy" />
  </resultMap>
</mapper>