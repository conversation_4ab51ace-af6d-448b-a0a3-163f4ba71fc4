<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lyy.user.domain.benefit.repository.BenefitScopeMapper">
  <resultMap id="BaseResultMap" type="com.lyy.user.domain.benefit.entity.BenefitScope">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="NUMERIC" property="id" />
    <result column="merchant_id" jdbcType="NUMERIC" property="merchantId" />
    <result column="applicable" jdbcType="NUMERIC" property="applicable" />
    <result column="associated_id" jdbcType="NUMERIC" property="associatedId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
    <insert id="insertBatch">
        insert into  um_benefit_scope(id,benefit_id,applicable,associated_id,create_time,update_time,merchant_id)
        values
        <foreach collection="list" item="e" separator=",">
            (#{e.id},#{e.benefitId},#{e.applicable},#{e.associatedId},#{e.createTime},#{e.updateTime},#{e.merchantId})
        </foreach>
    </insert>
    <delete id="deleteByBenefitIdsAndMerchantId">
        delete from um_benefit_scope where merchant_id = #{merchantId}
        <if test="benefitIds != null and benefitIds.size > 0">
            and benefit_id in
            <foreach collection="benefitIds" item="benefitId" open="(" close=")" separator=",">
                #{benefitId}
            </foreach>
        </if>
        and applicable = 2
    </delete>

    <select id="findAllScopeId" resultType="java.lang.Long">
      select distinct associated_id
        from um_benefit_scope
        <where>
            <if test="merchantId != null">
                and merchant_id = #{merchantId}
            </if>
            <if test="applicable != null">
                and applicable = #{applicable}
            </if>
            <if test="benefitIds != null and benefitIds.size > 0">
                and benefit_id in
                <foreach collection="benefitIds" item="benefitId" open="(" close=")" separator=",">
                    #{benefitId}
                </foreach>
            </if>
        </where>
    </select>


    <select id="findBenefitIdsByAssociatedIds" resultType="java.lang.Long">
        select
            benefit_id
        from
            um_benefit_scope
        where
            applicable =#{selectDTO.applicable}
            and merchant_id = #{selectDTO.merchantId}
        group by
            benefit_id
          <if test="selectDTO.associatedIdList != null and selectDTO.associatedIdList.size > 0">
            having
                sum(case when associated_id not in
                <foreach collection="selectDTO.associatedIdList" item="associatedId" open="(" close=")" separator=",">
                    #{associatedId}
                </foreach>
                then 1 else 0 end) = 0
                and sum(case when associated_id in
                <foreach collection="selectDTO.associatedIdList" item="associatedId" open="(" close=")" separator=",">
                    #{associatedId}
                </foreach>
                then 1 else 0 end) = #{selectDTO.associatedIdSize}
          </if>
    </select>

</mapper>