apollo:
  meta: ${APOLLO_META:http://service-apollo-config-server-dev.sre.svc.cluster.local:8080}
  bootstrap:
    enabled: true
    namespaces: application.yml,redis.yml,sharding-datasource.properties,eureka.yml,sentinel-app-rules.properties,sentinel-dashboard-middle.properties,rocket-business.properties,rocket-payment.properties,dynamictp.yml,dynamictp-common.properties,cache-redis.properties,middle-stage.properties,ds-selectdb-user-member.properties
  eagerLoad:
    enabled: true

app:
  id: user-member-server

env: ${APOLLO_ENV:DEV}

logging:
  config: classpath:logback-${APOLLO_ENV:DEV}.xml
