package com.lyy.user.service.member;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupPageRequestDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberRangeAssociatedDTO;
import com.lyy.user.application.member.IMemberGroupService;
import com.lyy.user.domain.member.entity.MemberGroup;
import com.lyy.user.domain.member.repository.MemberRangeMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * 会员组
 * <AUTHOR>
 * @className: MemberGroupControllerTest
 * @date 2021/3/31
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
@Slf4j
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class MemberGroupServiceTest {

    @Autowired
    private MemberRangeMapper memberRangeMapper;

    @Autowired
    private IMemberGroupService memberGroupService;

    private Gson gson;

    private Long memberGroupId;
    private Long merchantId;

    @Before
    public void before(){
        gson = new GsonBuilder()
                //重写map的反序列化
//                .registerTypeAdapter(new TypeToken<Map<String, Object>>() {}.getType(), new MapTypeAdapter())
                //data格式化
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                .create();
        memberGroupId = 589781150612848640L;
        merchantId = 1001L;

    }

    @Test
    @Ignore
    public void test1SaveOrUpdate() {
        MemberGroupSaveDTO memberGroupSaveDTO = new MemberGroupSaveDTO();
//        memberGroupSaveDTO.setId(memberGroupId);
        memberGroupSaveDTO.setName("test" + merchantId +"-1");
        memberGroupSaveDTO.setDescription("des");
        memberGroupSaveDTO.setStartDate(new Date());
        memberGroupSaveDTO.setEndDate(DateUtils.addDays(new Date(),2));
        memberGroupSaveDTO.setActive(true);
        memberGroupSaveDTO.setLiftingStrategy((short)1);
        memberGroupSaveDTO.setMerchantId(merchantId);
        memberGroupSaveDTO.setOpenMethod((short) 1);
        memberGroupSaveDTO.setRuleStrategy((short) 1);
        memberGroupSaveDTO.setMemberEffectiveTime(-2);
        memberGroupSaveDTO.setMemberStartTime(new Date());
        memberGroupSaveDTO.setMemberEndTime(DateUtils.addYears(new Date(),1));
        memberGroupSaveDTO.setCreateTime(new Date());
        //范围
        List<MemberRangeAssociatedDTO> memberRangeAssociatedList = new ArrayList<>();
        MemberRangeAssociatedDTO m1 = new MemberRangeAssociatedDTO();
        m1.setApplicable((short) 1);
        m1.setAssociatedIdList(Arrays.asList(101L,102L,103L));
        memberRangeAssociatedList.add(m1);
        MemberRangeAssociatedDTO m2 = new MemberRangeAssociatedDTO();
        m2.setApplicable((short) 2);
        m2.setAssociatedIdList(Arrays.asList(201L,202L,203L));
        memberRangeAssociatedList.add(m2);
        MemberRangeAssociatedDTO m3 = new MemberRangeAssociatedDTO();
        m3.setApplicable((short) 3);
        m3.setAssociatedIdList(Arrays.asList(101L,202L,303L));
        memberRangeAssociatedList.add(m3);
        memberGroupSaveDTO.setMemberRangeAssociatedList(memberRangeAssociatedList);
        log.info("saveOrUpdate param-->{}",gson.toJson(memberGroupSaveDTO));
        memberGroupId = memberGroupService.saveOrUpdate(memberGroupSaveDTO);

        //更新范围
        m1.setAssociatedIdList(Arrays.asList(101L,102L));
        m2.setAssociatedIdList(Arrays.asList(201L,202L,205L));
        memberGroupSaveDTO.setId(memberGroupId);
        memberGroupSaveDTO.setName("test" + merchantId +":" + memberGroupId);
        log.info("saveOrUpdate param-->{}",gson.toJson(memberGroupSaveDTO));
        memberGroupId = memberGroupService.saveOrUpdate(memberGroupSaveDTO);

    }

    @Test
    public void test3GetById(){
        MemberGroup memberGroup = memberGroupService.getById(merchantId,memberGroupId);
        log.info("testGetById-->{}",memberGroup);
    }

    @Test
    public void test4GetInfoById(){
        MemberGroupSaveDTO memberGroupSaveDTO = memberGroupService.getInfoById(merchantId,memberGroupId);
        log.info("getInfoById-->{}",memberGroupSaveDTO);
    }

    @Test
    public void test5RemoveByMemberGroup(){
        memberGroupService.removeByMemberGroup(merchantId,memberGroupId);
    }
    @Test
    public void test6CountMember(){
       Integer count = memberGroupService.countMemberByGroup(merchantId, memberGroupId);
       log.info("countMemberByGroup->>{}",count);
        Map<Long,Integer> map = memberGroupService.countLevelMemberByGroup(merchantId, memberGroupId);
        log.info("countLevelMemberByGroup->>{}",map);
    }
    @Test
    @Ignore
    public void testDeleteAndAddAssociated(){
        Long memberGroupId =10000L;
        List<MemberRangeAssociatedDTO> memberRangeAssociatedList = new ArrayList<>();
        MemberRangeAssociatedDTO m1 = new MemberRangeAssociatedDTO();
        m1.setApplicable((short) 1);
        m1.setAssociatedIdList(Arrays.asList(101L,102L,103L));
        memberRangeAssociatedList.add(m1);
//        MemberRangeAssociatedDTO m2 = new MemberRangeAssociatedDTO();
//        m2.setApplicable("2");
//        m2.setAssociatedIdList(Arrays.asList(201L,202L,203L));
//        memberRangeAssociatedList.add(m2);
//        MemberRangeAssociatedDTO m3 = new MemberRangeAssociatedDTO();
//        m3.setApplicable("1");
//        m3.setAssociatedIdList(Arrays.asList(101L,202L,303L));
//        memberRangeAssociatedList.add(m3);

//        int d1= memberRangeMapper.deleteNotInAssociated(memberGroupId,memberRangeAssociatedList);
        int d1 = 0;
        int d2=memberRangeMapper.addAssociated(memberGroupId,memberRangeAssociatedList);
        log.info("第一次，memberGroupId：{},memberRangeAssociatedList:{} 共删除{}条数据，共增加{}条数据",memberGroupId,memberRangeAssociatedList,d1,d2);

        m1.setAssociatedIdList(Arrays.asList(101L,102L));
//        m2.setAssociatedIdList(Arrays.asList(201L,202L,205L));
        int d3= memberRangeMapper.deleteNotInAssociated(merchantId, memberGroupId,memberRangeAssociatedList);
        int d4=memberRangeMapper.addAssociated(memberGroupId,memberRangeAssociatedList);
        log.info("第2次，memberGroupId：{},memberRangeAssociatedList:{} 共删除{}条数据，共增加{}条数据",memberGroupId,memberRangeAssociatedList,d3,d4);

    }
}
