package com.lyy.user.service.member;

import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupRangCheckDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberTouchRuleDTO;
import com.lyy.user.application.member.IMemberLevelService;
import com.lyy.user.application.member.IMemberLiftingRuleRecordService;
import com.lyy.user.application.member.IMemberService;
import com.lyy.user.domain.member.entity.Member;
import com.lyy.user.domain.member.entity.MemberLevel;
import com.lyy.user.domain.member.entity.MemberLifting;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 会员服务
 * <AUTHOR>
 * @className: MemberServiceTest
 * @date 2021/4/6
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
@Slf4j
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class MemberServiceTest {

    @Autowired
    private IMemberService memberService;
    @Autowired
    private IMemberLevelService memberLevelService;

    @Autowired
    private IMemberLiftingRuleRecordService memberLiftingRuleRecordService;
    private Long userId;
    private Long memberGroupId;
    private Long merchantId;

    @Before
    public void before(){
        memberGroupId = -1L;
        userId = 21751792L;
        merchantId= 1444788L;
    }

    @Test
    public void test1GetMemberOrInit(){
        Member member = memberService.getMemberOrInit(merchantId,userId,memberGroupId);
        log.debug("test1GetMemberOrInit--->{}",member);
    }

    @Test
    public void test2UpdateMemberOfRule(){
//        MemberTouchRuleDTO memberTouchRuleDTO = new MemberTouchRuleDTO();
//        memberTouchRuleDTO.setUserId(userId);
//        memberTouchRuleDTO.setCategory((short)1);
//        memberTouchRuleDTO.setOtherInfo("测试数据1");
//        MemberGroupRangCheckDTO memberGroupRangCheckDTO = new MemberGroupRangCheckDTO();
//        memberTouchRuleDTO.setMemberGroupRangCheckDTO(memberGroupRangCheckDTO);
//        memberGroupRangCheckDTO.setMerchantId(merchantId);
//        memberGroupRangCheckDTO.setEquipmentTypeId(103L);
//        int i = memberService.updateMemberOfRule(memberTouchRuleDTO);
//        log.debug("test2UpdateMemberOfRule1--->{}",i);
//        MemberTouchRuleDTO memberTouchRuleDTO2 = new MemberTouchRuleDTO();
//        memberTouchRuleDTO2.setUserId(userId);
//        memberTouchRuleDTO2.setCategory((short)2);
//        memberTouchRuleDTO2.setOtherInfo("测试数据2");
//        MemberGroupRangCheckDTO memberGroupRangCheckDTO2 = new MemberGroupRangCheckDTO();
//        memberTouchRuleDTO2.setMemberGroupRangCheckDTO(memberGroupRangCheckDTO2);
//        memberGroupRangCheckDTO2.setMerchantId(merchantId);
//        memberGroupRangCheckDTO2.setEquipmentTypeId(103L);
//        int i2 = memberService.updateMemberOfRule(memberTouchRuleDTO2);
//        log.debug("test2UpdateMemberOfRule2--->{}",i2);

        MemberTouchRuleDTO memberTouchRuleDTO3 = new MemberTouchRuleDTO();
        memberTouchRuleDTO3.setUserId(userId);
        memberTouchRuleDTO3.setCategory((short)3);
        memberTouchRuleDTO3.setOtherInfo("测试数据3");
        memberTouchRuleDTO3.setRangeValue(new BigDecimal(4.5));
        memberTouchRuleDTO3.setMerchantId(merchantId);
        MemberGroupRangCheckDTO memberGroupRangCheckDTO3 = new MemberGroupRangCheckDTO();
        memberTouchRuleDTO3.setMemberGroupRangCheckDTO(memberGroupRangCheckDTO3);
        //不限制设备类型
        //memberGroupRangCheckDTO3.setEquipmentTypeId(103L);
        int i3 = memberService.updateMemberOfRule(memberTouchRuleDTO3);
        log.debug("test2UpdateMemberOfRule3--->{}",i3);
    }

    @Test
    public void test3UpdateGrowValueOfLifting(){
        int i = memberService.updateGrowValueOfLifting(merchantId, userId);
        log.debug("test3UpdateGrowValueOfLifting--->{}",i);
//        memberService.updateGrowValueOfLifting(userId);
//        memberService.updateGrowValueOfLifting(userId);
//        memberService.updateGrowValueOfLifting(userId);
    }

    @Test
    @Ignore
    public void test4UpdateGrowValue(){
        MemberLifting memberLifting = new MemberLifting();
        memberLifting.setGrowValue(1000L);
        memberLifting.setMemberGroupId(memberGroupId);
        memberLifting.setLifting((short)1);
        Member member = memberService.getMember(merchantId,userId,memberGroupId);
        List<MemberLevel> memberLevelList = memberService.updateGrowValueAndLevel(member,memberLifting, "test", memberLifting.getGrowValue(),"");
        log.info("等级变化-->{}",memberLevelList);
    }

    @Test
    public void countUserRecordByRule(){
        memberLiftingRuleRecordService.countUserRecordByRule(1001L, Arrays.asList(1L,2L,3L),Arrays.asList(10000L),(short)2,true);
    }

    @Test
    public void testInitSmallVenueUserMemberLevel() throws Exception{
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        for (int i = 0; i < 10; i++) {
            CompletableFuture.runAsync(() -> {
                Boolean flag = memberService.initSmallVenueUserMemberLevel(1444907L, 21779840L);
                log.debug("{},{}", Thread.currentThread().getName(), flag);
            }, executorService);
        }
        Thread.sleep(60000);
    }
}
