package com.lyy.user.service.member;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.constant.MemberLiftingConditionEnum;
import com.lyy.user.account.infrastructure.member.dto.MemberLiftingRuleDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberLiftingSaveDTO;
import com.lyy.user.application.member.IMemberLiftingService;
import com.lyy.user.infrastructure.constants.MemberLiftingStrategyEnum;
import com.lyy.user.interfaces.schedule.MemberLiftingJudgeFalseHandler;
import com.lyy.user.domain.member.entity.MemberLifting;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 会员升级策略
 * <AUTHOR>
 * @className: MemberLiftingServiceTest
 * @date 2021/4/2
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
@Slf4j
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class MemberLiftingServiceTest {
    @Autowired
    private IMemberLiftingService memberLiftingService;
    @Autowired
    private MemberLiftingJudgeFalseHandler memberLiftingJudgeFalseHandler;


    private Long memberGroupId;
    private Long memberLiftingId;
    private Long merchantId;

    @Before
    public void before(){
//        gson = new GsonBuilder()
//                //重写map的反序列化
////                .registerTypeAdapter(new TypeToken<Map<String, Object>>() {}.getType(), new MapTypeAdapter())
//                //data格式化
//                .setDateFormat("yyyy-MM-dd HH:mm:ss")
//                .create();
        memberGroupId = 10010L;
        memberLiftingId = 10030L;
        merchantId = 1001L;

    }

    /**
     * 保存或更新升级策略信息
     * @param
     * @return
     */

    @Test
    public void test1SaveOrUpdate(){
        MemberLiftingSaveDTO memberLiftingSaveDTO = new MemberLiftingSaveDTO();
        memberLiftingSaveDTO.setMemberGroupId(memberGroupId);
//        memberLiftingSaveDTO.setId(memberLiftingId);
        memberLiftingSaveDTO.setMerchantId(merchantId);
        memberLiftingSaveDTO.setLifting(MemberLiftingStrategyEnum.LIFTING_STRATEGY_UPGRADE.getValue());
        memberLiftingSaveDTO.setGrowValue(100L);
        memberLiftingSaveDTO.setEffectiveNumber((short)-1);
        memberLiftingSaveDTO.setCondition(MemberLiftingConditionEnum.OR_ONE.getValue());

        //规则信息
        List<MemberLiftingRuleDTO> ruleList = new ArrayList<>();
        memberLiftingSaveDTO.setRuleList(ruleList);
        MemberLiftingRuleDTO rule1 = new MemberLiftingRuleDTO();
        ruleList.add(rule1);
        rule1.setRangeDate((short) 30);
        rule1.setCategory((short) 2);
        rule1.setRangeValue(new BigDecimal(2));
        rule1.setJudgeCondition(true);
        MemberLiftingRuleDTO rule2 = new MemberLiftingRuleDTO();
        ruleList.add(rule2);
        rule2.setRangeDate((short) 60);
        rule2.setCategory((short) 2);
        rule2.setRangeValue(new BigDecimal(3));
        rule2.setJudgeCondition(true);
        MemberLiftingRuleDTO rule3 = new MemberLiftingRuleDTO();
        ruleList.add(rule3);
        rule3.setRangeDate((short) 60);
        rule3.setCategory((short) 3);
        rule3.setRangeValue(new BigDecimal(1));
        rule3.setJudgeCondition(true);
        long result = memberLiftingService.saveOrUpdate(memberLiftingSaveDTO);

        log.info("创建结果为 {}",result);
        memberLiftingId = result;

    }

    /**
     * 根据会员组获取对应的会员策略
     * @return
     */
    @Test
    public void test2FindByMemberGroup(){
        List<MemberLifting> list =  memberLiftingService.findByMemberGroup(merchantId, memberGroupId);
        log.info("testFindByMemberGroup-->{}",list);

    }

    /**
     * 根据id获取详情信息
     * @return
     */
    @Test
    public void test3GetInfoById(){
        memberLiftingId = 589820113398530048L;
        MemberLiftingSaveDTO memberLiftingSaveDTO = memberLiftingService.getInfoById(merchantId, memberLiftingId);
        log.info("testGetInfoById -->{}",memberLiftingSaveDTO);
    }

    /**
     * 根据id获取详情信息
     * @return
     */
    @Test
    public void test4RemoveByLiftingIds(){
        memberLiftingId = 589820113398530048L;
        int del= memberLiftingService.removeByLiftingIds(merchantId, Arrays.asList(memberLiftingId));
        log.info("testRemoveByLiftingIds -->{}",del);
    }


    @Test
    public void test5JudgeFalseRunTask(){
        memberLiftingJudgeFalseHandler.runTask(1L);
    }

    @Test
    public void findLiftingDemoting() {
        memberLiftingService.findLiftingDemoting(new Page() ,22L);
    }

}
