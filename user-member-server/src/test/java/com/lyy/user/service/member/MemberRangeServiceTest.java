package com.lyy.user.service.member;

import com.lyy.user.Application;
import com.lyy.user.application.member.IMemberRangeService;
import lombok.extern.slf4j.Slf4j;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @ClassName: MemberRangeServiceTest
 * @description: TODO
 * @author: pengkun
 * @date: 2022/09/06
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
@Slf4j
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class MemberRangeServiceTest {

    @Autowired
    private IMemberRangeService memberRangeService;

    @Test
    public void getRangeAssociatedList() {
        memberRangeService.getRangeAssociatedList(1000319L,963021337805455360L);
    }

}
