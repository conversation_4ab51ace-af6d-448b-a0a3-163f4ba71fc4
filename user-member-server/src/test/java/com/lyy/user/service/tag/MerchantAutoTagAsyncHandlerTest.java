package com.lyy.user.service.tag;

import com.lyy.user.Application;
import com.lyy.user.domain.user.dto.TagUserParamDTO;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.domain.user.service.MerchantAutoTagAsyncHandler;
import java.util.Date;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

/**
 * @description:
 * @author: qgw
 * @date on 2021-06-25.
 * @Version: 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class MerchantAutoTagAsyncHandlerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private MerchantAutoTagAsyncHandler autoTagAsyncHandler;

    @Test
    public void taggingMerchantUser() {


        MerchantUser user = new MerchantUser();
        user.setId(Long.valueOf("1412598389501071362"));
        user.setUserId(Long.valueOf("21755880"));
        user.setMerchantId(Long.valueOf("1000319"));
        //user.setTelephone();
        //user.setDescription();
        //user.setName();
        user.setUserType("U");
        user.setGender("男 ");
        //user.setHeadImg();
        //user.setBirthday();
        //user.setUserType();
        //user.setCityId();
        //user.setProvinceCity();
        //user.setActive();
        //user.setCreateTime();
        user.setUpdateTime(new Date());
        //user.setCreatedby();
        //user.setUpdatedby();
        //user.setProvinceId();
        //user.setRegionId();

        //支付方式+性别 标签
        TagUserParamDTO param = new TagUserParamDTO();
        param.setTradeType(null);
        param.setUserType(user.getUserType());
        param.setUserId(user.getUserId());
        param.setMerchantUserId(user.getId());
        param.setGender(user.getGender());
        param.setMerchantId(user.getMerchantId());
        autoTagAsyncHandler.autoCreateTag(param);
    }
}