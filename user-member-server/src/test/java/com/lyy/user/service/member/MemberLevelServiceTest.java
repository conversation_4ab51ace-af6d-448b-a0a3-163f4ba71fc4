package com.lyy.user.service.member;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapter;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.member.dto.MemberLevelSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberRuleDTO;
import com.lyy.user.application.member.IMemberLevelService;
import com.lyy.user.domain.member.entity.MemberLevel;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 会员等级
 * <AUTHOR>
 * @className: MemberLevelServiceTest
 * @date 2021/4/2
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@Slf4j
public class MemberLevelServiceTest {
    @Autowired
    private IMemberLevelService memberLevelService;
//    @Autowired
//    private IMemberLevelBenefitMappingService memberLevelBenefitMappingService;

    private Long memberGroupId;
    private Long memberLevelId;
    private Long merchantId;
    private Gson gson;
    @Before
    public void before(){
        memberGroupId = 10010L;
        memberLevelId = 10010L;
        merchantId = 1001L;
        gson = new GsonBuilder()
                //重写localDateTime的反序列化
                .registerTypeAdapter(new TypeToken<LocalDateTime>() {
                }.getType(), new TypeAdapter<LocalDateTime>() {
                    DateTimeFormatter sdf= DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");//设置时间日期格式
                    @Override
                    public void write(JsonWriter out, LocalDateTime value) throws IOException {
                        if(value !=null){
                            out.value(value.format(sdf));
                        }else{
//                            out.value("");
                            out.nullValue();
                        }

                    }
                    @Override
                    public LocalDateTime read(JsonReader in) throws IOException {
                       return LocalDateTime.parse(in.nextString(),sdf);
                    }
                })
                //data格式化
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                .create();
    }

    @Test
    @Ignore
    public void test1SaveOrUpdate() {
        MemberLevelSaveDTO memberLevelSaveDTO = new MemberLevelSaveDTO();
        memberLevelSaveDTO.setCreateBy(100L);
        memberLevelSaveDTO.setUpdateBy(100L);
        memberLevelSaveDTO.setGrowValue(100L);
        memberLevelSaveDTO.setName("测试等级");
        memberLevelSaveDTO.setMemberGroupId(memberGroupId);
        memberLevelSaveDTO.setMerchantId(merchantId);
//        memberLevelSaveDTO.setCreateTime(LocalDateTime.now());
//        memberLevelSaveDTO.setUpdateTime(LocalDateTime.now());

        List<MemberRuleDTO> memberRuleList = new ArrayList<>();
        memberLevelSaveDTO.setMemberRuleList(memberRuleList);
        MemberRuleDTO r1 = new MemberRuleDTO();
        memberRuleList.add(r1);
        r1.setUpTime("2021-04-01");
        r1.setDownTime("2022-05-01");
        r1.setExpiryDateCategory((short) 1);
        r1.setBenefitValue(new BigDecimal(100));
        r1.setBenefitClassify((short)2);
        r1.setBenefitUnit("经验值");

        memberLevelId = memberLevelService.saveOrUpdate(memberLevelSaveDTO);
        log.info("创建结果为 {}",memberLevelId);

    }
    @Test
    public void test2FindByMemberGroup() {

        List<MemberLevel> list =  memberLevelService.findByMemberGroup(merchantId, memberGroupId);
        log.info("test2FindByMemberGroup-->{}",list);
    }
    @Test
    public void test3GetInfoById() {
        memberLevelId = 589812815812886528L;
        MemberLevelSaveDTO memberLevelSaveDTO = memberLevelService.getInfoById(merchantId, memberLevelId);
        log.info("testGetInfoById -->{}",gson.toJson(memberLevelSaveDTO));
    }

    @Test
    @Ignore
    public void test4RemoveByMemberLevel() {
        int del= memberLevelService.removeByMemberLevel(merchantId, memberLevelId);
        log.info("test4RemoveByMemberLevel -->{}",del);
    }

//    @Test
//    public void test5UpdateLevelBenefit() {
//        memberLevelId = 10040L;
//        List<Long> list1 = memberLevelBenefitMappingService.findBenefitOfLevelUpdate(memberLevelId);
//        log.info("findBenefitOfLevelUpdate 1-->{}",list1);
//        memberLevelBenefitMappingService.updateLevelBenefit(memberLevelId, Arrays.asList(1L,2L,8L,10L,14L,17L));
//        List<Long> list2 = memberLevelBenefitMappingService.findBenefitOfLevelUpdate(memberLevelId);
//        log.info("findBenefitOfLevelUpdate 2-->{}",list2);
//    }
}
