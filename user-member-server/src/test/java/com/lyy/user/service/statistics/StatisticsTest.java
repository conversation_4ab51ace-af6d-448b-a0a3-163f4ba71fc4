package com.lyy.user.service.statistics;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsUpdateDTO;
import com.lyy.user.application.statistics.StatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/20 17:32
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class StatisticsTest {

    @Resource
    private StatisticsService statisticsService;

    @Test
    public void testInsert() {
        Date now = new Date();
        UserStatisticsUpdateDTO param = new UserStatisticsUpdateDTO();
        param.setMerchantUserId(0L);
        param.setMerchantId(0L);
        param.setUserId(0L);
        param.setStartTimes(0);
        param.setPayTimes(0);
        param.setPayAmount(new BigDecimal("0"));
        param.setPayForServiceTimes(0);
        param.setPayForServiceAmount(new BigDecimal("0"));
        param.setCoinsConsumption(new BigDecimal("0"));
        param.setAmountConsumption(new BigDecimal("0"));
        param.setTotalCoins(new BigDecimal("0"));
        param.setBalanceCoins(new BigDecimal("0"));
        param.setBalanceAmount(new BigDecimal("0"));
        param.setRecentConsumptionTime(now);
        param.setTotalAmount(new BigDecimal("0"));
        param.setUpdateTime(now);

        param.setMerchantUserId(888L);
        param.setMerchantId(888L);
        param.setUserId(888L);
        param.setBalanceCoins(new BigDecimal("3"));
        param.setBalanceAmount(new BigDecimal("3"));
        param.setAmountConsumption(BigDecimal.TEN);
        param.setRecentConsumptionTime(now);
        param.setUpdateTime(now);

        statisticsService.updateStatistics(param);
    }
}
