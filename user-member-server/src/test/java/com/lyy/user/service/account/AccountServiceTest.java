package com.lyy.user.service.account;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import cn.lyy.base.utils.RedisClient;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitCountDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitCreateDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountConditionDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountCreateDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.ConsumeDTO;
import com.lyy.user.account.infrastructure.account.dto.ConsumeDetailDTO;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyGroupEnum;
import com.lyy.user.application.account.AccountService;
import com.lyy.user.domain.account.dto.AccountInitDTO;
import com.lyy.user.domain.account.dto.AccountInitResultDTO;
import com.lyy.user.infrastructure.constants.RedisKey;
import com.lyy.user.infrastructure.repository.account.SmallVenueAccountRepository;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;


/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/1 14:06
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class AccountServiceTest {

    @Resource
    private AccountService accountService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private MockMvc mockMvc;

    @Resource
    private SmallVenueAccountRepository accountRepository;

    /**
     * 测试新增账户权益
     */
    @Test
    public void testIncr() {

        AccountCreateDTO createDTO = new AccountCreateDTO();
        createDTO.setUserId(888L);
        createDTO.setMerchantId(1000319L);
        createDTO.setStoreId(666L);
        createDTO.setEquipmentId(666L);
        createDTO.setOutTradeNo("6626");
        createDTO.setOrderNo("6626");
        createDTO.setClassify(1);
        createDTO.setDescription("666TEST");
        createDTO.setResource("COME FROM 666");
        createDTO.setMerchantUserId(10000L);
        createDTO.setOperator(8848L);
        AccountBenefitCreateDTO benefitCreateDTO = new AccountBenefitCreateDTO();
        benefitCreateDTO.setBenefitId(1L);
        benefitCreateDTO.setTotal(BigDecimal.valueOf(6));
        benefitCreateDTO.setClassify(1);


        createDTO.setAccountBenefit(Arrays.asList(benefitCreateDTO));

        AccountCreateDTO createDTO2 = new AccountCreateDTO();
        createDTO2.setUserId(888L);
        createDTO2.setMerchantId(1000319L);
        createDTO2.setStoreId(666L);
        createDTO2.setEquipmentId(666L);
        createDTO2.setOutTradeNo("6626");
        createDTO2.setOrderNo("6626");
        createDTO2.setClassify(1);
        createDTO2.setDescription("666TEST");
        createDTO2.setResource("COME FROM 666");
        createDTO2.setMerchantUserId(10000L);
        createDTO2.setOperator(8848L);
        AccountBenefitCreateDTO benefitCreateDTO2 = new AccountBenefitCreateDTO();
        benefitCreateDTO2.setBenefitId(1L);
        benefitCreateDTO2.setTotal(BigDecimal.valueOf(6));
        benefitCreateDTO2.setClassify(1);


        createDTO2.setAccountBenefit(Arrays.asList(benefitCreateDTO2));


        accountService.increaseAccountBenefit(Arrays.asList(createDTO, createDTO2));
    }

    /**
     * 测试新增账户权益
     */
    @Test
    public void testIncrConcurrent() {
        final CountDownLatch count = new CountDownLatch(20);
        Executor executor = new ThreadPoolExecutor(20, 20, 10, TimeUnit.MINUTES, new ArrayBlockingQueue<>(20));
        List<Runnable> runnables = new ArrayList<>();
        for (int index = 0; index < 20; index ++) {
            runnables.add(new Runnable() {
                @Override
                public void run() {
                    AccountCreateDTO createDTO = new AccountCreateDTO();
                    createDTO.setUserId(21745745l);
                    createDTO.setMerchantId(1000319l);
                    createDTO.setStoreId(888L);
                    createDTO.setEquipmentId(888L);
                    createDTO.setOutTradeNo("6626");
                    createDTO.setOrderNo("6626");
                    createDTO.setClassify(1);
                    createDTO.setDescription("666TEST");
                    createDTO.setResource("COME FROM 666");
                    createDTO.setMerchantUserId(1390575798011826178L);
                    createDTO.setOperator(888L);
                    AccountBenefitCreateDTO benefitCreateDTO = new AccountBenefitCreateDTO();
                    benefitCreateDTO.setBenefitId(837643801812660224L);
                    benefitCreateDTO.setTotal(BigDecimal.TEN);
                    benefitCreateDTO.setClassify(1);


                    createDTO.setAccountBenefit(Arrays.asList(benefitCreateDTO));
                    accountService.increaseAccountBenefit(Arrays.asList(createDTO));
                    log.info("finish--");
                    count.countDown();
                }
            });
        }

        for (Runnable task : runnables) {
            executor.execute(task);
        }

        try {
            count.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            e.printStackTrace();
        }
    }

    @Test
    public void testDecr() {
        AccountBenefitAdjustDTO adjustDTO = new AccountBenefitAdjustDTO();
        adjustDTO.setId(10030L);
        adjustDTO.setAdjustType(AdjustTypeEnum.DECREMENT);
        adjustDTO.setAmount(BigDecimal.valueOf(2));
        adjustDTO.setClassify(1);
        adjustDTO.setUserId(666L);
        adjustDTO.setMerchantId(666L);
        adjustDTO.setMerchantUserId(666L);
        adjustDTO.setStoreId(666L);
        adjustDTO.setEquipmentId(666L);
        adjustDTO.setOutTradeNo("again");
        adjustDTO.setOrderNo("again");
        adjustDTO.setResource("666");
        adjustDTO.setOperator(999L);



        accountService.decreaseAccountBenefit(Arrays.asList(adjustDTO));
    }


    @Test
    public void testGetAccount() {

        AccountConditionDTO condition = new AccountConditionDTO();
        condition.setUserId(666L);
        condition.setMerchantId(666L);
        condition.setClassify(1);

        Object resp = accountService.getByUserAndMerchant(condition);

        try {
            log.info("输出结果 -> {}", objectMapper.writeValueAsString(resp));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testBenefitCount() {

        AccountQueryDTO param = new AccountQueryDTO();
//        param.setEquipmentTypeId(Arrays.asList(1000001L));
//        param.setStoreId(Arrays.asList(1028486L));
        param.setMerchantId(1000319L);
        param.setMerchantUserId(846746749381476352L);
        param.setBenefitClassify(BenefitClassifyGroupEnum.getClassifyByType(2));
        Object resp = accountService.benefitCount(param);

        try {
            log.info("输出结果 -> {}", objectMapper.writeValueAsString(resp));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testConsume() throws Exception {
        Long merchantId = 1033377L ;
        Long userId = 33208067L ;
        Long merchantUserId = 1397825620427149313L ;
        ConsumeDTO consume = new ConsumeDTO();
        consume.setUserId(userId);
        consume.setMerchantId(merchantId);
        //consume.setStoreId(1028479L);
        //consume.setEquipmentId(888L);
        //consume.setEquipmentTypeId(888L);
        //consume.setCommodityId(888L);
        //consume.setOutTradeNo("ttttt");
        //consume.setOrderNo("ttttt");
        consume.setResource("测试用例");
        consume.setOperator(888L);


        ConsumeDetailDTO detail = new ConsumeDetailDTO();
        detail.setClassify(Arrays.asList(2));
        detail.setAmount(BigDecimal.valueOf(1));


        ConsumeDetailDTO detail2 = new ConsumeDetailDTO();
        detail2.setClassify(Arrays.asList(14));
        detail2.setAmount(BigDecimal.valueOf(1));


        consume.setConsume(Arrays.asList(detail,detail2));


        mockMvc.perform(post("/account/benefit/consume")
                .content(objectMapper.writeValueAsString(consume))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andDo(print());
    }


    @Test
    public void testClear() throws Exception {
        List<AccountBenefitAdjustDTO> param = new ArrayList<>();
        AccountBenefitAdjustDTO item1 = new AccountBenefitAdjustDTO();
        item1.setId(10210L);
        item1.setAdjustType(AdjustTypeEnum.DECREMENT);
        item1.setAmount(new BigDecimal("5"));
        item1.setClassify(1);
        item1.setMerchantId(888L);
        item1.setOperator(12L);

        mockMvc.perform(post("/account/benefit/clear")
                .content(objectMapper.writeValueAsString(Arrays.asList(item1)))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andDo(print());

    }

    @Test
    public void testRedis() {

        String aaa = RedisClient.get(RedisKey.MEMBER_CONSUME_CACHE.concat("ttttt"));
        log.info(aaa);
    }
    /**
     * 测试统计账号权益
     */
    @Test
    public void sumAccountBenefit() {
        Long memberId = 1033342L;
        Long merchantUserId = 851853018828177408L;
        List<Integer> classifyList = Arrays.asList(2,4,14);
        AccountBenefitCountDTO accountBenefitCountDTO = accountService.sumAccountBenefit(memberId,merchantUserId,classifyList);
        log.info("sumAccountBenefit -->{}",accountBenefitCountDTO);
    }

    @Test
    public void initAccount() throws Exception{
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        for (int i = 0; i < 5; i++) {
            int finalI = i;
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    AccountInitDTO initDTO = new AccountInitDTO();
                    initDTO.setUserId(21778180L);
                    initDTO.setStoreId(10000L);
                    initDTO.setOperatorId(0L);
                    if(finalI > 2 ){
                        initDTO.setCardNo("Lyy90001" + finalI);
                    } else {
                        initDTO.setCardNo("Lyy900011");
                    }
                    initDTO.setMerchantUserId(933720258458656768L);
                    initDTO.setMerchantId(172L);
                    initDTO.setDeposit(new BigDecimal(5));
                    if (finalI == 4) {
                        initDTO.setDefaultFlag(true);
                    }
                    initDTO.setClassify(BenefitClassifyEnum.SMALL_VENUE_DEFAULT.getCode());
                    AccountInitResultDTO resultDTO = accountRepository.initAccount(initDTO);
                    log.debug("resultDTO:{}", resultDTO);
                }
            });
        }

        Thread.sleep(20000);
    }
}
