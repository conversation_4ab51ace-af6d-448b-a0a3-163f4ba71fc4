package com.lyy.user.domain.account.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.lyy.lock.redis.RedisLock;
import com.lyy.user.ResourceUtil;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitDecrementDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitIncrementDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitIncrementItemDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.CardTransferDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.response.BenefitConsumptionSummaryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.response.BenefitRechargeSummaryDTO;
import com.lyy.user.account.infrastructure.constant.AccountBenefitNumTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountStatusEnum;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import com.lyy.user.account.infrastructure.constant.MemberGrowRecordModeEnum;
import com.lyy.user.application.benefit.SmallVenueAccountService;
import com.lyy.user.domain.account.dto.AccountInitDTO;
import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.domain.account.entity.AccountBenefit;
import com.lyy.user.domain.account.entity.AccountRecord;
import com.lyy.user.domain.statistics.entity.SmallVenueStoredStatistics;
import com.lyy.user.domain.user.entity.MerchantUser;
import com.lyy.user.domain.user.service.MerchantAutoTagAsyncHandler;
import com.lyy.user.infrastructure.execption.BusinessException;
import com.lyy.user.infrastructure.mapstruct.AccountMapStruct;
import com.lyy.user.infrastructure.repository.account.SmallVenueAccountRepository;
import com.lyy.user.infrastructure.repository.user.MerchantUserRepository;
import com.lyy.user.infrastructure.support.tuple.AccountTransferTuple;
import com.lyy.user.infrastructure.support.tuple.AccountTuple;
import com.lyy.user.infrastructure.support.tuple.MainAccountTuple;
import com.lyy.user.infrastructure.util.DateTimeUtils;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.ArgumentCaptor;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@Import(SmallVenueAccountServiceImpl.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class SmallVenueAccountServiceImplTest {

    private static final Long MERCHANT_ID = *********L;
    private static final Long USER_ID = *********L;

    @MockBean(name = "smallVenueAccountRepository")
    private SmallVenueAccountRepository smallVenueAccountRepository;
    @MockBean
    private MerchantUserRepository merchantUserRepository;
    @MockBean
    private AccountMapStruct mapStruct;
    @MockBean
    private RedisLock redisLock;
    @MockBean
    private AsyncVenueAccountHandler asyncVenueAccountHandler;
    @MockBean
    private ApplicationContext applicationContext;
    @MockBean
    private MerchantAutoTagAsyncHandler merchantAutoTagAsyncHandler;
    @Resource
    private SmallVenueAccountServiceImpl service;

    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void getAccountTuple() {
        when(smallVenueAccountRepository.listAccountWithDefault(MERCHANT_ID, USER_ID, null)).thenReturn(Lists.newArrayList());
        AccountTuple accountTuple = service.getAccountTuple(MERCHANT_ID, USER_ID, null);
        assertThat(accountTuple.getCardAccount().isPresent()).isFalse();
        assertThat(accountTuple.getDefaultAccount().isPresent()).isFalse();
    }

    @Test
    void getAccountTupleWithDefault() {
        when(smallVenueAccountRepository.listAccountWithDefault(MERCHANT_ID, USER_ID, null)).thenReturn(mockAccountsFromFile());
        AccountTuple accountTuple = service.getAccountTuple(MERCHANT_ID, USER_ID, null);
        assertThat(accountTuple.getCardAccount().isPresent()).isFalse();
        assertThat(accountTuple.getDefaultAccount().isPresent()).isTrue();
        assertThat(accountTuple.getDefaultAccount().get().getDefaultFlag()).isTrue();
        assertThat(accountTuple.getDefaultAccount().get().getId()).isEqualTo(932597107968315392L);
        assertThat(accountTuple.getDefaultAccount().get().getCardNo()).isEqualTo("*********");
    }

    @Test
    void getAccountTuple2() {
        when(smallVenueAccountRepository.listAccountWithDefault(eq(MERCHANT_ID), eq(USER_ID), anyString())).thenReturn(mockAccountsFromFile());
        AccountTuple accountTuple = service.getAccountTuple(MERCHANT_ID, USER_ID, "1");
        assertThat(accountTuple.getCardAccount().isPresent()).isFalse();
        assertThat(accountTuple.getDefaultAccount().isPresent()).isTrue();
        assertThat(accountTuple.getDefaultAccount().get().getDefaultFlag()).isTrue();
        assertThat(accountTuple.getDefaultAccount().get().getId()).isEqualTo(932597107968315392L);
        assertThat(accountTuple.getDefaultAccount().get().getCardNo()).isEqualTo("*********");
    }

    @Test
    void getAccountTuple3() {
        when(smallVenueAccountRepository.listAccountWithDefault(eq(MERCHANT_ID), eq(USER_ID), anyString())).thenReturn(mockAccountsFromFile());
        AccountTuple accountTuple = service.getAccountTuple(MERCHANT_ID, USER_ID, "*********");

        assertThat(accountTuple.getCardAccount().isPresent()).isTrue();
        assertThat(accountTuple.getCardAccount().get().getDefaultFlag()).isFalse();
        assertThat(accountTuple.getCardAccount().get().getId()).isEqualTo(932597111449587712L);
        assertThat(accountTuple.getCardAccount().get().getCardNo()).isEqualTo("*********");
        assertThat(accountTuple.getDefaultAccount().isPresent()).isTrue();
        assertThat(accountTuple.getDefaultAccount().get().getDefaultFlag()).isTrue();
        assertThat(accountTuple.getDefaultAccount().get().getId()).isEqualTo(932597107968315392L);
        assertThat(accountTuple.getDefaultAccount().get().getCardNo()).isEqualTo("*********");
    }

    @Test
    void getAccountTuple4() {
        Account account = new Account();
        account.setCardNo("1");
        when(smallVenueAccountRepository.listAccountWithDefault(eq(MERCHANT_ID), eq(USER_ID), anyString())).thenReturn(Lists.newArrayList(account, new Account()));
        AccountTuple accountTuple = service.getAccountTuple(MERCHANT_ID, USER_ID, "1");
        assertThat(accountTuple.getCardAccount().isPresent()).isTrue();
        assertThat(accountTuple.getDefaultAccount().isPresent()).isFalse();
    }

    @Disabled
    @Test
    void getAccountTuple5() {
        Account account = new Account();
        account.setCardNo("1");
        Account account2 = new Account();
        account2.setCardNo("2");
        account2.setDefaultFlag(true);
        Account account3 = new Account();
        account3.setCardNo("3");
        account3.setDefaultFlag(true);
        when(smallVenueAccountRepository.listAccountWithDefault(eq(MERCHANT_ID), eq(USER_ID), anyString()))
                .thenReturn(Lists.newArrayList(account, account2, account3));
        AccountTuple accountTuple = service.getAccountTuple(MERCHANT_ID, USER_ID, "1");
        assertThat(accountTuple.getCardAccount().isPresent()).isTrue();
        assertThat(accountTuple.getDefaultAccount().isPresent()).isTrue();
        assertThat(accountTuple.getDefaultAccount().get().getCardNo()).isEqualTo("3");
    }

    @DisplayName("get: 用户无账号")
    @Test
    void nullAccount() {
        when(smallVenueAccountRepository.listAccountWithDefault(eq(MERCHANT_ID), eq(USER_ID), anyString())).thenReturn(Lists.newArrayList());
        Optional<Account> optional = service.get(MERCHANT_ID, USER_ID, null);
        assertThat(optional.isPresent()).isFalse();
    }

    @DisplayName("get: 多个会员卡账户-卡账户不存在")
    @Test
    void noAccount() {
        when(smallVenueAccountRepository.listAccountWithDefault(MERCHANT_ID, USER_ID, "11111")).thenReturn(mockAccounts());
        Optional<Account> optional = service.get(MERCHANT_ID, USER_ID, "11111");
        assertThat(optional.isPresent()).isFalse();
    }

    @DisplayName("get: 多个会员卡账户-默认无卡账户不存在")
    @Test
    void defaultAccount() {
        when(smallVenueAccountRepository.listAccountWithDefault(MERCHANT_ID, USER_ID, "11111")).thenReturn(Lists.newArrayList(new Account()));
        Optional<Account> optional = service.get(MERCHANT_ID, USER_ID, "");
        assertThat(optional.isPresent()).isFalse();
    }

    @DisplayName("get: 多个会员卡账户-获取指定会员卡账户")
    @Test
    void get() {
        when(smallVenueAccountRepository.listAccountWithDefault(MERCHANT_ID, USER_ID, "1")).thenReturn(mockAccounts());
        Optional<Account> optional = service.get(MERCHANT_ID, USER_ID, "1");
        assertThat(optional.isPresent()).isTrue();
        assertThat(optional.get().getCardNo()).isEqualTo("1");
    }


    @DisplayName("get: 默认无卡账户-卡账户不存在")
    @Test
    void getDefaultAccountNull() {
        when(smallVenueAccountRepository.listAccountWithDefault(MERCHANT_ID, USER_ID, "1")).thenReturn(Lists.newArrayList(new Account()));
        Optional<Account> optional = service.get(MERCHANT_ID, USER_ID, "1");
        assertThat(optional.isPresent()).isFalse();
    }

    @DisplayName("get: 默认无卡账户")
    @Test
    void getDefaultAccount() {
        Account account = new Account();
        account.setDefaultFlag(true);
        when(smallVenueAccountRepository.listAccountWithDefault(eq(MERCHANT_ID), eq(USER_ID), anyString())).thenReturn(Lists.newArrayList(account));
        Optional<Account> optional = service.get(MERCHANT_ID, USER_ID, "");
        assertThat(optional.isPresent()).isTrue();
    }

    @DisplayName("initAccountIfAbsent: 无卡号获取账户-已存在多个账户（包括默认账户）")
    @Test
    @Disabled
    // TODO 2022/6/2: 调整用例
    void existCardAccountWithException() {
        when(smallVenueAccountRepository.listAccountWithDefault(MERCHANT_ID, USER_ID, null)).thenReturn(mockAccounts());

        Account account = service.initAccountIfAbsent(MERCHANT_ID, USER_ID, anyLong(), null, () -> mock(AccountInitDTO.class));

        assertThat(account.getDefaultFlag()).isTrue();
    }

    @DisplayName("initAccountIfAbsent: 无卡号获取账户-无账户")
    @Test
    void noCardAndAccount() {
        BenefitIncrementItemDTO benefitIncrementItemDTO = new BenefitIncrementItemDTO();

        Supplier<AccountInitDTO> supplier = () -> mock(AccountInitDTO.class);
        SmallVenueAccountService spy = spy(service);

        when(smallVenueAccountRepository.listAccountWithDefault(MERCHANT_ID, USER_ID, null)).thenReturn(Lists.newArrayList());
        doReturn(new Account()).when(spy).initAccount(supplier);

        spy.initAccountIfAbsent(MERCHANT_ID, USER_ID, 1L, benefitIncrementItemDTO, supplier);

        verify(spy, times(1)).initAccount(supplier);
    }

    @DisplayName("initAccountIfAbsent: 根据卡号获取账户-已存在多个账户")
    @Test
    void initAccountIfAbsent() {
        when(smallVenueAccountRepository.listAccountWithDefault(MERCHANT_ID, USER_ID, "1")).thenReturn(mockAccounts());

        Account account = service.initAccountIfAbsent(MERCHANT_ID, USER_ID, "1", null, () -> mock(AccountInitDTO.class));

        assertThat(account).isNotNull();
        assertThat(account.getCardNo()).isEqualTo("1");
    }

    @DisplayName("initAccountIfAbsent: 根据卡号获取账户-已存在多个账户但该卡号无账户")
    @Test
    void noRelateAccountWithCard() {
        Supplier<AccountInitDTO> supplier = () -> mock(AccountInitDTO.class);
        SmallVenueAccountService spy = spy(service);
        Account newAccount = new Account();
        newAccount.setCardNo("3");

        when(smallVenueAccountRepository.listAccountWithDefault(eq(MERCHANT_ID), eq(USER_ID), anyString())).thenReturn(mockAccounts());
        doReturn(newAccount).when(spy).initAccount(supplier);

        Account account = spy.initAccountIfAbsent(MERCHANT_ID, USER_ID, "3", null, supplier);

        assertThat(account.getCardNo()).isEqualTo("3");
        verify(spy, times(1)).initAccount(supplier);
    }

    @DisplayName("initAccountIfAbsent: 根据卡号获取账户-默认账户绑定会员卡")
    @Test
    void updateDefaultCardNo() {
        BigDecimal deposit = new BigDecimal("200");
        Account defaultAccount = new Account();
        defaultAccount.setDefaultFlag(true);
        defaultAccount.setDeposit(BigDecimal.ZERO);
        when(smallVenueAccountRepository.listAccountWithDefault(MERCHANT_ID, USER_ID, "1")).thenReturn(Lists.newArrayList(defaultAccount));

        Account account = service.initAccountIfAbsent(MERCHANT_ID, USER_ID, "1", deposit, () -> mock(AccountInitDTO.class));

        assertThat(account).isNotNull();
        assertThat(account.getCardNo()).isEqualTo("1");
        assertThat(account.getDeposit()).isEqualTo(deposit);
    }

    @DisplayName("initAccountIfAbsent: 根据卡号获取账户-默认账户绑定会员卡")
    @Test
    void updateDefaultCardNo2() {
        BigDecimal deposit = new BigDecimal("200");
        Account defaultAccount = new Account();
        defaultAccount.setDefaultFlag(true);
        when(smallVenueAccountRepository.listAccountWithDefault(MERCHANT_ID, USER_ID, "1")).thenReturn(Lists.newArrayList(defaultAccount));

        Account account = service.initAccountIfAbsent(MERCHANT_ID, USER_ID, "1", deposit, () -> mock(AccountInitDTO.class));

        assertThat(account).isNotNull();
        assertThat(account.getCardNo()).isEqualTo("1");
        assertThat(account.getDeposit()).isEqualTo(deposit);
    }

    // TODO 2022/5/30: 调整用例
    @DisplayName("增加权益-已有卡账户")
    @Test
    void increaseAccountBenefit() {
        SmallVenueAccountServiceImpl spy = spy(service);
        BenefitIncrementDTO incrementDTO = ResourceUtil.readJson("asset/smallvenue/account/increment/IncrementDTO.json", BenefitIncrementDTO.class);

        when(merchantUserRepository.selectBy(anyLong(), anyLong())).thenReturn(Optional.of(mock(MerchantUser.class)));
        when(smallVenueAccountRepository.listAccountWithDefault(anyLong(), anyLong(), anyString())).thenReturn(mockAccountsFromFile());
        when(smallVenueAccountRepository.selectUnlimitedBenefit(anyLong(), anyLong(), anyList())).thenReturn(Lists.newArrayList());

        IncreaseByAccountBenefitIdAnswer answer = new IncreaseByAccountBenefitIdAnswer();
        when(spy.isIncreaseByRefund(any(), any(), any())).thenAnswer(answer);

        doAnswer((Answer<AccountBenefit>) invocation -> {
            AccountBenefit ab = (AccountBenefit) invocation.callRealMethod();
            assertThat(ab.getAccountId()).isEqualTo(932597107968315392L);
            assertThat(ab.getBalance()).isEqualTo(new BigDecimal(10));
            assertThat(ab.getTotal()).isEqualTo(new BigDecimal(10));
            assertThat(ab.getMerchantBenefitClassifyId()).isEqualTo(*********L);
            assertThat(ab.getUseRuleId()).isEqualTo(100L);
            return ab;
        }).doAnswer((Answer<AccountBenefit>) invocation -> {
            AccountBenefit ab = (AccountBenefit) invocation.callRealMethod();
            assertThat(ab.getAccountId()).isEqualTo(932597107968315392L);
            assertThat(ab.getBalance()).isEqualTo(new BigDecimal(10));
            assertThat(ab.getTotal()).isEqualTo(new BigDecimal(10));
            assertThat(ab.getMerchantBenefitClassifyId()).isEqualTo(*********L);
            assertThat(ab.getUseRuleId()).isEqualTo(101);
            return ab;
        }).doAnswer((Answer<AccountBenefit>) invocation -> {
            AccountBenefit ab = (AccountBenefit) invocation.callRealMethod();
            assertThat(ab.getAccountId()).isEqualTo(932597111449587712L);
            assertThat(ab.getBalance()).isEqualTo(new BigDecimal(20));
            assertThat(ab.getTotal()).isEqualTo(new BigDecimal(20));
            assertThat(ab.getMerchantBenefitClassifyId()).isEqualTo(*********);
            assertThat(ab.getUseRuleId()).isEqualTo(103);
            assertThat(ab.getValueType()).isEqualTo(2);
            return ab;
        }).when(spy).saveOrUpdateAccountBenefit4Increment(
                any(BenefitIncrementDTO.class),
                any(Account.class), anyBoolean(), anyMap(),
                any(BenefitIncrementItemDTO.BenefitDetailsDTO.class), any(Date.class)
        );

        doAnswer((Answer<SmallVenueStoredStatistics>) invocation -> {
            SmallVenueStoredStatistics result = (SmallVenueStoredStatistics) invocation.callRealMethod();
            assertThat(result.getBalance()).isEqualTo(new BigDecimal(10));
            assertThat(result.getTotal()).isEqualTo(new BigDecimal(10));
            assertThat(result.getMerchantBenefitClassifyId()).isEqualTo(*********);
            return result;
        }).doAnswer((Answer<SmallVenueStoredStatistics>) invocation -> {
            SmallVenueStoredStatistics result = (SmallVenueStoredStatistics) invocation.callRealMethod();
            assertThat(result.getBalance()).isEqualTo(new BigDecimal(20));
            assertThat(result.getTotal()).isEqualTo(new BigDecimal(20));
            assertThat(result.getMerchantBenefitClassifyId()).isEqualTo(*********);
            return result;
        }).doAnswer((Answer<SmallVenueStoredStatistics>) invocation -> {
            SmallVenueStoredStatistics result = (SmallVenueStoredStatistics) invocation.callRealMethod();
            assertThat(result.getBalance()).isEqualTo(new BigDecimal("20"));
            assertThat(result.getBalance()).isEqualTo(new BigDecimal("20"));
            assertThat(result.getRemainNum()).isEqualTo(1);
            assertThat(result.getTotalNum()).isEqualTo(1);
            assertThat(result.getMerchantBenefitClassifyId()).isEqualTo(*********);
            return result;
        }).when(spy).saveOrUpdateAccountStatistics4Increment(
                any(BenefitIncrementDTO.class),
                any(Account.class),
                anyMap(),
                any(BenefitIncrementItemDTO.BenefitDetailsDTO.class),
                any(Date.class)
        );

        spy.increaseAccountBenefit(incrementDTO);

        assertThat(answer.getResult()).isFalse();
        verify(smallVenueAccountRepository, times(3)).saveAccountBenefit(any());
        verify(smallVenueAccountRepository, times(2)).saveAccountStatistics(any());
        verify(smallVenueAccountRepository, times(1)).updateAccountStatistics(any(), any());
    }

    @DisplayName("退单-启动失败")
    @Test
    void updateAccountBenefit4IncrementRefund() {
        BenefitIncrementDTO incrementDTO = ResourceUtil.readJson("asset/smallvenue/account/increment/IncrementDTO3.json", BenefitIncrementDTO.class);
        assertThat(incrementDTO).isNotNull();
        List<AccountBenefit> benefits = ResourceUtil.readJson("asset/smallvenue/account/increment/benefits.json",
                new TypeReference<List<AccountBenefit>>() {
                });
        assertThat(benefits).isNotNull();
        BenefitIncrementItemDTO.BenefitDetailsDTO detail = incrementDTO.getItems().get(0).getBenefitDetails().get(0);
        detail.setAccountBenefitId(931860053026312192L);
        AccountBenefit benefit = benefits.get(0);
        Map<String, AccountBenefit> map = new HashMap<>();
        map.put("931860053026312192", benefit);

        AccountBenefit accountBenefit = service.saveOrUpdateAccountBenefit4Increment(incrementDTO, new Account(), true, map, detail, new Date());
        assertThat(accountBenefit.getBalance()).isEqualTo(new BigDecimal("35.00"));
        assertThat(accountBenefit.getTotal()).isEqualTo(new BigDecimal("20.00"));
    }

    @DisplayName("退单 error")
    @Test
    void updateAccountBenefit4IncrementException() {
        BenefitIncrementDTO incrementDTO = ResourceUtil.readJson("asset/smallvenue/account/increment/IncrementDTO3.json", BenefitIncrementDTO.class);
        assertThat(incrementDTO).isNotNull();
        BenefitIncrementItemDTO.BenefitDetailsDTO detail = incrementDTO.getItems().get(0).getBenefitDetails().get(0);
        detail.setAccountBenefitId(931860053026312192L);

        BusinessException businessException = assertThrows(BusinessException.class, () ->
                service.saveOrUpdateAccountBenefit4Increment(incrementDTO, new Account(), true, new HashMap<>(), detail, new Date())
        );
        assertThat(businessException.getMessage()).isEqualTo("找不到对应的账户权益记录");
    }

    @DisplayName("充值")
    @Test
    void saveAccountBenefit4Increment() {
        BenefitIncrementDTO incrementDTO = ResourceUtil.readJson("asset/smallvenue/account/increment/IncrementDTO3.json", BenefitIncrementDTO.class);
        assertThat(incrementDTO).isNotNull();
        BenefitIncrementItemDTO.BenefitDetailsDTO detail = incrementDTO.getItems().get(0).getBenefitDetails().get(1);
        detail.setAccountBenefitId(931860053026312192L);
        Map<String, AccountBenefit> map = new HashMap<>();

        AccountBenefit accountBenefit = service.saveOrUpdateAccountBenefit4Increment(incrementDTO, new Account(), false, map, detail, new Date());
        assertThat(accountBenefit.getBalance()).isEqualTo(new BigDecimal("10.00"));
        assertThat(accountBenefit.getTotal()).isEqualTo(new BigDecimal("10.00"));
    }

    @DisplayName("充值-更新")
    @Test
    void updateAccountBenefit4Increment() {
        BenefitIncrementDTO incrementDTO = ResourceUtil.readJson("asset/smallvenue/account/increment/IncrementDTO3.json", BenefitIncrementDTO.class);
        assertThat(incrementDTO).isNotNull();
        List<AccountBenefit> benefits = ResourceUtil.readJson("asset/smallvenue/account/increment/benefits.json",
                new TypeReference<List<AccountBenefit>>() {
                });
        assertThat(benefits).isNotNull();
        BenefitIncrementItemDTO.BenefitDetailsDTO detail = incrementDTO.getItems().get(0).getBenefitDetails().get(0);
        detail.setAccountBenefitId(931860053026312192L);
        Map<String, AccountBenefit> map = new HashMap<>();
        map.put("*********:100", benefits.get(0));

        AccountBenefit accountBenefit = service.saveOrUpdateAccountBenefit4Increment(incrementDTO, new Account(), false, map, detail, new Date());
        assertThat(accountBenefit.getBalance()).isEqualTo(new BigDecimal("35.00"));
        assertThat(accountBenefit.getTotal()).isEqualTo(new BigDecimal("35.00"));
    }

    @DisplayName("增加权益-退单-额度")
    @ParameterizedTest(name = "{index} {0}")
    @EnumSource(value = AccountRecordTypeEnum.class, names = {"SV_EQUIPMENT_CONSUMPTION_REFUND", "SV_STORED_VALUE_DEDUCTION_REFUND", "SV_EXCHANGE_REFUND"})
    void generateUpdatedStatistics4IncrementRefundAmount(AccountRecordTypeEnum recordType) {
        BenefitIncrementItemDTO.BenefitDetailsDTO detail = new BenefitIncrementItemDTO.BenefitDetailsDTO();
        detail.setAccountBenefitId(1L);
        detail.setNum(BigDecimal.ONE);
        detail.setNumType(AccountBenefitNumTypeEnum.AMOUNT.getCode());

        SmallVenueStoredStatistics accountStatistics = new SmallVenueStoredStatistics();
        accountStatistics.setBalance(new BigDecimal("9.00"));
        accountStatistics.setTotal(BigDecimal.TEN);
        accountStatistics.setTotalConsume(BigDecimal.ONE);

        SmallVenueStoredStatistics updatedStatistics = SmallVenueAccountServiceImpl.generateUpdatedStatistics4Increment(detail, recordType.getCode(), accountStatistics);

        assertThat(updatedStatistics.getTotal()).isNull();
        assertThat(updatedStatistics.getTotalNum()).isNull();
        assertThat(updatedStatistics.getBalance()).isEqualTo(new BigDecimal("10.00"));
        assertThat(updatedStatistics.getTotalConsume()).isEqualTo(BigDecimal.ZERO);
    }

    @DisplayName("增加权益-退单-频次")
    @ParameterizedTest(name = "{index} {0}")
    @EnumSource(value = AccountRecordTypeEnum.class, names = {"SV_EQUIPMENT_CONSUMPTION_REFUND", "SV_STORED_VALUE_DEDUCTION_REFUND", "SV_EXCHANGE_REFUND"})
    void generateUpdatedStatistics4IncrementRefundFrequency(AccountRecordTypeEnum recordType) {
        BenefitIncrementItemDTO.BenefitDetailsDTO detail = new BenefitIncrementItemDTO.BenefitDetailsDTO();
        detail.setAccountBenefitId(1L);
        detail.setNum(new BigDecimal(10));
        detail.setNumType(AccountBenefitNumTypeEnum.FREQUENCY.getCode());

        SmallVenueStoredStatistics accountStatistics = new SmallVenueStoredStatistics();
        accountStatistics.setTotal(new BigDecimal(10));
        accountStatistics.setBalance(new BigDecimal(0));
        accountStatistics.setTotalConsume(new BigDecimal(10));
        accountStatistics.setRemainNum(0);
        accountStatistics.setTotalNum(1);
        accountStatistics.setTotalNumConsume(1);

        SmallVenueStoredStatistics updatedStatistics = SmallVenueAccountServiceImpl.generateUpdatedStatistics4Increment(detail, recordType.getCode(), accountStatistics);

        assertThat(updatedStatistics.getBalance().intValue()).isEqualTo(10);
        assertThat(updatedStatistics.getTotalConsume().intValue()).isEqualTo(0);
        assertThat(updatedStatistics.getRemainNum()).isEqualTo(1);
        assertThat(updatedStatistics.getTotalNumConsume()).isEqualTo(0);
    }

    @DisplayName("增加权益-额度")
    @ParameterizedTest(name = "{index} {0}")
    @EnumSource(value = AccountRecordTypeEnum.class, names = {
            "SV_RECHARGE",
            "SV_PRESENTATION",
            "SV_GIFT_RECYCLING"
    })
    void generateUpdatedStatistics4IncrementAmount(AccountRecordTypeEnum recordType) {
        BenefitIncrementItemDTO.BenefitDetailsDTO detail = new BenefitIncrementItemDTO.BenefitDetailsDTO();
        detail.setNum(new BigDecimal("10.00"));
        detail.setNumType(AccountBenefitNumTypeEnum.AMOUNT.getCode());

        SmallVenueStoredStatistics accountStatistics = new SmallVenueStoredStatistics();
        accountStatistics.setBalance(new BigDecimal("10.00"));
        accountStatistics.setTotal(new BigDecimal("10.00"));

        SmallVenueStoredStatistics updatedStatistics = SmallVenueAccountServiceImpl.generateUpdatedStatistics4Increment(detail, recordType.getCode(), accountStatistics);

        assertThat(updatedStatistics.getTotal()).isEqualTo(new BigDecimal("20.00"));
        assertThat(updatedStatistics.getBalance()).isEqualTo(new BigDecimal("20.00"));
    }

    @DisplayName("增加权益-充值-频次")
    @ParameterizedTest(name = "{index} {0}")
    @EnumSource(value = AccountRecordTypeEnum.class, names = {
            "SV_RECHARGE",
            "SV_PRESENTATION",
            "SV_GIFT_RECYCLING"
    })
    void generateUpdatedStatistics4IncrementFrequency(AccountRecordTypeEnum recordType) {
        BenefitIncrementItemDTO.BenefitDetailsDTO detail = new BenefitIncrementItemDTO.BenefitDetailsDTO();
        detail.setNum(new BigDecimal("10.00"));
        detail.setNumType(AccountBenefitNumTypeEnum.FREQUENCY.getCode());

        SmallVenueStoredStatistics accountStatistics = new SmallVenueStoredStatistics();
        accountStatistics.setBalance(new BigDecimal(10));
        accountStatistics.setTotal(new BigDecimal(10));
        accountStatistics.setRemainNum(1);
        accountStatistics.setTotalNum(1);

        SmallVenueStoredStatistics updatedStatistics = SmallVenueAccountServiceImpl.generateUpdatedStatistics4Increment(detail, recordType.getCode(), accountStatistics);

        assertThat(updatedStatistics.getBalance().intValue()).isEqualTo(20);
        assertThat(updatedStatistics.getTotal().intValue()).isEqualTo(20);
        assertThat(updatedStatistics.getRemainNum()).isEqualTo(2);
        assertThat(updatedStatistics.getTotalNum()).isEqualTo(2);
        assertThat(accountStatistics.getBalance().intValue()).isEqualTo(20);
        assertThat(accountStatistics.getTotal().intValue()).isEqualTo(20);
        assertThat(accountStatistics.getRemainNum()).isEqualTo(2);
        assertThat(accountStatistics.getTotalNum()).isEqualTo(2);
    }

    @Test
    void getAndCheckAccountTupleWithSubAccount() {
        BenefitDecrementDTO dto = ResourceUtil.readJson("asset/smallvenue/account/decrement/DecrementDTO-sub-account.json", BenefitDecrementDTO.class);
        assertThat(dto).isNotNull();

        SmallVenueAccountServiceImpl spy = spy(service);
        Account subAccount = new Account();
        subAccount.setCardNo("11");
        subAccount.setParentAccountId(1L);
        subAccount.setStatus(AccountStatusEnum.NORMAL.getStatus());
        Account mainAccount = new Account();
        mainAccount.setCardNo("1");
        mainAccount.setId(1L);
        mainAccount.setStatus(AccountStatusEnum.NORMAL.getStatus());

        doReturn(Optional.of(subAccount)).when(spy).get(anyLong(), anyLong(), anyString());
        when(smallVenueAccountRepository.get(anyLong(), anyLong())).thenReturn(Optional.of(mainAccount));

        MainAccountTuple mainAccountTuple = spy.getAndCheckAccountTuple(dto);

        assertThat(mainAccountTuple).isNotNull();
        assertThat(mainAccountTuple.getAccount()).isNotNull();
        assertThat(mainAccountTuple.getAccount().getCardNo()).isEqualTo("11");
        assertThat(mainAccountTuple.getMainAccount()).isNotNull();
        assertThat(mainAccountTuple.getMainAccount().getCardNo()).isEqualTo("1");
        assertThat(mainAccountTuple.getMainAccount().getId()).isEqualTo(1L);

    }

    @Test
    void getAndCheckAccountTuple() {
        BenefitDecrementDTO dto = ResourceUtil.readJson("asset/smallvenue/account/decrement/DecrementDTO-sub-account.json", BenefitDecrementDTO.class);
        assertThat(dto).isNotNull();

        SmallVenueAccountServiceImpl spy = spy(service);
        Account account = new Account();
        account.setCardNo("11");
        account.setId(1L);
        account.setStatus(AccountStatusEnum.NORMAL.getStatus());

        doReturn(Optional.of(account)).when(spy).get(anyLong(), anyLong(), anyString());

        MainAccountTuple mainAccountTuple = spy.getAndCheckAccountTuple(dto);

        assertThat(mainAccountTuple).isNotNull();
        assertThat(mainAccountTuple.getAccount()).isNotNull();
        assertThat(mainAccountTuple.getAccount().getCardNo()).isEqualTo("11");
        assertThat(mainAccountTuple.getMainAccount()).isNotNull();
        assertThat(mainAccountTuple.getMainAccount().getCardNo()).isEqualTo("11");
        assertThat(mainAccountTuple.getMainAccount().getId()).isEqualTo(1L);

    }

    @Test
    void getAndCheckAccountTupleNull() {
        assertThrows(BusinessException.class, () -> {
            BenefitDecrementDTO dto = ResourceUtil.readJson("asset/smallvenue/account/decrement/DecrementDTO-sub-account.json", BenefitDecrementDTO.class);
            assertThat(dto).isNotNull();

            when(smallVenueAccountRepository.listAccountWithDefault(anyLong(), anyLong(), anyString())).thenReturn(Lists.newArrayList(new Account()));

            service.getAndCheckAccountTuple(dto);
        });
    }

    @Test
    void decreaseAccountBenefit() {
        BenefitDecrementDTO dto = ResourceUtil.readJson("asset/smallvenue/account/decrement/DecrementDTO.json", BenefitDecrementDTO.class);
        assertThat(dto).isNotNull();
        List<AccountBenefit> benefits = ResourceUtil.readJson("asset/smallvenue/account/decrement/benefits.json",
                new TypeReference<List<AccountBenefit>>() {
                });
        assertThat(benefits).isNotNull();
        List<SmallVenueStoredStatistics> statistics = ResourceUtil.readJson("asset/smallvenue/account/decrement/statistics.json",
                new TypeReference<List<SmallVenueStoredStatistics>>() {
                });
        assertThat(statistics).isNotNull();

        when(smallVenueAccountRepository.listAccountWithDefault(anyLong(), anyLong(), anyString())).thenReturn(mockAccountsFromFile());
        when(smallVenueAccountRepository.selectBenefit(anyLong(), anyList())).thenReturn(benefits);
        when(smallVenueAccountRepository.selectStatistics(anyLong(), anyLong(), anyList())).thenReturn(statistics);

        ArgumentCaptor<Long> abIdCapture = ArgumentCaptor.forClass(Long.class);
        ArgumentCaptor<BigDecimal> balanceCapture = ArgumentCaptor.forClass(BigDecimal.class);
        doNothing().when(smallVenueAccountRepository).updateAccountBenefit(anyLong(), abIdCapture.capture(), balanceCapture.capture(), any(), any());

        ArgumentCaptor<SmallVenueStoredStatistics> statisticsArgumentCaptor = ArgumentCaptor.forClass(SmallVenueStoredStatistics.class);
        doNothing().when(smallVenueAccountRepository).updateAccountStatistics(anyLong(), statisticsArgumentCaptor.capture());

        service.decreaseAccountBenefit(dto);

        assertThat(abIdCapture.getValue()).isEqualTo(931860053026312192L);
        assertThat(balanceCapture.getValue()).isEqualTo(new BigDecimal("19.00"));
        assertThat(statisticsArgumentCaptor.getValue().getBalance()).isEqualTo(new BigDecimal("19.00"));
    }

    @Test
    void decreaseAccountBenefit2() {
        assertThrows(BusinessException.class, () -> {
            BenefitDecrementDTO dto = ResourceUtil.readJson("asset/smallvenue/account/decrement/DecrementDTO.json", BenefitDecrementDTO.class);
            assertThat(dto).isNotNull();

            when(smallVenueAccountRepository.listAccountWithDefault(anyLong(), anyLong(), anyString())).thenReturn(Lists.newArrayList(new Account()));

            service.decreaseAccountBenefit(dto);
        });
    }

    @DisplayName("减扣权益-消费-额度")
    @ParameterizedTest(name = "{index} {0}")
    @EnumSource(value = AccountRecordTypeEnum.class, names = {
            "SV_EQUIPMENT_CONSUMPTION",
            "SV_STORED_VALUE_DEDUCTION",
            "SV_EXCHANGE"
    })
    void generateUpdatedStatistics4Decrement(AccountRecordTypeEnum recordType) {
        BenefitDecrementDTO.BenefitDetailsDTO detail = new BenefitDecrementDTO.BenefitDetailsDTO();
        detail.setAccountBenefitId(1L);
        detail.setNum(new BigDecimal("-1.00"));
        detail.setNumType(AccountBenefitNumTypeEnum.AMOUNT.getCode());

        SmallVenueStoredStatistics accountStatistics = new SmallVenueStoredStatistics();
        accountStatistics.setBalance(new BigDecimal("10.00"));
        accountStatistics.setTotalConsume(new BigDecimal("1.00"));

        SmallVenueStoredStatistics updatedStatistics = SmallVenueAccountServiceImpl.generateUpdatedStatistics4Decrement(detail, accountStatistics, null, recordType.getCode());

        assertThat(updatedStatistics.getBalance()).isEqualTo(new BigDecimal("9.00"));
        assertThat(updatedStatistics.getTotalConsume()).isEqualTo(new BigDecimal("2.00"));
        assertThat(accountStatistics.getBalance()).isEqualTo(new BigDecimal("9.00"));
        assertThat(accountStatistics.getTotalConsume()).isEqualTo(new BigDecimal("2.00"));
    }


    @DisplayName("减扣权益-消费-频次-部分消费")
    @ParameterizedTest(name = "{index} {0}")
    @EnumSource(value = AccountRecordTypeEnum.class, names = {
            "SV_EQUIPMENT_CONSUMPTION",
            "SV_STORED_VALUE_DEDUCTION",
            "SV_EXCHANGE"
    })
    void generateUpdatedStatistics4DecrementFrequency(AccountRecordTypeEnum recordType) {
        BenefitDecrementDTO.BenefitDetailsDTO detail = new BenefitDecrementDTO.BenefitDetailsDTO();
        detail.setAccountBenefitId(1L);
        detail.setNum(new BigDecimal("-1.00"));
        detail.setNumType(AccountBenefitNumTypeEnum.FREQUENCY.getCode());

        SmallVenueStoredStatistics accountStatistics = new SmallVenueStoredStatistics();
        accountStatistics.setTotal(new BigDecimal(10));
        accountStatistics.setBalance(new BigDecimal(10));
        accountStatistics.setTotalConsume(new BigDecimal(0));
        accountStatistics.setTotalNum(1);
        accountStatistics.setRemainNum(1);
        accountStatistics.setTotalNumConsume(0);

        AccountBenefit accountBenefit = new AccountBenefit();
        accountBenefit.setBalance(new BigDecimal(10));

        SmallVenueStoredStatistics updatedStatistics = SmallVenueAccountServiceImpl.generateUpdatedStatistics4Decrement(detail, accountStatistics, accountBenefit, recordType.getCode());

        assertThat(updatedStatistics.getBalance().intValue()).isEqualTo(9);
        assertThat(updatedStatistics.getTotalConsume().intValue()).isEqualTo(1);
        assertThat(accountStatistics.getRemainNum()).isEqualTo(1);
        assertThat(accountStatistics.getTotalNumConsume()).isEqualTo(0);
        assertThat(accountStatistics.getTotalNum()).isEqualTo(1);
    }


    @DisplayName("减扣权益-消费-频次-整卡消费")
    @ParameterizedTest(name = "{index} {0}")
    @EnumSource(value = AccountRecordTypeEnum.class, names = {
            "SV_EQUIPMENT_CONSUMPTION",
            "SV_STORED_VALUE_DEDUCTION",
            "SV_EXCHANGE"
    })
    void generateUpdatedStatistics4DecrementFrequency2(AccountRecordTypeEnum recordType) {
        BenefitDecrementDTO.BenefitDetailsDTO detail = new BenefitDecrementDTO.BenefitDetailsDTO();
        detail.setAccountBenefitId(1L);
        detail.setNum(new BigDecimal("-10.00"));
        detail.setNumType(AccountBenefitNumTypeEnum.FREQUENCY.getCode());

        SmallVenueStoredStatistics accountStatistics = new SmallVenueStoredStatistics();
        accountStatistics.setTotal(new BigDecimal(10));
        accountStatistics.setBalance(new BigDecimal(10));
        accountStatistics.setTotalConsume(new BigDecimal(0));
        accountStatistics.setTotalNum(1);
        accountStatistics.setRemainNum(1);
        accountStatistics.setTotalNumConsume(0);

        AccountBenefit accountBenefit = new AccountBenefit();
        accountBenefit.setBalance(new BigDecimal(0));

        SmallVenueStoredStatistics updatedStatistics = SmallVenueAccountServiceImpl.generateUpdatedStatistics4Decrement(detail, accountStatistics, accountBenefit, recordType.getCode());

        assertThat(updatedStatistics.getBalance().intValue()).isEqualTo(0);
        assertThat(updatedStatistics.getTotalConsume().intValue()).isEqualTo(10);
        assertThat(updatedStatistics.getRemainNum()).isEqualTo(0);
        assertThat(updatedStatistics.getTotalNumConsume()).isEqualTo(1);
        assertThat(accountStatistics.getRemainNum()).isEqualTo(0);
        assertThat(accountStatistics.getTotalNumConsume()).isEqualTo(1);
    }

    @DisplayName("减扣权益-退单-额度")
    @ParameterizedTest(name = "{index} {0}")
    @EnumSource(value = AccountRecordTypeEnum.class, names = {
            "SV_REFUND",
            "SV_GIFT_REFUND"
    })
    void generateUpdatedStatistics4DecrementRefund(AccountRecordTypeEnum recordType) {
        BenefitDecrementDTO.BenefitDetailsDTO detail = new BenefitDecrementDTO.BenefitDetailsDTO();
        detail.setAccountBenefitId(1L);
        detail.setNum(new BigDecimal("-10.00"));
        detail.setNumType(AccountBenefitNumTypeEnum.AMOUNT.getCode());

        SmallVenueStoredStatistics accountStatistics = new SmallVenueStoredStatistics();
        accountStatistics.setBalance(new BigDecimal("10.00"));

        AccountBenefit accountBenefit = new AccountBenefit();
        accountBenefit.setBalance(new BigDecimal(10));

        SmallVenueStoredStatistics updatedStatistics = SmallVenueAccountServiceImpl.generateUpdatedStatistics4Decrement(detail, accountStatistics, accountBenefit, recordType.getCode());

        assertThat(updatedStatistics.getBalance()).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    void shouldNotChangeNum() {
        AccountBenefit ab = new AccountBenefit();
        ab.setBalance(new BigDecimal("1.5"));
        BenefitDecrementDTO.BenefitDetailsDTO detail = new BenefitDecrementDTO.BenefitDetailsDTO();
        detail.setNumType(2);
        boolean changeNum = SmallVenueAccountServiceImpl.shouldChangeNum(detail, ab, new BigDecimal("-1"));
        assertThat(changeNum).isFalse();
    }

    @Test
    void shouldChangeNum() {
        AccountBenefit ab = new AccountBenefit();
        ab.setBalance(new BigDecimal("0"));
        BenefitDecrementDTO.BenefitDetailsDTO detail = new BenefitDecrementDTO.BenefitDetailsDTO();
        detail.setNumType(2);
        boolean changeNum = SmallVenueAccountServiceImpl.shouldChangeNum(detail, ab, new BigDecimal("-1.5"));
        assertThat(changeNum).isTrue();
    }

    @Test
    void getTransferAccountTuple() {
        CardTransferDTO dto = ResourceUtil.readJson("asset/smallvenue/account/transfer/transfer.json", CardTransferDTO.class);
        assertThat(dto).isNotNull();

        Account a1 = new Account();
        a1.setCardNo("*********");
        Account a2 = new Account();
        a2.setCardNo("*********");
        when(smallVenueAccountRepository.selectAccount(anyLong(), anyLong(), anyList())).thenReturn(Lists.newArrayList(a1, a2));

        AccountTransferTuple accountTuple = service.getAccountTuple(dto);

        assertThat(accountTuple.getOutAccount()).isNotNull();
        assertThat(accountTuple.getOutAccount().getCardNo()).isEqualTo("*********");
        assertThat(accountTuple.getInAccount()).isNotNull();
        assertThat(accountTuple.getInAccount().getCardNo()).isEqualTo("*********");
    }

    @Test
    void getTransferAccountTupleThrow() {
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            CardTransferDTO dto = ResourceUtil.readJson("asset/smallvenue/account/transfer/transfer.json", CardTransferDTO.class);
            assertThat(dto).isNotNull();

            when(smallVenueAccountRepository.selectAccount(anyLong(), anyLong(), anyList())).thenReturn(Lists.newArrayList(new Account()));

            service.getAccountTuple(dto);
        });
        assertThat(exception.getMessage()).isEqualTo("找不到对应的账户记录");
    }

    @DisplayName("transferUnlimitedAmountTypeBenefit: 无限期权益转账")
    @Test
    void transferUnlimitedCategoryBenefit() {
        CardTransferDTO dto = ResourceUtil.readJson("asset/smallvenue/account/transfer/transfer.json", CardTransferDTO.class);
        List<Account> accounts = ResourceUtil.readJson("asset/smallvenue/account/transfer/account.json", new TypeReference<List<Account>>() {
        });
        List<AccountBenefit> outBenefits = ResourceUtil.readJson("asset/smallvenue/account/transfer/out-benefit.json", new TypeReference<List<AccountBenefit>>() {
        });
        Map<String, AccountBenefit> inBenefits = ResourceUtil.readJson("asset/smallvenue/account/transfer/in-benefit.json", new TypeReference<List<AccountBenefit>>() {
                })
                .stream()
                .collect(Collectors.toMap(SmallVenueAccountServiceImpl::compositeKey, Function.identity()));

        when(smallVenueAccountRepository.selectAccount(anyLong(), anyLong(), anyList())).thenReturn(accounts);


        SmallVenueAccountServiceImpl spy = spy(service);
        AccountTransferTuple accountTuple = spy.getAccountTuple(dto);
        List<AccountRecord> records = spy.transferUnlimitedAmountTypeBenefit(outBenefits, inBenefits, new BigDecimal("5.00"), accountTuple, null, new Date());

        assertThat(records.size()).isEqualTo(2);
        assertThat(records.stream().anyMatch(record -> StringUtils.equals(record.getCardNo(), "*********"))).isTrue();
        assertThat(records.stream().anyMatch(record -> StringUtils.equals(record.getCardNo(), "*********"))).isTrue();

        records.forEach(record -> {
            if (Objects.equals(record.getCardNo(), "*********")) {
                assertThat(record.getInitialBenefit()).isEqualTo(new BigDecimal("10.00"));
                assertThat(record.getOriginalBenefit()).isEqualTo(new BigDecimal("5.00"));
                assertThat(record.getMode()).isEqualTo(MemberGrowRecordModeEnum.DECREMENT.getMode());
            }
            if (Objects.equals(record.getCardNo(), "*********")) {
                assertThat(record.getInitialBenefit()).isEqualTo(new BigDecimal("10.00"));
                assertThat(record.getOriginalBenefit()).isEqualTo(new BigDecimal("5.00"));
                assertThat(record.getMode()).isEqualTo(MemberGrowRecordModeEnum.INCREMENT.getMode());
            }
        });
    }

    @Disabled("not ready yet")
    @DisplayName("transferAnotherBenefit: 非无限期权益转账")
    @Test
    void transferLimitedCategoryBenefit() {
        service.transferAnotherBenefit(Lists.newArrayList(), null, null, null, null);
    }

    @SuppressWarnings("unchecked")
    @Test
    void transfer() {
        CardTransferDTO dto = ResourceUtil.readJson("asset/smallvenue/account/transfer/transfer.json", CardTransferDTO.class);
        List<Account> accounts = ResourceUtil.readJson("asset/smallvenue/account/transfer/account.json", new TypeReference<List<Account>>() {
        });
        List<AccountBenefit> outBenefits = ResourceUtil.readJson("asset/smallvenue/account/transfer/out-benefit.json", new TypeReference<List<AccountBenefit>>() {
        });
        List<AccountBenefit> inBenefits = ResourceUtil.readJson("asset/smallvenue/account/transfer/in-benefit.json", new TypeReference<List<AccountBenefit>>() {
        });
        SmallVenueAccountServiceImpl spy = spy(service);

        when(smallVenueAccountRepository.selectAccount(anyLong(), anyLong(), anyList())).thenReturn(accounts);
        when(smallVenueAccountRepository.selectTransferOutBenefit(anyLong(), anyLong(), anyLong(), anyList())).thenReturn(outBenefits);
        when(smallVenueAccountRepository.selectBenefit(anyLong(), anyLong(), anyLong(), anyList(), eq(ExpiryDateCategoryEnum.NO_LIMIT))).thenReturn(inBenefits);

        ArgumentCaptor<List<AccountRecord>> recordCaptor = ArgumentCaptor.forClass(List.class);
        when(smallVenueAccountRepository.batchSaveRecord(recordCaptor.capture())).thenReturn(true);

        spy.transfer(dto);

        ArgumentCaptor<List<AccountBenefit>> benefitCaptor = ArgumentCaptor.forClass(List.class);
        verify(spy, times(1)).transferUnlimitedAmountTypeBenefit(benefitCaptor.capture(), anyMap(), any(BigDecimal.class), any(AccountTransferTuple.class), anyLong(), any(Date.class));

        assertThat(benefitCaptor.getValue().size()).isEqualTo(2);
        assertThat(benefitCaptor.getValue().get(0).getTotal()).isEqualTo(new BigDecimal("10.00"));
        assertThat(benefitCaptor.getValue().get(0).getBalance()).isEqualTo(new BigDecimal("10.00"));
        assertThat(benefitCaptor.getValue().get(1).getTotal()).isEqualTo(new BigDecimal("10.00"));
        assertThat(benefitCaptor.getValue().get(1).getBalance()).isEqualTo(new BigDecimal("10.00"));
        assertThat(benefitCaptor.getValue().size()).isEqualTo(2);

        List<AccountRecord> records = recordCaptor.getValue();
        assertThat(records.size()).isEqualTo(4);
        assertThat(records.stream().anyMatch(record -> StringUtils.equals(record.getCardNo(), "*********"))).isTrue();
        assertThat(records.stream().anyMatch(record -> StringUtils.equals(record.getCardNo(), "*********"))).isTrue();
        records.forEach(record -> {
            if (Objects.equals(record.getCardNo(), "*********")) {
                if (record.getAccountBenefitId() == 935487035475722240L) {
                    assertThat(record.getInitialBenefit()).isEqualTo(new BigDecimal("10.00"));
                    assertThat(record.getOriginalBenefit()).isEqualTo(new BigDecimal("10.00"));
                    assertThat(record.getMode()).isEqualTo(MemberGrowRecordModeEnum.DECREMENT.getMode());
                }
                if (record.getAccountBenefitId() == 935487035559608320L) {
                    assertThat(record.getInitialBenefit()).isEqualTo(new BigDecimal("10.00"));
                    assertThat(record.getOriginalBenefit()).isEqualTo(new BigDecimal("5.00"));
                    assertThat(record.getMode()).isEqualTo(MemberGrowRecordModeEnum.DECREMENT.getMode());
                }
            }
            if (Objects.equals(record.getCardNo(), "*********")) {
                if (Objects.equals(record.getAccountBenefitId(), 935487035756740608L)) {
                    assertThat(record.getInitialBenefit()).isEqualTo(new BigDecimal("10.00"));
                    assertThat(record.getOriginalBenefit()).isEqualTo(new BigDecimal("10.00"));
                    assertThat(record.getMode()).isEqualTo(MemberGrowRecordModeEnum.INCREMENT.getMode());
                } else {
                    assertThat(record.getInitialBenefit()).isEqualTo(BigDecimal.ZERO);
                    assertThat(record.getOriginalBenefit()).isEqualTo(new BigDecimal("5.00"));
                    assertThat(record.getMode()).isEqualTo(MemberGrowRecordModeEnum.INCREMENT.getMode());
                }
            }
        });
    }

    @Test
    void cancellation() {

    }

    @DisplayName("sortedMapList: 非无限期权益排序")
    @Test
    void sortedMapList() {
        Map<Long, List<AccountBenefit>> map = SmallVenueAccountServiceImpl.sortedMapList(mockBenefit());

        assertThat(map.get(1L).size()).isEqualTo(2);
        assertThat(map.get(2L).size()).isEqualTo(1);
        assertThat(map.get(3L).size()).isEqualTo(3);
        LocalDateTime d11 = DateTimeUtils.parse(map.get(1L).get(0).getDownTime(), "yyyy-MM-dd HH:mm:ss");
        LocalDateTime d12 = DateTimeUtils.parse(map.get(1L).get(1).getDownTime(), "yyyy-MM-dd HH:mm:ss");
        assertThat(d11.isBefore(d12)).isTrue();
        LocalTime d31 = LocalTime.parse(map.get(3L).get(0).getDownTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));
        LocalTime d32 = LocalTime.parse(map.get(3L).get(1).getDownTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));
        LocalTime d33 = LocalTime.parse(map.get(3L).get(2).getDownTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));
        assertThat(d31.isBefore(d32)).isTrue();
        assertThat(d31.isBefore(d33)).isTrue();
        assertThat(d32.isBefore(d33)).isTrue();
        assertThat(d33.isAfter(d31)).isTrue();
    }
    @DisplayName("sortedMapList: 有生效时间，无过期时间排序")
    @Test
    void sortedMapList3() {
        Map<Long, List<AccountBenefit>> map = SmallVenueAccountServiceImpl.sortedMapList(mockBenefit3());
        assertThat(map.get(180L).size()).isEqualTo(2);
        LocalDateTime d11 = DateTimeUtils.parse(map.get(180L).get(0).getUpTime(), "yyyy-MM-dd HH:mm:ss");
        LocalDateTime d12 = DateTimeUtils.parse(map.get(180L).get(1).getUpTime(), "yyyy-MM-dd HH:mm:ss");
        assertThat(d11.isBefore(d12)).isTrue();
    }

    @DisplayName("sortedMapList: 混合时间段排序")
    @Test
    void sortedMapList4() {
        Map<Long, List<AccountBenefit>> map = SmallVenueAccountServiceImpl.sortedMapList(mockBenefit4());
        assertThat(map.get(180L).size()).isEqualTo(3);
        assertThat(map.get(180L).get(0).getId()).isEqualTo(985188791158263808L);
        assertThat(map.get(180L).get(1).getId()).isEqualTo(983830229119094784L);
        assertThat(map.get(180L).get(2).getId()).isEqualTo(985188791158263809L);
    }

    @Test
    void sortedMapList2() {
        assertDoesNotThrow(() -> {
            Map<Long, List<AccountBenefit>> map = SmallVenueAccountServiceImpl.sortedMapList(mockOutBenefit());
            assertThat(map.get(66L).size()).isEqualTo(3);
        });
    }

    @DisplayName("sortedMapList: 非无限期权益排序-null 默认值")
    @Test
    void sortedMapListNull() {
        assertDoesNotThrow(() -> {
            List<AccountBenefit> list = mockBenefit2();
            Map<Long, List<AccountBenefit>> map = SmallVenueAccountServiceImpl.sortedMapList(list);

            assertThat((map.get(1L).get(0).getDownTime())).isEqualTo("2020-10-10 00:00:00");
            assertThat((map.get(1L).get(1).getDownTime())).isBlank();
            assertThat((map.get(2L).get(0).getDownTime())).isEqualTo("03:00:00");
            assertThat((map.get(2L).get(1).getDownTime())).isBlank();
        });
    }

    @Test
    void getBalanceSumGroupMap() {
        Map<Long, BigDecimal> sumGroupMap = SmallVenueAccountServiceImpl.getBalanceSumGroupMap(mockBenefit(), AccountBenefit::getMerchantBenefitClassifyId);

        assertThat(sumGroupMap.size()).isEqualTo(3);
    }

    @Test
    void calcRechargeOrder() {
        List<AccountRecord> records = ResourceUtil.readJson("asset/smallvenue/account/record/consumption-order.json", new TypeReference<List<AccountRecord>>() {
        });
        assert records != null;
        List<BenefitRechargeSummaryDTO> consumptions = SmallVenueAccountServiceImpl.calcRecharge(records, false);
        assertThat(consumptions.size()).isEqualTo(2);
        assertThat(consumptions.get(0).getOriginBalance()).isEqualTo(new BigDecimal("60.00"));
        assertThat(consumptions.get(0).getRechargeBalance()).isEqualTo(new BigDecimal("90.00"));
        assertThat(consumptions.get(0).getBalance()).isEqualTo(new BigDecimal("150.00"));
        assertThat(consumptions.get(0).getOriginNum()).isNull();
        assertThat(consumptions.get(0).getRechargeNum()).isNull();
        assertThat(consumptions.get(1).getOriginBalance()).isEqualTo(new BigDecimal("0.00"));
        assertThat(consumptions.get(1).getRechargeBalance()).isEqualTo(new BigDecimal("20.00"));
        assertThat(consumptions.get(1).getOriginNum()).isEqualTo(new BigDecimal("0.00"));
        assertThat(consumptions.get(1).getRechargeNum().intValue()).isEqualTo(2);
        assertThat(consumptions.get(1).getNum().intValue()).isEqualTo(2);
    }

    @Test
    void calcConsumptionOrder() {
        List<AccountRecord> records = ResourceUtil.readJson("asset/smallvenue/account/record/consumption-order.json", new TypeReference<List<AccountRecord>>() {
        });
        assert records != null;
        List<BenefitConsumptionSummaryDTO> consumptions = SmallVenueAccountServiceImpl.calcConsumption(records, Lists.newArrayList(100L), false);
        assertThat(consumptions.size()).isEqualTo(1);
        assertThat(consumptions.get(0).getConsumption()).isEqualTo(new BigDecimal("50.00"));
        assertThat(consumptions.get(0).getOriginBalance()).isEqualTo(new BigDecimal("100.00"));
        assertThat(consumptions.get(0).getPresentation()).isEqualTo(new BigDecimal("10.00"));
        assertThat(consumptions.get(0).getBalance()).isEqualTo(new BigDecimal("60.00"));
    }

    @Test
    void calcRechargeRefund() {
        List<AccountRecord> records = ResourceUtil.readJson("asset/smallvenue/account/record/consumption-refund.json", new TypeReference<List<AccountRecord>>() {
        });
        assert records != null;
        List<BenefitRechargeSummaryDTO> consumptions = SmallVenueAccountServiceImpl.calcRecharge(records, true);
        assertThat(consumptions.size()).isEqualTo(2);
        assertThat(consumptions.get(0).getOriginBalance()).isEqualTo(new BigDecimal("150.00"));
        assertThat(consumptions.get(0).getRechargeBalance()).isEqualTo(new BigDecimal("90.00"));
        assertThat(consumptions.get(0).getBalance()).isEqualTo(new BigDecimal("60.00"));
        assertThat(consumptions.get(0).getOriginNum()).isNull();
        assertThat(consumptions.get(0).getRechargeNum()).isNull();
        assertThat(consumptions.get(1).getOriginBalance()).isEqualTo(new BigDecimal("20.00"));
        assertThat(consumptions.get(1).getRechargeBalance()).isEqualTo(new BigDecimal("20.00"));
        assertThat(consumptions.get(1).getOriginNum()).isEqualTo(new BigDecimal("2.00"));
        assertThat(consumptions.get(1).getRechargeNum().intValue()).isEqualTo(2);
        assertThat(consumptions.get(1).getNum().intValue()).isEqualTo(0);
    }

    @Test
    void calcConsumptionRefund() {
        List<AccountRecord> records = ResourceUtil.readJson("asset/smallvenue/account/record/consumption-refund.json", new TypeReference<List<AccountRecord>>() {
        });
        assert records != null;
        List<BenefitConsumptionSummaryDTO> consumptions = SmallVenueAccountServiceImpl.calcConsumption(records, Lists.newArrayList(100L), true);
        assertThat(consumptions.size()).isEqualTo(1);
        assertThat(consumptions.get(0).getConsumption()).isEqualTo(new BigDecimal("50.00"));
        assertThat(consumptions.get(0).getOriginBalance()).isEqualTo(new BigDecimal("50.00"));
        assertThat(consumptions.get(0).getPresentation()).isEqualTo(new BigDecimal("10.00"));
        assertThat(consumptions.get(0).getBalance()).isEqualTo(new BigDecimal("90.00"));
    }

    @DisplayName("兑换-权益消耗明细")
    @Test
    void calcExchangeOrder() {
        List<AccountRecord> records = ResourceUtil.readJson("asset/smallvenue/account/record/exchange/order.json", new TypeReference<List<AccountRecord>>() {
        });
        assert records != null;
        List<BenefitConsumptionSummaryDTO> consumptions = SmallVenueAccountServiceImpl.calcConsumption(records, Lists.newArrayList(), false);
        assertThat(consumptions.size()).isEqualTo(1);
        assertThat(consumptions.get(0).getConsumption()).isEqualTo(new BigDecimal("1.00"));
        assertThat(consumptions.get(0).getOriginBalance()).isEqualTo(new BigDecimal("4036.00"));
        assertThat(consumptions.get(0).getPresentation()).isNull();
        assertThat(consumptions.get(0).getBalance()).isEqualTo(new BigDecimal("4035.00"));
        assertThat(consumptions.get(0).getMerchantBenefitClassifyName()).isEqualTo("储值币");
    }

    @DisplayName("退兑换-权益消耗明细")
    @Test
    void calcExchangeConsumptionRefund() {
        List<AccountRecord> records = ResourceUtil.readJson("asset/smallvenue/account/record/exchange/refund.json", new TypeReference<List<AccountRecord>>() {
        });
        assert records != null;
        List<BenefitConsumptionSummaryDTO> consumptions = SmallVenueAccountServiceImpl.calcConsumption(records, Lists.newArrayList(), true);
        assertThat(consumptions.size()).isEqualTo(1);
        assertThat(consumptions.get(0).getConsumption()).isEqualTo(new BigDecimal("1.00"));
        assertThat(consumptions.get(0).getOriginBalance()).isEqualTo(new BigDecimal("4034.00"));
        assertThat(consumptions.get(0).getPresentation()).isNull();
        assertThat(consumptions.get(0).getBalance()).isEqualTo(new BigDecimal("4035.00"));
        assertThat(consumptions.get(0).getMerchantBenefitClassifyName()).isEqualTo("储值币");
    }

    @DisplayName("礼品回收-权益消耗明细")
    @Test
    void calcGiftRechargeOrder() {

        List<AccountRecord> records = ResourceUtil.readJson("asset/smallvenue/account/record/gift/order.json", new TypeReference<List<AccountRecord>>() {
        });
        assert records != null;
        List<BenefitRechargeSummaryDTO> summary = SmallVenueAccountServiceImpl.calcRecharge(records, false);
        assertThat(summary.size()).isEqualTo(1);
        assertThat(summary.get(0).getOriginBalance()).isEqualTo(new BigDecimal("4686.00"));
        assertThat(summary.get(0).getRechargeBalance()).isEqualTo(new BigDecimal("40.00"));
        assertThat(summary.get(0).getBalance()).isEqualTo(new BigDecimal("4726.00"));
        assertThat(summary.get(0).getOriginNum()).isNull();
        assertThat(summary.get(0).getRechargeNum()).isNull();
    }


    @DisplayName("退礼品回收-权益消耗明细")
    @Test
    void calcGiftRechargeRefund() {
        List<AccountRecord> records = ResourceUtil.readJson("asset/smallvenue/account/record/gift/refund.json", new TypeReference<List<AccountRecord>>() {
        });
        assert records != null;
        List<BenefitRechargeSummaryDTO> summary = SmallVenueAccountServiceImpl.calcRecharge(records, true);
        assertThat(summary.size()).isEqualTo(1);
        assertThat(summary.get(0).getOriginBalance()).isEqualTo(new BigDecimal("4626.00"));
        assertThat(summary.get(0).getRechargeBalance()).isEqualTo(new BigDecimal("40.00"));
        assertThat(summary.get(0).getBalance()).isEqualTo(new BigDecimal("4586.00"));
        assertThat(summary.get(0).getOriginNum()).isNull();
        assertThat(summary.get(0).getRechargeNum()).isNull();
    }

    @Test
    void updateAccountStatisticMapValueIncrement() {
        List<AccountBenefit> abs = getMockAccountBenefits();

        Map<Long, SmallVenueAccountServiceImpl.AccountStatistic> map = new HashMap<>();
        SmallVenueAccountServiceImpl.AccountStatistic as1 = new SmallVenueAccountServiceImpl.AccountStatistic();
        as1.setInitialBalance(new BigDecimal(100));
        map.put(100L, as1);

        List<Integer> nums = Lists.newArrayList(30, 20, 10, 10);

        for (int i = 0; i < nums.size(); i++) {
            Integer num = nums.get(i);
            SmallVenueAccountServiceImpl.AccountStatistic statistic = SmallVenueAccountServiceImpl.getAccountStatistic(false, map, new BigDecimal(num), abs.get(i));
            if (i == 0) {
                assertThat(statistic.getNum().intValue()).isEqualTo(0);
                assertThat(statistic.getInitialBalance().intValue()).isEqualTo(0);
                assertThat(statistic.getTotalBalance().intValue()).isEqualTo(30);
            }
            else if (i == 2) {
                assertThat(statistic.getNum().intValue()).isEqualTo(1);
                assertThat(statistic.getInitialBalance().intValue()).isEqualTo(30);
                assertThat(statistic.getTotalBalance().intValue()).isEqualTo(40);
            }
            else if (i == 1) {
                assertThat(statistic.getNum()).isNull();
                assertThat(statistic.getInitialBalance().intValue()).isEqualTo(100);
                assertThat(statistic.getTotalBalance().intValue()).isEqualTo(120);
            }
            else if (i == 3) {
                assertThat(statistic.getNum()).isNull();
                assertThat(statistic.getInitialBalance().intValue()).isEqualTo(120);
                assertThat(statistic.getTotalBalance().intValue()).isEqualTo(130);
            }
        }
    }

    @Test
    void updateAccountStatisticMapValue() {
        List<AccountBenefit> abs = getMockAccountBenefits();

        Map<Long, SmallVenueAccountServiceImpl.AccountStatistic> map = new HashMap<>();
        SmallVenueAccountServiceImpl.AccountStatistic as1 = new SmallVenueAccountServiceImpl.AccountStatistic();
        as1.setInitialBalance(new BigDecimal(100));
        SmallVenueAccountServiceImpl.AccountStatistic as2 = new SmallVenueAccountServiceImpl.AccountStatistic();
        as2.setInitialBalance(new BigDecimal(20));
        as2.setNum(new BigDecimal(2));
        map.put(100L, as1);
        map.put(101L, as2);

        List<Integer> nums = Lists.newArrayList(-10, -20, -5, -10);

        System.out.println("map = " + map);
        for (int i = 0; i < nums.size(); i++) {
            Integer num = nums.get(i);
            SmallVenueAccountServiceImpl.updateAccountStatisticMapValue(map, new BigDecimal(num), abs.get(i), AdjustTypeEnum.DECREMENT);
            if (i == 0) {
                assertThat(map.get(101L).getNum().intValue()).isEqualTo(2);
                assertThat(map.get(101L).getInitialBalance().intValue()).isEqualTo(10);
            }
            else if (i == 2) {
                assertThat(map.get(101L).getNum().intValue()).isEqualTo(2);
                assertThat(map.get(101L).getInitialBalance().intValue()).isEqualTo(5);
            }
            else if (i == 1) {
                assertThat(map.get(100L).getNum()).isNull();
                assertThat(map.get(100L).getInitialBalance().intValue()).isEqualTo(80);
            }
            else if (i == 3) {
                assertThat(map.get(100L).getNum()).isNull();
                assertThat(map.get(100L).getInitialBalance().intValue()).isEqualTo(70);
            }
        }
    }


    private List<Account> mockAccounts() {
        Account account1 = new Account();
        account1.setCardNo("1");
        account1.setDefaultFlag(true);
        Account account2 = new Account();
        account2.setCardNo("2");
        return Lists.newArrayList(account1, account2);
    }

    private List<Account> mockAccountsFromFile() {
        return ResourceUtil.readJson("asset/smallvenue/account/increment/accounts.json", new TypeReference<List<Account>>() {
        });
    }

    private List<AccountBenefit> mockBenefit() {
        List<AccountBenefit> list = new ArrayList<>();
        AccountBenefit ab11 = new AccountBenefit();
        ab11.setId(1L);
        ab11.setMerchantBenefitClassifyId(1L);
        ab11.setExpiryDateCategory(ExpiryDateCategoryEnum.TIME_INTERVAL.getValue());
        ab11.setDownTime("2021-10-10 00:00:00");
        ab11.setBalance(new BigDecimal("10.00"));
        list.add(ab11);
        AccountBenefit ab12 = new AccountBenefit();
        ab12.setId(3L);
        ab12.setMerchantBenefitClassifyId(1L);
        ab12.setExpiryDateCategory(ExpiryDateCategoryEnum.TIME_INTERVAL.getValue());
        ab12.setDownTime("2020-10-10 00:00:00");
        ab12.setBalance(new BigDecimal("10.00"));
        list.add(ab12);

        AccountBenefit ab21 = new AccountBenefit();
        ab21.setId(4L);
        ab21.setMerchantBenefitClassifyId(2L);
        ab21.setDownTime("2021-12-10 00:00:00");
        ab21.setBalance(new BigDecimal("10.00"));
        ab21.setExpiryDateCategory(ExpiryDateCategoryEnum.TIME_INTERVAL.getValue());
        list.add(ab21);

        AccountBenefit ab31 = new AccountBenefit();
        ab31.setId(5L);
        ab31.setMerchantBenefitClassifyId(3L);
        ab31.setExpiryDateCategory(ExpiryDateCategoryEnum.ONE_DAY_TIME_INTERVAL.getValue());
        ab31.setDownTime("01:00:00");
        ab31.setBalance(new BigDecimal("10.00"));
        list.add(ab31);
        AccountBenefit ab32 = new AccountBenefit();
        ab32.setId(6L);
        ab32.setMerchantBenefitClassifyId(3L);
        ab32.setExpiryDateCategory(ExpiryDateCategoryEnum.ONE_DAY_TIME_INTERVAL.getValue());
        ab32.setDownTime("03:00:00");
        ab32.setBalance(new BigDecimal("12.00"));
        list.add(ab32);
        AccountBenefit ab33 = new AccountBenefit();
        ab33.setId(7L);
        ab33.setMerchantBenefitClassifyId(3L);
        ab33.setExpiryDateCategory(ExpiryDateCategoryEnum.ONE_DAY_TIME_INTERVAL.getValue());
        ab33.setDownTime("02:00:00");
        ab33.setBalance(new BigDecimal("30.00"));
        list.add(ab33);
        return list;
    }

    private List<AccountBenefit> mockBenefit3() {
        List<AccountBenefit> list = new ArrayList<>();
        AccountBenefit ab11 = new AccountBenefit();
        ab11.setId(983830229119094784L);
        ab11.setMerchantBenefitClassifyId(180L);
        ab11.setExpiryDateCategory(ExpiryDateCategoryEnum.TIME_INTERVAL.getValue());
        ab11.setUpTime("2022-06-07 20:30:04");
        ab11.setDownTime(null);
        ab11.setBalance(new BigDecimal("0.00"));
        ab11.setValueType(2);
        list.add(ab11);
        AccountBenefit ab12 = new AccountBenefit();
        ab12.setId(985188791158263808L);
        ab12.setMerchantBenefitClassifyId(180L);
        ab12.setExpiryDateCategory(ExpiryDateCategoryEnum.TIME_INTERVAL.getValue());
        ab12.setUpTime("2022-06-11 14:28:31");
        ab12.setDownTime(null);
        ab12.setBalance(new BigDecimal("96.00"));
        ab12.setValueType(2);
        list.add(ab12);

        return list;
    }
    private List<AccountBenefit> mockBenefit4() {
        List<AccountBenefit> list = new ArrayList<>();
        AccountBenefit ab11 = new AccountBenefit();
        ab11.setId(983830229119094784L);
        ab11.setMerchantBenefitClassifyId(180L);
        ab11.setExpiryDateCategory(ExpiryDateCategoryEnum.TIME_INTERVAL.getValue());
        ab11.setUpTime("2022-06-07 20:30:04");
        ab11.setDownTime(null);
        ab11.setBalance(new BigDecimal("0.00"));
        ab11.setValueType(2);
        list.add(ab11);
        AccountBenefit ab12 = new AccountBenefit();
        ab12.setId(985188791158263808L);
        ab12.setMerchantBenefitClassifyId(180L);
        ab12.setExpiryDateCategory(ExpiryDateCategoryEnum.ONE_DAY_TIME_INTERVAL.getValue());
        ab12.setUpTime("14:00:00");
        ab12.setDownTime("18:00:00");
        ab12.setBalance(new BigDecimal("20.00"));
        ab12.setValueType(2);
        list.add(ab12);
        AccountBenefit ab13 = new AccountBenefit();
        ab13.setId(985188791158263809L);
        ab13.setMerchantBenefitClassifyId(180L);
        ab13.setExpiryDateCategory(ExpiryDateCategoryEnum.NO_LIMIT.getValue());
        ab13.setBalance(new BigDecimal("20.00"));
        ab13.setValueType(2);
        list.add(ab13);

        return list;
    }

    private List<AccountBenefit> mockOutBenefit() {
        return ResourceUtil.readJson("asset/smallvenue/account/transfer/out-benefit-all.json", new TypeReference<List<AccountBenefit>>() {
        });
    }

    private List<AccountBenefit> mockBenefit2() {
        List<AccountBenefit> list = new ArrayList<>();
        AccountBenefit ab11 = new AccountBenefit();
        ab11.setId(1L);
        ab11.setMerchantBenefitClassifyId(1L);
        ab11.setExpiryDateCategory(ExpiryDateCategoryEnum.TIME_INTERVAL.getValue());
        list.add(ab11);
        AccountBenefit ab12 = new AccountBenefit();
        ab12.setId(2L);
        ab12.setMerchantBenefitClassifyId(1L);
        ab12.setExpiryDateCategory(ExpiryDateCategoryEnum.TIME_INTERVAL.getValue());
        ab12.setDownTime("2020-10-10 00:00:00");
        list.add(ab12);

        AccountBenefit ab21 = new AccountBenefit();
        ab21.setId(3L);
        ab21.setMerchantBenefitClassifyId(2L);
        ab21.setExpiryDateCategory(ExpiryDateCategoryEnum.ONE_DAY_TIME_INTERVAL.getValue());
        list.add(ab21);
        AccountBenefit ab22 = new AccountBenefit();
        ab21.setId(4L);
        ab22.setMerchantBenefitClassifyId(2L);
        ab22.setExpiryDateCategory(ExpiryDateCategoryEnum.ONE_DAY_TIME_INTERVAL.getValue());
        ab22.setDownTime("03:00:00");
        list.add(ab22);
        return list;
    }


    private List<AccountBenefit> getMockAccountBenefits() {
        AccountBenefit ab1 = new AccountBenefit();
        ab1.setMerchantBenefitClassifyId(101L);
        ab1.setValueType(2);

        AccountBenefit ab2 = new AccountBenefit();
        ab2.setMerchantBenefitClassifyId(100L);
        ab2.setValueType(1);

        AccountBenefit ab3 = new AccountBenefit();
        ab3.setMerchantBenefitClassifyId(101L);
        ab3.setValueType(2);

        AccountBenefit ab4 = new AccountBenefit();
        ab4.setMerchantBenefitClassifyId(100L);
        ab4.setValueType(1);
        return Lists.newArrayList(ab1, ab2, ab3, ab4);
    }

    static class IncreaseByAccountBenefitIdAnswer implements Answer<Boolean> {
        private Boolean result;

        @Override
        public Boolean answer(InvocationOnMock invocation) throws Throwable {
            this.result = (Boolean) invocation.callRealMethod();
            return this.result;
        }

        public Boolean getResult() {
            return result;
        }
    }

}