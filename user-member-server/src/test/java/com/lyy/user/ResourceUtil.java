package com.lyy.user;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2021/11/20
 */
@Slf4j
public class ResourceUtil {

    private ResourceUtil() {
    }

    @SuppressWarnings("rawtypes")
    public static <T> T readJson(String resource, Class<T> clazz) {
        try {
            File file = ResourceUtils.getFile("classpath:" + resource);
            String json = FileUtils.readFileToString(file);
            if (StringUtils.isBlank(json)) {
                log.warn("Json is blank");
                return null;
            }
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(json, clazz);
        } catch (IOException e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public static <T> T readJson(String resource, TypeReference<T> valueTypeRef) {
        try {
            File file = ResourceUtils.getFile("classpath:" + resource);
            String json = FileUtils.readFileToString(file);
            if (StringUtils.isBlank(json)) {
                log.warn("Json is blank");
                return null;
            }
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(json, valueTypeRef);
        } catch (IOException e) {
            log.error(e.getMessage());
            return null;
        }
    }

}
