package com.lyy.user.listener;

import cn.hutool.core.date.DateUtil;
import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.account.dto.BenefitAdjustMessage;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import com.lyy.user.infrastructure.listener.BenefitAdjustListener;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 权益增加监听测试类
 *
 * <AUTHOR>
 * @create 2021/9/7 14:59
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class BenefitAdjustListenerTest {

    @Resource
    private BenefitAdjustListener benefitAdjustListener;

    /**
     * 测试调整券
     */
    @Test
    public void testAdjustCoupon() throws InterruptedException {

        BenefitAdjustMessage benefitAdjustMessage = new BenefitAdjustMessage();
        benefitAdjustMessage.setAdjustType(AdjustTypeEnum.INCREMENT);
        benefitAdjustMessage.setClassify(14);
        benefitAdjustMessage.setAmount(BigDecimal.ONE);
        benefitAdjustMessage.setUserId(21751792L);
        benefitAdjustMessage.setMerchantId(1000319L);
        benefitAdjustMessage.setResource("智能引流自动派发");
        benefitAdjustMessage.setUpTime("2021-09-07 00:00:00");
        benefitAdjustMessage.setDownTime("2021-09-18 00:00:00");
        benefitAdjustMessage.setExpiryDateCategory(ExpiryDateCategoryEnum.TIME_INTERVAL);
        benefitAdjustMessage.setUpdateTime(DateUtil.parse("2021-09-07 14:20:00", "yyyy-MM-dd HH:mm:ss"));

        ExecutorService executorService =  Executors.newCachedThreadPool();
        for(int i= 0 ;i< 5;i++) {
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    benefitAdjustListener.benefitAdjust(benefitAdjustMessage);
                }
            });
        }
        Thread.sleep(10000);
        System.out.println("over");


    }

}
