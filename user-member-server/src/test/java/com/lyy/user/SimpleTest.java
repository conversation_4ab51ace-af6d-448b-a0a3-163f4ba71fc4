package com.lyy.user;

import com.google.gson.Gson;
import com.lyy.user.account.infrastructure.constant.AccountBenefitNumTypeEnum;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueMobileUserListSelectDTO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/4/16 10:39
 */
public class SimpleTest {

    public static void main(String[] args) {

        SmallVenueMobileUserListSelectDTO smallVenueUserListSelectDTO = new SmallVenueMobileUserListSelectDTO();


        smallVenueUserListSelectDTO.setKeyword("杨楠锋");
        smallVenueUserListSelectDTO.setMerchantId(1000319L);
        smallVenueUserListSelectDTO.setGroupTagIdList(Arrays.asList(1L,2L));
        smallVenueUserListSelectDTO.setOtherTagIdList(Arrays.asList(959154275198697472L));
        smallVenueUserListSelectDTO.setEquipmentTypeTagIdList(Arrays.asList(1L,2L));
        SmallVenueMobileUserListSelectDTO.SpecificFilterInfoDTO specificFilterInfoDTO = new SmallVenueMobileUserListSelectDTO.SpecificFilterInfoDTO();
        specificFilterInfoDTO.setTotalConsumeMoneyMin(0);
        specificFilterInfoDTO.setTotalConsumeMoneyMax(1000);
        specificFilterInfoDTO.setMemberLevelIdList(Arrays.asList(917341577502916608L));
        specificFilterInfoDTO.setUserTypeList(Arrays.asList("Z","A","W"));

        smallVenueUserListSelectDTO.setSpecificFilterInfoDTO(specificFilterInfoDTO);
        List<SmallVenueMobileUserListSelectDTO.MerchantBenefitClassifyDTO> merchantBenefitClassifyDTOList = new ArrayList<>();
        SmallVenueMobileUserListSelectDTO.MerchantBenefitClassifyDTO merchantBenefitClassifyDTO1 = new SmallVenueMobileUserListSelectDTO.MerchantBenefitClassifyDTO();
        merchantBenefitClassifyDTO1.setMerchantBenefitClassifyId(65L);
//        merchantBenefitClassifyDTO1.setMerchantBenefitClassifyName();
//        merchantBenefitClassifyDTO1.setNumType(AccountBenefitNumTypeEnum.FREQUENCY.getCode());
        merchantBenefitClassifyDTO1.setAmountMin(new BigDecimal("1"));
        merchantBenefitClassifyDTO1.setAmountMax(new BigDecimal("10"));
        merchantBenefitClassifyDTOList.add(merchantBenefitClassifyDTO1);
        smallVenueUserListSelectDTO.setMerchantBenefitClassifyDTOList(merchantBenefitClassifyDTOList);
        SmallVenueMobileUserListSelectDTO.SortInfoDTO sortInfoDTO = new SmallVenueMobileUserListSelectDTO.SortInfoDTO();
        sortInfoDTO.setLastConsumeTimeSort("asc");
        sortInfoDTO.setTotalConsumeMoneySort("desc");
        sortInfoDTO.setRegisterTimeSort("asc");
        smallVenueUserListSelectDTO.setSortInfoDTO(sortInfoDTO);

        smallVenueUserListSelectDTO.setPageIndex(1);
        smallVenueUserListSelectDTO.setPageSize(20);


        System.out.println(new Gson().toJson(smallVenueUserListSelectDTO));
    }


}
