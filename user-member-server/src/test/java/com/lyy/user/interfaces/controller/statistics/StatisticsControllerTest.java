package com.lyy.user.interfaces.controller.statistics;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.constant.TimeScopeEnum;
import com.lyy.user.account.infrastructure.statistics.dto.StatisticsUserQueryDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsConditionDTO;
import com.lyy.user.account.infrastructure.statistics.dto.UserStatisticsUpdateDTO;
import java.math.BigDecimal;
import java.util.Date;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

/**
 * <AUTHOR>
 * @create 2021/5/21 10:15
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class StatisticsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    /**
     * 跟新统计数据
     * @throws Exception
     */
    @Test
    public void updateStatistics() throws Exception{

        UserStatisticsUpdateDTO userStatisticsUpdateDTO = new UserStatisticsUpdateDTO();
        userStatisticsUpdateDTO.setMerchantId(1035814L);
        userStatisticsUpdateDTO.setUserId(33175796L);
        userStatisticsUpdateDTO.setBalanceAmount(new BigDecimal("1"));
        userStatisticsUpdateDTO.setUpdateTime(new Date());

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/statistics/update");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(userStatisticsUpdateDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }


    @Test
    public void getMerchantStatistics() throws Exception{

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/statistics/getMerchantStatistics")
                .param("merchantId","1000319");
        requestBuilder.header("Content-Type","application/json");

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void getStatisticsUserList() throws Exception{

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/statistics/queryStatisticsUserList");

        StatisticsUserQueryDTO statisticsUserQueryDTO = new StatisticsUserQueryDTO();
        statisticsUserQueryDTO.setPageSize(10);
        statisticsUserQueryDTO.setPageIndex(1);
        statisticsUserQueryDTO.setMerchantId(1000319L);
        statisticsUserQueryDTO.setTimeScope(TimeScopeEnum.YESTERDAY);
        statisticsUserQueryDTO.setTotalRechargeMoneySort("desc");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(statisticsUserQueryDTO));

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);

        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void cleanStatistics() throws Exception{

        UserStatisticsUpdateDTO userStatisticsUpdateDTO = new UserStatisticsUpdateDTO();
        userStatisticsUpdateDTO.setMerchantId(1000319L);
        userStatisticsUpdateDTO.setUserId(21751792L);
        userStatisticsUpdateDTO.setBalanceAmount(BigDecimal.ZERO);
        userStatisticsUpdateDTO.setUpdateTime(new Date());

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/statistics/clean");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(userStatisticsUpdateDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void initStatistics() throws Exception{


        JSONObject requestBody = new JSONObject();
        requestBody.put("merchantId","1000319");
        //requestBody.put("userId","21751792");

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/statistics/init");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(requestBody));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void clearStatistics() throws Exception{


        JSONObject requestBody = new JSONObject();
        requestBody.put("merchantId","1000319");
        requestBody.put("userId","21751792");

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/statistics/clear");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(requestBody));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void find() throws Exception{

        UserStatisticsConditionDTO userStatisticsConditionDTO = new UserStatisticsConditionDTO();
        userStatisticsConditionDTO.setMerchantId(1000319L);
        userStatisticsConditionDTO.setUserId(21757012L);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/statistics/find");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(userStatisticsConditionDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }
}
