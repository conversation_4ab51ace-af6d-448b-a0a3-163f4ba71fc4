package com.lyy.user.interfaces.controller.benefit;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.alibaba.fastjson.JSON;
import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.benefit.dto.ConsumeRuleSaveDTO;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

/**
 * <AUTHOR>
 * @create 2021/4/6 17:33
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class BenefitConsumeRuleControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    public void saveBenefitConsume() throws Exception{

        ConsumeRuleSaveDTO consumeRuleSaveDTO = new ConsumeRuleSaveDTO();
        consumeRuleSaveDTO.setMerchantId(1062179L);
        consumeRuleSaveDTO.setBenefitClassify(BenefitClassifyEnum.USER_RECHARGE_BALANCE);
        consumeRuleSaveDTO.setWeight(1);
        consumeRuleSaveDTO.setExpirePriority(1);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/benefit/consumeRule/save");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(consumeRuleSaveDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }


    @Test
    public void initMerchantBenefitConsume() throws Exception{
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/benefit/consume/initMerchantBenefitConsume");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.param("merchantId","11111");
        requestBuilder.param("operationId","10010");

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void batchMerchantBenefitConsume() throws Exception {
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/benefit/consume/batchMerchantBenefitConsume");
        requestBuilder.header("Content-Type","application/json");
        ConsumeRuleSaveDTO consumeRuleSaveDTO = new ConsumeRuleSaveDTO();
        List<Long> merchantIds = new ArrayList<>();
        merchantIds.add(1000319L);
        merchantIds.add(1061504L);
        merchantIds.add(1061541L);
        merchantIds.add(1061583L);
        consumeRuleSaveDTO.setMerchantIds(merchantIds);
        consumeRuleSaveDTO.setBenefitClassify(BenefitClassifyEnum.THIRD_PLATFORM_COINS);
        consumeRuleSaveDTO.setExpirePriority(0);
        consumeRuleSaveDTO.setIsDefault(false);
        consumeRuleSaveDTO.setWeight(6);
        requestBuilder.content(JSON.toJSONString(consumeRuleSaveDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

}
