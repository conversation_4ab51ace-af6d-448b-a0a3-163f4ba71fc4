package com.lyy.user.interfaces.schedule;

import com.lyy.user.interfaces.schedule.clear.Expired6mAccountBenefitClearJob;
import com.lyy.user.interfaces.schedule.clear.NotScopeExpiredBenefitClearJob;
import javax.annotation.Resource;
import org.junit.Ignore;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@Ignore
@SpringBootTest
@ExtendWith(SpringExtension.class)
class BenefitClearHandlerTest {

    @Resource
    private Expired6mAccountBenefitClearJob expired6mAccountBenefitClearJob;
    @Resource
    private NotScopeExpiredBenefitClearJob notScopeExpiredBenefitClearJob;

    @Test
    void expired6mAccountBenefitClear() throws Exception {
        Long merchantId = 1035824L;
        expired6mAccountBenefitClearJob.clearAccountBenefit(merchantId);
    }

    @Test
    void notScopeExpiredBenefitClear() throws Exception {
        Long merchantId = 1036973L;
        notScopeExpiredBenefitClearJob.clearBenefit(merchantId);
    }
}