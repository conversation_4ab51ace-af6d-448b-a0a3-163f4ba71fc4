package com.lyy.user.interfaces.controller.account;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountConditionDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AddSupplementaryCardDTO;
import com.lyy.user.account.infrastructure.account.dto.BenefitRollbackDTO;
import com.lyy.user.account.infrastructure.account.dto.ConsumeDTO;
import com.lyy.user.account.infrastructure.account.dto.PayRefundBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.PayRefundBenefitDetailDTO;
import com.lyy.user.account.infrastructure.account.dto.RecordBenefitScopeQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.SupplementaryCardCheckDTO;
import com.lyy.user.account.infrastructure.account.dto.SupplyAnonymousCardDataDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.AccountRecordInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.AvailableBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitDetailSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitIncrementItemDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.CardRenewalDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.HasSupplementaryCardDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MerchantBenefitIncrementDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalRecordSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.ReissueCardDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueAccountRecordSaveDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueRecordSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.UpdateCardStatusDTO;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class AccountControllerTest {


    private final Long lyyUserId_Jason =  ********L;


    private final Long USER_ID_1062066 =  ********L;
    private final Long MERCHANT_ID_1062066 =  1062066L;
    private final Long MERCHANT_USER_ID_1062066 =  857926184700755968L;
    private final Long MERCHANT_TAG_ID_1062066 =  1408307968310513665L;

    // 开发常用账户
    private final Long MERCHANT_ID_15018442322 =  1062179L;



    private final Long MERCHANT_ID_1000319 =  1000319L;

    private final Long MERCHANT_USER_ID =   Long.valueOf("891989026013724672");
    private final Long TAG_ID =  Long.valueOf("1400411232363614209");



    @Autowired
    private MockMvc mockMvc;

    @Test
    public void testRecordPage() throws Exception{
        AccountRecordQueryDTO accountRecordQueryDTO = new AccountRecordQueryDTO();
        accountRecordQueryDTO.setMerchantId(1000319L);
        //accountRecordQueryDTO.setMerchantUserId(855168610372943872L);
        accountRecordQueryDTO.setUserId(21744049L);
        //accountRecordQueryDTO.setOutTradeNoList(Arrays.asList("202202091716209742174404916052","202202091632586542174404968403","202202081412507972174404944435"));
        accountRecordQueryDTO.setRecordTypeList(Arrays.asList(9,73,23,50,59,114,112,132));
        accountRecordQueryDTO.setHasFreezeAndUnFreezeRecord(true);
        accountRecordQueryDTO.setPageIndex(1);
        accountRecordQueryDTO.setPageSize(20);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/record/listRecord");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(accountRecordQueryDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    @Test
    public void merchantAdjustBenefit() throws Exception{

        AccountBenefitAdjustDTO accountBenefitAdjustDTO = new AccountBenefitAdjustDTO();
        accountBenefitAdjustDTO.setAdjustType(AdjustTypeEnum.INCREMENT);
        accountBenefitAdjustDTO.setUserId(21751792L);
        accountBenefitAdjustDTO.setMerchantId(1000319L);
        accountBenefitAdjustDTO.setClassify(BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON.getCode());
        accountBenefitAdjustDTO.setExpiryDateCategory(ExpiryDateCategoryEnum.NO_LIMIT);
        //accountBenefitAdjustDTO.setStoreId(1028378L);
        //accountBenefitAdjustDTO.setStoreId(1028164L);

        //accountBenefitAdjustDTO.setDownTime("2021-05-30");
        accountBenefitAdjustDTO.setAmount(new BigDecimal("1"));
//        accountBenefitAdjustDTO.setOrderNo("************");
//        accountBenefitAdjustDTO.setOperator(21751792L);
        accountBenefitAdjustDTO.setRecordType(AccountRecordTypeEnum.INTELLIGENT_AUTO.getCode());



        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/benefit/merchant/adjust");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(accountBenefitAdjustDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    @Test
    public void merchantClearBenefit() throws Exception{

        List<AccountBenefitAdjustDTO> list = new ArrayList<>();
        AccountBenefitAdjustDTO accountBenefitAdjustDTO = new AccountBenefitAdjustDTO();
        accountBenefitAdjustDTO.setUserId(21751792L);
        accountBenefitAdjustDTO.setMerchantId(1000319L);
        accountBenefitAdjustDTO.setAdjustType(AdjustTypeEnum.DECREMENT);
        accountBenefitAdjustDTO.setClassify(BenefitClassifyEnum.USER_RECHARGE_COIN.getCode());
        accountBenefitAdjustDTO.setOperator(1220509L);
        accountBenefitAdjustDTO.setResource("调整余币");
        accountBenefitAdjustDTO.setRecordType(AccountRecordTypeEnum.MERCHANT_ADJUST_COIN.getCode());
        list.add(accountBenefitAdjustDTO);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/benefit/merchant/clear");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(list));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }



    @Test
    public void benefitCount() throws Exception{


        AccountQueryDTO accountQueryDTO = new AccountQueryDTO();
        accountQueryDTO.setMerchantId(Long.valueOf("1062066"));
        accountQueryDTO.setMerchantUserId(Long.valueOf("857926184700755968"));
        //accountQueryDTO.setBenefitClassify(Arrays.asList(2,4,6,16));
        accountQueryDTO.setBenefitClassify(Arrays.asList(3,4,21));
        accountQueryDTO.setExcludeClassify(Arrays.asList(21));
        //accountQueryDTO.setStoreId(Arrays.asList(1028440L));
        //accountQueryDTO.setEquipmentTypeId(Arrays.asList(1000028L));

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/benefit/count");
        requestBuilder.header("Content-Type","application/json");
        //requestBuilder.content(JSON.toJSONString(accountQueryDTO));
        requestBuilder.content(JSON.toJSONString(accountQueryDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }
    @Test
    public void benefitConsume() throws Exception{
        //Long merchantId = 1033377L ;
        //Long userId = 33208067L ;
        //Long merchantUserId = 1397825620427149313L ;
        ConsumeDTO consume = new ConsumeDTO();
        //consume.setUserId(33175796L);
        //consume.setMerchantId(1033342L);
        //consume.setStoreId(1062385L);
        //consume.setCheckBalance(true);
        //consume.setAllowNegative(false);
        //consume.setEquipmentId(1554319L);
        //consume.setEquipmentTypeId(1001096L);
        //consume.setCommodityId(null);
        ////consume.setOutTradeNo("ttttt");
        //consume.setOrderNo(RandomUtil.getRandom());
        //consume.setResource("测试用例");
        //consume.setOperator(888L);
        //consume.setRecordType(AccountRecordTypeEnum.EQUIP_START_BALANCE_START.getCode());
        //
        //
        //
        //ConsumeDetailDTO detail2 = new ConsumeDetailDTO();
        //detail2.setClassify(Arrays.asList(1,3));
        //detail2.setAmount(BigDecimal.valueOf(8.7));
        //
        //
        //consume.setConsume(Arrays.asList(detail2));
        //consumeDTO.setCheckBalance(Boolean.FALSE);
        //consumeDTO.setAllowNegative(Boolean.TRUE);

        String json = "{\"consume\":[{\"amount\":0,\"classify\":[1,17,25,3,5]}],\"description\":\"氛围类型(加油)\",\"equipmentId\":1615999,\"equipmentTypeId\":1001311,\"equipmentTypeName\":\"氛围机\",\"equipmentValue\":\"********\",\"merchantId\":1033342,\"operator\":0,\"orderNo\":\"918582830068621312\",\"recordType\":15,\"storeId\":1065920,\"storeName\":\"超级娃娃机\",\"userId\":********}\n";
        consume = new Gson().fromJson(json, ConsumeDTO.class);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/benefit/consume");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(consume));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    /**
     * 消耗一个红包币
     * @throws Exception
     */
    @Test
    public void testConsume() throws Exception {
        String json = "{\"checkBalance\":false,\"consume\":[{\"amount\":5.01,\"classify\":[1]}],\"equipmentId\":1554319,\"equipmentTypeId\":1000078,\"equipmentTypeName\":\"充电桩\",\"equipmentValue\":\"********\",\"merchantId\":1033342,\"operator\":0,\"orderNo\":\"*****************3317579632517\",\"outTradeNo\":\"*****************3317579632517\",\"recordType\":54,\"resource\":\"退款退权益\",\"storeId\":1062385,\"storeName\":\"广州万达广场+商场\",\"tradeAmount\":0.01,\"tradeType\":2,\"userId\":33175796}";
        ConsumeDTO consume = new Gson().fromJson(json, ConsumeDTO.class);
        //ConsumeDTO consume = new ConsumeDTO();
        //consume.setUserId(USER_ID_1062066);
        //consume.setMerchantId(MERCHANT_ID_1062066);
        //consume.setOperator(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID);
        //consume.setDescription("开发测试");
        //
        //consume.setRecordType(2);
        //
        //
        //consume.setOperator(888L);
        //
        //
        //ConsumeDetailDTO detail = new ConsumeDetailDTO();
        //detail.setClassify(Arrays.asList(BenefitClassifyEnum.RED_BALANCE.getCode()));
        //detail.setAmount(BigDecimal.valueOf(1.96));
        //
        //ConsumeDetailDTO detail2 = new ConsumeDetailDTO();
        //detail2.setClassify(Arrays.asList(BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE.getCode()));
        //detail2.setAmount(BigDecimal.valueOf(1));
        //
        //consume.setConsume(Arrays.asList(detail,detail2));


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/benefit/consume");
        requestBuilder.header("Content-Type","application/json");

        requestBuilder.content(JSON.toJSONString(consume));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));

    }

    @Test
    public void benefitPreDeductionConsume() throws Exception {
        //开发 娃娃机 264966
        String json = "{\"consume\":[{\"amount\":1.00,\"classify\":[16,2,18,4,6]}],\"description\":\"余币启动扣减权益\",\"equipmentId\":1186720,\"equipmentTypeId\":1000058,\"equipmentTypeName\":\"兑币机\",\"equipmentValue\":\"264966\",\"merchantId\":1000319,\"operator\":0,\"orderNo\":\"898622769191862272\",\"recordType\":0,\"storeId\":1028378,\"storeName\":\"默认场地\",\"userId\":********}\n";
        //开发 洗衣机 12212565
        //String json = "{\"consume\":[{\"amount\":0.01,\"classify\":[1,17,3,5]}],\"description\":\"余额启动扣减权益\",\"equipmentId\":1315214,\"equipmentTypeId\":1000028,\"equipmentTypeName\":\"洗衣机\",\"equipmentValue\":\"12212565\",\"merchantId\":1000319,\"operator\":0,\"orderNo\":\"898626085179244544\",\"recordType\":0,\"storeId\":1028609,\"storeName\":\"创智园设备\",\"userId\":********}";
        ConsumeDTO consume = new Gson().fromJson(json, ConsumeDTO.class);

        //预扣金额
        consume.setPreDeduction(true);

        //consume.setUserId(lyyUserId_Jason);
        //consume.setMerchantId(MERCHANT_ID_1000319);
        //consume.setOperator(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID);
        //consume.setDescription("开发测试");
        //
        //consume.setRecordType(2);
        //consume.setEquipmentId(Long.valueOf("1186720"));
        //consume.setEquipmentTypeName("兑币机");
        //consume.setEquipmentValue("264966");
        //consume.setOrderNo("9999999999111111111111");
        //consume.setStoreId(Long.valueOf("1028378"));
        //consume.setStoreName("默认场地");
        //consume.setEquipmentTypeId(Long.valueOf("1000058"));
        //consume.setOperator(888L);
        //
        //consume.setPreDeduction(true);
        //
        //ConsumeDetailDTO detail = new ConsumeDetailDTO();
        //detail.setClassify(Arrays.asList(16,2,18,4,6));
        //detail.setAmount(BigDecimal.valueOf(1));
        //
        //ConsumeDetailDTO detail2 = new ConsumeDetailDTO();
        //detail2.setClassify(Arrays.asList(BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE.getCode()));
        //detail2.setAmount(BigDecimal.valueOf(1));
        //
        //consume.setConsume(Arrays.asList(detail,detail2));


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/benefit/preDeductionConsume");
        requestBuilder.header("Content-Type","application/json");

        requestBuilder.content(JSON.toJSONString(consume));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    @Test
    public void payRefundRollbackBenefit() throws Exception {

        PayRefundBenefitDTO dto = new PayRefundBenefitDTO();
        dto.setUserId(21751792L);
        dto.setMerchantId(1000319L);
        //dto.setStoreId(1028164L);
        dto.setStoreId(1028378L);
        dto.setEquipmentId(1554319L);
        dto.setEquipmentTypeId(1000078L);
        dto.setCommodityId(null);
        dto.setOutTradeNo("************");
        dto.setOrderNo("************");
        dto.setResource("退款退权益");
        dto.setOperator(0L);
        dto.setDescription(null);
        dto.setTradeType(2);
        dto.setTradeAmount(new BigDecimal("5"));
        dto.setCommodityName(null);
        dto.setStoreName("广州万达广场+商场");
        dto.setEquipmentTypeName("充电桩");
        dto.setEquipmentValue("********");
        dto.setRecordType(54);
        dto.setAllRollBack(true);
        List<PayRefundBenefitDetailDTO> refundBenefitDetails = new ArrayList<>();
        PayRefundBenefitDetailDTO detailDTO1 = new PayRefundBenefitDetailDTO();
        detailDTO1.setClassify(1);
        detailDTO1.setAmount(new BigDecimal("1"));

        //PayRefundBenefitDetailDTO detailDTO2 = new PayRefundBenefitDetailDTO();
        //detailDTO2.setClassify();
        //detailDTO2.setAmount();
        refundBenefitDetails.add(detailDTO1);
        //refundBenefitDetails.add(detailDTO2);

        dto.setRefundBenefitDetails(refundBenefitDetails);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/benefit/payRefundRollbackBenefit");
        requestBuilder.header("Content-Type","application/json");

        requestBuilder.content(JSON.toJSONString(dto));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }
    @Test
    public void callbackRealConsume() throws Exception {

        //开发 格斗机 ********
        //String json = "{\"consume\":[{\"amount\":0,\"classify\":[16,2,18,4,6]}],\"description\":\"余币启动扣减权益\",\"equipmentId\":1315213,\"equipmentTypeId\":1000010,\"equipmentTypeName\":\"格斗机\",\"equipmentValue\":\"********\",\"merchantId\":1000319,\"operator\":0,\"orderNo\":\"999944752649453568\",\"recordType\":0,\"storeId\":1028609,\"storeName\":\"创智园设备\",\"userId\":********}\n";

        //开发 洗衣机 12212565
        //String json = "{\"consume\":[{\"amount\":0.01,\"classify\":[1,17,3,5]}],\"description\":\"余额启动扣减权益\",\"equipmentId\":1315214,\"equipmentTypeId\":1000028,\"equipmentTypeName\":\"洗衣机\",\"equipmentValue\":\"12212565\",\"merchantId\":1000319,\"operator\":0,\"orderNo\":\"898626085179244544\",\"recordType\":0,\"storeId\":1028609,\"storeName\":\"创智园设备\",\"userId\":********}";


        //开发 游乐车 60035092
        String json = "{\"consume\":[{\"amount\":11.2,\"classify\":[1,17,3,5]}],\"description\":\"设备启动\",\"equipmentId\":1186192,\"equipmentTypeId\":1001237,\"equipmentTypeName\":\"游乐车\",\"equipmentValue\":\"60035092\",\"merchantId\":1000319,\"operator\":0,\"orderNo\":\"999956933180899328\",\"recordType\":0,\"storeId\":1028164,\"storeName\":\"公用设备禁止转移\",\"userId\":********}";

        ConsumeDTO consume = new Gson().fromJson(json, ConsumeDTO.class);

        //预扣金额
        //consume.setPreDeduction(false);

        //consume.setUserId(lyyUserId_Jason);
        //consume.setMerchantId(MERCHANT_ID_1000319);
        //consume.setOperator(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID);
        //consume.setDescription("开发测试");
        //
        //consume.setRecordType(2);
        //consume.setEquipmentId(Long.valueOf("1186720"));
        //consume.setEquipmentTypeName("兑币机");
        //consume.setEquipmentValue("264966");
        //consume.setOrderNo("9999999999111111111111");
        //consume.setStoreId(Long.valueOf("1028378"));
        //consume.setStoreName("默认场地");
        //consume.setEquipmentTypeId(Long.valueOf("1000058"));
        //consume.setOperator(888L);
        //
        //consume.setPreDeduction(true);
        //
        //ConsumeDetailDTO detail = new ConsumeDetailDTO();
        //detail.setClassify(Arrays.asList(16,2,18,4,6));
        //detail.setAmount(BigDecimal.valueOf(1));
        //
        //ConsumeDetailDTO detail2 = new ConsumeDetailDTO();
        //detail2.setClassify(Arrays.asList(BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE.getCode()));
        //detail2.setAmount(BigDecimal.valueOf(1));
        //
        //consume.setConsume(Arrays.asList(detail,detail2));


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/benefit/callback/realConsume");
        requestBuilder.header("Content-Type","application/json");

        requestBuilder.content(JSON.toJSONString(consume));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    /**
     * 测试用户账户
     * @throws Exception
     */
    @Test
    public void testAccountInfo() throws Exception {
        AccountConditionDTO accountConditionDTO = new AccountConditionDTO();
        accountConditionDTO.setUserId(Long.valueOf("********"));
        accountConditionDTO.setMerchantId(Long.valueOf("1062066"));
        accountConditionDTO.setClassifies(Arrays.asList(3,4,21));
        accountConditionDTO.setExcludeClassify(Arrays.asList(21));



        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/info");
        requestBuilder.header("Content-Type","application/json");

        requestBuilder.content(JSON.toJSONString(accountConditionDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    @Test
    public void testGetChooseBenefitBalance()  throws Exception{
        AccountBenefitQueryDTO dto = new AccountBenefitQueryDTO();
        dto.setMerchantId(MERCHANT_ID_1000319);
        dto.setMerchantUserId(MERCHANT_USER_ID);
        //dto.setBenefitIds(Arrays.asList(Long.valueOf("856168698050248704")));
        dto.setClassify(Arrays.asList(2,4,6,18,21,16));
        //dto.setStoreIds(Arrays.asList(1062294L));
        //dto.setQueryType(1);
        //dto.setEquipmentTypeIds();
        //LocalDateTime localDateTime = LocalDateTime.now();
        //Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        //Date date = Date.from(instant);
        //dto.setStartTime(date);
        dto.setNotHandleBenefitIdList(Arrays.asList(123456L));
        dto.setPageIndex(1);
        dto.setPageSize(10);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/benefit/getChooseBenefitBalance");
        requestBuilder.header("Content-Type","application/json");
        //requestBuilder.content(JSON.toJSONString(accountQueryDTO));
        requestBuilder.content(JSON.toJSONString(dto));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    @Test
    public void findAccountBenefitData() throws Exception{
        AccountBenefitQueryDTO dto = new AccountBenefitQueryDTO();
        dto.setMerchantId(Long.valueOf("1000319"));
        //dto.setMerchantUserId(Long.valueOf("924007329431044096"));
        dto.setUserId(21751792L);
        //dto.setBenefitIds(Arrays.asList(Long.valueOf("852131870561271808")));
        dto.setClassify(Arrays.asList(3));
        //dto.setBenefitGroupType();
        //dto.setStoreIds(Arrays.asList(1062294L));
        //dto.setQueryType(1);
        //dto.setEquipmentTypeIds();
        //LocalDateTime localDateTime = LocalDateTime.now();
        //Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        //Date date = Date.from(instant);
        //dto.setStartTime(date);
        dto.setExcludeClassify(Arrays.asList(21));
        dto.setPageIndex(1);
        dto.setPageSize(10);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/benefit/findAccountBenefitData");
        requestBuilder.header("Content-Type","application/json");
        //requestBuilder.content(JSON.toJSONString(accountQueryDTO));
        requestBuilder.content(JSON.toJSONString(dto));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    @Test
    public void listBenefitDetail() throws Exception{
        AccountBenefitQueryDTO dto = new AccountBenefitQueryDTO();
        dto.setMerchantId(Long.valueOf("1062066"));
        dto.setMerchantUserId(Long.valueOf("857926184700755968"));
        //dto.setBenefitIds(Arrays.asList(Long.valueOf("856168698050248704")));
        dto.setClassify(Arrays.asList(2,4,6,18,21,16));
        //dto.setBenefitGroupType();
        //dto.setStoreIds(Arrays.asList(1062294L));
        //dto.setQueryType(1);
        //dto.setEquipmentTypeIds();
        //LocalDateTime localDateTime = LocalDateTime.now();
        //Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        //Date date = Date.from(instant);
        //dto.setStartTime(date);
        dto.setExcludeClassify(Arrays.asList(21));
        dto.setPageIndex(1);
        dto.setPageSize(10);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/benefit/listBenefitDetail");
        requestBuilder.header("Content-Type","application/json");
        //requestBuilder.content(JSON.toJSONString(accountQueryDTO));
        requestBuilder.content(JSON.toJSONString(dto));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }
    //
    @Test
    public void testCleanWithBenefitId() throws Exception {
        AccountBenefitQueryDTO dto = new AccountBenefitQueryDTO();
        dto.setMerchantId(MERCHANT_ID_1062066);
        dto.setMerchantUserId(MERCHANT_USER_ID_1062066);

        dto.setBenefitIds(Arrays.asList(
                Long.valueOf("1408332256358961154")
        ));
        //dto.setBenefitGroupType();
        //dto.setStoreIds(Arrays.asList(1062294L));
        //dto.setQueryType(1);
        //dto.setEquipmentTypeIds();
        //LocalDateTime localDateTime = LocalDateTime.now();
        //Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        //Date date = Date.from(instant);
        //dto.setStartTime(date);
        dto.setPageIndex(1);
        dto.setPageSize(10);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/benefit/cleanAll");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(dto));

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    @Test
    public void testBenefitCleanAll() throws Exception {
        AccountBenefitQueryDTO dto = new AccountBenefitQueryDTO();
        dto.setMerchantId(Long.valueOf("1000319"));
        dto.setMerchantUserId(Long.valueOf("856168697790201856"));
        //dto.setBenefitIds(Arrays.asList(Long.valueOf("852131870561271808")));
        dto.setClassify(Arrays.asList(2,14,4,6,18,21,16));
        //dto.setBenefitGroupType();
        //dto.setStoreIds(Arrays.asList(1062294L));
        //dto.setQueryType(1);
        //dto.setEquipmentTypeIds();
        //LocalDateTime localDateTime = LocalDateTime.now();
        //Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        //Date date = Date.from(instant);
        //dto.setStartTime(date);
        dto.setPageIndex(1);
        dto.setPageSize(9999);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/benefit/cleanAll");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(dto));

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    @Test
    public void testGetMerchantAccount() throws Exception {
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/account/getMerchantUserAccount")
                .param("merchantUserId","852132524528762880")
                .param("merchantId","1000319")
                .param("classifyGroupType","2");
        requestBuilder.header("Content-Type","application/json");

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    @Test
    public void listRecord() throws Exception{

        AccountRecordQueryDTO accountRecordQueryDTO = new AccountRecordQueryDTO();
        accountRecordQueryDTO.setMerchantId(1062179L);
        accountRecordQueryDTO.setUserId(21757953L);
        accountRecordQueryDTO.setPageIndex(1);
        accountRecordQueryDTO.setPageSize(5);
        accountRecordQueryDTO.setRecordType(132);
        accountRecordQueryDTO.setOutTradeNo("91852180187578368012920990337415");

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/record/listRecord");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(accountRecordQueryDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }


    @Test
    public void refundBenefitByOrderNo() throws Exception{

        BenefitRollbackDTO rollbackDTO = new BenefitRollbackDTO();
        rollbackDTO.setUserId(21744049L);
        rollbackDTO.setMerchantId(1000319L);
        rollbackDTO.setRefundAmount(new BigDecimal("3"));
        rollbackDTO.setClassify(Arrays.asList(BenefitClassifyEnum.THIRD_PLATFORM_AMOUNT.getCode(),
                BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE.getCode(),
                BenefitClassifyEnum.PLATFORM_PAYOUT_BALANCE.getCode(),
                BenefitClassifyEnum.RED_BALANCE.getCode()));
        rollbackDTO.setOrderNo("202111161507437032174404930441");
        rollbackDTO.setRecordType(AccountRecordTypeEnum.REFUND_BENEFIT.getCode());

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/benefit/refundBenefitByOrderNo");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(rollbackDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }


    @Test
    public void findCardStatus() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/account/smallVenue/cardStatus")
                .param("merchantId", "1000319")
                .param("cardNo", "LYYDJB00001945");

        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    /**
     * 查询账户信息
     * @throws Exception
     */
    @Test
    public void getAccountInfo() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/account/smallVenue/getAccountInfo")
                .param("merchantId", "1000319")
                .param("keyword", "969894444970557440");

        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }


    @Test
    public void smallVenueAllCard() throws Exception{
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/account/smallVenue/allCard")
                .param("merchantId", "0")
                .param("userId", "********");
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void findUserAvailableBenefit() throws Exception {
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/smallVenue/findUserAvailableBenefit");
        requestBuilder.header("Content-Type", "application/json");
        AvailableBenefitQueryDTO queryDTO = new AvailableBenefitQueryDTO();
        queryDTO.setMerchantId(0L);
        queryDTO.setCardNo("1111");
        queryDTO.setUserId(********L);
        requestBuilder.content(JSON.toJSONString(queryDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    /**
     * 附属卡校验
     * @throws Exception
     */
    @Test
    public void supplementaryCardCheck() throws Exception {

        SupplementaryCardCheckDTO supplementaryCardCheckDTO = new SupplementaryCardCheckDTO().setUserId(********L).setMerchantId(0L).setAccountId(926116686380355584L);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/smallVenue/addSupplementaryCard/check");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(supplementaryCardCheckDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }


    /**
     * 附属卡添加
     * @throws Exception
     */
    @Test
    public void addSupplementaryCard() throws Exception {

        AddSupplementaryCardDTO addSupplementaryCardDTO = new AddSupplementaryCardDTO()
                .setCardNo("123456")
                .setUserId(21758448L)
                .setMerchantId(0L)
                .setMerchantUserId(922932342769762305L)
                .setSupplementaryCardNo("shi123456")
                .setCardValidType(1)
                .setValidDays(3)
                .setDeposit(BigDecimal.valueOf(50))
                .setOperatorId(95123L)
                .setOperatorName("admin");
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/smallVenue/addSupplementaryCard");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(addSupplementaryCardDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    /**
     * 权益详情(余额详情)
     * @throws Exception
     */
    @Test
    public void smallVenueBenefitDetail() throws Exception {

        BenefitDetailSelectDTO benefitDetailSelectDTO = new BenefitDetailSelectDTO()
                .setAccountId(933732733727989760L)
                .setMerchantBenefitClassifyId(26L)
                .setMerchantId(172L)
                .setUserId(21778180L)
                .setStoreId(10080L)
                .setPageIndex(1)
                .setPageSize(100);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/smallVenue/benefit/detail");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(benefitDetailSelectDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    /**
     * 查询权益统计数据（根据门店进行分组）
     * @throws Exception
     */
    @Test
    public void benefitStatistics() throws Exception {
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/account/smallVenue/benefitStatistics")
                .param("merchantId", "1000319")
                .param("accountId", "936202002176933888")
                .param("userId", "********");
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    /**
     * 会员卡挂失恢复
     *
     * @throws Exception
     */
    @Test
    public void cardReportLossOrRecover() throws Exception {

        UpdateCardStatusDTO updateCardStatusDTO = new UpdateCardStatusDTO()
                .setCardNo("9527123")
                .setDescription("")
                .setOperatorId(123456L)
                .setOperatorName("")
                .setType(1)
                .setMerchantId(0L);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/smallVenue/cardReportLossOrRecover");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(updateCardStatusDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }


    /**
     * 会员卡补卡换卡
     * @throws Exception
     */
    @Test
    public void reissueCard() throws Exception {

        ReissueCardDTO reissueCardDTO = new ReissueCardDTO()
                .setMerchantId(0L)
                .setMerchantUserId(0L)
                .setNewCardNo("9527123")
                .setOldCardNo("1239527")
                .setOperatorId(1234567L)
                .setOperatorName("")
                .setType(1)
                .setUserId(********L);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/smallVenue/reissueCard");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(reissueCardDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }


    /**
     * 会员账户流水列表
     * @throws Exception
     */
    @Test
    public void smallVenueRecord() throws Exception {

        SmallVenueRecordSelectDTO smallVenueRecordSelectDTO = new SmallVenueRecordSelectDTO()
                .setOperationType(4)
                .setMerchantId(100000001L)
                .setExcludeRecordTypes(Arrays.asList(1401))
                .setPageIndex(1)
                .setPageSize(10);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/smallVenue/record");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(smallVenueRecordSelectDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    /**
     * 会员账户流水保存
     * @throws Exception
     */
    @Test
    public void smallVenueSaveRecord() throws Exception {

        SmallVenueAccountRecordSaveDTO smallVenueAccountRecordSaveDTO = new SmallVenueAccountRecordSaveDTO()
                .setActualBenefit(BigDecimal.ONE)
                .setCardNo("************")
                .setCommodityName("测试商品名称2")
                .setDescription("测试描述2")
                .setDiscountsAmount(BigDecimal.TEN)
                .setGoodsClassify(1L)
                .setGoodsId(1L)
                .setGoodsPrice(BigDecimal.valueOf(100L))
                .setMode(1)
                .setMerchantId(0L)
                .setGoodsNum(BigDecimal.TEN)
                .setOperationEquipmentName("操作设备名称2")
                .setOperatorName("操作人名称2")
                .setStoreId(1L)
                .setStoreName("门店名称2")
                .setUserId(21758443L)
                .setOrderNo("233223")
                .setRefundNo("3893498")
                .setOperationType(1)
                .setOperationChannel(1)
                .setBenefitClassify(1)
                .setInitialBenefit(BigDecimal.TEN)
                .setOriginalBenefit(BigDecimal.TEN)
                .setRecordType(1)
                .setOperatorId(1l);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/smallVenue/saveRecord");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(smallVenueAccountRecordSaveDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }


    /**
     * 查询主卡是否存在附属卡
     * @throws Exception
     */
    @Test
    public void hasSupplementaryCard() throws Exception {

        HasSupplementaryCardDTO hasSupplementaryCardDTO = new HasSupplementaryCardDTO()
                .setAccountId(928329838912299008L)
                .setUserId(********l)
                .setMerchantId(0l);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/smallVenue/hasSupplementaryCard");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(hasSupplementaryCardDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }


    /**
     * 会员卡续期
     *
     * @throws Exception
     */
    @Test
    public void renewalCard() throws Exception {

        CardRenewalDTO cardRenewalDTO = new CardRenewalDTO()
                .setCardNo("654321")
                .setMerchantId(0L)
                .setOperatorId(996L)
                .setOperatorName("qwe")
                .setExtendedDays(3);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/smallVenue/renewalCard");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(cardRenewalDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }


    @Test
    public void recordListType() throws Exception {
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/account/smallVenue/recordTypeList");
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    /**
     * 不记名卡补充资料
     * @throws Exception
     */
    @Test
    public void supplyAnonymousCardData() throws Exception {

        SupplyAnonymousCardDataDTO supplyAnonymousCardDataDTO = new SupplyAnonymousCardDataDTO()
                .setCardNo("Lyy000002")
                .setMerchantId(1000319L)
                .setMerchantUserId(940632270753234944L)
                .setUserId(21778373L);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/smallVenue//supplyAnonymousCardData");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(supplyAnonymousCardDataDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    @Test
    public void findRecordBenefitScopeByOrderNo() throws Exception {
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/record/findRecordBenefitScopeByOrderNo");
        requestBuilder.header("Content-Type", "application/json");
        RecordBenefitScopeQueryDTO queryDTO = new RecordBenefitScopeQueryDTO();
        queryDTO.setMerchantId(1000319L);
        queryDTO.setUserId(21754670L);
        queryDTO.setOutTradeNo("969334129378344960");
        queryDTO.setClassifies(Arrays.asList(BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE.getCode()));
        queryDTO.setMode(AdjustTypeEnum.DECREMENT.getType());

        requestBuilder.content(JSON.toJSONString(queryDTO));

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void findRecordByOrderNoAndCondition() throws Exception {
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/record/findRecordByOrderNoAndCondition");
        requestBuilder.header("Content-Type", "application/json");
        AccountRecordQueryDTO queryDTO = new AccountRecordQueryDTO();
        queryDTO.setMerchantId(1000319L);
        queryDTO.setUserId(21754670L);
        queryDTO.setOutTradeNo("969334129378344960");
        queryDTO.setMode(AdjustTypeEnum.DECREMENT.getType());
        requestBuilder.content(JSON.toJSONString(queryDTO));

        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    /**
     * 移动端账户流水记录
     * @throws Exception
     */
    @Test
    public void mobileTerminalRecord() throws Exception {

        MobileTerminalRecordSelectDTO smallVenueRecordSelectDTO = new MobileTerminalRecordSelectDTO()
                .setOperationType(1)
                .setMerchantId(0L)
                .setExcludeRecordTypes(Arrays.asList(30))
                .setPageIndex(1)
                .setPageSize(10);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/mobileTerminal/record");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(smallVenueRecordSelectDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }


    @Test
    public void mobileTerminalAccountDetails() throws Exception {
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/account/mobileTerminal/accountDetails");
        requestBuilder.param("merchantId", "1000319");
        requestBuilder.param("userId", "********");
        requestBuilder.param("accountId", "968568398298742784");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }


    /**
     * 移动端会员卡列表
     * @throws Exception
     */
    @Test
    public void mobileTerminalAccountList() throws Exception {

        MobileTerminalAccountSelectDTO accountSelectDTO = new MobileTerminalAccountSelectDTO()
                .setPageIndex(1)
                .setPageSize(20)
                .setKeyword("66")
                .setMerchantId(1444907L);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/mobileTerminal/accountList");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(accountSelectDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }


    @Test
    public void merchantIncrementAccountBenefit() throws Exception {
        MerchantBenefitIncrementDTO dto = new MerchantBenefitIncrementDTO();
        dto.setMerchantId(1000319L);
        dto.setOperatorId(100000L);
        dto.setStoreId(10105L);
        dto.setUserIds(Arrays.asList(21778215L));

        BenefitIncrementItemDTO incrementItemDTO = new BenefitIncrementItemDTO();
        List<BenefitIncrementItemDTO.BenefitDetailsDTO> benefitDetails = new ArrayList<>();
        BenefitIncrementItemDTO.BenefitDetailsDTO benefitDetailsDTO = new BenefitIncrementItemDTO.BenefitDetailsDTO();
        benefitDetailsDTO.setMerchantBenefitClassifyId(64L);
        benefitDetailsDTO.setMerchantBenefitClassifyName("余额");
        benefitDetailsDTO.setNum(new BigDecimal("1"));
        benefitDetailsDTO.setNumType(1);
        benefitDetailsDTO.setExpiryDateCategory(1);
        benefitDetailsDTO.setUpTime("2022-06-16 00:00:00");
        benefitDetailsDTO.setDownTime("2022-06-20 23:59:59");
        benefitDetails.add(benefitDetailsDTO);
        incrementItemDTO.setBenefitDetails(benefitDetails);
        dto.setIncrementItemDTO(incrementItemDTO);

        AccountRecordInfoDTO record = new AccountRecordInfoDTO();
        record.setRecordType(AccountRecordTypeEnum.SV_PRESENTATION.getCode());
        record.setStoreName("收银机扫码专用店铺");
        dto.setRecord(record);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/account/smallVenue/benefit/merchant/increment");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(dto));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));

    }
}
