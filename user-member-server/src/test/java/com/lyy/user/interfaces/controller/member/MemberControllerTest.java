package com.lyy.user.interfaces.controller.member;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.alibaba.fastjson.JSON;
import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.member.dto.MemberGroupRangCheckDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberTouchRuleDTO;
import com.lyy.user.account.infrastructure.member.dto.UpdateUserMemberLevelDTO;
import java.math.BigDecimal;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

/**
 * <AUTHOR>
 * @create 2021/6/18 20:28
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class MemberControllerTest {

    @Autowired
    private MockMvc mockMvc;

    private final String prefix = "/rest/member/member";

    @Test
    public void testFindPageMemberUser() throws Exception{

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get(prefix +"/findPageMemberUser")
                .param("current","1")
                .param("size","10")
                .param("merchantId","1033278")
                .param("userId","33597776");

        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void testGetSmallVenueUserMemberInfo() throws Exception{

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get(prefix +"/getSmallVenueUserMemberInfo")
                .param("merchantId","100001")
                .param("userId","10004");

        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    /**
     * 小场地用户触发会员规则
     * @throws Exception
     */
    @Test
    public void testSmallVenueUserUpdateMemberOfRule() throws Exception{
        MemberTouchRuleDTO memberTouchRuleDTO3 = new MemberTouchRuleDTO();
        memberTouchRuleDTO3.setUserId(21751792L);
        memberTouchRuleDTO3.setCategory((short)3);
        memberTouchRuleDTO3.setOtherInfo("测试数据3");
        memberTouchRuleDTO3.setRangeValue(new BigDecimal(3));
        memberTouchRuleDTO3.setMerchantId(1444788L);
        // 不限制使用范围
        MemberGroupRangCheckDTO memberGroupRangCheckDTO3 = new MemberGroupRangCheckDTO();
        memberTouchRuleDTO3.setMemberGroupRangCheckDTO(memberGroupRangCheckDTO3);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post(prefix +"/updateMemberOfRule");

        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(memberTouchRuleDTO3));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }


    /**
     * 跟新会员等级
     * @throws Exception
     */
    @Test
    public void testSmallVenueUserUpdateMemberLevel() throws Exception{
        UpdateUserMemberLevelDTO updateUserMemberLevelDTO = new UpdateUserMemberLevelDTO();
        updateUserMemberLevelDTO.setMerchantId(1444788L);
        updateUserMemberLevelDTO.setMerchantUserId(936678656344342528L);
        updateUserMemberLevelDTO.setMemberLevelId(123L);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post(prefix +"/updateUserMemberLevel");

        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(updateUserMemberLevelDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }


    /**
     * 小场地用户成长值记录回退
     * @throws Exception
     */
    @Test
    public void testSmallVenueUserRemoveMemberOfRule() throws Exception{
        MemberTouchRuleDTO memberTouchRuleDTO3 = new MemberTouchRuleDTO();
        memberTouchRuleDTO3.setUserId(21751792L);
        memberTouchRuleDTO3.setCategory((short)3);
        memberTouchRuleDTO3.setOtherInfo("测试数据3");
        memberTouchRuleDTO3.setRangeValue(new BigDecimal(3));
        memberTouchRuleDTO3.setMerchantId(1444788L);
        // 不限制使用范围
        MemberGroupRangCheckDTO memberGroupRangCheckDTO3 = new MemberGroupRangCheckDTO();
        memberTouchRuleDTO3.setMemberGroupRangCheckDTO(memberGroupRangCheckDTO3);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post(prefix +"/removeMemberOfRule");

        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(memberTouchRuleDTO3));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void testInitSmallVenueUserMemberLevel() throws Exception{

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get(prefix +"/initSmallVenueUserMemberLevel")
                .param("merchantId","1444788")
                .param("userId","21751792");

        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }



}
