package com.lyy.user.interfaces.controller.benefit;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.alibaba.fastjson.JSON;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.account.dto.BenefitExcludeClassifyDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitRuleSaveDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitSaveDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitScopeSaveDTO;
import com.lyy.user.account.infrastructure.benefit.dto.GeneralGroupBenefitSaveDTO;
import com.lyy.user.account.infrastructure.benefit.dto.GenerateAccountDTO;
import com.lyy.user.account.infrastructure.constant.ApplicableEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.BenefitGenerateTypeEnum;
import com.lyy.user.account.infrastructure.constant.RuleUnitEnum;
import com.lyy.user.account.infrastructure.constant.ShowDateCategoryEnum;
import com.lyy.user.application.benefit.BenefitService;
import com.lyy.user.domain.benefit.repository.BenefitMapper;
import com.lyy.user.infrastructure.constants.CommodityCategoryEnum;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class BenefitControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private BenefitService benefitService;

    @Autowired
    private BenefitMapper benefitMapper;

    /**
     * 初始化商户权益，只限于某一类权益
     * @throws Exception
     */
    @Test
    public void saveBenefit() throws Exception{
        BenefitSaveDTO dto = new BenefitSaveDTO();
        dto.setMerchantId(1062179L);
        //dto.setMerchantId(UserMemberSysConstants.PLATFORM_MERCHANT_ID);
        dto.setBenefitClassify(BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE);
        dto.setTitle("商户派发余额");
        dto.setSubTitle("此权益由商户提供");
        dto.setBenefitCount(9999L);
//        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//        dto.setUpTime(LocalDateTime.parse("2021-01-01 00:00:00",dateTimeFormatter));
//        dto.setDownTime(LocalDateTime.parse("2999-01-01 00:00:00",dateTimeFormatter));
        dto.setShowDateCategory(ShowDateCategoryEnum.NO_LIMIT);
        List<BenefitScopeSaveDTO> benefitScopeSaveDTOList = new ArrayList<>();
        BenefitScopeSaveDTO benefitScopeSaveDTO = new BenefitScopeSaveDTO();
        benefitScopeSaveDTO.setApplicable(ApplicableEnum.CATEGORY);
        benefitScopeSaveDTOList.add(benefitScopeSaveDTO);

        BenefitScopeSaveDTO benefitScopeSaveDTO2 = new BenefitScopeSaveDTO();
        benefitScopeSaveDTO2.setApplicable(ApplicableEnum.DEVICE);
        benefitScopeSaveDTOList.add(benefitScopeSaveDTO2);

        BenefitScopeSaveDTO benefitScopeSaveDTO3 = new BenefitScopeSaveDTO();
        benefitScopeSaveDTO3.setApplicable(ApplicableEnum.GROUP);
        benefitScopeSaveDTOList.add(benefitScopeSaveDTO3);
        dto.setBenefitScopeList(benefitScopeSaveDTOList);


        List<BenefitRuleSaveDTO> benefitRuleList = new ArrayList<>();
        BenefitRuleSaveDTO benefitRuleSaveDTO = new BenefitRuleSaveDTO();
        //benefitRuleSaveDTO.setBenefitClassify(BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE);
        benefitRuleSaveDTO.setGenerateType(BenefitGenerateTypeEnum.GIVE);
        benefitRuleSaveDTO.setRuleValue(100);
        benefitRuleSaveDTO.setRuleUnit(RuleUnitEnum.YUAN.getUnit());
        benefitRuleList.add(benefitRuleSaveDTO);
        dto.setBenefitRuleList(benefitRuleList);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/benefit/save");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(dto));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void saveGeneralGroupBenefit() throws Exception{

        GeneralGroupBenefitSaveDTO generalGroupBenefitSaveDTO = new GeneralGroupBenefitSaveDTO();
        generalGroupBenefitSaveDTO.setMerchantId(1000319L);
        List<BenefitClassifyEnum> classifyEnumList = new ArrayList<>();
        classifyEnumList.add(BenefitClassifyEnum.USER_RECHARGE_BALANCE);
        generalGroupBenefitSaveDTO.setBenefitClassifyEnumList(classifyEnumList);

        List<Long> groupIdList = new ArrayList<>();
        groupIdList.add(1028514L);
        groupIdList.add(1028513L);
        groupIdList.add(1028512L);
        generalGroupBenefitSaveDTO.setGroupIdList(groupIdList);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/benefit/saveGeneralGroupBenefit");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(generalGroupBenefitSaveDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void saveGroupBenefit() throws Exception{

        GeneralGroupBenefitSaveDTO generalGroupBenefitSaveDTO = new GeneralGroupBenefitSaveDTO();
        generalGroupBenefitSaveDTO.setMerchantId(1000319L);
        List<BenefitClassifyEnum> classifyEnumList = new ArrayList<>();
        classifyEnumList.add(BenefitClassifyEnum.USER_RECHARGE_BALANCE);
        generalGroupBenefitSaveDTO.setBenefitClassifyEnumList(classifyEnumList);

        List<Long> groupIdList = new ArrayList<>();
        groupIdList.add(1037992L);
        generalGroupBenefitSaveDTO.setGroupIdList(groupIdList);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/benefit/saveGroupBenefit");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(generalGroupBenefitSaveDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void createBenefitAccount() throws Exception{

        GenerateAccountDTO generateAccountDTO = new GenerateAccountDTO();
        generateAccountDTO.setGenerateType(BenefitGenerateTypeEnum.SELL);
        generateAccountDTO.setMerchantId(1062179L);
        generateAccountDTO.setMerchantUserId(1220509L);
        generateAccountDTO.setUserId(********L);
        generateAccountDTO.setStoreId(123456L);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/benefit/generateAccount");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(generateAccountDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    @Test
    public void commodityBind() throws Exception{

        GenerateAccountDTO generateAccountDTO = new GenerateAccountDTO();
        generateAccountDTO.setGenerateType(BenefitGenerateTypeEnum.SETTING_COMMODITY);
        generateAccountDTO.setMerchantId(1062179L);
        generateAccountDTO.setCommodityCategoryCode(CommodityCategoryEnum.SET_MEAL.getCode());


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/benefit/generateAccount");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(generateAccountDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    @Test
    public void getMerchantBenefit() throws Exception{

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/benefit/getMerchantBenefit")
                .param("merchantId","1000319")
                .param("benefitClassifyCode","2")
                .param("applicable","2")
                .param("associatedId","1028505")
                ;
        requestBuilder.header("Content-Type","application/json");
        //requestBuilder.content(JSON.toJSONString(generateAccountDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(GlobalErrorCode.OK.getCode())));
    }

    @Test
    public void getBenefitList() throws Exception{

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/benefit/getLyyUserBenefitList")
                .param("lyyUserId","********")
                .param("merchantId","1062179");
        requestBuilder.header("Content-Type","application/json");
        //requestBuilder.content(JSON.toJSONString(dto));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }


    @Test
    public void test() throws Exception {
        BenefitExcludeClassifyDTO benefitExcludeClassifyDTO = new BenefitExcludeClassifyDTO().setMerchantId(1000319L)
                .setExcludeClassifys(Arrays.asList(4))
                .setBenefitIds(Arrays.asList(916743690462445568L, 956869009810669568L, 916743690470834176L, 916743690479222784L));

        List<Long> benefitIdByExcludeClassify = benefitMapper.findBenefitIdByExcludeClassify(benefitExcludeClassifyDTO);

        System.out.println(benefitIdByExcludeClassify);
    }
}
