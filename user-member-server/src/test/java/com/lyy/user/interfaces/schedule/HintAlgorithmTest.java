package com.lyy.user.interfaces.schedule;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lyy.user.domain.correction.dto.CorrectionDTO;
import com.lyy.user.domain.correction.repository.DataGCParser;
import com.lyy.user.domain.user.entity.TagUser;
import com.lyy.user.domain.user.repository.TagUserMapper;
import com.lyy.user.infrastructure.constants.ShardingGCEnum;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Map;
import javax.annotation.Resource;
import javax.sql.DataSource;
import org.apache.shardingsphere.api.hint.HintManager;
import org.apache.shardingsphere.shardingjdbc.jdbc.core.datasource.ShardingDataSource;
import org.assertj.core.util.Lists;
import org.junit.Ignore;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR>
 * @date 2023/9/26
 */
@Ignore
@SpringBootTest
@ExtendWith(SpringExtension.class)
public class HintAlgorithmTest {

    @Resource
    private TagUserMapper tagUserMapper;
    @Resource
    private DataSource dataSource;

    @Test
    public void test01() {
        // 没有配置HintStrategy的话、 HintManage是不会生效的
//        HintManager hintManager = HintManagerHolder.getInstance();
//        hintManager.addDatabaseShardingValue("um_tag_user", 2L);
//        hintManager.setDatabaseShardingValue(1L);
        ShardingDataSource shardingDataSource = (ShardingDataSource) dataSource;
        Map<String, DataSource> dataSourceMap = shardingDataSource.getDataSourceMap();
        System.out.println(dataSourceMap);
        DataSource ds1 = dataSourceMap.get("ds3");
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            connection = ds1.getConnection();
            statement = connection.createStatement();
            TagUser tagUser = tagUserMapper.selectOne(new QueryWrapper<TagUser>().eq("name", "商家-楚饭lee"));
            System.out.println(tagUser);

            String sql = "select * from um_tag_user where name = '商家-楚饭lee';";
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                System.out.println(resultSet);
            }
        } catch(Exception e) {
            e.printStackTrace();
        } finally {
            try {
                resultSet.close();
                statement.close();
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
//            HintManager.clear();
        }

    }

    @Test
    public void test02() {
        DataGCParser umBenefit = DataGCParser.builder().tableName("um_benefit").pageSize(200L).build();
        CorrectionDTO correctionDTO = new CorrectionDTO();
        correctionDTO.setMerchantId(1001018L);
        correctionDTO.setId(910618170180042755L);
        umBenefit.nextIndex(Lists.newArrayList(correctionDTO));
        String sql = umBenefit.pageParser(ShardingGCEnum::getSourceClass);
        System.out.println(sql);
        Assertions.assertNotNull(sql);
    }

    @Test
    public void test05() {
        DataGCParser umBenefit = DataGCParser.builder().tableName("um_benefit").pageSize(200L).build();
        CorrectionDTO correctionDTO = new CorrectionDTO();
        correctionDTO.setMerchantId(1001018L);
        correctionDTO.setId(910618170180042755L);
        String sql = umBenefit.insertParser(TagUser.class);
        System.out.println(sql);
        
        ShardingDataSource shardingDataSource = (ShardingDataSource) dataSource;
        Map<String, DataSource> dataSourceMap = shardingDataSource.getDataSourceMap();
        System.out.println(dataSourceMap);
        DataSource ds1 = dataSourceMap.get("ds3");
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            connection = ds1.getConnection();
            statement = connection.prepareStatement(sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                System.out.println(resultSet);
            }
        } catch(Exception e) {
            e.printStackTrace();
        } finally {
            try {
                resultSet.close();
                statement.close();
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
//            HintManager.clear();
        }


    }

    @Test
    public void test03() {
        DataGCParser umBenefit = DataGCParser.builder().tableName("um_benefit").build();
        String sql = umBenefit.deleteParser(Lists.newArrayList(1L, 2L));
        System.out.println(sql);
        Assertions.assertNotNull(sql);
    }

    @Test
    public void test04() {
        // 没有配置HintStrategy的话、 HintManage是不会生效的
//        HintManager hintManager = HintManagerHolder.getInstance();
//        hintManager.addDatabaseShardingValue("um_tag_user", 2L);
//        hintManager.setDatabaseShardingValue(1L);
        ShardingDataSource shardingDataSource = (ShardingDataSource) dataSource;
        Map<String, DataSource> dataSourceMap = shardingDataSource.getDataSourceMap();
        System.out.println(dataSourceMap);
        DataSource ds1 = dataSourceMap.get("ds3");
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        try {
            connection = ds1.getConnection();
            statement = connection.prepareStatement("");
            TagUser tagUser = tagUserMapper.selectOne(new QueryWrapper<TagUser>().eq("name", "商家-楚饭lee"));
            System.out.println(tagUser);

            String sql = "select * from um_tag_user where name = '商家-楚饭lee';";
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                System.out.println(resultSet);
            }

        } catch(Exception e) {
            e.printStackTrace();
        } finally {
            try {
                resultSet.close();
                statement.close();
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
//            HintManager.clear();
        }

    }

    @Test
    public void test06() {
        HintManager instance = HintManager.getInstance();
        instance.addDatabaseShardingValue("um_tag_user", "ds2");
        TagUser tagUser = tagUserMapper.selectOne(new QueryWrapper<TagUser>().eq("name", "商家-楚饭lee"));
        Assertions.assertNotNull(tagUser);
        HintManager.clear();
    }

}
