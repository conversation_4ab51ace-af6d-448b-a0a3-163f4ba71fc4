package com.lyy.user.interfaces.schedule;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.lyy.user.account.infrastructure.constant.AccountStatusEnum;
import com.lyy.user.domain.account.entity.Account;
import com.lyy.user.infrastructure.repository.account.SmallVenueAccountRepository;
import java.util.ArrayList;
import java.util.Date;
import java.util.Random;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@Import(MemberAccountExpiredHandler.class)
@ExtendWith(SpringExtension.class)
@Slf4j
class MemberAccountExpiredHandlerTest {

    @Resource
    private MemberAccountExpiredHandler handler;
    @MockBean
    private SmallVenueAccountRepository accountRepository;

    @Test
    void doExpired() {
        Account account = new Account();
        account.setId(new Random().nextLong());
        account.setMerchantId(new Random().nextLong());
        account.setDownTime(new Date());
        ArrayList<Account> accounts = Lists.newArrayList(account);
        when(accountRepository.pageExpiredAccount(anyLong(), anyInt(), anyInt()))
                .thenReturn(new Page<Account>().setCurrent(1).setSize(10).setTotal(50).setRecords(accounts))
                .thenReturn(new Page<Account>().setCurrent(2).setSize(10).setTotal(50).setRecords(accounts))
                .thenReturn(new Page<Account>().setCurrent(3).setSize(10).setTotal(50).setRecords(accounts))
                .thenReturn(new Page<Account>().setCurrent(4).setSize(10).setTotal(50).setRecords(accounts))
                .thenReturn(new Page<Account>().setCurrent(5).setSize(10).setTotal(50).setRecords(accounts));

        handler.doExpired(1L);

        verify(accountRepository, times(5)).updateAccountStatus(anyLong(), anyLong(), eq(AccountStatusEnum.OVERDUE));

    }

    @Ignore
    @SpringBootTest
    @ExtendWith(SpringExtension.class)
    static class CIMemberAccountExpiredHandlerTest {

        @Resource
        private MemberAccountExpiredHandler handler;

        @Test
        void doExpired() {
            handler.doExpired(1L);
        }
    }

}