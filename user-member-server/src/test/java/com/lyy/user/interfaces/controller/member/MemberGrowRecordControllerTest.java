package com.lyy.user.interfaces.controller.member;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.google.gson.Gson;
import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.constant.MemberGrowRecordModeEnum;
import com.lyy.user.account.infrastructure.constant.MemberGrowRuleEnum;
import com.lyy.user.account.infrastructure.member.dto.MemberGrowRecordDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGrowRecordQueryDTO;
import java.util.Arrays;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

/**
 * @description:
 * @author: qgw
 * @date on 2021-04-21.
 * @Version: 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class MemberGrowRecordControllerTest {
    Gson gson = new Gson();

    @Autowired
    private MockMvc mockMvc;

    private static final Long MERCHANT_ID = Long.valueOf("1000319");
    private static final Long MERCHANT_USER_ID = Long.valueOf("844983525833179136");
    private static final String CODE = "CODE100";
    private static final Long DB_LOGIN_USERID = 21733732L;

    private static List<Long> USER_ID_LIST = Arrays.asList(1384472711628103682L);


    @Test
    public void list() throws Exception {
        MemberGrowRecordQueryDTO dto = new MemberGrowRecordQueryDTO();
        dto.setMerchantUserIds(Arrays.asList(MERCHANT_USER_ID));
        dto.setMemberIds(Arrays.asList(Long.valueOf("1396016513953042434")));
        dto.setMerchantIds(Arrays.asList(MERCHANT_ID));
        dto.setPageIndex(1);
        dto.setPageSize(20);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/member/grow/record/list");
        requestBuilder.content(gson.toJson(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }


    @Test
    public void saveMemberGrowRecord() throws Exception {
        MemberGrowRecordDTO dto = new MemberGrowRecordDTO();
        dto.setUserId(DB_LOGIN_USERID);
        dto.setMerchantUserId(MERCHANT_USER_ID);
        //dto.setMerchantId(MERCHANT_ID);
        dto.setMerchantId(Long.valueOf("100001"));
        dto.setMemberId(Long.valueOf("4"));
        dto.setRuleId(null);
        dto.setGrowValue(9L);
        //dto.setMoney(BigDecimal.TEN);
        dto.setOutTradeNo("55555555555555555");
        dto.setGrowRuleEnum(MemberGrowRuleEnum.BIND_PHONE);
        dto.setRecordModeEnum(MemberGrowRecordModeEnum.INCREMENT);
        //dto.setResources("支付来源");
        //dto.setDescription("支付来源");
        dto.setOperatorId(DB_LOGIN_USERID);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/rest/member/grow/record/saveMemberGrowRecord");
        requestBuilder.content(gson.toJson(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }
}