package com.lyy.user.interfaces.controller.user;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.google.gson.Gson;
import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneDTO;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneQueryParam;
import java.util.Arrays;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

/**
 * @description:
 * @author: qgw
 * @date on 2021/4/6.
 * @Version: 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class PlatformUserPhoneControllerTest {

    Gson gson = new Gson();
    private static final Long MERCHANT_ID = 1000319L;


    private static final String CODE = "CODE100";
    private static final Long DB_LOGIN_USERID = 1000367L;

    private static List<Long> USER_ID_LIST = Arrays.asList(21744049L, 21743568L);

    @Autowired
    private MockMvc mockMvc;

    @Test
    public void saveOrUpdatePlatformUserPhone() throws Exception {
        PlatformUserPhoneDTO dto = new PlatformUserPhoneDTO();
        dto.setUserId(Long.valueOf("21743568"));
        dto.setMerchantId(Long.valueOf("1000319"));
        dto.setMerchantUserId(Long.valueOf("852544015811739648"));
        dto.setTelephone("13212344321");
        dto.setOperatorId(UserMemberSysConstants.DEFAULT_OPERATION_USER_ID);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/platform/user/phone/saveOrUpdate");
        requestBuilder.content(gson.toJson(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    private PlatformUserPhoneDTO getWithMerchantId() {
        PlatformUserPhoneDTO dto = new PlatformUserPhoneDTO();
        dto.setUserId(USER_ID_LIST.get(1));
        dto.setMerchantId(UserMemberSysConstants.PLATFORM_MERCHANT_ID);
        //dto.setMerchantId(MERCHANT_ID);
        dto.setTelephone("13812341234");
        dto.setOperatorId(DB_LOGIN_USERID);
        return dto;
    }
    private PlatformUserPhoneDTO getWithoutMerchantId() {
        PlatformUserPhoneDTO dto = new PlatformUserPhoneDTO();
        dto.setUserId(USER_ID_LIST.get(1));
        //dto.setMerchantId(MERCHANT_ID);
        dto.setTelephone("13812341234");
        dto.setOperatorId(DB_LOGIN_USERID);
        return dto;
    }

    @Test
    public void list() throws Exception {
        PlatformUserPhoneQueryParam param = new PlatformUserPhoneQueryParam();
        //param.setId();
        param.setUserId(21743568L);
        //param.setMerchantId(MERCHANT_ID);
        param.setTelephone("13812341234");
        //param.setActive();


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/platform/user/phone/list");
        requestBuilder.content(gson.toJson(param));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }
}