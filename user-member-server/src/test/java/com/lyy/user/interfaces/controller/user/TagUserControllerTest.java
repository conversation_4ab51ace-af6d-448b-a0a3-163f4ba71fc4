package com.lyy.user.interfaces.controller.user;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum;
import com.lyy.user.account.infrastructure.constant.TagCategoryEnum;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import com.lyy.user.account.infrastructure.user.dto.tag.TagBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserNumberParam;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagSimpleInfoParam;
import com.lyy.user.account.infrastructure.user.dto.tag.TagStatusDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagStatusInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUnBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserSaveDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingMerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingMerchantUserLinkDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingUserLinkDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.UpdateSpecificBusinessTagsParam;
import com.lyy.user.account.infrastructure.user.dto.tag.ValidList;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

/**
 * @description:
 * @author: qgw
 * @date on 2021/4/2.
 * @Version: 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class TagUserControllerTest {

    Gson gson = new Gson();

    @Autowired
    private MockMvc mockMvc;

    //private static final Long MERCHANT_ID = Long.valueOf("1033412");
    //private static final Long MERCHANT_USER_ID = Long.valueOf("852491030251503616");
    //private static String TAG_ID = "1396649510165340162";

//
//    private static final Long MERCHANT_ID = Long.valueOf("1062179");
//    private static final Long MERCHANT_USER_ID = Long.valueOf("849689015968333824");
//    private static String TAG_ID = "1396726327148580865";

    private static final Long MERCHANT_ID = Long.valueOf("1000319");
    private static final Long MERCHANT_USER_ID = Long.valueOf("875324237548109825");
    private static Long TAG_ID = Long.valueOf("875324033713324032");

    private static final String CODE = "CODE100";
    private static final Long DB_LOGIN_USERID = 21743568L;

    private static List<Long> USER_ID_LIST = Arrays.asList(Long.valueOf("874599468238376961"));

    @Test
    public void list() throws Exception {
        TagUserQueryDTO dto = new TagUserQueryDTO();
        dto.setId(Long.valueOf("933733043779379200"));
        dto.setMerchantId(Long.valueOf("172"));
        //dto.setName("测试");
        //dto.setActive(true);
        dto.setTagType(UserMemberSysConstants.TAG_MERCHANT_USER);
        //dto.setBusinessType(TagBusinessTypeEnum.NORMAL.getStatus());
        dto.setQueryUserNumber(true);
        dto.setPageIndex(1);
        dto.setPageSize(20);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/user/list");
        requestBuilder.content(gson.toJson(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void listTagByUser() throws Exception {
        TagUserQueryDTO dto = new TagUserQueryDTO();
        dto.setId(null);
        dto.setMerchantUserId(Long.valueOf("874599458453065728"));
        dto.setMerchantId(Long.valueOf("1062066"));
        dto.setName("");
        dto.setActive(true);
        dto.setTagType(1);
        dto.setCategory(1);
        dto.setBusinessType(null);
        dto.setState(null);
        dto.setQueryUserNumber(null);
        dto.setPageIndex(1);
        dto.setPageSize(10);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/user/listTagByUser")
                ;
        requestBuilder.content(JSON.toJSONString(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void findByTagId() throws Exception {

        TagUserQueryDTO dto = new TagUserQueryDTO();
        dto.setId(Long.valueOf("874599453881274368"));
        //dto.setMerchantUserId(MERCHANT_USER_ID);
        dto.setMerchantId(Long.valueOf("1062066"));
        dto.setName(null);
        dto.setActive(null);
        dto.setTagType(1);
        dto.setCategory(null);
        dto.setBusinessType(null);
        //dto.setState();
        dto.setQueryUserNumber(null);
        dto.setPageIndex(1);
        dto.setPageSize(10);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/user/findByTagId")
                ;
        requestBuilder.content(JSON.toJSONString(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void findMemberCountByTagIds() throws Exception {

        TagCountUserNumberParam dto = new TagCountUserNumberParam();
        dto.setTagIds(Arrays.asList(933733043779379200L, 933720259486261248L));
        dto.setMerchantId(Long.valueOf("172"));
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/user/findMemberCountByTagIds")
                ;
        requestBuilder.content(JSON.toJSONString(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void countFindByTagId() throws Exception {

        TagCountUserQueryDTO dto = new TagCountUserQueryDTO();
        dto.setId(Long.valueOf("874599453881274368"));
        //dto.setMerchantUserId(MERCHANT_USER_ID);
        dto.setMerchantId(Long.valueOf("1062066"));
        dto.setName(null);
        dto.setActive(null);
        dto.setTagType(1);
        dto.setCategory(null);
        dto.setBusinessType(null);
        //dto.setState();
        dto.setQueryUserNumber(null);
        dto.setPageIndex(1);
        dto.setPageSize(10);
        List<Long> notHandleUserIds = new ArrayList<>();
        notHandleUserIds.add(Long.valueOf("874599484893958144"));
        dto.setNotHandleUserIds(notHandleUserIds);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/user/countFindByTagId")
                ;
        requestBuilder.content(JSON.toJSONString(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }


    @Test
    public void saveOrUpdateTagUser() throws Exception {
            TagUserSaveDTO dto = new TagUserSaveDTO();
            //dto.setId(TAG_ID);
            dto.setName("2帅哥"+System.currentTimeMillis());
            dto.setCode(CODE);
            dto.setMerchantId(MERCHANT_ID);
            dto.setCategory(TagCategoryEnum.MANUAL.getStatus());
            dto.setDescription("创建手动标签1");
            dto.setActive(true);
            dto.setTagType(UserMemberSysConstants.TAG_MERCHANT_USER);
            dto.setOperatorId(DB_LOGIN_USERID);
            dto.setUserIds(USER_ID_LIST);
            dto.setBusinessType(TagBusinessTypeEnum.NORMAL.getStatus());

            MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/user/saveOrUpdateTagUser");
            requestBuilder.content(gson.toJson(dto));
            requestBuilder.header("Content-Type", "application/json");
            ResultActions resultActions = this.mockMvc.perform(requestBuilder);
            // 设置编码
            resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
            // 断言并打印结果
            resultActions.andDo(print())
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code", is("0000000")));

    }


    @Test
    public void changeTagStatus() throws Exception {

        TagStatusDTO dto = new TagStatusDTO();
        List<Long> tagIds = Arrays.asList(TAG_ID);
        List<TagStatusInfoDTO> tagInfos = new ArrayList<>();
        TagStatusInfoDTO tagInfo = new TagStatusInfoDTO();
        tagInfo.setTagIds(tagIds);
        tagInfo.setMerchantId(MERCHANT_ID);
        tagInfos.add(tagInfo);
        dto.setTagInfos(tagInfos);
        dto.setActive(true);
        dto.setOperatorId(DB_LOGIN_USERID);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/user/changeTagStatus");
        requestBuilder.content(gson.toJson(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }


    @Test
    public void deleteTag() throws Exception {

        TagStatusDTO dto = new TagStatusDTO();
        List<Long> tagIds = Arrays.asList(TAG_ID);
        List<TagStatusInfoDTO> tagInfos = new ArrayList<>();
        TagStatusInfoDTO tagInfo = new TagStatusInfoDTO();
        tagInfo.setTagIds(tagIds);
        tagInfo.setMerchantId(MERCHANT_ID);
        tagInfos.add(tagInfo);
        dto.setTagInfos(tagInfos);
        dto.setOperatorId(DB_LOGIN_USERID);
        dto.setState(UserMemberSysConstants.RECORD_STATE_DELETE);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/user/changeTagStatus");
        requestBuilder.content(gson.toJson(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void bindUser() throws Exception {

        TagBindUserDTO dto = new TagBindUserDTO();
        dto.setTagIds(Arrays.asList(Long.valueOf("875324033767849984"),Long.valueOf("875324033713324032")));
        dto.setUserIds(Arrays.asList(Long.valueOf("875324237548109825")));
        dto.setOperatorId(1000367L);
        dto.setChooseAll(false);
        dto.setNotHandleUserIds(null);
        dto.setPageIndex(1);
        dto.setPageSize(20);
        dto.setKeyword(null);
        dto.setMerchantId(MERCHANT_ID);
        dto.setGroupTagIdList(null);
        dto.setOtherTagIdList(null);
        dto.setEquipmentTypeTagIdList(null);
        dto.setTotalConsumeMoneyMin(null);
        dto.setTotalConsumeMoneyMax(null);
        dto.setTotalBalanceMin(null);
        dto.setTotalBalanceMax(null);
        dto.setTotalCoinMin(null);
        dto.setTotalCoinMax(null);
        dto.setLastConsumeTimeSort(null);
        dto.setTotalConsumeMoneySort(null);
        dto.setTotalBalanceSort(null);
        dto.setTotalCoinSort(null);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/user/bindUser");
        requestBuilder.content(gson.toJson(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void unBindUser() throws Exception {

        TagUnBindUserDTO dto = new TagUnBindUserDTO();
        dto.setTagIds(Arrays.asList(Long.valueOf("874599453881274368")));
        dto.setChooseAll(true);
        dto.setNotHandleUserIds(null);
        dto.setUserIds(Arrays.asList(Long.valueOf("874599479013543936"),Long.valueOf("874599480611573760")));
        dto.setId(null);
        dto.setMerchantUserId(null);
        dto.setMerchantId(MERCHANT_ID);
        dto.setName(null);
        dto.setActive(null);
        dto.setTagType(null);
        dto.setCategory(null);
        dto.setBusinessType(null);
        dto.setState(null);
        dto.setQueryUserNumber(null);
        dto.setPageIndex(1);
        dto.setPageSize(10);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/user/unBindUser");
        requestBuilder.content(gson.toJson(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void updateTagName() throws Exception{

        TagDTO dto = new TagDTO();
        dto.setTagId(TAG_ID);
        dto.setMerchantId(MERCHANT_ID);
        //dto.setOriginalName("新增标签A1");
        dto.setNewName("新增标签"+System.currentTimeMillis() );
        dto.setOperatorId(DB_LOGIN_USERID);



        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/user/updateTagName");
        requestBuilder.content(gson.toJson(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void taggingUser() throws Exception{
        //dto:TaggingMerchantUserDTO(merchantId=0, name=女, businessType=SEX, userId=21744049, tagType=0, operatorId=0)

        TaggingMerchantUserDTO dto = new TaggingMerchantUserDTO();
        dto.setMerchantId(1000319L);
        dto.setName("东晙");
        dto.setBusinessType(TagBusinessTypeEnum.USER_SOURCE.getStatus());
        dto.setUserId(21744049L);
        dto.setTagType(UserMemberSysConstants.TAG_MERCHANT_USER);
        dto.setOperatorId(0L);


   /*     TaggingMerchantUserDTO dto = new TaggingMerchantUserDTO();
        dto.setMerchantId(MERCHANT_ID);
        dto.setName("绑定标签");
        dto.setBusinessType(TagBusinessTypeEnum.NORMAL);
        dto.setUserId(88888L);
        dto.setTagType(UserMemberSysConstants.TAG_MERCHANT_USER);
        dto.setOperatorId(DB_LOGIN_USERID);*/

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/user/taggingUser");
        requestBuilder.content(gson.toJson(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }
    @Test
    public void taggingPlatformUser() throws Exception{
        ValidList<TaggingUserLinkDTO> list = new ValidList<>();
        TaggingUserLinkDTO dto = new TaggingUserLinkDTO();
        dto.setName("1平台自动标签");
        dto.setBusinessType(TagBusinessTypeEnum.MEMBER.getStatus());
        dto.setUserId(DB_LOGIN_USERID);
        list.add(dto);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/user/tagging/platformTag");
        requestBuilder.content(gson.toJson(list));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void taggingMerchant() throws Exception{
        ValidList<TaggingMerchantUserLinkDTO> list = new ValidList<>();
        TaggingMerchantUserLinkDTO dto = new TaggingMerchantUserLinkDTO();
        dto.setName("A380标签1");
        dto.setBusinessType(TagBusinessTypeEnum.NORMAL.getStatus());
        dto.setUserId(Long.valueOf("845709771969986560"));
        dto.setMerchantId(MERCHANT_ID);
        dto.setOperatorId(DB_LOGIN_USERID);
        list.add(dto);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/user/tagging/merchantTag");
        requestBuilder.content(gson.toJson(list));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }
    @Test
    public void syncMerchantTag() throws Exception{

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/tag/user/tagging/syncMerchantTag");
        requestBuilder.param("merchantIds", "1000319");
        requestBuilder.param("tagIds", TAG_ID.toString());
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void batchUpdateSpecificBusinessTagsByUserId() throws Exception{
        UpdateSpecificBusinessTagsParam dto = new UpdateSpecificBusinessTagsParam();
        dto.setTagIds(Arrays.asList(Long.valueOf("933733043779379200"),Long.valueOf("933720259486261248"),Long.valueOf("934096802150584320")));
        dto.setOperatorId(0L);
        dto.setMerchantUserId(Long.valueOf("934100097049006080"));
        dto.setMerchantId(172L);
        dto.setBusinessType(TagBusinessTypeEnum.NORMAL);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/user/batchUpdateSpecificBusinessTagsByUserId");
        requestBuilder.content(gson.toJson(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));

    }

    @Test
    public void tagSimpleInfoList() throws Exception {
        TagSimpleInfoParam dto = new TagSimpleInfoParam();
        dto.setTagIds(Arrays.asList(951519775606177792L));
        dto.setMerchantId(1000319L);
        dto.setNames(Arrays.asList("男", "女", "标签测试"));
        dto.setCategory(1);
        dto.setActive(true);
        dto.setState(0);
        dto.setBusinessTypes(Arrays.asList(1,2,3,4,5));
        dto.setSearchTagName("测试");

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/user/tagSimpleInfoList");
        requestBuilder.content(gson.toJson(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

}