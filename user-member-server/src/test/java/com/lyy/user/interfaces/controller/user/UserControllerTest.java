package com.lyy.user.interfaces.controller.user;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.alibaba.fastjson.JSON;
import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.constant.UserSourceEnum;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserCheckPasswordDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserUpdateTelephoneDTO;
import com.lyy.user.account.infrastructure.user.dto.MobileTerminalUserCancellationDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueMobileUserListSelectDTO;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueUserListSelectDTO;
import com.lyy.user.account.infrastructure.user.dto.UpdateMerchantUserInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.UserCreateDTO;
import com.lyy.user.account.infrastructure.user.dto.UserInfoDTO;
import java.util.Arrays;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

/**
 * @ClassName: UserControllerTest
 * @description: 用户测试
 * @author: pengkun
 * @date: 2021/04/01
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class UserControllerTest {

    @Autowired
    private MockMvc mockMvc;

    private final Long lyyUserId_Jason =  21743568L;

    // 开发常用账户
    private final Long merchantId_15018442322 =  1062179L;


    @Test
    public void getUserInfoByUserIdAndMerchantId() throws Exception{
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/user/getUserInfoByUserIdAndMerchantId")
                .param("userId",lyyUserId_Jason.toString())
                .param("merchantId",merchantId_15018442322.toString());
        requestBuilder.header("Content-Type","application/json");
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void getUserInfoByUserIdAndMerchantUserId() throws Exception{
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/user/getUserInfoByUserIdAndMerchantUserId")
                .param("userId","21744049")
                .param("merchantId","1000319")
                .param("merchantUserId","855427544404983808");
        requestBuilder.header("Content-Type","application/json");
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void getUserInfoByOpenIdOrUnionId() throws Exception{
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/user/getUserInfoByOpenIdOrUnionId")
                .param("openId","o5DGls83lPYsF485F5G7KeF330B8")
//                .param("unionId","oddSPwCB8hs3yHnSIZ9ZuxugeaTg")
                .param("merchantId","1000319")
                ;
        requestBuilder.header("Content-Type","application/json");
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void listByTelephoneAndMerchantId() throws Exception{
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/user/listByTelephoneAndMerchantId")
                .param("telephone","13337227105")
                .param("merchantId","1000319")
                ;
        requestBuilder.header("Content-Type","application/json");
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void updateTelephoneTest() throws Exception{
        UserInfoDTO dto = new UserInfoDTO();
        dto.setMerchantId(1000319L);
        dto.setMerchantUserId(1385150690604134402L);
        dto.setLyyUserId(21744049L);
        dto.setTelephone("18578781458");
        dto.setUpdateby(10001L);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/updateTelephone");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(dto));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void deleteUserInfoTest() throws Exception{
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/user/deleteUserInfo")
                .param("userId","21754381")
                .param("merchantUserId","1385150690604134402")
                .param("merchantId","1000319")
                .param("operationUserId","123456")
                ;
        requestBuilder.header("Content-Type","application/json");
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void weChatUserWebInitTest() throws Exception{
        UserCreateDTO userCreateDTO = new UserCreateDTO();
        userCreateDTO.setAppId("123456");
        userCreateDTO.setOpenid("wxwebopenid1000005");
        userCreateDTO.setUnionid("wxwebunionid1000005");
        userCreateDTO.setNickname("weixin000004");
        userCreateDTO.setPassword("ff8aaa8a2dde9154");
        userCreateDTO.setGender("男");
        userCreateDTO.setHeadImg("");
        userCreateDTO.setSilent("1");
        userCreateDTO.setProvince("广东");
        userCreateDTO.setCityName("广州");

        userCreateDTO.setTelephone("18578781321");

        userCreateDTO.setUserSourceEnum(UserSourceEnum.WE_CHAT_WEB);

        UserCreateDTO.MerchantUserCreateDTO merchantUserCreateDTO = new UserCreateDTO.MerchantUserCreateDTO();
        merchantUserCreateDTO.setMerchantId(1000319L);
//        merchantUserCreateDTO.setTelephone();
        userCreateDTO.setMerchantUserCreateDTO(merchantUserCreateDTO);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/initUserInfo");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(userCreateDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void weChatUserMiniProgramInitTest() throws Exception{
        UserCreateDTO userCreateDTO = new UserCreateDTO();
        userCreateDTO.setAppId("123456");
        userCreateDTO.setOpenid("wxminiprogramopenid1000006");
        userCreateDTO.setUnionid("wxminiprogramunionid1000006");
        userCreateDTO.setNickname("weixinminiprogram000006");
        userCreateDTO.setPassword("ff8aaa8a2dde9154");
        userCreateDTO.setGender("男");
        userCreateDTO.setHeadImg("");
        userCreateDTO.setSilent("1");
        userCreateDTO.setProvince("广东");
        userCreateDTO.setCityName("深圳");

        userCreateDTO.setTelephone("");

        userCreateDTO.setUserSourceEnum(UserSourceEnum.WE_CHAT_MINI_PROGRAM);

        UserCreateDTO.MerchantUserCreateDTO merchantUserCreateDTO = new UserCreateDTO.MerchantUserCreateDTO();
        merchantUserCreateDTO.setMerchantId(1000319L);
//        merchantUserCreateDTO.setTelephone();
        userCreateDTO.setMerchantUserCreateDTO(merchantUserCreateDTO);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/initUserInfo");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(userCreateDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void aliUserWebInitTest() throws Exception{
        UserCreateDTO userCreateDTO = new UserCreateDTO();
        userCreateDTO.setUserSourceEnum(UserSourceEnum.ALI_WEB);
        userCreateDTO.setAppId("123456");
        userCreateDTO.setOpenid("aliwebopenid1000003");
        userCreateDTO.setUnionid("aliwebunionid1000003");
        userCreateDTO.setNickname("aliweb000003");
        userCreateDTO.setPassword("ff8aaa8a2dde9154");
        userCreateDTO.setGender("男");
        userCreateDTO.setHeadImg("");
        userCreateDTO.setSilent("1");
        userCreateDTO.setProvince("广东");
        userCreateDTO.setCityName("深圳");
        userCreateDTO.setTelephone("15580928311");

        UserCreateDTO.MerchantUserCreateDTO merchantUserCreateDTO = new UserCreateDTO.MerchantUserCreateDTO();
        userCreateDTO.setTelephone("15580928311");
        merchantUserCreateDTO.setMerchantId(1000319L);
        userCreateDTO.setMerchantUserCreateDTO(merchantUserCreateDTO);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/initUserInfo");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(userCreateDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void aliUserMiniProgramInitTest() throws Exception{
        UserCreateDTO userCreateDTO = new UserCreateDTO();
        userCreateDTO.setUserSourceEnum(UserSourceEnum.ALI_MINI_PROGRAM);
        userCreateDTO.setAppId("123456");
        userCreateDTO.setOpenid("aliminiprogramopenid1000005");
        userCreateDTO.setUnionid("aliminiprogramunionid1000005");
        userCreateDTO.setNickname("aliminiprogram000005");
        userCreateDTO.setPassword("ff8aaa8a2dde9154");
        userCreateDTO.setGender("男");
        userCreateDTO.setHeadImg("");
        userCreateDTO.setSilent("1");
        userCreateDTO.setProvince("广东");
        userCreateDTO.setCityName("深圳");
        userCreateDTO.setTelephone("15580928366");

//        UserCreateDTO.MerchantUserCreateDTO merchantUserCreateDTO = new UserCreateDTO.MerchantUserCreateDTO();
//        userCreateDTO.setTelephone("15580928311");
//        merchantUserCreateDTO.setMerchantId(1000319L);
//        userCreateDTO.setMerchantUserCreateDTO(merchantUserCreateDTO);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/initUserInfo");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(userCreateDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void jdUserInitTest() throws Exception{
        UserCreateDTO userCreateDTO = new UserCreateDTO();
        userCreateDTO.setUserSourceEnum(UserSourceEnum.JD);
        userCreateDTO.setAppId("123456");
        userCreateDTO.setOpenid("jdopenid1000004");
        userCreateDTO.setUnionid("jdunionid1000004");
        userCreateDTO.setNickname("jd000004");
        userCreateDTO.setPassword("ff8aaa8a2dde9154");
        userCreateDTO.setGender("男");
        userCreateDTO.setHeadImg("");
//        userCreateDTO.setSilent("1");
        userCreateDTO.setProvince("广东");
        userCreateDTO.setCityName("深圳");
//        userCreateDTO.setTelephone("15580928366");

        UserCreateDTO.MerchantUserCreateDTO merchantUserCreateDTO = new UserCreateDTO.MerchantUserCreateDTO();
        merchantUserCreateDTO.setMerchantId(1000319L);
        userCreateDTO.setMerchantUserCreateDTO(merchantUserCreateDTO);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/initUserInfo");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(userCreateDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void unionUserInitTest() throws Exception{
        UserCreateDTO userCreateDTO = new UserCreateDTO();
        userCreateDTO.setUserSourceEnum(UserSourceEnum.UNION);
        userCreateDTO.setAppId("123456");
        userCreateDTO.setOpenid("unionopenid1000001");
        userCreateDTO.setUnionid("unionunionid1000001");
        userCreateDTO.setNickname("union000001");
        userCreateDTO.setPassword("ff8aaa8a2dde9154");
        userCreateDTO.setGender("男");
        userCreateDTO.setHeadImg("");
        userCreateDTO.setProvince("广东");
        userCreateDTO.setCityName("深圳");
//        userCreateDTO.setTelephone("15580928366");

        UserCreateDTO.MerchantUserCreateDTO merchantUserCreateDTO = new UserCreateDTO.MerchantUserCreateDTO();
        userCreateDTO.setMerchantUserCreateDTO(merchantUserCreateDTO);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/initUserInfo");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(userCreateDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void otherUserInitTest() throws Exception{
        UserCreateDTO userCreateDTO = new UserCreateDTO();
        userCreateDTO.setUserSourceEnum(UserSourceEnum.OTHER);
        userCreateDTO.setNickname("union000001");
        userCreateDTO.setPassword("ff8aaa8a2dde9154");
        userCreateDTO.setGender("男");
        userCreateDTO.setHeadImg("");
        userCreateDTO.setProvince("广东");
        userCreateDTO.setCityName("深圳");
        userCreateDTO.setTelephone("15580928026");

        UserCreateDTO.MerchantUserCreateDTO merchantUserCreateDTO = new UserCreateDTO.MerchantUserCreateDTO();
        userCreateDTO.setMerchantUserCreateDTO(merchantUserCreateDTO);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/initUserInfo");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(userCreateDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void smallVenueUserInitTest() throws Exception{
        UserCreateDTO userCreateDTO = new UserCreateDTO();
        userCreateDTO.setAppId("");
        userCreateDTO.setOpenid("");
        userCreateDTO.setUnionid("");
        userCreateDTO.setNickname("");
        userCreateDTO.setPassword("ff8aaa8a2dde9154");
        userCreateDTO.setGender("男");
        userCreateDTO.setHeadImg("");
//        userCreateDTO.setProvince("广东");
//        userCreateDTO.setCityName("广州");
//        userCreateDTO.setTelephone("18578781321");
        userCreateDTO.setCardNo("Lyy000001");

        userCreateDTO.setUserSourceEnum(UserSourceEnum.SMALL_VENUE_MOBILE_COUNTER);

        UserCreateDTO.MerchantUserCreateDTO merchantUserCreateDTO = new UserCreateDTO.MerchantUserCreateDTO();
        merchantUserCreateDTO.setMerchantId(1000319L);
//        merchantUserCreateDTO.setTelephone();
        userCreateDTO.setMerchantUserCreateDTO(merchantUserCreateDTO);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/initUserInfo");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(userCreateDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void smallVenueUserInitByPhoneTest() throws Exception{
        UserCreateDTO userCreateDTO = new UserCreateDTO();
        userCreateDTO.setAppId("");
        userCreateDTO.setOpenid("");
        userCreateDTO.setUnionid("");
        userCreateDTO.setNickname("测试111");
        userCreateDTO.setPassword("ff8aaa8a2dde912442322254");
        userCreateDTO.setGender("男");
        userCreateDTO.setHeadImg("");
        userCreateDTO.setProvince("广东");
        userCreateDTO.setCityName("广州");
        userCreateDTO.setTelephone("13337227018");
        userCreateDTO.setProvinceId(1000014L);
        userCreateDTO.setCityId(1000073L);
        userCreateDTO.setRegionId(1000552L);
        userCreateDTO.setAddress("山卡拉");
//        userCreateDTO.setCardNo("Lyy000001");

        userCreateDTO.setUserSourceEnum(UserSourceEnum.SMALL_VENUE_MOBILE_COUNTER);

        UserCreateDTO.MerchantUserCreateDTO merchantUserCreateDTO = new UserCreateDTO.MerchantUserCreateDTO();
        merchantUserCreateDTO.setMerchantId(1000319L);
        merchantUserCreateDTO.setStoreName("大学城");
        merchantUserCreateDTO.setStoreId(111111L);
//        merchantUserCreateDTO.setTelephone();
        userCreateDTO.setMerchantUserCreateDTO(merchantUserCreateDTO);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/initUserInfo");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(userCreateDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void smallVenueWeChatUserIniTest() throws Exception{
        UserCreateDTO userCreateDTO = new UserCreateDTO();
        userCreateDTO.setAppId("aaaaaa");
        userCreateDTO.setOpenid("Lyy_Venue_13337227018");
        userCreateDTO.setUnionid("Lyy_Venue_13337227018");
        userCreateDTO.setAppId("az0001");
        userCreateDTO.setNickname("测试1");
        userCreateDTO.setPassword("ff8aaa8a2dde912442322254");
        userCreateDTO.setGender("男");
        userCreateDTO.setHeadImg("");
        userCreateDTO.setProvince("广东");
        userCreateDTO.setCityName("广州");
        userCreateDTO.setTelephone("13337227018");
//        userCreateDTO.setAddress("山卡拉");
//        userCreateDTO.setProvinceId(1000014L);
//        userCreateDTO.setCityId(1000073L);
//        userCreateDTO.setRegionId(1000552L);
//        userCreateDTO.setCardNo("Lyy000001");

        userCreateDTO.setUserSourceEnum(UserSourceEnum.SMALL_VENUE_WE_CHAT_MINI_PROGRAM);

        UserCreateDTO.MerchantUserCreateDTO merchantUserCreateDTO = new UserCreateDTO.MerchantUserCreateDTO();
        merchantUserCreateDTO.setMerchantId(1000319L);
        merchantUserCreateDTO.setStoreName("大学城");
        merchantUserCreateDTO.setStoreId(111111L);
//        merchantUserCreateDTO.setTelephone();
        userCreateDTO.setMerchantUserCreateDTO(merchantUserCreateDTO);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/initUserInfo");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(userCreateDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void updateUserInfoTest() throws Exception{
        MerchantUserDTO dto = new MerchantUserDTO();
        dto.setUserId(21744049L);
        dto.setMerchantId(1000319L);
        dto.setId(855427544404983808L);
        dto.setName("测试01");

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/updateUserInfo");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(dto));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void getPlatformUserInfoByOpenIdAndAppId() throws Exception{
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/user/getPlatformUserInfoByOpenIdAndAppId")
                .param("openId","ou9w95YuhMsGp4NSTfuB6ISehcbc")
                .param("appId","wx9bb6e499d0994f6b")
                ;
        requestBuilder.header("Content-Type","application/json");
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    /**
     * 会员密码校验
     * @throws Exception
     */
    @Test
    public void smallVenueCheckUserPassword() throws Exception {

        MerchantUserCheckPasswordDTO merchantUserCheckPasswordDTO = new MerchantUserCheckPasswordDTO()
                .setMerchantId(10000L)
                .setUserId(21758448L)
                .setPassWord("123456");

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/smallVenue/checkUserPassword");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(merchantUserCheckPasswordDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }


    @Test
    public void smallVenueSearchByKeywords() throws Exception{
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/user/smallVenue/searchByKeywords")
                .param("merchantId", "1000319")
                .param("keyWords", "18819273423");
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    /**
     * 更新会员信息
     * @throws Exception
     */
    @Test
    public void updateMerchantUserInfo() throws Exception {

        UpdateMerchantUserInfoDTO updateMerchantUserInfoDTO = new UpdateMerchantUserInfoDTO()
                .setId(922932342769762305L)
                .setMerchantId(10000L)
                .setUserId(21758448L)
                .setBirthday("1999-09-09")
                .setName("ximenqing")
                .setProvinceCity("广东省广州市")
                .setGender("女")
                .setOperatorId(9567L)
                .setOperatorName("西门");

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/smallVenue/updateMerchantUserInfo");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(updateMerchantUserInfoDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    /**
     * 会员列表
     * @throws Exception
     */
    @Test
    public void userSmallVenueList() throws Exception {

        SmallVenueUserListSelectDTO smallVenueUserListSelectDTO = new SmallVenueUserListSelectDTO()
                .setUserId(934179985458192384L)
                .setMerchantId(172L)
                .setPageIndex(1)
                .setUserType(Arrays.asList("Z"))
                .setPageSize(75);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/smallVenue/list");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(smallVenueUserListSelectDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }
    /**
     * 会员列表
     * @throws Exception
     */
    @Test
    public void smallVenueMobileUserList() throws Exception {

        SmallVenueMobileUserListSelectDTO smallVenueUserListSelectDTO = new SmallVenueMobileUserListSelectDTO();


        smallVenueUserListSelectDTO.setKeyword("1");
        smallVenueUserListSelectDTO.setMerchantId(172L);
        smallVenueUserListSelectDTO.setGroupTagIdList(Arrays.asList(1L,2L));
        smallVenueUserListSelectDTO.setOtherTagIdList(Arrays.asList(1L,2L));
        smallVenueUserListSelectDTO.setEquipmentTypeTagIdList(Arrays.asList(1L,2L));
        SmallVenueMobileUserListSelectDTO.SpecificFilterInfoDTO specificFilterInfoDTO = new SmallVenueMobileUserListSelectDTO.SpecificFilterInfoDTO();
        specificFilterInfoDTO.setTotalConsumeMoneyMin(0);
        specificFilterInfoDTO.setTotalConsumeMoneyMax(1000);
        specificFilterInfoDTO.setMemberLevelIdList(Arrays.asList(1L,2L));
        specificFilterInfoDTO.setUserTypeList(Arrays.asList("Z","A","W"));

        smallVenueUserListSelectDTO.setSpecificFilterInfoDTO(specificFilterInfoDTO);
        List<SmallVenueMobileUserListSelectDTO.MerchantBenefitClassifyDTO> merchantBenefitClassifyDTO = null;
        smallVenueUserListSelectDTO.setMerchantBenefitClassifyDTOList(merchantBenefitClassifyDTO);
        SmallVenueMobileUserListSelectDTO.SortInfoDTO sortInfoDTO = new SmallVenueMobileUserListSelectDTO.SortInfoDTO();
        sortInfoDTO.setLastConsumeTimeSort("asc");
        sortInfoDTO.setTotalConsumeMoneySort("desc");
        sortInfoDTO.setRegisterTimeSort("asc");
        smallVenueUserListSelectDTO.setSortInfoDTO(sortInfoDTO);

        smallVenueUserListSelectDTO.setPageIndex(1);
        smallVenueUserListSelectDTO.setPageSize(20);


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/smallVenue/mobile/list");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(smallVenueUserListSelectDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }


    /**
     * 更新会员手机号码
     * @throws Exception
     */
    @Test
    public void userUpdateTelephone() throws Exception {

        MerchantUserUpdateTelephoneDTO merchantUserUpdateTelephoneDTO = new MerchantUserUpdateTelephoneDTO()
                .setMerchantId(1061500L)
                .setUserId(21751793L)
                .setOldTelephone("13662693677")
                .setNewTelephone("13662693676");
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/smallVenue/updateUserTelephone");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(merchantUserUpdateTelephoneDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    /**
     * 移动端会员注销
     * @throws Exception
     */
    @Test
    public void mobileTerminalMerchantUserCancellation() throws Exception {

        MobileTerminalUserCancellationDTO mobileTerminalUserCancellationDTO = new MobileTerminalUserCancellationDTO()
                .setMerchantUserId(973675692222332928L)
                .setMerchantId(1444743L)
                .setOperatorId(1234567L);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/user/mobileTerminal/merchantUserCancellation");
        requestBuilder.header("Content-Type", "application/json");
        requestBuilder.content(JSON.toJSONString(mobileTerminalUserCancellationDTO));
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

}
