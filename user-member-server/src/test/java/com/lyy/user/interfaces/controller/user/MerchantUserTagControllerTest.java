package com.lyy.user.interfaces.controller.user;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.google.gson.Gson;
import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserInfoQueryDTO;
import java.util.Arrays;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
/**
 * @description:
 * @author: qgw
 * @date on 2021/4/15.
 * @Version: 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class MerchantUserTagControllerTest {

    Gson gson = new Gson();

    @Autowired
    private MockMvc mockMvc;

    private static final Long MERCHANT_ID = 1000319L;
    private static final String CODE = "CODE100";
    private static final Long DB_LOGIN_USERID = 1000367L;

    private static List<Long> USER_ID_LIST = Arrays.asList(21744049L, 21743568L);

    @Test
    public void listByTagIds() throws Exception{
        MerchantUserInfoQueryDTO dto = new MerchantUserInfoQueryDTO();
        //dto.setIds(Lists.newArrayList());
        dto.setMerchantId(MERCHANT_ID);
        dto.setPageIndex(1);
        dto.setPageSize(20);




        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/tag/merchant/user/listByTagIds");
        requestBuilder.content(gson.toJson(dto));
        requestBuilder.header("Content-Type", "application/json");
        ResultActions resultActions = this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }
}