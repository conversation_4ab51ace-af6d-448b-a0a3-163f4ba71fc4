package com.lyy.user.interfaces.schedule;

import com.google.common.collect.Lists;
import java.util.ArrayList;
import javax.annotation.Resource;
import org.junit.Ignore;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@Ignore
@SpringBootTest
@ExtendWith(SpringExtension.class)
class ExpireBenefitClearHandlerTest {

    @Resource
    private ExpireBenefitClearHandler handler;
    @Resource
    private ExpireBenefitClearRollbackHandler rollbackHandler;

    @Test
    void expireBenefitClear() throws Exception {
        handler.expireBenefitClear(2, 10, 100, new ArrayList<>());
    }

    @Test
    void expireBenefitClearRollback() throws Exception {
        rollbackHandler.expireBenefitClearRollback(0, Lists.newArrayList(1062047L));
    }

}