package com.lyy.user.interfaces.controller.user;

import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.alibaba.fastjson.JSON;
import com.lyy.user.Application;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.PayoutBenefitDTO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

/**
 * @ClassName: MerchantUserTest
 * @description: 商户用户单元测试类
 * @author: pengkun
 * @date: 2021/03/31
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class MerchantUserControllerTest {

    @Autowired
    private MockMvc mockMvc;

    private final Long merchantId_15899974725 =  1000319L;


    @Test
    public void saveOrUpdateMerchantUserTest() throws Exception{
        MerchantUserDTO dto = new MerchantUserDTO();
        dto.setId(846747934830198784L);
        dto.setMerchantId(1000319L);
        dto.setUserId(21743568L);
        dto.setTelephone("13250512728");
        dto.setUserType("A");
        dto.setGender("女");
//        dto.setId(10910L);

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/merchant/user/saveOrUpdateMerchantUser");
        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(dto));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void findByIdTest() throws Exception{
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/merchant/user/findById")
                .param("merchantId","1000319")
                .param("merchantUserId","10910");
        requestBuilder.header("Content-Type","application/json");
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }
    
    @Test
    public void findByUserIdAndMerchantIdTest() throws Exception{
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/merchant/user/findByUserIdAndMerchantId")
                .param("userId","21744049")
                .param("merchantId","1000319");
        requestBuilder.header("Content-Type","application/json");
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void queryUserListByMerchant() throws Exception{
        MerchantUserQueryDTO merchantUserQueryDTO = new MerchantUserQueryDTO();
        merchantUserQueryDTO.setMerchantId(merchantId_15899974725);
        //merchantUserQueryDTO.setKeyword("866744857980846080");

        List<Long> groupTagIdList = new ArrayList<>();
        groupTagIdList.add(875324034166308864L);
        merchantUserQueryDTO.setGroupTagIdList(groupTagIdList);

        List<Long> otherTagIdList = new ArrayList<>();
        otherTagIdList.add(875338420594077696L);
        otherTagIdList.add(875769360789213184L);
        merchantUserQueryDTO.setOtherTagIdList(otherTagIdList);

        List<Long> equipmentTypeIdList = new ArrayList<>();
        //equipmentTypeIdList.add();
        merchantUserQueryDTO.setEquipmentTypeTagIdList(equipmentTypeIdList);

        //merchantUserQueryDTO.setSexTagId(860228642462515200L);
        merchantUserQueryDTO.setLastConsumeTimeSort("desc");
        //merchantUserQueryDTO.setTotalConsumeMoneySort("asc");
        //merchantUserQueryDTO.setTotalBalanceSort("asc");
        //merchantUserQueryDTO.setTotalCoinSort("desc");
        merchantUserQueryDTO.setPageIndex(1);
        merchantUserQueryDTO.setPageSize(10);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/merchant/user/queryUserListByMerchant");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(merchantUserQueryDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void countUserListByMerchant() throws Exception{
        MerchantUserQueryDTO merchantUserQueryDTO = new MerchantUserQueryDTO();
        merchantUserQueryDTO.setMerchantId(merchantId_15899974725);
        //merchantUserQueryDTO.setKeyword("866744857980846080");

        List<Long> groupTagIdList = new ArrayList<>();
        groupTagIdList.add(875324034166308864L);
        merchantUserQueryDTO.setGroupTagIdList(groupTagIdList);

        List<Long> otherTagIdList = new ArrayList<>();
        otherTagIdList.add(875338420594077696L);
        otherTagIdList.add(875769360789213184L);
        merchantUserQueryDTO.setOtherTagIdList(otherTagIdList);

        List<Long> equipmentTypeIdList = new ArrayList<>();
        //equipmentTypeIdList.add();
        merchantUserQueryDTO.setEquipmentTypeTagIdList(equipmentTypeIdList);

        //merchantUserQueryDTO.setSexTagId(860228642462515200L);
        merchantUserQueryDTO.setLastConsumeTimeSort("desc");
        //merchantUserQueryDTO.setTotalConsumeMoneySort("asc");
        //merchantUserQueryDTO.setTotalBalanceSort("asc");
        //merchantUserQueryDTO.setTotalCoinSort("desc");
        merchantUserQueryDTO.setPageIndex(1);
        merchantUserQueryDTO.setPageSize(10);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/merchant/user/countUserListByMerchant");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(merchantUserQueryDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void testPayoutWelfare() throws Exception {

        PayoutBenefitDTO payoutWelfareDTO = new PayoutBenefitDTO();

        payoutWelfareDTO.setIsAllUser(true);
        //payoutWelfareDTO.getGroupIdList()
        //payoutWelfareDTO.setMerchantUserIdList(Arrays.asList(856168698587119616L));
        payoutWelfareDTO.setMerchantId(1000319L);

        //payoutWelfareDTO.setIsAllEquipmentType(true);
        List<Long> groupIdList = new ArrayList<>();
        //groupIdList.add(1028505L);
        payoutWelfareDTO.setGroupIdList(groupIdList);

        List<Long> equipmentTypeIdList = new ArrayList<>();
        //equipmentTypeIdList.add(1000001L);
        //payoutWelfareDTO.setEquipmentTypeIdList(equipmentTypeIdList);


        payoutWelfareDTO.setPayoutBalance(new BigDecimal("2"));
        //payoutWelfareDTO.setPayoutCoin(new BigDecimal("1"));
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.post("/merchant/user/payout/benefit");

        requestBuilder.header("Content-Type","application/json");
        requestBuilder.content(JSON.toJSONString(payoutWelfareDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void testGetIndexUserAccount() throws Exception {


        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/merchant/user/getIndexAccountInfo")
                .param("merchantUserId","1400034165117685762")
                .param("merchantId","1000319");

        requestBuilder.header("Content-Type","application/json");
        //requestBuilder.content(JSON.toJSONString(payoutWelfareDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }

    @Test
    public void testGetAllMerchantUserId() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders.get("/merchant/user/getAllMerchantUserId")
                .param("merchantId","1000319");

        requestBuilder.header("Content-Type","application/json");
        //requestBuilder.content(JSON.toJSONString(payoutWelfareDTO));
        ResultActions resultActions =  this.mockMvc.perform(requestBuilder);
        // 设置编码
        resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
        // 断言并打印结果
        resultActions.andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is("0000000")));
    }
}
