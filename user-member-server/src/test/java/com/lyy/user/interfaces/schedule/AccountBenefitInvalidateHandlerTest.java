package com.lyy.user.interfaces.schedule;

import org.junit.Ignore;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

@Ignore
@SpringBootTest
@ExtendWith(SpringExtension.class)
class AccountBenefitInvalidateHandlerTest {

    @Resource
    private AccountBenefitInvalidateHandler handler;

    @Test
    void smallVenueBenefitExpire() {
        handler.smallVenueBenefitExpire(1L);
    }

    @Test
    void invalidateBenefit() {
        handler.invalidateBenefit(31L);
    }
}