<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>user-member</artifactId>
        <groupId>com.lyy</groupId>
        <version>0.2.0-SNAPSHOT</version>
    </parent>
    <artifactId>user-member-server</artifactId>
    <name>user-member-server</name>
    <version>${project.version}</version>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>4.3.1</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.28</version>
        </dependency>
        <dependency>
            <groupId>com.lyy.starter</groupId>
            <artifactId>common</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.lyy.base</groupId>
            <artifactId>utils2</artifactId>
            <version>${common.version}</version>
            <exclusions>
                <!-- mybatis-spring 已经引用，且此处有冲突  -->
                <exclusion>
                    <groupId>org.mybatis.spring.boot</groupId>
                    <artifactId>mybatis-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>lyy-lock</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.lyy.starter</groupId>
            <artifactId>nacos-discovery-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
            <version>4.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-json</artifactId>
        </dependency>

        <!-- 内部项目依赖包 -->

        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>user-member-rpc</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>user-app-rpc</artifactId>
            <version>${user-app.version}</version>
        </dependency>

        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>error-code</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>lyy_dto</artifactId>
            <version>${common.version}</version>
            <optional>true</optional>
        </dependency>
        <!--用xxl来做定时任务-->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.github.shalousun</groupId>
            <artifactId>smart-doc</artifactId>
            <version>2.1.7</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>multilevel_cache_starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>multilevel_cache_core</artifactId>
        </dependency>

        <!-- 临时加入，避免项目报错 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.2.12</version>
        </dependency>

        <!--营销用于同步券数量-->
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>marketing_api</artifactId>
            <version>${marketing.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-hystrix</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>tools</artifactId>
                    <groupId>cn.lyy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>user_statistics_api</artifactId>
                    <groupId>cn.lyy</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.lyy</groupId>
                    <artifactId>commodity-rpc</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cn.lyy</groupId>
                    <artifactId>marketing_dto</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.lyy</groupId>
            <artifactId>marketing_dto</artifactId>
            <version>${marketing.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>tools</artifactId>
                    <groupId>cn.lyy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>user_statistics_api</artifactId>
                    <groupId>cn.lyy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>

        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${org.mapstruct.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>circurtbreaker-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>lyy-idempotent-spring-boot-starter</artifactId>
            <version>${idempotent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.lyy</groupId>
            <artifactId>lyy-idempotent-postgres</artifactId>
            <version>${idempotent.version}</version>
        </dependency>
        <dependency>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
            <version>6.1.10.RELEASE</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <deploy>local</deploy>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <deploy>dev</deploy>
            </properties>
        </profile>
        <profile>
            <id>sit</id>
            <properties>
                <deploy>sit</deploy>
            </properties>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <deploy>uat</deploy>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <deploy>prod</deploy>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>app</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.1</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <source>8</source>
                    <target>8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.7</version>
                <configuration>
                    <configurationFile>${basedir}/src/main/resources/generator/generatorConfig.xml</configurationFile>
                    <overwrite>true</overwrite>
                    <verbose>true</verbose>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.postgresql</groupId>
                        <artifactId>postgresql</artifactId>
                        <version>${postgresql.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>tk.mybatis</groupId>
                        <artifactId>mapper-generator</artifactId>
                        <version>1.0.0</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-prod-resources</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <!-- this is important -->
                            <overwrite>true</overwrite>
                            <outputDirectory>${basedir}/target/classes</outputDirectory>
                            <resources>
                                <resource>
                                    <filtering>true</filtering>
                                    <directory>deploy/${deploy}</directory>
                                    <targetPath>${basedir}/target/classes</targetPath>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>2.1.7</version>
                <configuration>
                    <!--指定生成文档的使用的配置文件-->
                    <configFile>./src/main/resources/smart-doc.json</configFile>
                    <!--指定项目名称-->
                    <projectName>新会员中心</projectName>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>html</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <sourceDirectory>${basedir}/src/main/java</sourceDirectory>
        <testSourceDirectory>${basedir}/src/test/java</testSourceDirectory>
        <testOutputDirectory>target/test-classes</testOutputDirectory>
    </build>

</project>
