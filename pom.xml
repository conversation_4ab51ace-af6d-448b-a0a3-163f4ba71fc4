<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.lyy.base</groupId>
        <artifactId>base-parent</artifactId>
        <version>1.1.0</version>
    </parent>


    <groupId>com.lyy</groupId>
    <artifactId>user-member</artifactId>
    <version>0.2.0-SNAPSHOT</version>
    <name>user-member</name>
    <packaging>pom</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.plugin.version>3.8.1</maven.plugin.version>
        <java.version>1.8</java.version>
        <org.mapstruct.version>1.4.2.Final</org.mapstruct.version>
    </properties>

    <modules>
        <module>user-member-common</module>
        <module>user-member-rpc</module>
        <module>user-member-server</module>
    </modules>

    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <url>https://nexus.leyaoyao.com/repository/releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>https://nexus.leyaoyao.com/repository/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>nexus-releases</id>
            <url>https://nexus.leyaoyao.com/repository/public/</url>
        </repository>
    </repositories>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.lyy.base</groupId>
                <artifactId>base-bom</artifactId>
                <version>${base-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <deploy>local</deploy>
                <project.version>0.2.0-SNAPSHOT</project.version>
                <common.version>0.0.1-SNAPSHOT</common.version>
                <base-bom.version>1.1.1-SNAPSHOT</base-bom.version>
                <idempotent.version>1.0-SNAPSHOT</idempotent.version>
                <common-starter.version>2.1-SNAPSHOT</common-starter.version>
                <marketing.version>0.1.0-SNAPSHOT</marketing.version>
                <user-app.version>0.1.0-SNAPSHOT</user-app.version>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <deploy>dev</deploy>
                <project.version>0.2.0-SNAPSHOT</project.version>
                <common.version>0.0.1-SNAPSHOT</common.version>
                <base-bom.version>1.1.1-SNAPSHOT</base-bom.version>
                <idempotent.version>1.0-SNAPSHOT</idempotent.version>
                <common-starter.version>2.1-SNAPSHOT</common-starter.version>
                <marketing.version>0.1.0-SNAPSHOT</marketing.version>
                <user-app.version>0.1.0-SNAPSHOT</user-app.version>
            </properties>
        </profile>
        <profile>
            <id>sit</id>
            <properties>
                <deploy>sit</deploy>
                <project.version>0.2.0-SNAPSHOT</project.version>
                <common.version>0.0.1-SNAPSHOT</common.version>
                <base-bom.version>1.1.1-SNAPSHOT</base-bom.version>
                <idempotent.version>1.0-SNAPSHOT</idempotent.version>
                <common-starter.version>2.1-SNAPSHOT</common-starter.version>
                <marketing.version>0.1.0-SNAPSHOT</marketing.version>
                <user-app.version>0.1.0-SNAPSHOT</user-app.version>
            </properties>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <deploy>uat</deploy>
                <project.version>0.2.0-BETA</project.version>
                <common.version>0.0.1-BETA</common.version>
                <base-bom.version>1.1.1-BETA</base-bom.version>
                <idempotent.version>1.0-BETA</idempotent.version>
                <common-starter.version>2.1</common-starter.version>
                <marketing.version>0.1.0-BETA</marketing.version>
                <user-app.version>0.1.0-BETA</user-app.version>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <deploy>prod</deploy>
                <project.version>0.2.0-RELEASE</project.version>
                <common.version>0.0.1-RELEASE</common.version>
                <base-bom.version>1.1.1-RELEASE</base-bom.version>
                <idempotent.version>1.0-RELEASE</idempotent.version>
                <common-starter.version>2.1</common-starter.version>
                <marketing.version>0.1.0-RELEASE</marketing.version>
                <user-app.version>0.1.0-RELEASE</user-app.version>
            </properties>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <executable>true</executable>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>
