# user-member
会员域

[CHANGELOG](CHANGELOG.md)


## 4 项目结构
### 4.1 DDD 项目结构
DDD 总体结构分为四层
1. Infrastructure 基础实施层
    1.  向其他层提供 通用的 技术能力(比如工具类,第三方库类支持,常用基本配置,数据访问底层实现)
    2.  基础实施层主要包含以下的内容:
    3.  为应用层 传递消息(比如通知)
    4.  为领域层 提供持久化机制(最底层的实现)
    5.  为用户界面层 提供组件配置
    6.  基础设施层还能够通过架构框架来支持四个层次间的交互模式。
2. Domain 领域层
    1. 领域层主要负责表达业务概念,业务状态信息和业务规则。Domain层是整个系统的核心层,几乎全部的业务逻辑会在该层实现。领域模型层主要包含以下的内容:
    2. 实体(Entities):具有唯一标识的对象
    3. 值对象(Value Objects): 无需唯一标识的对象
    4. 领域服务(Domain Services): 一些行为无法归类到实体对象或值对象上,本质是一些操作,而非事物(与本例中domain/service包下的含义不同)
    5. 聚合/聚合根(Aggregates,Aggregate Roots):聚合是指一组具有内聚关系的相关对象的集合,每个聚合都有一个root和boundary
    6. 工厂(Factories): 创建复杂对象,隐藏
    7. 创建细节仓储(Repository): 提供查找和持久化对象的方法
3. Application 应用层
    1. 相对于领域层,应用层是很薄的一层,应用层定义了软件要完成的任务,要尽量简单.
    2. 它不包含任务业务规则或知识, 为下一层的领域对象协助任务、委托工作。
    3. 它没有反映业务情况的状态,但它可以具有反映用户或程序的某个任务的进展状态。
    4. 对外 为展现层提供各种应用功能(service)。
    5. 对内 调用领域层（领域对象或领域服务）完成各种业务逻辑任务
    6. 这一层也很适合写一些任务处理,日志监控
4. Interfaces 表示层，也叫用户界面层或接口层
    1. 负责向用户显示信息和解释用户命令
    2. 请求应用层以获取用户所需要展现的数据(比如获取首页的商品数据)
    3. 发送命令给应用层要求其执行某个用户命令(实现某个业务逻辑,比如用户要进行转账)
    4. 用户界面层应该包含以下的内容:
        1. 数据传输对象(Data Transfer Object): DTO也常被称作值对象，VO,实质上与领域层的VO并不相同
        2. DTO是数据传输的载体,内部不应该存在任何业务逻辑,通过DTO把内部的领域对象与外界隔离。
        3. 装配(Assembler): 实现DTO与领域对象之间的相互转换,数据交换,因此Assembler几乎总是同DTO一起出现。
        4. 表面,门面(Facade): Facade的用意在于为远程客户端提供粗粒度的调用接口
        5. 它的主要工作就是将一个用户请求委派给一个或多个Service进行处理,也就是我们常说的Controller。
        6. 通常也会在这层做JSR303参数校验
           
![avatar](docs/DDD.jpeg)<p>
![avatar](docs/EB6A9D73-3922-4416-B282-7D2BFB2E9734.png)<p>
![avatar](docs/C36D14E9-1B54-471F-AF18-B920BBCB8DB1.png)

### 4.2 依赖规范问题

1. 所有内部应用项目包都需要加上<optional>true</optional>,预防传递依赖
2. api项目下所有包都需要加<optinal>true</optional>