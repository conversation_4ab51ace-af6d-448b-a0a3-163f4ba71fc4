package com.lyy.user.account.infrastructure.user.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

/**
 * 标签查询
 */
@Getter
@Setter
@ToString
public class TagUserQueryDTO  {

    /**
     * 标签
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long id;

    /**
     * 标签
     */
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long merchantUserId;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 标签名称模糊查询
     */
    private String name;
    /**
     * 标签名称精确查询
     */
    private String exactName;

    /**
     * 启用状态
     */
    private Boolean active;


    /** @see com.lyy.user.account.infrastructure.constant.UserMemberSysConstants
     * 0 平台用户标签
     * 1 商户用户标签
     *
     */
    //@Range(min = 0, message = "标签类型错误",max = 1)
    //@NotNull
    private Integer tagType;

    /**
     * 标签类型，1:自动, 2:手动
     *  @see  com.lyy.user.account.infrastructure.constant.TagCategoryEnum
     */
    private Integer category;
    /**
     * 标签的业务类型
     * @see  com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum
     */
    private Integer businessType;

    /**
     * 记录是否已删除，1:是，0：否
     *
     */
    private Integer  state;

    private Boolean queryUserNumber;


    /**
     * 页码
     */
    @Range(min = 1, message = "页码参数错误")
    @NotNull
    private Integer pageIndex;

    /**
     * 数据量
     */
    @NotNull
    private Integer pageSize;

    private Boolean countSql;

}
