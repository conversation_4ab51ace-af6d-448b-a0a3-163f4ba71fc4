package com.lyy.user.account.infrastructure.member.dto;

import lombok.Data;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @className: MemberLevelSaveDTO
 * @date 2021/4/2
 */
@Data
public class MemberLevelSaveDTO extends MemberLevelDTO {

    private List<MemberRuleDTO> memberRuleList;
    /**
     * 字符串格式的id
     */
    private String idStr;

    public String getIdStr() {
        return Optional.ofNullable(getId()).map(id->id.toString()).orElse("");
    }
}
