package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 会员卡列表储值信息
 */
@Data
@Accessors(chain = true)
public class MobileTerminalAccountBenefitInfo {

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 账户id
     */
    private Long accountId;

    /**
     * 权益分类id
     */
    private Long merchantBenefitClassifyId;

}
