package com.lyy.user.account.infrastructure.account.dto;

import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AccountBenefitModifyDTO {

    /**
     * 增加权益列表
     */
    @Valid
    private List<AccountBenefitAdjustDTO> adjustList;

    /**
     * 扣减权益列表
     */
    @Valid
    private List<ConsumeDTO> consumeList;

    private String orderNo;

    @NotNull(message = "商户id不能为空")
    private Long merchantId;

    @NotNull(message = "用户id不能为空")
    private Long userId;

}
