package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @ClassName: SmallVenueUserAvailableBenefitDTO
 * @description: 用户可用的权益数据
 * @author: pengkun
 * @date: 2022/01/10
 **/
@Setter
@Getter
@ToString
public class SmallVenueUserAvailableBenefitDTO {
    /**
     * 账号权益明细id
     */
    private Long accountBenefitId;
    /**
     * 商户id
     */
    private Long merchantId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 商户用户id
     */
    private Long merchantUserId;
    /**
     * 商户自定义权益id
     */
    private Long merchantBenefitClassifyId;
    /**
     * 使用规则
     */
    private Long useRuleId;
    /**
     * 过期类型
     */
    private Integer expiryDateCategory;
    /**
     * 开始时间
     */
    private String upTime;
    /**
     * 结束时间
     */
    private String downTime;
    /**
     * 总权益
     */
    private BigDecimal total;
    /**
     * 剩余权益
     */
    private BigDecimal balance;
    /**
     * 今日使用权益
     */
    private BigDecimal useNum;
    /**
     * 门店id
     */
    private Long storeId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
}
