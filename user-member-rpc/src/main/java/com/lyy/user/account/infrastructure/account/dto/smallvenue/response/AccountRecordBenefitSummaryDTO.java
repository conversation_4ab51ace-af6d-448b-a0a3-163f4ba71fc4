package com.lyy.user.account.infrastructure.account.dto.smallvenue.response;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 订单权益消费充值汇总查询
 *
 * <AUTHOR>
 * @since 2022/3/28 - 17:29
 */
@Data
@AllArgsConstructor
public class AccountRecordBenefitSummaryDTO {

    /**
     * 抵扣权益汇总
     */
    private List<BenefitConsumptionSummaryDTO> consumptionSummary;

    /**
     * 重合汇总
     */
    private List<BenefitRechargeSummaryDTO> rechargeSummary;

    public AccountRecordBenefitSummaryDTO() {
        this.consumptionSummary = Lists.newArrayList();
        this.rechargeSummary = Lists.newArrayList();
    }


}
