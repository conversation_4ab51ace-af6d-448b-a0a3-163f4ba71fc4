package com.lyy.user.account.infrastructure.benefit.feign;

import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.benefit.dto.ConsumeRuleSaveDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @ClassName: BenefitConsumeRuleFeignFallback
 * @description: 商户权益消耗规则熔断处理
 * @author: pengkun
 * @date: 2021/08/14
 **/
@Slf4j
@Component
public class BenefitConsumeRuleFeignFallback implements FallbackFactory<BenefitConsumeRuleFeignClient> {
    @Override
    public BenefitConsumeRuleFeignClient create(Throwable throwable) {
        if (throwable != null && StringUtils.isNotEmpty(throwable.getMessage())) {
            log.error(throwable.getMessage(), throwable);
        }

        return new BenefitConsumeRuleFeignClient() {

            /**
             * 初始化商户权益消耗规则
             *
             * @param merchantId  商户id
             * @param operationId 操作人id
             * @return
             */
            @Override
            public RespBody<Void> initMerchantBenefitConsume(Long merchantId, Long operationId) {
                log.error("[新会员] 初始化商户权益消耗规则,initMerchantBenefitConsume 熔断,merchantId:{},operationId:{}", merchantId, operationId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 批量商户权益消耗规则处理
             *
             * @param consumeRuleSaveDTO 保存参数
             * @return
             */
            @Override
            public RespBody<Void> batchMerchantBenefitConsume(ConsumeRuleSaveDTO consumeRuleSaveDTO) {
                log.error("[新会员] 批量商户权益消耗规则处理, batchMerchantBenefitConsume 熔断,consumeRuleSaveDTO:{}", consumeRuleSaveDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }
        };
    }
}
