package com.lyy.user.account.infrastructure.user.dto.userInfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * 会员和会员卡的信息
 */
@Data
@Accessors(chain = true)
public class MerchantUserInfoAndAccountDTO {

    private Long merchantUserId;

    private String name;

    private Long userId;

    private String headImg;

    private Date createTime;

    private String userType;

    private String birthday;

    private String telephone;

    private String gender;

    private String provinceCity;

    private Long accountId;

    private String cardNo;

    private Long parentAccountId;

    private Date downTime;

    private Long storeId;

    private Integer status;

    private Long merchantBenefitClassifyId;

    private BigDecimal balance;

    private Boolean defaultFlag;

    private BigDecimal deposit;

    private String address;

    private Long provinceId;

    private Long regionId;

    private Long cityId;

    private Long useRuleId;

    private Long benefitStoreId;

    /**
     * 权益生效时间
     */
    private Date benefitUpTime;

    /**
     * 权益过期时间
     */
    private Date benefitDownTime;
}
