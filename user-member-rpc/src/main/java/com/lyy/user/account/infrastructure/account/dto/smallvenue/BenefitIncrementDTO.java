package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/8
 */
@NoArgsConstructor
@Data
public class BenefitIncrementDTO {

    /**
     * 用户Id
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    /**
     * 商户Id
     */
    @NotNull(message = "商户ID不能为空")
    private Long merchantId;
    /**
     * 操作人Id
     */
    private Long operatorId;

    /**
     * 门店id
     */
    @NotNull(message = "门店ID不能为空")
    private Long storeId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 退单单号
     */
    private String refundNo;

    /**
     * 子项
     */
    @Valid
    @NotEmpty(message = "权益子项不能为空")
    private List<BenefitIncrementItemDTO> items;


    /**
     * 流水信息
     */
    @Valid
    private AccountRecordInfoDTO record;
    /**
     * 是否包含过期回退权益
     */
    private Boolean containExpireBenefit = false;

}
