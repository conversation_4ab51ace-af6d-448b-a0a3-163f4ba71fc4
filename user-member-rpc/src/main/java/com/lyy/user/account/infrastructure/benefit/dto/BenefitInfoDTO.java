package com.lyy.user.account.infrastructure.benefit.dto;

import lombok.Data;

/**
 * 用户权益DTO
 *
 * <AUTHOR>
 * @create 2021/4/1 11:35
 */
@Data
public class BenefitInfoDTO {

    private Long id;

    /**
     * 权益编码
     */
    private String code;


    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 用户ID
     */
    private Long lyyUserId;

    /**
     * 标题
     */
    private String title;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 权益分类
     */
    private Integer benefitClassify;


    /**
     * 权益数量
     */
    private Integer benefitCount;

    /**
     * 可用有效期类型
     */
    private Integer expiryDateCategory;

    private String upTime;

    private String downTime;

    private Integer showDateCategory;

    /**
     * 标记是否已存在的benefit
     */
    private Boolean oldBenefit;
}
