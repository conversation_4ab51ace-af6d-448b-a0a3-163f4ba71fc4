package com.lyy.user.account.infrastructure.account.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @ClassName: AccountRecordSaveDTO
 * @description: 消费记录保存DTO
 * @author: pengkun
 * @date: 2021/11/25
 **/
@Setter
@Getter
@ToString
public class AccountRecordSaveDTO {

    private Long accountId;

    /**
     * 	平台用户ID
     */
    @NotNull(message = "用户id必须传值")
    private Long userId;

    /**
     * 	商户用户ID
     */
    private Long merchantUserId;

    /**
     * 	商户
     */
    @NotNull(message = "商户id必须传值")
    private Long merchantId;

    /**
     * 	店铺
     */
    private Long storeId;

    /**
     * 	设备
     */
    private Long equipmentId;

    /**
     * 	权益类型
     */
    @NotNull(message = "权益类型必须传值")
    private Integer benefitClassify;

    /**
     * 	权益ID
     */
    private Long benefitId;

    /**
     * 	期初权益
     */
    private BigDecimal initialBenefit;

    /**
     * 	权益
     */
    private BigDecimal originalBenefit;

    /**
     * 	实际权益
     */
    private BigDecimal actualBenefit;

    /**
     * 	交易单号
     */
    private String outTradeNo;

    /**
     * 	业务单号
     */
    private String orderNo;

    private String refundNo;

    /**
     * 	方式1=加，2=减
     */
    @NotNull(message = "权益处理的方式为空")
    private Integer mode;

    /**
     * 	来源
     */
    private String resource;

    /**
     * 	备注
     */
    private String description;

    /**
     * 交易类型
     */
    private Integer tradeType;

    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;

    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 设备类型名称
     */
    private String equipmentTypeName;

    /**
     * 设备编号
     */
    private String equipmentValue;

    /**
     * 机台号
     */
    private Integer groupNumber;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 设备类型id
     */
    private Long equipmentTypeId;

    /**
     * 记录类型
     * @see com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum
     */
    @NotNull(message = "记录类型为空")
    private Integer recordType;

    /**
     * 卡号
     */
    private String cardNo;
}
