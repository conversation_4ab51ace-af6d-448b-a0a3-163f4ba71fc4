package com.lyy.user.account.infrastructure.user.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @ClassName: MerchantUserDTO
 * @description: 商户用户
 * @author: pengkun
 * @date: 2021/03/31
 **/
@Setter
@Getter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MerchantUserDTO {
    /**
     * 商户用户Id
     */
    private Long id;

    /**
     * 商户Id
     */
    private Long merchantId;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 是否可用，true:是，false：否
     */
    private Boolean active;
    /**
     * 创建人
     */
    private Long createdby;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 手机号码
     */
    private String telephone;
    /**
     * 描述
     */
    private String description;
    /**
     * 更新人
     */
    private Long updatedby;
    /**
     * 姓名
     */
    private String name;
    /**
     * 性别
     */
    private String gender;
    /**
     * 头像链接
     */
    private String headImg;
    /**
     * 生日
     */
    private String birthday;
    /**
     * 用户类型
     * W 微信
     * A 支付宝
     * J 京东
     * U 云闪付
     * O 其他渠道
     */
    private String userType;
    /**
     * 城市Id
     */
    private Long cityId;
    /**
     * 城市
     */
    private String provinceCity;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String cityName;
    /**
     * 省份Id
     */
    private Long provinceId;
    /**
     * 区域Id
     */
    private Long regionId;
    /**
     * 场地名称
     */
    private String storeName;
    /**
     * 地址
     */
    private String address;

    /**
     * 是否创建标签
     */
    private Boolean isCreateTag;
}
