package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SmallVenueRecordListDTO {


    /**
     * 业务单号
     */
    private String orderNo;


    /**
     * 备注
     */
    private String description;

    /**
     * 实付金额/实兑换储值/回收储值数量
     */
    private BigDecimal actualBenefit;


    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 操作人名称
     */
    private String operatorName;
    /**
     * 卡号
     */
    private String cardNo;
    /**
     * 退单号
     */
    private String refundNo;
    /**
     * 商品id
     */
    private Long goodsId;
    /**
     * 商品所属分类
     */
    private Long goodsClassify;
    /**
     * 商品数量
     */
    private BigDecimal goodsNum;
    /**
     * 优惠金额
     */
    private BigDecimal discountsAmount;
    /**
     * 操作设备名称
     */
    private String operationEquipmentName;
    /**
     * 记录类型（1.商品购买记录 2.商品兑换记录；3.商品回收记录；4.储值变更记录（储值变更记录包括设备消费记录））
     */
    private Integer operationType;
    /**
     * 操作渠道（1.微信小程序；2.桌面收银台；3.移动收银台）
     */
    private Integer operationChannel;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 商家自定义权益名称
     */
    private String merchantBenefitClassifyName;

    /**
     * 应兑换储值数量
     */
    private BigDecimal originalBenefit;

    /**
     * 账号下权益初始总数（总次数）
     */
    private BigDecimal accountInitialBalance;

    /**
     * 设备编号
     */
    private String equipmentValue;

    /**
     * 设备名称
     */
    private String equipmentName;

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 消费方式 1.刷卡消费 2.微信小程序；3.桌面收银台；4.移动收银台
     */
    private Integer consumeType;

    /**
     * 业务类型
     */
    private Integer recordType;

    /**
     * 业务类型名称
     */
    private String recordTypeName;

    /**
     * 商品单价
     */
    private BigDecimal goodsPrice;


    /**
     * 商品类型名称
     */
    private String goodsTypeName;

    /**
     * 商品所属分类名称
     */
    private String goodsClassifyName;

    /**
     * 商品类型id
     */
    private Long goodsTypeId;

    /**
     * 加减方式:1=加，2=减
     */
    private Integer mode;

    /**
     * 商户权益分类id
     */
    private String merchantBenefitClassifyId;

    /**
     * 交易类型
     */
    private Integer tradeType;

    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;


}
