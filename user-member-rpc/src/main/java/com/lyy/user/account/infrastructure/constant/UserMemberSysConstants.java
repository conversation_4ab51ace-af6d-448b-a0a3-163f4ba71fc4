package com.lyy.user.account.infrastructure.constant;

/**
 * @description:
 * @author: qgw
 * @date on 2021/3/31.
 * @Version: 1.0
 */
public class UserMemberSysConstants {

    public final static Boolean ENABLE = true;
    public final static Boolean DISABLE = false;

    public final static String YES = "Y";
    public final static String NO = "N";


    /**
     * 0 平台用户标签-已废弃，不创建新数据
     * 1 商户用户标签
     *
     */
    public final static Integer TAG_PLATFORM_USER = 0;
    public final static Integer TAG_MERCHANT_USER = 1;

    /**
     * 假定平台的商家ID为 0
     */
    public static final Long PLATFORM_MERCHANT_ID = 0L;

    /**
     * 默认操作人
     */
    public final static Long DEFAULT_OPERATION_USER_ID = 0L;

    /**
     * 记录已经逻辑删除
     */
    public final static Integer RECORD_STATE_DELETE = 1;


    public final static Integer RECORD_STATE_NORMAL= 0;

    /**
     * 扣减或回退余币
     */
    public final static String NEW_MEMBER_SYSTEM_ORDER_NO = "memberOrderNo";

    /**
     * B端商户初始化平台用户前缀
      */
    public static final String MERCHANT_PREFIX = "Lyy_Merchant_";

    /**
     * 小场地用户前缀
     */
    public static final String SMALL_VENUE_USER_PREFIX = "Lyy_Venue_";

    /**
     * 多金宝系统标识
     */
    public static final Integer SMALL_VENUE_SYSTEM_FLAG = 1;

    /**
     * 多金宝指定根据userId搜索标识
     */
    public static final Integer SMALL_VENUE_SEARCH_TYPE_USERID = 1;

    /**
     * 多金宝指定根据手机号码搜索标识
     */
    public static final Integer SMALL_VENUE_SEARCH_TYPE_PHONENUM = 2;

    /**
     * 多金宝指定根据会员昵称搜索标识
     */
    public static final Integer SMALL_VENUE_SEARCH_TYPE_USERMEMBERNAME = 3;

    /**
     * 多金宝指定根据会员卡号搜索标识
     */
    public static final Integer SMALL_VENUE_SEARCH_TYPE_CARDNO = 4;
}
