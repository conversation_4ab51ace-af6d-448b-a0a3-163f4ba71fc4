package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName: UserAccountBenefit
 * @description: 用户账户权益明细
 * @author: pengkun
 * @date: 2022/01/12
 **/
@Setter
@Getter
@ToString
public class UserAccountBenefit {
    /**
     * 账户权益明细id
     */
    private Long accountBenefitId;
    /**
     * 剩余权益
     */
    private BigDecimal balance;
    /**
     * 总权益
     */
    private BigDecimal total;
    /**
     *  有效期类型:0、无限期,1、日期区间可用,2、单日时间区间可用
     */
    private Integer expiryDateCategory;
    /**
     * 上线时间如果是类型1(日期区间)，字段内容格式是:  yyyy-MM-dd HH:mm:ss如果是类型2(时间区间)，字段内容格式是:  HH:mm:ss
     */
    private String upTime;
    /**
     * 下线时间如果是类型1(日期区间)，字段内容格式是:  yyyy-MM-dd HH:mm:ss如果是类型2(时间区间)，字段内容格式是:  HH:mm:ss
     */
    private String downTime;
    /**
     * 商家自定义权益id
     */
    private Long merchantBenefitClassifyId;
    /**
     * 使用规则id
     */
    private Long useRuleId;
    /**
     * 所属门店
     */
    private Long storeId;

    private Date createTime;

    private Date updateTime;


}
