package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MobileTerminalAccountSelectDTO {

    /**
     * 会员卡关键字
     */
    private String keyword;

    /**
     * 门店id
     */
    private List<Long> storeIds;

    /**
     * 商户id
     */
    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    @NotNull(message = "pageIndex不能为空")
    private Integer pageIndex;

    @NotNull(message = "pageSize不能为空")
    private Integer pageSize;

    private Boolean countSql;

}
