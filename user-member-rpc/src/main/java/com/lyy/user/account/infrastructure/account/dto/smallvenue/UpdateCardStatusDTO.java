package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class UpdateCardStatusDTO {

    /**
     * 卡号
     */
    @NotNull(message = "cardNo不能为空")
    private String cardNo;

    /**
     * 商户id
     */
    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    /**
     * 更新类型:1.恢复 3.挂失
     */
    @NotNull(message = "type不能为空")
    private Integer type;

    /**
     * 描述
     */
    private String description;

    /**
     * 操作人id
     */
    @NotNull(message = "operatorId不能为空")
    private Long operatorId;

    /**
     * 操作人名称
     */
    @NotNull(message = "operatorName不能为空")
    private String operatorName;
}
