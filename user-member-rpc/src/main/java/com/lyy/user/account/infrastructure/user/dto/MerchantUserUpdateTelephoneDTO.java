package com.lyy.user.account.infrastructure.user.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MerchantUserUpdateTelephoneDTO {

    @NotNull(message = "userId不能为空")
    private Long userId;

    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

//    @NotNull(message = "oldTelephone不能为空")
    private String oldTelephone;

    @NotNull(message = "newTelephone不能为空")
    private String newTelephone;

}
