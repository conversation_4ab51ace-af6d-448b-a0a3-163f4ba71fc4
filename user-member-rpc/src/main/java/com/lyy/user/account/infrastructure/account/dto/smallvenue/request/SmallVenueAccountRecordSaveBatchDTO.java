package com.lyy.user.account.infrastructure.account.dto.smallvenue.request;

import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueAccountRecordSaveDTO;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SmallVenueAccountRecordSaveBatchDTO {

    /**
     * 平台用户id
     */
    @NotNull(message = "userId不能为空")
    private Long userId;

    /**
     * 商户
     */
    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    private Long merchantUserId;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;


    @NotEmpty(message = "records不能为空")
    @Valid
    private List<SmallVenueAccountRecordSaveDTO> records;
}
