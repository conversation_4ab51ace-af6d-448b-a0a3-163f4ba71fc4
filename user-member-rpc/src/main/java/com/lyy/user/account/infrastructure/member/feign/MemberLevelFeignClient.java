package com.lyy.user.account.infrastructure.member.feign;

import com.lyy.user.account.infrastructure.member.dto.MemberLevelDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberLevelSaveDTO;
import com.lyy.user.account.infrastructure.member.dto.SmallVenueMemberLevelSaveDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 会员组接口
 * <AUTHOR>
 * @date 2022/01/25
 */
@FeignClient(name = "user-member-center", fallbackFactory = MemberLevelFeignFallback.class)
public interface MemberLevelFeignClient {


    /**
     * 会员等级列表
     * @param memberGroupId 会员组id ,不传查商户默认的
     * @param merchantId 商户ID
     * @return
     */
    @GetMapping("/rest/member/level/list")
    RespBody<List<MemberLevelDTO>> list(@RequestParam(value = "memberGroupId",defaultValue = "-1") Long memberGroupId, @RequestParam("merchantId") Long merchantId);

    /**
     * 保存会员等级，新增或更新数据
     * @param smallVenueMemberLevelSaveDTO
     * @return
     */
    @PostMapping("/rest/member/level/saveOrUpdate")
    RespBody saveOrUpdate(@RequestBody SmallVenueMemberLevelSaveDTO smallVenueMemberLevelSaveDTO);

    /**
     * 获取会员等级详情
     * @param memberLevelId
     * @param merchantId
     * @return
     */
    @GetMapping("/rest/member/level/detail")
    RespBody<MemberLevelSaveDTO> detail(@RequestParam(value = "memberLevelId") Long memberLevelId, @RequestParam("merchantId") Long merchantId);
}
