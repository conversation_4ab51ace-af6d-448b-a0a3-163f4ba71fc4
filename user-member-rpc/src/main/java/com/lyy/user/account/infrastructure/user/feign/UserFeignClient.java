package com.lyy.user.account.infrastructure.user.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.common.DataList;
import com.lyy.user.account.infrastructure.member.dto.MemberBatchQueryRequestDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.user.dto.*;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneQueryParam;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneRecordDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserAndLevelInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserInfoByKeywordDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @ClassName: UserFeignClient
 * @description: 用户
 * @author: pengkun
 * @date: 2021/04/15
 **/
@FeignClient(name = "user-member-center", fallbackFactory = UserFeignClientFallback.class)
public interface UserFeignClient {

    /**
     * 获取平台用户电话
     *
     * @param param
     * @return
     */
    @PostMapping("/platform/user/phone/list")
    RespBody<List<PlatformUserPhoneRecordDTO>> list(@RequestBody PlatformUserPhoneQueryParam param);

    /**
     * 根据平台用户id和商户id获取用户信息
     *
     * @param userId     平台用户Id
     * @param merchantId 商户Id
     * @return
     */
    @GetMapping("/user/getUserInfoByUserIdAndMerchantId")
    RespBody<UserInfoDTO> getUserInfoByUserIdAndMerchantId(@RequestParam("userId") Long userId,
                                                           @RequestParam(value = "merchantId", required = false) Long merchantId);

    /**
     * 根据平台用户id和商户id获取用户信息[内含异步初始化指定商户下的 商户用户统计数据]
     * @param userId    平台用户Id
     * @param merchantId    商户Id
     * @return
     */
    @GetMapping("/user/getUserInfoByUserIdAndMerchantIdAsyn")
    RespBody<UserInfoDTO> getUserInfoByUserIdAndMerchantIdAsyn(@RequestParam("userId") Long userId,
                                                           @RequestParam(value = "merchantId", required = false) Long merchantId);
    /**
     * 根据平台用户id,商户用户id,商户id获取用户信息
     *
     * @param userId         平台用户Id
     * @param merchantUserId 商户用户Id
     * @param merchantId     商户Id
     * @return
     */
    @GetMapping("/user/getUserInfoByUserIdAndMerchantUserId")
    RespBody<UserInfoDTO> getUserInfoByUserIdAndMerchantUserId(@RequestParam("userId") Long userId,
                                                               @RequestParam(value = "merchantUserId", required = false) Long merchantUserId,
                                                               @RequestParam(value = "merchantId", required = false) Long merchantId);

    /**
     * 根据openId或unionId获取用户信息
     *
     * @param openId     openId
     * @param unionId    unionId
     * @param merchantId 商户Id
     * @return
     */
    @GetMapping("/user/getUserInfoByOpenIdOrUnionId")
    RespBody<UserInfoDTO> getUserInfoByOpenIdOrUnionId(@RequestParam(value = "openId", required = false) String openId,
                                                       @RequestParam(value = "unionId", required = false) String unionId,
                                                       @RequestParam(value = "merchantId", required = false) Long merchantId);

    /**
     * 根据手机号和商户id查询可切换的用户列表信息
     *
     * @param telephone  手机号
     * @param merchantId 商户id
     * @return
     */
    @GetMapping("/user/listByTelephoneAndMerchantId")
    RespBody<List<UserInfoDTO>> listByTelephoneAndMerchantId(@RequestParam("telephone") String telephone,
                                                             @RequestParam("merchantId") Long merchantId);

    /**
     * 更新用户绑定的手机号码
     *
     * @param dto
     * @return
     */
    @PostMapping("/user/updateTelephone")
    @Deprecated
    RespBody<Boolean> updateTelephone(@RequestBody MerchantUserDTO dto);

    /**
     * 用户初始化
     *
     * @param userCreateDTO
     * @return
     */
    @PostMapping("/user/initUserInfo")
    RespBody<UserInfoDTO> initUserInfo(@RequestBody UserCreateDTO userCreateDTO);

    /**
     * 更新用户信息
     * 平台和商户用户基本信息，如：头像，名字，生日，城市信息等
     *
     * @param dto
     * @return
     */
    @PostMapping("/user/updateUserInfo")
    RespBody<Boolean> updateUserInfo(@RequestBody MerchantUserDTO dto);

    /**
     * 更新平台用户信息
     *
     * @param userCreateDTO
     * @return
     */
    @PostMapping("/user/updatePlatformUserInfo")
    RespBody<Boolean> updatePlatformUserInfo(@RequestBody UserCreateDTO userCreateDTO);

    /**
     * 注销用户
     *
     * @param userId          平台用户id
     * @param merchantUserId  商户用户id
     * @param merchantId      商户id
     * @param operationUserId 操作人id
     * @return
     */
    @GetMapping("/user/deleteUserInfo")
    RespBody<Boolean> deleteUserInfo(@RequestParam(value = "userId", required = false) Long userId,
                                     @RequestParam(value = "merchantUserId") Long merchantUserId,
                                     @RequestParam(value = "merchantId") Long merchantId,
                                     @RequestParam(value = "operationUserId", required = false) Long operationUserId);

    /**
     * 创建用户APP信息
     *
     * @param userAppDTO 用户APP信息
     * @return
     */
    @PostMapping("/user/createUserApp")
    RespBody<Boolean> createUserApp(@RequestBody UserAppDTO userAppDTO);

    /**
     * 根据openId和appId获取平台用户信息
     *
     * @param openId
     * @param appId
     * @return
     */
    @GetMapping("/user/getPlatformUserInfoByOpenIdAndAppId")
    RespBody<UserInfoDTO> getPlatformUserInfoByOpenIdAndAppId(@RequestParam("openId") String openId,
                                                              @RequestParam("appId") String appId);

    /**
     * 根据id获取商户用户信息
     *
     * @param merchantUserId 商户用户id
     * @param merchantId     商户id
     * @return
     */
    @GetMapping("/merchant/user/findById")
    RespBody<MerchantUserDTO> findById(@RequestParam("merchantUserId") Long merchantUserId, @RequestParam("merchantId") Long merchantId);

    /**
     * 根据id批量获取商户用户信息
     * @return
     */
    @PostMapping("/merchant/user/findByUserIds")
    RespBody<List<MerchantUserDTO>> findByUserIds(@RequestBody MemberBatchQueryRequestDTO request);

    /**
     * 根据平台用户ID和商户ID获取商户用户信息
     * @param userId 平台用户ID
     * @param merchantId 商户ID
     * @return 用户不存在时，body为null
     */
    @GetMapping("/merchant/user/findByUserIdAndMerchantId")
    RespBody<MerchantUserDTO> findByUserIdAndMerchantId(@RequestParam("userId") Long userId, @RequestParam("merchantId") Long merchantId);

    /**
     * 根据手机号和商户Id获取商户用户信息
     *
     * @param telephone  手机号码
     * @param merchantId 商户Id
     * @return
     */
    @GetMapping("/merchant/user/findByTelephoneAndMerchantId")
    RespBody<List<MerchantUserDTO>> findByTelephoneAndMerchantId(@RequestParam("telephone") String telephone, @RequestParam("merchantId") Long merchantId);

    /**
     * 商户查询用户会员
     *
     * @param merchantUserQueryDTO
     * @return
     */
    @PostMapping("/merchant/user/queryUserListByMerchant")
    RespBody<Page<MerchantUserListDTO>> queryUserListByMerchant(@RequestBody MerchantUserQueryDTO merchantUserQueryDTO);

    /**
     * 商户查询用户会员总数
     *
     * @param merchantUserQueryDTO
     * @return
     */
    @PostMapping("/merchant/user/countUserListByMerchant")
    RespBody<Long> countUserListByMerchant(@RequestBody MerchantUserQueryDTO merchantUserQueryDTO);

    /**
     * 商户给用户 派发福利
     *
     * @param payoutBenefitDTO
     * @return
     */
    @PostMapping(value = "/merchant/user/payout/benefit")
    RespBody<Void> payoutBenefit(@RequestBody PayoutBenefitDTO payoutBenefitDTO);

    /**
     * 商户用户 信息自查询
     *
     * @param merchantUserId
     * @param merchantId
     * @return
     */
    @GetMapping(value = "/merchant/user/getIndexAccountInfo")
    RespBody<IndexUserAccountDTO> getIndexAccountInfo(@RequestParam("merchantUserId") Long merchantUserId, @RequestParam("merchantId") Long merchantId);

    /**
     * 根据userId获取平台用户信息
     *
     * @param userId
     * @return
     */
    @GetMapping("/user/getPlatformUserInfoByUserId")
    RespBody<UserInfoDTO> getPlatformUserInfoByUserId(@RequestParam("userId") Long userId);

    /**
     * 根据会员卡/手机号码/平台用户id获取会员信息
     * @param merchantId
     * @param keyWords
     * @return
     */
    @GetMapping("/user/smallVenue/searchByKeywords")
    RespBody<MerchantUserInfoByKeywordDTO> searchByKeywords(@RequestParam("merchantId") Long merchantId, @RequestParam("keyWords") String keyWords);

    /**
     * 根据会员卡/手机号码/平台用户id获取会员信息
     * @param merchantId
     * @param keyWords
     * @param keyWordsType
     * @return
     */
    @GetMapping("/user/smallVenue/searchByKeywordsAndType")
    RespBody<MerchantUserInfoByKeywordDTO> searchByKeywordsAndType(@RequestParam("merchantId") Long merchantId,
                                                                   @RequestParam("keyWords") String keyWords,
                                                                   @RequestParam(value = "keyWordsType") Integer keyWordsType);

    /**
     * 修改会员密码
     * @param merchantUserUpdatePasswordDTO
     * @return
     */
    @PostMapping(value = "/user/smallVenue/updateUserPassword")
    RespBody<Boolean> updateUserPassword(@RequestBody MerchantUserUpdatePasswordDTO merchantUserUpdatePasswordDTO);

    /**
     * 校验会员密码
     * @param merchantUserCheckPasswordDTO
     * @return
     */
    @PostMapping(value = "/user/smallVenue/checkUserPassword")
    RespBody<Boolean> checkUserPassword(@RequestBody MerchantUserCheckPasswordDTO merchantUserCheckPasswordDTO);

    /**
     * 更新会员信息
     * @param updateMerchantUserInfoDTO
     * @return
     */
    @PostMapping("/user/smallVenue/updateMerchantUserInfo")
    RespBody<Boolean> updateMerchantUserInfo(@RequestBody UpdateMerchantUserInfoDTO updateMerchantUserInfoDTO);

    /**
     * 会员列表
     * @param smallVenueUserListSelectDTO
     * @return
     */
    @PostMapping("/user/smallVenue/list")
    RespBody<DataList<SmallVenueUserDTO>> selectSmallVenueUserList(@RequestBody SmallVenueUserListSelectDTO smallVenueUserListSelectDTO);

    /**
     * 小场地会员列表-B端
     * @param smallVenueMobileUserListSelectDTO
     * @return
     */
    @PostMapping("/user/smallVenue/mobile/list")
    RespBody<DataList<SmallVenueUserMobileVO>> smallVenueMobileUserList(@RequestBody SmallVenueMobileUserListSelectDTO smallVenueMobileUserListSelectDTO);

    /**
     * 修改会员手机号码
     * @param merchantUserUpdateTelephoneDTO
     * @return
     */
    @PostMapping("/user/smallVenue/updateUserTelephone")
    RespBody<Boolean> updateUserTelephone(@RequestBody MerchantUserUpdateTelephoneDTO merchantUserUpdateTelephoneDTO);

    /**
     * 根据条件获取商户用户信息
     * @param queryDTO
     * @return
     */
    @PostMapping("/merchant/user/getMerchantUserInfoByCondition")
    RespBody<MerchantUserDTO> getMerchantUserByCondition(@RequestBody MerchantIntegralUserQueryDTO queryDTO);

    /**
     * 移动端会员注销
     * @param cancellationDTO
     * @return
     */
    @PostMapping("/user/mobileTerminal/merchantUserCancellation")
    RespBody<Boolean> mobileTerminalMerchantUserCancellation(@RequestBody MobileTerminalUserCancellationDTO cancellationDTO);

    /**
     * 根据openId和appId获取用户信息
     *
     * @param openId     openId
     * @param appId      appId
     * @param merchantId 商户id
     * @return
     */
    @GetMapping("/user/getUserInfoByOpenIdAndAppId")
    RespBody<UserInfoDTO> getUserInfoByOpenIdAndAppId(@RequestParam(value = "openId") String openId,
                                                      @RequestParam(value = "appId") String appId,
                                                      @RequestParam(value = "merchantId", required = false) Long merchantId);

    /**
     * 根据会员卡/手机号码/平台用户id获取会员信息
     *
     * @param merchantId
     * @param keyWords
     * @param keyWordsType
     * @return
     */
    @GetMapping("/user/smallVenue/searchBaseInfoKeywordsAndType")
    RespBody<MerchantUserAndLevelInfoDTO> searchBaseInfoKeywordsAndType(@RequestParam("merchantId") Long merchantId,
            @RequestParam("keyWords") String keyWords,
            @RequestParam(value = "keyWordsType") Integer keyWordsType);
}
