package com.lyy.user.account.infrastructure.user.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description:  标签的用户
 * @author: qgw
 * @date on 2021/4/2.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class TagUserInfoDTO {

    /**
     * 平台用户Id或商户用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long businessUserId;

    /**
     * 性别
     */
    private String gender;

    /**
     * 手机号码
     */
    private String telephone;

    /**
     * 昵称
     */
    private String name;

    /**
     * 头像链接
     */
    private String headImg;

}
