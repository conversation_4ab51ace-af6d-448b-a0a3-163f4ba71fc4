package com.lyy.user.account.infrastructure.user.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lyy.user.account.infrastructure.base.StringArrayToLongDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 统计选中标签总数
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TagCountUserQueryDTO extends TagUserQueryDTO{


    /**
     * 不处理的用户IDs
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> notHandleUserIds;

}
