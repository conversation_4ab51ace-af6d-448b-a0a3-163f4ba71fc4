package com.lyy.user.account.infrastructure.user.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 小场地会员列表-B端
 */
@Data
@Accessors(chain = true)
public class SmallVenueUserMobileVO {

    /**
     * 会员姓名
     */
    private String name;

    /**
     * 会员ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 平台用户id
     */
    private Long userId;

    /**
     * 会员头像
     */
    private String headImg;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 会员来源
     */
    private String userType;

    /**
     * 会员等级id
     */
    private Long memberLevelId;

    /**
     * 会员等级名称
     */
    private String memberLevelName;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 消费累计
     */
    private BigDecimal amountConsumption;

    /**
     * 累计支付金额
     */
    private BigDecimal totalPayConsume;

    /**
     * 最近消费时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recentConsumptionTime;

    /**
     *  标签信息
     */
    private List<TagUserDTO> tagInfoList;


    /**
     * 签栏左侧显示会员卡数量（不包括附属卡数量，显示为标签样式）
     */
    private Integer cardNum;


    private List<MerchantBenefitBalanceInfoVO> benefitAmountInfoList;



}
