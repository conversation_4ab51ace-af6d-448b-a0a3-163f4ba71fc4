package com.lyy.user.account.infrastructure.account.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lyy.user.account.infrastructure.base.StringArrayToLongDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 类描述：账户明细余额查询对象
 *
 *
 * <AUTHOR>
 * @since 2021/10/26 14:59
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ChooseBenefitBalanceQueryDTO {

    /**
     * 商户id
     */
    @NotNull(message = "商户必须传值")
    private Long merchantId;

    /**
     * 商家用户id
     */
    private Long merchantUserId;

    /**
     * 权益ID集合 -调整记录id（权益账户表ID）
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> benefitIds;

    /**
     * 权益类型集合
     */
    private List<Integer> classify;


    /**
     * 场地id
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> storeIds;

    /**
     * 设备类型id
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> equipmentTypeIds;

    /**
     * 权益明细查询类型
     *  EFFECT(1,"生效中"),
     *  expired(2,"已过期"),
     *  consumed(3,"已消耗");
     */
    private Integer queryType;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;


    /**
     * 操作人
     */
    private Long operatorId;

    /**
     * true 选择全部
     */
    private Boolean  chooseAll;

    /**
     * 不处理的用户IDs
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> notHandleBenefitIdList;


    /**
     * 不查的权益类型
     */
    private List<Integer> excludeClassify;

}
