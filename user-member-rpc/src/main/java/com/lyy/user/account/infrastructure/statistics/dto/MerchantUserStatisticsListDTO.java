package com.lyy.user.account.infrastructure.statistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商户用户统计信息
 *
 * @author: 蒋成
 * @create 2021/10/23 15:26
 **/
@Setter
@Getter
@ToString
public class MerchantUserStatisticsListDTO {
    /**
     * 商户用户Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 商户Id
     */
    private Long merchantId;


    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 头像
     */
    private String headImg;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 性别
     */
    private String gender;

    /**
     * 手机号码
     */
    private String telephone;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 累计充值金额
     */
    private BigDecimal totalRechargeMoney;

    /**
     * 累计启动支付
     */
    private BigDecimal totalPayServiceMoney;


    /**
     * 累计余额支付
     */
    private BigDecimal totalConsumeBalance;

    /**
     * 累计消费余币
     */
    private BigDecimal totalConsumeCoin;

    /**
     * 当前剩余余额
     */
    private BigDecimal balanceAmount;

    /**
     * 当前剩余余币
     */
    private BigDecimal balanceCoins;
}
