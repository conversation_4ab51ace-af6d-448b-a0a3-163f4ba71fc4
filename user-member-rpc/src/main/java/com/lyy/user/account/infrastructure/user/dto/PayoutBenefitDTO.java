package com.lyy.user.account.infrastructure.user.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 派发福利DTO
 * <AUTHOR>
 * @create 2021/6/5 14:57
 */
@Data
public class PayoutBenefitDTO {

    /**
     * 商户用户ID 集合
     */
    private List<Long> merchantUserIdList;

    /**
     * 全用户
     */
    private Boolean isAllUser;

    /**
     * 全用户剔除之外的用户
     */
    private List<Long> exceptMerchantUserIdList;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 商户操作人ID
     */
    private Long adUserId;


    /**
     * 品类ID
     */
    private List<Long> EquipmentTypeIdList;

    /**
     * 生效场地
     */
    private List<Long> groupIdList;

    /**
     * 赠送余额
     */
    private BigDecimal payoutBalance;

    /**
     * 赠送余币
     */
    private BigDecimal payoutCoin;

    /**
     * 搜索关键字  支持微信昵称，会员ID,手机号码
     */
    private String keyword;

    /**
     * 场地标签集合
     */
    private List<Long> groupTagIdList;

    /**
     * 普通标签
     */
    private List<Long> otherTagIdList;
    /**
     * 设备类型标签
     */
    private List<Long> equipmentTypeTagIdList;

    /**
     * 性别标签ID
     */
    private List<Long> sexTagIdList;

    /**
     * 累计消费最小值
     */
    private Integer totalConsumeMoneyMin;

    /**
     * 累计消费最大值
     */
    private Integer totalConsumeMoneyMax;

    /**
     * 余额最小值
     */
    private Integer totalBalanceMin;

    /**
     * 余额最大值
     */
    private Integer totalBalanceMax;

    /**
     * 余币最小值
     */
    private Integer totalCoinMin;

    /**
     * 余币最大值
     */
    private Integer totalCoinMax;
}
