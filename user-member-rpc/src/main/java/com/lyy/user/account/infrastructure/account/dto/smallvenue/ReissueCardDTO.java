package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ReissueCardDTO {

    /**
     * 用户id
     */
    @NotNull(message = "userId不能为空")
    private Long userId;

    /**
     * 商户id
     */
    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    /**
     * 商户用户id
     */
    private Long merchantUserId;

    /**
     * 原卡卡号
     */
    @NotNull(message = "oldCardNo不能为空")
    private String oldCardNo;

    /**
     * 新卡卡号
     */
    @NotNull(message = "newCardNo不能为空")
    private String newCardNo;

    /**
     * 类别，1:补卡,2:换卡
     */
    @NotNull(message = "type不能为空")
    private Integer type;

    /**
     * 操作人id
     */
    @NotNull(message = "operatorId不能为空")
    private Long operatorId;

    /**
     * 操作人名称
     */
    @NotNull(message = "operatorName不能为空")
    private String operatorName;
}
