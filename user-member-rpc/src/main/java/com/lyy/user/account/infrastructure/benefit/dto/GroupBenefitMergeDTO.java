package com.lyy.user.account.infrastructure.benefit.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GroupBenefitMergeDTO {

    /**
     * 商户id
     */
    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    /**
     * 旧场地
     */
    private List<Long> oldGroupList;

    /**
     * 新场地
     */
    private List<Long> newGroupList;

    /**
     * 组类型
     */
    @NotNull(message = "groupType不能为空")
    private Integer groupType;
}
