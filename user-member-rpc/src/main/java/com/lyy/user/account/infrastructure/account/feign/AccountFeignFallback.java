package com.lyy.user.account.infrastructure.account.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.account.dto.AccountAdjustRecordDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustRefundDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDataDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDecreaseReqDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitModifyDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitResultDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitScopeDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitScopeQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountClassifyBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountClassifyBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountConditionDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountConsumption;
import com.lyy.user.account.infrastructure.account.dto.AccountDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordSaveDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountStatusDTO;
import com.lyy.user.account.infrastructure.account.dto.AddSupplementaryCardDTO;
import com.lyy.user.account.infrastructure.account.dto.BenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.BenefitRollbackDTO;
import com.lyy.user.account.infrastructure.account.dto.ConsumeDTO;
import com.lyy.user.account.infrastructure.account.dto.PayRefundBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.RecordBenefitScopeDTO;
import com.lyy.user.account.infrastructure.account.dto.RecordBenefitScopeQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.RecordTypeDTO;
import com.lyy.user.account.infrastructure.account.dto.SmallVenueAccountInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.SmallVenueAllCardDTO;
import com.lyy.user.account.infrastructure.account.dto.SupplementaryCardCheckDTO;
import com.lyy.user.account.infrastructure.account.dto.SupplyAnonymousCardDataDTO;
import com.lyy.user.account.infrastructure.account.dto.UserAccountBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountStatusBatchDTO;
import com.lyy.user.account.infrastructure.account.dto.request.AccountAdjustRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.request.AccountInfoByCardsQueryDto;
import com.lyy.user.account.infrastructure.account.dto.request.OrderBenefitRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.request.StoreBenefitClearDTO;
import com.lyy.user.account.infrastructure.account.dto.response.OrderBenefitInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.*;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.request.SmallVenueAccountRecordSaveBatchDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitConsumeInfoDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitPreDeductionDTO;
import com.lyy.user.account.infrastructure.common.DataList;
import com.lyy.user.account.infrastructure.resp.RespBody;
import feign.hystrix.FallbackFactory;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/15 11:18
 */
@Slf4j
@Component
public class AccountFeignFallback implements FallbackFactory<AccountFeignClient> {
    @Override
    public AccountFeignClient create(Throwable throwable) {

        if (throwable != null && StringUtils.isNotEmpty(throwable.getMessage())) {
            log.error(throwable.getMessage(), throwable);
        }

        return new AccountFeignClient() {
            @Override
            public RespBody<DataList<SmallVenueBenefitDetailDTO>> queryAccountBenefitList(AccountBenefitReqDTO accountBenefitReqDTO) {
                log.error("[新会员] benefitDetailList熔断,accountBenefitReqDTO:{}", accountBenefitReqDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<UserAccountBenefitDTO>> findBenefitByIds(BenefitQueryDTO query) {
                log.error("[新会员] findBenefitByIds熔断,query:{}", query);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<SmallVenueAccountInfoDTO>> getAccountInfoByCards(AccountInfoByCardsQueryDto dto) {
                log.error("[新会员] getAccountInfoByCards熔断,dto:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<MobileTerminalCardCountInfo>> getSupplementaryCard(AccountSupplementaryCardQueryDTO dto) {
                log.error("[新会员] getSupplementaryCard熔断,dto:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Page<AccountConsumption>> listRecord(AccountRecordQueryDTO param) {
                log.error("[新会员]查询权益流水, listRecord 熔断:{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Void> benefitConsume(ConsumeDTO consume) {
                log.error("[新会员]账户权益消耗, benefitConsume 熔断:{}", consume);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<BenefitPreDeductionDTO>> benefitPreDeductionConsume(ConsumeDTO consume) {
                log.error("[新会员]-权益扣除预计算, benefitPreDeductionConsume 熔断:{}", consume);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<BenefitConsumeInfoDTO> callbackRealConsume(ConsumeDTO consume) {
                log.error("[新会员] 权益扣减返回实扣金额接口, callbackRealConsume 熔断:{}", consume);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Void> benefitAdjust(List<AccountBenefitAdjustDTO> param) {
                log.error("[新会员] benefitAdjust 熔断:{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody merchantAdjustBenefit(AccountBenefitAdjustDTO accountBenefitAdjustDTO) {
                log.error("[新会员] merchantAdjustBenefit 熔断:{}", accountBenefitAdjustDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 商家调整权益退还，主要解决返还的权益需要有有效期限制
             * 此接口经测试，只适合做权益新增，不适合做权益扣减，权益扣减使用权益消耗接口
             *
             * @param accountBenefitAdjustDTO
             * @return
             */
            @Override
            public RespBody merchantRefundBenefit(AccountBenefitAdjustRefundDTO accountBenefitAdjustDTO) {
                log.error("[新会员] merchantRefundBenefit 熔断:{}", accountBenefitAdjustDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }


            @Override
            public RespBody<List<AccountBenefitResultDTO>> clearBalanceAndCoin(AccountBenefitQueryDTO param) {
                log.error("[新会员] 筛选条件下的余额余币清除, clearBalanceAndCoin 熔断,param:{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Void> merchantClearBenefit(List<AccountBenefitAdjustDTO> accountBenefitAdjustDTOList) {
                log.error("[新会员] merchantClearBenefit 熔断:{}", accountBenefitAdjustDTOList);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Void> merchantClearBatchBenefit(List<AccountBenefitAdjustDTO> accountBenefitAdjustList) {
                log.error("[新会员] merchantClearBatchBenefit 熔断:{}", accountBenefitAdjustList);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Map<Integer, BigDecimal>> benefitCount(AccountQueryDTO param) {
                log.error("[新会员] benefitCount 熔断:{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }


            @Override
            public RespBody<Void> benefitRollback(BenefitRollbackDTO rollback) {
                log.error("[新会员] benefitRollback 熔断:{}", rollback);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 根据订单回退权益接口
             *
             * @param rollback
             * @param retryFlag
             * @return
             */
            @Override
            public RespBody<Void> benefitRollback(BenefitRollbackDTO rollback, Boolean retryFlag) {
                log.error("[新会员]根据订单回退权益,benefitRollback 接口熔断,rollback:{},retryFlag:{}", rollback, retryFlag);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> payRefundRollbackBenefit(PayRefundBenefitDTO param) {
                log.error("[新会员]支付充值订单退款-根据订单号扣减权益, payRefundRollbackBenefit接口熔断,param:{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 用户账户信息
             * @param condition
             * @return
             */
            @Override
            public RespBody<List<AccountDTO>> accountInfo(AccountConditionDTO condition) {
                log.error("[新会员]查询用户账户信息,accountInfo 熔断:{}", condition);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }


            @Override
            public RespBody<AccountBenefitDataDTO> findAccountBenefitData(AccountBenefitQueryDTO param) {
                log.error("[新会员]用户权益信息-汇总,findAccountBenefitData 熔断:{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<AccountBenefitScopeDTO>> listBenefitWithScope(AccountBenefitScopeQueryDTO query) {
                log.error("[新会员]查询权益及生效范围, listBenefitWithScope 熔断:{}", query);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Page<AccountBenefitDTO>> listBenefitDetail(AccountBenefitQueryDTO param) {
                log.error("[新会员] 用户权益信息-明细, listBenefitDetail 熔断：{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }


            /**
             * 消费记录保存
             *
             * @param recordDTO
             * @return
             */
            @Override
            public RespBody<Boolean> saveRecord(AccountRecordSaveDTO recordDTO) {
                log.error("[新会员] saveRecord 熔断,recordDTO:{}", recordDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 根据订单号退回扣除的权益
             *
             * @param rollbackDTO
             * @return
             */
            @Override
            public RespBody<List<BenefitPreDeductionDTO>> refundBenefitByOrderNo(BenefitRollbackDTO rollbackDTO) {
                log.error("[新会员] refundBenefitByOrderNo 熔断,rollbackDTO:{}", rollbackDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<AccountStatusDTO> findCardStatus(String cardNo, Long merchantId) {
                log.error("[新会员] findCardStatus熔断,cardNo:{},merchantId:{}", cardNo, merchantId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<AccountStatusBatchDTO>> findBatchCardStatus(AccountInfoByCardsQueryDto dto) {
                log.error("[新会员] findBatchCardStatus熔断,dto:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<SmallVenueAccountInfoDTO> getAccountInfo(String keywords, Long merchantId) {
                log.error("[新会员] getAccountInfo熔断,keywords:{},merchantId:{}", keywords, merchantId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<SmallVenueAllCardDTO>> selectUserAllCard(Long merchantId, Long userId, Boolean hasSupplementaryCard) {
                log.error("[新会员] selectUserAllCard熔断,userId:{},merchantId:{},hasSupplementaryCard: {}", userId, merchantId, hasSupplementaryCard);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> supplementaryCardCheck(SupplementaryCardCheckDTO supplementaryCardCheckDTO) {
                log.error("[新会员] supplementaryCardCheck熔断,supplementaryCardCheckDTO:{}", supplementaryCardCheckDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }


            @Override
            public RespBody<Boolean> addSupplementaryCard(AddSupplementaryCardDTO addSupplementaryCardDTO) {
                log.error("[新会员] addSupplementaryCard熔断,addSupplementaryCardDTO:{}", addSupplementaryCardDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<DataList<SmallVenueBenefitDetailDTO>> smallVenueBenefitDetail(BenefitDetailSelectDTO benefitDetailSelectDTO) {
                log.error("[新会员] smallVenueBenefitDetail熔断,benefitDetailSelectDTO:{}", benefitDetailSelectDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<BenefitStatisticsDetailDTO> benefitStatistics(Long merchantId, Long userId, Long accountId) {
                log.error("[新会员] benefitStatistics熔断,merchantId:{},userId:{},accountId:{}", merchantId, userId, accountId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> cardReportLossOrRecover(UpdateCardStatusDTO updateCardStatusDTO) {
                log.error("[新会员] cardReportLossOrRecover熔断,updateCardStatusDTO:{}", updateCardStatusDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> cardDisabledOrRecover(UpdateCardStatusDTO updateCardStatusDTO) {
                log.error("[新会员] cardDisabledOrRecover,updateCardStatusDTO:{}", updateCardStatusDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> reissueCard(ReissueCardDTO reissueCardDTO) {
                log.error("[新会员] reissueCard熔断,reissueCardDTO:{}", reissueCardDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> smallVenueSaveRecord(SmallVenueAccountRecordSaveDTO smallVenueAccountRecordSaveDTO) {
                log.error("[新会员] smallVenueSaveRecord熔断,smallVenueAccountRecordSaveDTO:{}", smallVenueAccountRecordSaveDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> smallVenueSaveRecordBatch(SmallVenueAccountRecordSaveBatchDTO dto) {
                log.error("[新会员] smallVenueSaveRecordBatch熔断,param:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<DataList<SmallVenueRecordListDTO>> smallVenueRecord(SmallVenueRecordSelectDTO smallVenueRecordSelectDTO) {
                log.error("[新会员] smallVenueRecord熔断,smallVenueRecordSelectDTO:{}", smallVenueRecordSelectDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> hasSupplementaryCard(HasSupplementaryCardDTO hasSupplementaryCardDTO) {
                log.error("[新会员] hasSupplementaryCard熔断,hasSupplementaryCardDTO:{}", hasSupplementaryCardDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> renewalCard(CardRenewalDTO cardRenewalDTO) {
                log.error("[新会员] renewalCard熔断,cardRenewalDTO:{}", cardRenewalDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<RecordTypeDTO>> recordTypeList() {
                log.error("[新会员] recordTypeList熔断");
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> supplyAnonymousCardData(SupplyAnonymousCardDataDTO supplyAnonymousCardDataDTO) {
                log.error("[新会员] supplyAnonymousCardData熔断,supplyAnonymousCardDataDTO:{}", supplyAnonymousCardDataDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 根据订单和权益类型查询消耗权益的使用范围
             *
             * @param queryDTO
             * @return
             */
            @Override
            public RespBody<List<RecordBenefitScopeDTO>> findRecordBenefitScopeByOrderNo(RecordBenefitScopeQueryDTO queryDTO) {
                log.error("[新会员] findRecordBenefitScopeByOrderNo熔断,{}", queryDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 根据订单查询消费明细
             *
             * @param queryDTO
             * @return
             */
            @Override
            public RespBody<List<AccountRecordDTO>> findRecordByOrderNoAndCondition(AccountRecordQueryDTO queryDTO) {
                log.error("[新会员] findRecordByOrderNoAndCondition熔断,{}", queryDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 商家充值抵扣金退款校验
             *
             * @param merchantId 商户id
             * @param userId     用户id
             * @param outTradeNo 订单号
             * @return
             */
            @Override
            public RespBody<Boolean> deductionRechargeRefundCheck(Long merchantId, Long userId, String outTradeNo) {
                log.error("[新会员] deductionRechargeRefundCheck熔断,merchantId:{},userId:{},outTradeNo:{}", merchantId, userId, outTradeNo);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<DataList<SmallVenueRecordListDTO>> mobileTerminalRecord(MobileTerminalRecordSelectDTO mobileTerminalRecordSelectDTO) {
                log.error("[新会员] mobileTerminalRecord熔断,{}", mobileTerminalRecordSelectDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<DataList<MobileTerminalAccountListDTO>> mobileTerminalAccountList(MobileTerminalAccountSelectDTO mobileTerminalAccountSelectDTO) {
                log.error("[新会员] mobileTerminalAccountList熔断,{}", mobileTerminalAccountSelectDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<MobileTerminalAccountDetailsDTO> mobileTerminalAccountDetails(Long merchantId, Long userId, Long accountId) {
                log.error("[新会员] mobileTerminalAccountDetails熔断,merchantId:{},userId:{},accountId:{}", merchantId, userId, accountId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<AccountClassifyBenefitDTO>> findAccountClassifyBenefit(AccountClassifyBenefitQueryDTO request) {
                log.error("[新会员] findAccountClassifyBenefit熔断,request:{}", request);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> decreaseAccountBenefit(AccountBenefitDecreaseReqDTO request) {
                log.error("[新会员] decreaseAccountBenefit熔断,request:{}", request);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<OrderBenefitInfoDTO> listOrderBenefitRecord(OrderBenefitRecordQueryDTO query) {
                log.error("[新会员] listOrderBenefitRecord熔断,request:{}", query);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }


            @Override
            public RespBody<Page<AccountAdjustRecordDTO>> listGrantCoinsRecord(AccountAdjustRecordQueryDTO param){
                log.error("[新会员] listGrantCoinsRecord熔断,request:{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Void> benefitModify(AccountBenefitModifyDTO modify) {
                log.error("[新会员] benefitModify熔断,request:{}", modify);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Void> storeBenefitClear(StoreBenefitClearDTO param) {
                log.error("[新会员] storeBenefitClear熔断,request:{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }
        };
    }
}
