package com.lyy.user.account.infrastructure.member.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.member.dto.MemberGrowRecordListDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGrowRecordQueryDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @ClassName: MemberGrowRecordFeignClient
 * @description: 成长值
 * @author: qgw
 * @date: 2021/04/19
 **/
@FeignClient(name = "user-member-center", fallbackFactory = MemberGrowRecordClientFallback.class)
public interface MemberGrowRecordFeignClient {


    /**
     * 查询用户成长值记录
     * @param param    商户用户Id
     * @return
     */
    @PostMapping("/rest/member/grow/record/list")
     RespBody<Page<MemberGrowRecordListDTO>> list(@RequestBody MemberGrowRecordQueryDTO param);



}
