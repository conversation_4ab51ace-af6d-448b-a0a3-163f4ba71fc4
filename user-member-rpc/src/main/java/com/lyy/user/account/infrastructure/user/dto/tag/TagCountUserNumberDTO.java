package com.lyy.user.account.infrastructure.user.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @description: 标签对应的用户数量
 * @author: qgw
 * @date on 2022/4/06.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class TagCountUserNumberDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long tagId;

    /**
     * 会员数量
     */
    private Integer memberCount;


}
