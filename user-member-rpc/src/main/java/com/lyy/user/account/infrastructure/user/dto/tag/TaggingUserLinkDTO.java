package com.lyy.user.account.infrastructure.user.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: qgw
 * @date on 2021-04-22.
 * @Version: 1.0
 */

@Getter
@Setter
@ToString
@Valid
public class TaggingUserLinkDTO {
    @NotNull(message = "标签名称不能为空")
    private String name;

    @NotNull(message = "请指定标签业务类型")
    private Integer businessType;

    @NotNull(message = "请指定平台用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long userId;
}
