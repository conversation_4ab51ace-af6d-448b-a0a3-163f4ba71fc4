package com.lyy.user.account.infrastructure.account.dto.smallvenue.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2022/5/24 - 15:49
 */
@Data
public class AccountDefaultStatusUpdateDTO {

    @NotNull(message = "商户ID不能为空")
    private Long merchantId;

    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @NotBlank(message = "会员卡号不能为空")
    private String carNo;

    private Long operator;

}
