package com.lyy.user.account.infrastructure.report.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.lyy.user.account.infrastructure.constant.AccountRecordOperationTypeEnum;
import java.time.LocalDateTime;
import java.util.List;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * <AUTHOR>
 */
@Data
public class SmallVenueMemberStoreValueRecordQueryDto {

    /**
     * 页码
     */
    @Min(value = 1,message = "页码必须大于0")
    private Integer pageIndex;

    /**
     * 页长
     */
    @Range(min = 1,max = 1000,message = "页长不能超过1000")
    private Integer pageSize;

    @NotNull(message = "商户id不能为空")
    private Long merchantId;

    private Long storeId;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    private String phone;

    private String cardNo;

    private Long accountId;

    private Long userId;

    private Long merchantUserId;

    private String name;

    private String assetEquipmentName;

    private String  orderNo;

    private List<Integer> recordTypeIds;

    private List<Integer> excludeRecordTypes;

    private List<Long> storeValeIds;

    private List<Long> storeIdList;

    private List<Integer> operationChannelIds;

    /**
     * 记录类型 默认储值变更记录
     */
    private Integer operationType = AccountRecordOperationTypeEnum.BENEFIT_MODIFY.getCode();

    /**
     * 店铺名称
     */
    private List<String> storeName;

    private Boolean summaryFlag;
}
