package com.lyy.user.account.infrastructure.user.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.lyy.user.account.infrastructure.constant.UserSourceEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName: UserCreateDTO
 * @description: 用户创建
 * @author: pengkun
 * @date: 2021/04/06
 **/
@Setter
@Getter
@ToString
public class UserCreateDTO {
    /**
     * 用户id
     */
    private Long lyyUserId;

    private String appId;
    /**
     * 用户的唯一标识
     */
    private String openid;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 用户的性别
     */
    private String gender;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String cityName;
    /**
     * 头像
     */
    private String headImg;
    /**
     * unionid
     */
    private String unionid;
    /**
     * 手机号码
     */
    private String telephone;
    /**
     * 静默授权
     * unionId授权模式 传:"1"
     */
    private String silent;

    private String birthday;
    /**
     * 密码
     */
    private String password;

    /**
     * 用户来源枚举
     */
    private UserSourceEnum userSourceEnum;

    /**
     * 商户用户
     */
    private MerchantUserCreateDTO merchantUserCreateDTO;

    /**
     * 操作人Id
     */
    private Long operationUserId;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 押金
     */
    private BigDecimal deposit;
    /**
     * 省份Id
     */
    private Long provinceId;
    /**
     * 城市id
     */
    private Long cityId;
    /**
     * 区域id
     */
    private Long regionId;
    /**
     * 地址信息
     */
    private String address;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date downTime;

    /**
     * 系统前缀标识
     */
    private String sysPrefix;

    @Getter
    @Setter
    @ToString
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class MerchantUserCreateDTO {
        /**
         * 商户Id
         */
        private Long merchantId;
        /**
         * 平台用户Id
         */
        private Long userId;
        /**
         * 手机号码
         */
        private String telephone;
        /**
         * 描述
         */
        private String description;
        /**
         * 所属门店
         */
        private Long storeId;
        /**
         * 门店名称
         */
        private String storeName;
    }

}
