package com.lyy.user.account.infrastructure.member.dto;

import lombok.Data;

import java.util.Optional;

/**
 * 会员组分页响应信息
 * <AUTHOR>
 * @className: MemberGroupPageResponseDTO
 * @date 2021/4/21
 */
@Data
public class MemberGroupPageResponseDTO extends MemberGroupDTO {

    /**
     * 字符串格式的id
     */
    private String idStr;

    public String getIdStr() {
        return Optional.ofNullable(getId()).map(id->id.toString()).orElse("");
    }

    /**
     * 会员数量
     */
    private Integer memberCount;

    /**
     * 设备类型(品类)数量
     */
    private Integer equipmentTypeCount;
    /**
     * 场地数量
     */
    private Integer equipmentGroupCount;
    /**
     * 设备数量
     */
    private Integer equipmentCount;
    /**
     * 商品数量
     */
    private Integer commodityCount;


    /**
     * 会员组的状态
     * -1:全部状态
     * 1:生效中
     * 2:已停用
     * 3:已过期
     * 4:已删除
     * 5:待生效
     */
    private Integer memberGroupStatus;

}
