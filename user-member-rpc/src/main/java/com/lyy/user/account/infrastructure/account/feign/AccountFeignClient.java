package com.lyy.user.account.infrastructure.account.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.account.dto.AccountAdjustRecordDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitAdjustRefundDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDataDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitDecreaseReqDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitModifyDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitResultDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitScopeDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountBenefitScopeQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountClassifyBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountClassifyBenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountConditionDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountConsumption;
import com.lyy.user.account.infrastructure.account.dto.AccountDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountRecordSaveDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountStatusDTO;
import com.lyy.user.account.infrastructure.account.dto.AddSupplementaryCardDTO;
import com.lyy.user.account.infrastructure.account.dto.BenefitQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.BenefitRollbackDTO;
import com.lyy.user.account.infrastructure.account.dto.ConsumeDTO;
import com.lyy.user.account.infrastructure.account.dto.PayRefundBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.RecordBenefitScopeDTO;
import com.lyy.user.account.infrastructure.account.dto.RecordBenefitScopeQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.RecordTypeDTO;
import com.lyy.user.account.infrastructure.account.dto.SmallVenueAccountInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.SmallVenueAllCardDTO;
import com.lyy.user.account.infrastructure.account.dto.SupplementaryCardCheckDTO;
import com.lyy.user.account.infrastructure.account.dto.SupplyAnonymousCardDataDTO;
import com.lyy.user.account.infrastructure.account.dto.UserAccountBenefitDTO;
import com.lyy.user.account.infrastructure.account.dto.AccountStatusBatchDTO;
import com.lyy.user.account.infrastructure.account.dto.request.AccountAdjustRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.request.AccountInfoByCardsQueryDto;
import com.lyy.user.account.infrastructure.account.dto.request.OrderBenefitRecordQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.request.StoreBenefitClearDTO;
import com.lyy.user.account.infrastructure.account.dto.response.OrderBenefitInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.AccountBenefitReqDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.AccountSupplementaryCardQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitDetailSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.BenefitStatisticsDetailDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.CardRenewalDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.HasSupplementaryCardDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountDetailsDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountListDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalAccountSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalCardCountInfo;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.MobileTerminalRecordSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.ReissueCardDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueAccountRecordSaveDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueBenefitDetailDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueRecordListDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.SmallVenueRecordSelectDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.UpdateCardStatusDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.request.SmallVenueAccountRecordSaveBatchDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitConsumeInfoDTO;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitPreDeductionDTO;
import com.lyy.user.account.infrastructure.common.DataList;
import com.lyy.user.account.infrastructure.resp.RespBody;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 类描述：账户接口feign
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/15 11:07
 */
@FeignClient(name = "user-member-center", fallbackFactory = AccountFeignFallback.class)
public interface AccountFeignClient {

    /**
     * 查询权益流水
     *
     * @param param
     * @return
     */
    @PostMapping("/account/record/listRecord")
    RespBody<Page<AccountConsumption>> listRecord(@RequestBody AccountRecordQueryDTO param);

    /**
     * 权益消耗
     *
     * @param consume
     * @return
     */
    @PostMapping("/account/benefit/consume")
    RespBody<Void> benefitConsume(@RequestBody ConsumeDTO consume);

    /**
     * 新会员中心先使用-权益扣除预计算
     *
     * @param consume
     * @return
     */
    @PostMapping("/account/benefit/preDeductionConsume")
    RespBody<List<BenefitPreDeductionDTO>> benefitPreDeductionConsume(@RequestBody ConsumeDTO consume);

    /**
     * 扣减并返回实扣金额，不校验金额是否足够
     *
     * @param consume
     * @return
     */
    @PostMapping("/account/benefit/callback/realConsume")
    RespBody<BenefitConsumeInfoDTO> callbackRealConsume(@RequestBody ConsumeDTO consume);

    /**
     * 权益调整
     *
     * @param param
     * @return
     */
    @PostMapping("/account/benefit/adjust")
    RespBody<Void> benefitAdjust(@RequestBody List<AccountBenefitAdjustDTO> param);

    /**
     * 商家调整权益
     * 此接口经测试，只适合做权益新增，不适合做权益扣减，权益扣减使用权益消耗接口
     *
     * @param accountBenefitAdjustDTO
     * @return
     */
    @PostMapping("/account/benefit/merchant/adjust")
    RespBody merchantAdjustBenefit(@RequestBody AccountBenefitAdjustDTO accountBenefitAdjustDTO);

    /**
     * 商家调整权益退还，主要解决返还的权益需要有有效期限制
     * 此接口经测试，只适合做权益新增，不适合做权益扣减，权益扣减使用权益消耗接口
     *
     * @param accountBenefitAdjustDTO
     * @return
     */
    @PostMapping("/account/benefit/merchant/refund")
    RespBody merchantRefundBenefit(@RequestBody AccountBenefitAdjustRefundDTO accountBenefitAdjustDTO);


    /**
     * 余额余币清除接口，目前用于运营后台
     *
     * @param param 匹配参数
     * @return RespBody  清除的权益，按类型统计
     */
    @PostMapping("/account/benefit/balance/coin/clear")
    RespBody<List<AccountBenefitResultDTO>> clearBalanceAndCoin(@RequestBody AccountBenefitQueryDTO param);

    /**
     * 用户B端商户清除指定用户的权益
     *
     * @param accountBenefitAdjustDTOList
     * @return
     */
    @PostMapping("/account/benefit/merchant/clear")
    RespBody<Void> merchantClearBenefit(@RequestBody List<AccountBenefitAdjustDTO> accountBenefitAdjustDTOList);

    /**
     * 清除指定用户的指定批次权益
     *
     * @param accountBenefitAdjustList
     * @return
     */
    @PostMapping("/account/benefit/merchant/batch/clear")
    RespBody<Void> merchantClearBatchBenefit(@RequestBody List<AccountBenefitAdjustDTO> accountBenefitAdjustList);

    /**
     * 查询权益余额
     *
     * @param param
     * @return
     */
    @PostMapping("/account/benefit/count")
    RespBody<Map<Integer, BigDecimal>> benefitCount(@RequestBody AccountQueryDTO param);


    /**
     * 根据订单回退权益接口
     *
     * @param rollback
     * @return
     */
    @PostMapping("/account/benefit/rollback")
    RespBody<Void> benefitRollback(@RequestBody BenefitRollbackDTO rollback);


    /**
     * 根据订单回退权益接口
     *
     * @param rollback
     * @return
     */
    @PostMapping("/account/benefit/rollback")
    RespBody<Void> benefitRollback(@RequestBody BenefitRollbackDTO rollback,
                                   @RequestParam(value = "retryFlag", required = false,defaultValue = "false") Boolean retryFlag);

    /**
     * 支付充值订单退款-根据订单号扣减权益
     *
     * @param param
     * @return
     */
    @PostMapping("/account/benefit/payRefundRollbackBenefit")
    RespBody<Boolean> payRefundRollbackBenefit(@RequestBody PayRefundBenefitDTO param);


    /**
     * 用户账户信息
     *
     * @param condition
     * @return
     */
    @PostMapping("/account/info")
    RespBody<List<AccountDTO>> accountInfo(@RequestBody AccountConditionDTO condition);


    /**
     * 用户权益信息-汇总
     *
     * @param param
     * @return
     */
    @PostMapping("/account/benefit/findAccountBenefitData")
    RespBody<AccountBenefitDataDTO> findAccountBenefitData(@RequestBody AccountBenefitQueryDTO param);

    @PostMapping("/account/benefit/scope")
    RespBody<List<AccountBenefitScopeDTO>> listBenefitWithScope(@RequestBody AccountBenefitScopeQueryDTO query);

    /**
     * 用户权益信息-明细
     *
     * @param param
     * @return
     */
    @PostMapping("/account/benefit/listBenefitDetail")
    RespBody<Page<AccountBenefitDTO>> listBenefitDetail(@RequestBody AccountBenefitQueryDTO param);

    /**
     * 消费记录保存
     *
     * @param recordDTO
     * @return
     */
    @PostMapping(value = "/account/record/save")
    RespBody<Boolean> saveRecord(@RequestBody AccountRecordSaveDTO recordDTO);

    /**
     * 根据订单号退回扣除的权益
     *
     * @param rollbackDTO
     * @return
     */
    @PostMapping(value = "/account/benefit/refundBenefitByOrderNo")
    RespBody<List<BenefitPreDeductionDTO>> refundBenefitByOrderNo(@RequestBody BenefitRollbackDTO rollbackDTO);


    /**
     * 查询会员卡状态
     *
     * @param cardNo
     * @param merchantId
     * @return
     */
    @GetMapping(value = "/account/smallVenue/cardStatus")
    RespBody<AccountStatusDTO> findCardStatus(@RequestParam("cardNo") String cardNo, @RequestParam("merchantId") Long merchantId);

    @PostMapping(value = "/account/smallVenue/batchCardStatus")
    RespBody<List<AccountStatusBatchDTO>> findBatchCardStatus(@RequestBody AccountInfoByCardsQueryDto dto);

    /**
     * 根据会员卡/会员id/手机号查询账户
     *
     * @param keywords
     * @param merchantId
     * @return
     */
    @GetMapping(value = "/account/smallVenue/getAccountInfo")
    RespBody<SmallVenueAccountInfoDTO> getAccountInfo(@RequestParam("keyword") String keyword, @RequestParam("merchantId") Long merchantId);


    /**
     * 获取用户的所有的会员主卡
     *
     * @param merchantId
     * @param userId
     * @return
     */
    @GetMapping(value = "/account/smallVenue/allCard")
    RespBody<List<SmallVenueAllCardDTO>> selectUserAllCard(@RequestParam("merchantId") Long merchantId,
                                                           @RequestParam("userId") Long userId,
                                                           @RequestParam("hasSupplementaryCard") Boolean hasSupplementaryCard);

    /**
     * 附属卡添加校验
     *
     * @param supplementaryCardCheckDTO
     * @return
     */
    @PostMapping(value = "/account/smallVenue/addSupplementaryCard/check")
    RespBody<Boolean> supplementaryCardCheck(@RequestBody SupplementaryCardCheckDTO supplementaryCardCheckDTO);


    /**
     * 添加附属卡
     *
     * @param addSupplementaryCardDTO
     * @return
     */
    @PostMapping(value = "/account/smallVenue/addSupplementaryCard")
    RespBody<Boolean> addSupplementaryCard(@RequestBody AddSupplementaryCardDTO addSupplementaryCardDTO);

    /**
     * 权益详情(余额详情)
     *
     * @param benefitDetailSelectDTO
     * @return
     */
    @PostMapping(value = "/account/smallVenue/benefit/detail")
    RespBody<DataList<SmallVenueBenefitDetailDTO>> smallVenueBenefitDetail(@RequestBody BenefitDetailSelectDTO benefitDetailSelectDTO);

    /**
     * 查询权益统计数据（根据门店进行分组）
     *
     * @param merchantId
     * @param userId
     * @param accountId
     * @return
     */
    @GetMapping(value = "/account/smallVenue/benefitStatistics")
    RespBody<BenefitStatisticsDetailDTO> benefitStatistics(@RequestParam("merchantId") Long merchantId, @RequestParam("userId") Long userId, @RequestParam("accountId") Long accountId);

    /**
     * 会员卡挂失/恢复
     *
     * @param updateCardStatusDTO
     * @return
     */
    @PostMapping(value = "/account/smallVenue/cardReportLossOrRecover")
    RespBody<Boolean> cardReportLossOrRecover(@RequestBody UpdateCardStatusDTO updateCardStatusDTO);

    /**
     * 会员卡禁用/恢复
     *
     * @param updateCardStatusDTO
     * @return
     */
    @PostMapping(value = "/account/smallVenue/cardDisabledOrRecover")
    RespBody<Boolean> cardDisabledOrRecover(@RequestBody UpdateCardStatusDTO updateCardStatusDTO);

    /**
     * 会员卡补卡/换卡
     *
     * @param reissueCardDTO
     * @return
     */
    @PostMapping(value = "/account/smallVenue/reissueCard")
    RespBody<Boolean> reissueCard(@RequestBody ReissueCardDTO reissueCardDTO);

    /**
     * 商品销售记录、兑换记录、商品回收记录保存
     *
     * @param smallVenueAccountRecordSaveDTO
     * @return
     */
    @PostMapping(value = "/account/smallVenue/saveRecord")
    RespBody<Boolean> smallVenueSaveRecord(@RequestBody SmallVenueAccountRecordSaveDTO smallVenueAccountRecordSaveDTO);

    /**
     * 商品销售记录、兑换记录、商品回收记录批量保存
     */
    @PostMapping("/account/smallVenue/saveRecord/batch")
    RespBody<Boolean> smallVenueSaveRecordBatch(@RequestBody SmallVenueAccountRecordSaveBatchDTO dto);

    /**
     * 商品购买记录、商品销售记录、兑换记录、商品回收记录、设备消费记录、储值变更记录查询
     *
     * @param smallVenueRecordSelectDTO
     * @return
     */
    @PostMapping(value = "/account/smallVenue/record")
    RespBody<DataList<SmallVenueRecordListDTO>> smallVenueRecord(@RequestBody SmallVenueRecordSelectDTO smallVenueRecordSelectDTO);

    /**
     * 查询主卡是否存在附属卡
     *
     * @param hasSupplementaryCardDTO
     * @return
     */
    @PostMapping(value = "/account/smallVenue/hasSupplementaryCard")
    RespBody<Boolean> hasSupplementaryCard(@RequestBody HasSupplementaryCardDTO hasSupplementaryCardDTO);

    /**
     * 会员卡续期
     *
     * @param cardRenewalDTO
     * @return
     */
    @PostMapping(value = "/account/smallVenue/renewalCard")
    RespBody<Boolean> renewalCard(@RequestBody CardRenewalDTO cardRenewalDTO);

    /**
     * 业务类型列表
     *
     * @return
     */
    @GetMapping(value = "/account/smallVenue/recordTypeList")
    RespBody<List<RecordTypeDTO>> recordTypeList();


    /**
     * 不记名卡的资料补充
     *
     * @param supplyAnonymousCardDataDTO
     * @return
     */
    @PostMapping(value = "/account/smallVenue/supplyAnonymousCardData")
    RespBody<Boolean> supplyAnonymousCardData(@RequestBody @Valid SupplyAnonymousCardDataDTO supplyAnonymousCardDataDTO);

    /**
     * 根据订单和权益类型查询消耗权益的使用范围
     *
     * @param queryDTO
     * @return
     */
    @PostMapping(value = "/account/record/findRecordBenefitScopeByOrderNo")
    RespBody<List<RecordBenefitScopeDTO>> findRecordBenefitScopeByOrderNo(@RequestBody RecordBenefitScopeQueryDTO queryDTO);

    /**
     * 根据订单查询消费明细
     *
     * @param queryDTO
     * @return
     */
    @PostMapping(value = "/record/findRecordByOrderNoAndCondition")
    RespBody<List<AccountRecordDTO>> findRecordByOrderNoAndCondition(@RequestBody AccountRecordQueryDTO queryDTO);


    /**
     * 商家充值抵扣金退款校验
     *
     * @param merchantId 商户id
     * @param userId     用户id
     * @param outTradeNo 订单号
     * @return
     */
    @GetMapping(value = "/account/deductionRechargeRefundCheck")
    RespBody<Boolean> deductionRechargeRefundCheck(@RequestParam("merchantId") Long merchantId,
                                                   @RequestParam("userId") Long userId,
                                                   @RequestParam("outTradeNo") String outTradeNo);


    /**
     * 移动端账户流水记录
     *
     * @param mobileTerminalRecordSelectDTO
     * @return
     */
    @PostMapping(value = "/account/mobileTerminal/record")
    RespBody<DataList<SmallVenueRecordListDTO>> mobileTerminalRecord(@RequestBody @Valid MobileTerminalRecordSelectDTO mobileTerminalRecordSelectDTO);

    /**
     * 移动端会员卡列表
     *
     * @param mobileTerminalAccountSelectDTO
     * @return
     */
    @PostMapping(value = "/account/mobileTerminal/accountList")
    RespBody<DataList<MobileTerminalAccountListDTO>> mobileTerminalAccountList(@RequestBody @Valid MobileTerminalAccountSelectDTO mobileTerminalAccountSelectDTO);


    /**
     * 移动端会员卡详情
     *
     * @param merchantId
     * @param userId
     * @param accountId
     * @return
     */
    @GetMapping(value = "/account/mobileTerminal/accountDetails")
    RespBody<MobileTerminalAccountDetailsDTO> mobileTerminalAccountDetails(@RequestParam("merchantId") Long merchantId, @RequestParam("userId") Long userId, @RequestParam("accountId") Long accountId);

    /**
     * 获取账户指定类型权益信息（包含消耗规则、范围）
     */
    @PostMapping("/account/classify/benefits")
    RespBody<List<AccountClassifyBenefitDTO>> findAccountClassifyBenefit(@RequestBody AccountClassifyBenefitQueryDTO request);

    /**
     * 扣减账户权益
     */
    @PostMapping("/account/benefit/decrease")
    RespBody<Boolean> decreaseAccountBenefit(@RequestBody AccountBenefitDecreaseReqDTO request);

    @PostMapping("account/benefit/order/records")
    RespBody<OrderBenefitInfoDTO> listOrderBenefitRecord(@RequestBody OrderBenefitRecordQueryDTO query);


    @PostMapping("/account/record/listGrantCoinsRecord")
    RespBody<Page<AccountAdjustRecordDTO>> listGrantCoinsRecord(@RequestBody AccountAdjustRecordQueryDTO param);

    /**
     * 权益变更
     */
    @PostMapping("/account/benefit/modify")
    RespBody<Void> benefitModify(@RequestBody AccountBenefitModifyDTO modify);

    /**
     * 用户指定场地权益清除
     */
    @PostMapping("/account/benefit/store/clear")
    RespBody<Void> storeBenefitClear(@RequestBody StoreBenefitClearDTO param);

    /**
     * 权益批次详情分页列表
     *
     * @param accountBenefitReqDTO
     * @return
     */
    @PostMapping("/account/benefit/detail/page")
    RespBody<DataList<SmallVenueBenefitDetailDTO>> queryAccountBenefitList(@RequestBody @Valid AccountBenefitReqDTO accountBenefitReqDTO);

    @PostMapping("/account/benefit/findByIds")
    RespBody<List<UserAccountBenefitDTO>> findBenefitByIds(@RequestBody BenefitQueryDTO query);

    @PostMapping(value = "/account/smallVenue/getAccountInfoByCards")
    RespBody<List<SmallVenueAccountInfoDTO>> getAccountInfoByCards(@RequestBody AccountInfoByCardsQueryDto dto);

    @PostMapping("/account/smallVenue/getSupplementaryCard")
    RespBody<List<MobileTerminalCardCountInfo>> getSupplementaryCard(@RequestBody AccountSupplementaryCardQueryDTO dto );
}
