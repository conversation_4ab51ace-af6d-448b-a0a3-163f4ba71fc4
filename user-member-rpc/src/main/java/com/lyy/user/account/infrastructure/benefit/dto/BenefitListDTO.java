package com.lyy.user.account.infrastructure.benefit.dto;

import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import lombok.Data;

import java.time.LocalDate;

/**
 * 用户权益列表DTO
 *
 * <AUTHOR>
 * @create 2021/4/1 11:35
 */
@Data
public class BenefitListDTO {

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 用户ID
     */
    private Long lyyUserId;

    /**
     * 标题
     */
    private String title;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 权益分类
     */
    private BenefitClassifyEnum benefitClassify;

    /**
     * 权益数量
     */
    private Integer benefitCount;

    /**
     * 可用有效期类型
     */
    private Integer expiryDateCategory;

    private LocalDate upTime;

    private LocalDate downTime;

    private Integer showDateCategory;
}
