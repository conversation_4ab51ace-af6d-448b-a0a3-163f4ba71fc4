package com.lyy.user.account.infrastructure.statistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 类描述：用户统计DTO
 * <p>
 *
 * <AUTHOR>
 * @since 2021/06/16 14:48
 */

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserStatisticsRecordDTO {


    private Long merchantId;


    private Long userId;

    /**
     * 商户用户id
     */
    private Long merchantUserId;

    /**
     * 启动次数
     */
    private Integer startTimes;

    /**
     * 充值次数
     */
    private Integer payTimes;

    /**
     * 充值金额
     */
    private BigDecimal payAmount;

    /**
     * 支付购买次数
     */
    private Integer payForServiceTimes;

    /**
     * 支付购买金额
     */
    private BigDecimal payForServiceAmount;

    /**
     * 消耗币数（累计投币）
     */
    private BigDecimal coinsConsumption;

    /**
     * 消耗金额（累计消耗金额）
     */
    private BigDecimal amountConsumption;

    /**
     * 储值币数
     */
    private BigDecimal totalCoins;

    /**
     * 剩余币数
     */
    private BigDecimal balanceCoins;

    /**
     * 剩余金额
     */
    private BigDecimal balanceAmount;

    /**
     * 最近消费时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recentConsumptionTime;

    /**
     * 储值余额
     */
    private BigDecimal totalAmount;


}
