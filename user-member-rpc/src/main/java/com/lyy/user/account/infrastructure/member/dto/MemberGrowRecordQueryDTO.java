package com.lyy.user.account.infrastructure.member.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lyy.user.account.infrastructure.user.dto.PageQueryDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * 标签查询
 */
@Getter
@Setter
@ToString(callSuper = true)
public class MemberGrowRecordQueryDTO extends PageQueryDTO {


    /**
     * 商户用户ID
     */
    private List<Long> merchantUserIds;

    /**
     * 商户id
     */
    @NotEmpty(message = "商户ID不能为空")
    private List<Long> merchantIds;

    /**
     * 会员id
     */
    private List<Long> memberIds;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

}
