package com.lyy.user.account.infrastructure.account.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 类描述：账户流水对象
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/1 15:52
 */
@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccountRecordCountDTO {

    /**
     * 	商户
     */
    private Long merchantId;
    /**
     * 	时间
     */
    private Date createTime;

    /**
     * 设备类型ID
     */
    private List<Long> equipmentTypeIdList;

    /**
     * 流水类型
     */
    private List<Integer> recordTypeList;

}
