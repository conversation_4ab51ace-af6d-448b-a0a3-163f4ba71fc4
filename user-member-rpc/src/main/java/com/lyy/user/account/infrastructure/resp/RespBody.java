package com.lyy.user.account.infrastructure.resp;

import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.error.constant.ResultCode;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * @param <T>
 * <AUTHOR>
 */
@Data
@ToString
@Slf4j
public class RespBody<T> {

    /**
     * 自定义业务码
     */
    private String code;
    /**
     * 自定义业务提示说明
     */
    private String message;
    /**
     * 自定义返回 数据结果集
     */
    private T body;


    public RespBody() {

    }

    public RespBody(String code) {
        this.code = code;
    }

    public RespBody(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public RespBody(String code, String message, T body) {
        this.code = code;
        this.message = message;
        this.body = body;
    }

    public static RespBody<Void> ok() {
        return ok(null);
    }

    public static <T> RespBody<T> ok(T body) {
        return build(GlobalErrorCode.OK, body);
    }

    public static RespBody<ErrorBody> error(String code, String message, ErrorBody errorBody) {
        return build(code, message, errorBody);
    }

    public static RespBody<ErrorBody> error(ResultCode resultCode, ErrorBody errorBody) {
        return build(resultCode.getCode(), resultCode.getMessage(), errorBody);
    }

    public static RespBody<ErrorBody> error(ResultCode resultCode, String message, ErrorBody errorBody) {
        return build(resultCode.getCode(), message, errorBody);
    }

    public static RespBody<?> error(ResultCode resultCode){
        return build(resultCode.getCode(), resultCode.getMessage(), null);
    }
    public static RespBody<?> error(String code, String message) {
        return build(code, message, null);
    }

    public static RespBody<?> error(ResultCode resultCode, String message) {
        return build(resultCode.getCode(), message, null);
    }

    public static <T> RespBody<T> fail(ResultCode resultCode) {
        return fail(resultCode, resultCode.getMessage(), null);
    }

    public static <T> RespBody<T> fail(ResultCode resultCode, String message, T data) {
        return build(resultCode.getCode(), message, data);
    }


    public static <T> RespBody<T> build(ResultCode resultCode, T body) {
        return build(resultCode.getCode(), resultCode.getMessage(), body);
    }

    /**
     * 以上所有构建均调用此底层方法
     *
     * @param stateCode 状态值
     * @param message   返回消息
     * @param body      返回数据体
     */
    public static <T> RespBody<T> build(String stateCode, String message, T body) {
        return new RespBody<>(stateCode, message, body);
    }
}
