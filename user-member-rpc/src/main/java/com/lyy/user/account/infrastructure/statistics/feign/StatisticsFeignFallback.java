package com.lyy.user.account.infrastructure.statistics.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.statistics.dto.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/15 11:18
 */
@Slf4j
@Component
public class StatisticsFeignFallback implements FallbackFactory<StatisticsFeignClient> {
    @Override
    public StatisticsFeignClient create(Throwable throwable) {

        if (throwable != null && StringUtils.isNotEmpty(throwable.getMessage())) {
            log.error(throwable.getMessage(), throwable);
        }

        return new StatisticsFeignClient() {
            @Override
            public RespBody<Void> updateStatistics(UserStatisticsUpdateDTO param) {
                log.error("[新会员] 统计-updateStatistics方法熔断,param:{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<UserStatisticsRecordDTO> find(UserStatisticsConditionDTO param) {
                log.error("[新会员] 统计-find方法熔断,param:{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<MerchantStatisticsRecordDTO> getMerchantStatistics(Long merchantId) {
                log.error("[新会员] 统计-getMerchantStatistics方法熔断,param:{}", merchantId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Page<MerchantUserStatisticsListDTO>> queryStatisticsUserList(StatisticsUserQueryDTO statisticsUserQueryDTO) {
                log.error("[新会员] 统计-queryStatisticsUserList方法熔断,param:{}", statisticsUserQueryDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<MerchantUserStatisticsDailyDTO> getUserCurrentDateStatistics(UserStatisticsConditionDTO param) {
                log.error("[新会员] 统计-getUserCurrentDateStatistics方法熔断,param:{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<MerchantUserRankRecordDTO>> getRechargeRank(MerchantUserRankConditionDTO condition) {
                log.error("[新会员] 统计-getRechargeRank方法熔断,param:{}", condition);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }
        };
    }
}
