package com.lyy.user.account.infrastructure.user.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description: 用户标签
 * @author: qgw
 * @date on 2021/4/2.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class TagUserDTO {

    /**
     * 标签ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签编码
     */
    private String code;

    /**
     * 商户ID或平台ID
     */
    private Long merchantId;

    /**
     * 标签类型，1:自动, 2:手动
     */
    private Integer category;

    /**
     * 标签的业务类型
     * @see com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum
     */
    private Integer businessType;


    private String businessTypeName;

}
