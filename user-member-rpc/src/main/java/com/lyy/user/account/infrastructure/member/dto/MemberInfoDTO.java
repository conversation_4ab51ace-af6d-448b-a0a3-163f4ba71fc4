package com.lyy.user.account.infrastructure.member.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserDTO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

/**
 * 会员的详细信息，包括会员组信息与会员等级信息
 * <AUTHOR>
 * @className: MemberInfoDTO
 * @date 2021/4/12
 */
@Data
public class MemberInfoDTO extends MemberDTO {
    /**
     * 最后消费时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date consumptionMoneyTime;
    /**
     * 总消费金额
     */
    private BigDecimal consumptionMoneySum;

    /**
     * 字符串格式的id
     */
    private String idStr;
    public String getIdStr() {
        return Optional.ofNullable(getId()).map(id->id.toString()).orElse("");
    }

    private MemberGroupDTO memberGroupDTO;
    private MemberLevelDTO memberLevelDTO;
    private MerchantUserDTO merchantUserDTO;

}
