package com.lyy.user.account.infrastructure.account.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 
 * @author: qgw
 * @date on 2021/11/25.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class PayRefundBenefitDTO  {
    /**
     * 用户id
     */
    @NotNull
    private Long userId;
    /**
     * 商户id
     */
    @NotNull
    private Long merchantId;
    /**
     * 场地id(查询权益使用场地范围标识)
     */
    private Long storeId;
    /**
     * 设备id
     */
    private Long equipmentId;
    /**
     * 设备类型id
     */
    private Long equipmentTypeId;
    /**
     * 商品id
     */
    private Long commodityId;
    /**
     * 支付单号
     */
    private String outTradeNo;

    /**
     * 业务单号
     */
    @NotNull
    private String orderNo;

    /**
     * 变更来源
     */
    private String resource;

    /**
     * 操作者
     */
    private Long operator;

    /**
     * 备注
     */
    private String description;
    /**
     * 交易类型
     */
    private Integer tradeType;

    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;

    /**
     * 商品名称
     */
    private String commodityName;
    /**
     * 场地/店铺名称
     */
    private String storeName;
    /**
     * 设备类型名称
     */
    private String equipmentTypeName;
    /**
     * 设备编号
     */
    private String equipmentValue;

    /**
     * 机台号
     */
    private Integer groupNumber;

    /**
     * 记录类型
     * @see com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum
     */
    @NotNull(message = "记录类型不能为空")
    private Integer recordType;

    /**
     * 是否全部回退
     */
    private Boolean allRollBack;

    /**
     * 是否处理账户的total
     */
    private Boolean modifyTotal;

    /**
     * 回退权益详情列表
     */
    private List<PayRefundBenefitDetailDTO> refundBenefitDetails;



}
