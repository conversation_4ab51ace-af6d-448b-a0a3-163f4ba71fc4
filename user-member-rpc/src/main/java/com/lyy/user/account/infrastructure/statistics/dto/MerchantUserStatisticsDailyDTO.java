package com.lyy.user.account.infrastructure.statistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Locale;

/**
 * 商户用户统计信息
 *
 * @author: 蒋成
 * @create 2021/10/23 15:26
 **/
@Setter
@Getter
@ToString
public class MerchantUserStatisticsDailyDTO {

    /**
     * 商户用户Id
     */
    private Long merchantUserId;

    /**
     * 商户Id
     */
    private Long merchantId;


    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 启动次数
     */
    private Integer startTimes;

    /**
     * 充值次数
     */
    private Integer payTimes;

    /**
     * 充值金额
     */
    private BigDecimal payAmount;

    /**
     * 支付购买次数
     */
    private Integer payForServiceTimes;

    /**
     * 支付购买金额
     */
    private BigDecimal payForServiceAmount;

    /**
     * 消耗币数（累计投币）
     */
    private BigDecimal coinsConsumption;

    /**
     * 消耗金额（累计消耗金额）
     */
    private BigDecimal amountConsumption;



}
