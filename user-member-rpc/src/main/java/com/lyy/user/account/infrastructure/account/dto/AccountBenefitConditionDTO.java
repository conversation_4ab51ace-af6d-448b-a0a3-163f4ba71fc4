package com.lyy.user.account.infrastructure.account.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 类描述：账户权益明细查询参数
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/19 14:09
 */
@Getter
@Setter
@ToString
public class AccountBenefitConditionDTO {

    /**
     * 商家用户id
     */
    @NotNull(message = "商家用户id必须传值")
    private Long merchantUserId;

    /**
     * 商户id
     */
    @NotNull(message = "商户必须传值")
    private Long merchantId;

    /**
     * 店铺id
     */
    private List<Long> storeId;

    /**
     * 设备类型id
     */
    private List<Long> equipmentTypeId;

    /**
     * 状态
     * @see com.lyy.user.account.infrastructure.constant.AccountBenefitStatusEnum
     */
    private Integer status;

    /**
     * 权益分组类型：
     * @see com.lyy.user.account.infrastructure.constant.BenefitClassifyGroupEnum
     */
    private Integer benefitGroupType;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;


    /**
     * 不查的权益类型
     */
    private List<Integer> excludeClassify;
}
