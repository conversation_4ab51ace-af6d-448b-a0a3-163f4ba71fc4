package com.lyy.user.account.infrastructure.member.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @className: MemberLiftingRuleRecordDTO
 * @date 2021/4/7
 */
@Data
public class MemberLiftingRuleRecordDTO {

    private Long id;
    /**
     * 平台用户
     */
    private Long userId;
    /**
     * 商户用户
     */
    private Long merchantUserId;
    /**
     * 会员id
     */
    private Long memberId;
    /**
     * 会员升级记录规则
     */
    private Long memberLiftingRuleId;
    /**
     * 状态，1：已经触发，未处理；2：已经处理完成；3：已经失效(过期)
     */
    private Short status;
    /**
     * 范围值,若为消费金额时，单位为元
     */
    private Long rangeValue;
    /**
     * 其他记录信息
     */
    private String otherInfo;
    /**
     * 有效期截止时间
     */
    private Date endTime;
    private Date createTime;
    private Date updateTime;

}
