package com.lyy.user.account.infrastructure.account.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 类描述：账户明细余额查询对象
 * <p>
 *
 * <AUTHOR>
 * @since 2021/6/05 14:59
 */
@Getter
@Setter
@ToString
public class AccountBenefitDataDTO {
    /**
     * 生效中
     */
    private BigDecimal effect;
    /**
     * 已过期
     */
    private BigDecimal expired;
    /**
     * 已消耗
     */
    private BigDecimal consumed;

    private List<AccountBenefitResultDTO> data;

}
