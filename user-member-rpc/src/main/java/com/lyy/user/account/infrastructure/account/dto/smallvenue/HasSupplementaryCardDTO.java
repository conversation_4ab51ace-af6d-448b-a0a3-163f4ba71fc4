package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class HasSupplementaryCardDTO {

    @NotNull(message = "userId不能为空")
    private Long userId;
    @NotNull(message = "merchantId不能为空")
    private Long merchantId;
    @NotNull(message = "accountId不能为空")
    private Long accountId;
}
