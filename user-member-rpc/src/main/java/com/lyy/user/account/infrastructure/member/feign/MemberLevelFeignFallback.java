package com.lyy.user.account.infrastructure.member.feign;

import com.lyy.user.account.infrastructure.base.BaseFeignFallback;
import com.lyy.user.account.infrastructure.member.dto.MemberLevelDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *
 * 成长等级
 * @author: 蒋成
 * @date: 2022/02/25
 **/
@Slf4j
@Component
public class MemberLevelFeignFallback extends BaseFeignFallback<MemberLevelFeignClient> {

}
