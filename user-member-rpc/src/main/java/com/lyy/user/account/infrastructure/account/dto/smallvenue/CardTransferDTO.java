package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import com.lyy.user.account.infrastructure.constant.AccountBenefitNumTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 会员卡转账
 *
 * <AUTHOR>
 * @since 2022/1/8
 */
@Data
@NoArgsConstructor
public class CardTransferDTO {

    /**
     * 转出卡号
     */
    @NotBlank(message = "转出卡号不能为空")
    private String outCardNo;

    /**
     * 转出卡号
     */
    @NotBlank(message = "转入卡号不能为空")
    private String inCardNo;

    /**
     * 转账明细
     */
    @Valid
    @NotEmpty(message = "转账明细不能为空")
    private List<BenefitDTO> benefit;

    /**
     * 操作人Id
     */
    @NotNull(message = "操作人Id不能为空")
    private Long operatorId;

    /**
     * 商户id
     */
    @NotNull(message = "商户ID不能为空")
    private Long merchantId;
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    /**
     * 门店id
     */
    @NotNull(message = "门店ID不能为空")
    private Long storeId;

    private String orderNo;

    /**
     * 流水信息
     */
    @Valid
    private AccountRecordInfoDTO record;

    @Data
    @NoArgsConstructor
    public static class BenefitDTO {

        @NotNull(message = "商户自定义权益类型ID不能为空")
        private Long merchantBenefitClassifyId;

        private String merchantBenefitClassifyName;

        /**
         * 频次卡需要按明细来转
         */
        private Long accountBenefitId;


        @NotNull(message = "转账数值不能为空")
        @PositiveOrZero(message = "权益数值不能为负数")
        private BigDecimal num;
        /**
         * 权益值类型
         *
         * @see AccountBenefitNumTypeEnum
         */
        @NotNull(message = "权益值类型不能为空")
        private Integer numType;
    }

}
