package com.lyy.user.account.infrastructure.account.dto.request;

import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class StoreBenefitClearDTO {

    @NotNull(message = "商户ID不能为空")
    private Long merchantId;

    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @NotNull(message = "权益分组类型 1:币 2:余额")
    private Integer classifyType;

    @NotNull(message = "场地ID不能为空")
    private Long storeId;

    @NotNull(message = "操作人不能为空")
    private Long operator;
}
