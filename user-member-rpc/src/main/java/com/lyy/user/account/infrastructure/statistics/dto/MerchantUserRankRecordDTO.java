package com.lyy.user.account.infrastructure.statistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/6/13
 */
@Data
public class MerchantUserRankRecordDTO {

    private Long lyyUserId;
    private String headImg;
    private String name;
    private BigDecimal totalRecharge;
    private BigDecimal recharge;
    private Integer rank;
    private String type;
}
