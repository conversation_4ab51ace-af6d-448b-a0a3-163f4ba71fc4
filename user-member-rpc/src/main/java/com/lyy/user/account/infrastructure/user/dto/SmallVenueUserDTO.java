package com.lyy.user.account.infrastructure.user.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * 小场地会员列表
 */
@Data
@Accessors(chain = true)
public class SmallVenueUserDTO {

    /**
     * 会员姓名
     */
    private String name;

    /**
     * 会员ID
     */
    private Long id;

    /**
     * 平台用户id
     */
    private Long userId;

    /**
     * 会员头像
     */
    private String headImg;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 会员来源
     */
    private String userType;

    /**
     * 会员等级id
     */
    private Long memberLevelId;

    /**
     * 会员等级名称
     */
    private String memberLevelName;

    /**
     * 注册时间
     */
    private Date createTime;

    /**
     * 消费累计
     */
    private BigDecimal amountConsumption;

    /**
     * 最近消费时间
     */
    private Date recentConsumptionTime;


}
