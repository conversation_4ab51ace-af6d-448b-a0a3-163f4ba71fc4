package com.lyy.user.account.infrastructure.benefit.dto;

import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 *
 * 通用场地保存
 * <AUTHOR>
 * @create 2021/5/28 16:25
 */
@Data
public class GeneralGroupBenefitSaveDTO {

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 权益分类
     */
    @NotNull(message = "权益类型不能为空")
    private List<BenefitClassifyEnum> benefitClassifyEnumList;

    /**
     * 使用场地集合
     */
    @NotNull(message = "场地集合不用为空")
    private List<Long> groupIdList;

    private Integer expiryDateCategory;
    private String upTime;
    private String downTime;

}
