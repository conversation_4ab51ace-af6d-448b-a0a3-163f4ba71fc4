package com.lyy.user.account.infrastructure.report.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @date : 2022-01-31 11:39
 **/
@Data
public class SmallVenueStoreValueSimpleDto {

    private Long merchantUserId;

    private Long accountId;

    private BigDecimal num ;

    /**
     * 储值id
     */
    private Long storeValueId;

    /**
     * 批次总和
     */
    private Long benefitTotalNum;

    /**
     * 权益值类型，1：额度类型，2：频次类型
     */
    private Integer valueType;



}
