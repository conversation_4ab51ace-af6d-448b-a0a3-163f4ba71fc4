package com.lyy.user.account.infrastructure.user.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MerchantUserUpdatePasswordDTO {

    @NotNull(message = "userId不能为空")
    private Long userId;

    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    private String oldPassWord;

    @NotNull(message = "newPassWord不能为空")
    private String newPassWord;
}
