package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 娱乐会员储值转移到多金宝会员下DTO
 *
 * <AUTHOR> =￣ω￣=
 * @date 2023/9/5
 */
@Data
public class SmallVenueAccountBenefitTransferQueryDTO {

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 会员ID
     */
    private Long merchantUserId;

    /**
     * 储值类型
     */
    private List<Integer> classifys;

    /**
     * 储值所属场地ID
     */
    private List<Long> storeIds;

    /**
     * 储值适用类型
     */
    private Integer applicable;

    /**
     * 操作描述
     */
    private String desc;
}
