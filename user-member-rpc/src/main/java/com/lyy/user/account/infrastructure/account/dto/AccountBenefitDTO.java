package com.lyy.user.account.infrastructure.account.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/1 15:39
 */
@Getter
@Setter
@ToString
public class AccountBenefitDTO {

    /**
     * 账户权益id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long id;

    /**
     * 账户权益总获得值
     */
    private BigDecimal total;

    /**
     * 账户权益余额
     */
    private BigDecimal balance;

    /**
     * 过期类型
     */
    private Integer expiryDateCategory;

    /**
     * 开始时间
     */
    private String upTime;

    /**
     * 结束时间
     */
    private String downTime;

    /**
     * 状态
     */
    private Integer status;


    private Integer classify;

    private String classifyName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private Long merchantBenefitClassifyId;

    private Long useRuleId;

    private Long storeId;

    private Integer valueType;
}
