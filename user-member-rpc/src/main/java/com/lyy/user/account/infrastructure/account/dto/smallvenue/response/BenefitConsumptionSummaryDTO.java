package com.lyy.user.account.infrastructure.account.dto.smallvenue.response;

import java.util.Date;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 消费汇总
 *
 * <AUTHOR>
 * @since 2022/3/28 - 16:59
 */
@Data
public class BenefitConsumptionSummaryDTO {

    private Long lastConsumptionAccountBenefitId;

    private Long merchantBenefitClassifyId;

    private String merchantBenefitClassifyName;

    /**
     * 初始额度
     */
    private BigDecimal originBalance;

    private BigDecimal balance;

    /**
     * 消费
     */
    private BigDecimal consumption;

    /**
     * 订单赠送额度
     */
    private BigDecimal presentation;

    private String lastConsumptionAccountBenefitDownTime;

}
