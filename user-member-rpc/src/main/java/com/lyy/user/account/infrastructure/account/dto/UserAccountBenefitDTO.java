package com.lyy.user.account.infrastructure.account.dto;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class UserAccountBenefitDTO {
    /**
     * 账号权益明细id
     */
    private Long accountBenefitId;
    /**
     * 商户id
     */
    private Long merchantId;
    /**
     * 用户id
     */
    private Long userId;
    private Integer classify;
    /**
     * 商户用户id
     */
    private Long merchantUserId;
    /**
     * 商户自定义权益id
     */
    private Long merchantBenefitClassifyId;
    /**
     * 使用规则
     */
    private Long useRuleId;
    /**
     * 过期类型
     */
    private Integer expiryDateCategory;
    /**
     * 开始时间
     */
    private String upTime;
    /**
     * 结束时间
     */
    private String downTime;
    /**
     * 总权益
     */
    private BigDecimal total;
    /**
     * 剩余权益
     */
    private BigDecimal balance;
    /**
     * 门店id
     */
    private Long storeId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
}
