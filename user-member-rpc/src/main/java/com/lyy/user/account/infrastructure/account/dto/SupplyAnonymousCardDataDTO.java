package com.lyy.user.account.infrastructure.account.dto;


import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SupplyAnonymousCardDataDTO {

    @NotNull(message = "userId不能为空")
    private Long userId;

    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    @NotNull(message = "merchantUserId不能为空")
    private Long merchantUserId;

    @NotNull(message = "cardNo不能为空")
    private String cardNo;

    private Boolean defaultFlag;

    @NotNull(message = "operatorId不能为空")
    private Long operatorId;
}
