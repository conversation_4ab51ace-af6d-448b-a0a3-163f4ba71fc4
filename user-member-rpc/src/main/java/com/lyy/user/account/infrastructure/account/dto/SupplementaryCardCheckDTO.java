package com.lyy.user.account.infrastructure.account.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SupplementaryCardCheckDTO {

    @NotNull(message = "accountId不能为空")
    private Long accountId;

    @NotNull(message = "userId不能为空")
    private Long userId;

    @NotNull(message = "merchantId不能为空")
    private Long merchantId;
}
