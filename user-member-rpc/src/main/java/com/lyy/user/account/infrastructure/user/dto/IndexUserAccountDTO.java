package com.lyy.user.account.infrastructure.user.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 主页账户信息
 * <AUTHOR>
 * @create 2021/6/7 15:46
 */
@Data
public class IndexUserAccountDTO {

    private Long merchantUserId;

    private Long userId;

    private Long merchantId;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 余币
     */
    private BigDecimal coin;

    /**
     * 剩余优惠券
     */
    private Integer couponNum;




}
