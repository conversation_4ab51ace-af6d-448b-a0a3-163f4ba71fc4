package com.lyy.user.account.infrastructure.user.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lyy.user.account.infrastructure.base.StringArrayToLongDeserializer;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SmallVenueUserListSelectDTO {

    private String name;

    private String telephone;

    private Long userId;

    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    private Date startTime;

    private Date endTime;

    private List<String> userType;

    private Long memberLevelId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 标签IDs
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> tagIds;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageIndex;

    /**
     * 页长
     */
    @Range(min = 1, max = 100, message = "页长不能超过100")
    private Integer pageSize;


}
