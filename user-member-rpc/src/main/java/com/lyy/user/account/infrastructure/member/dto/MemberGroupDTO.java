package com.lyy.user.account.infrastructure.member.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 会员组
 * <AUTHOR>
 * @className: MemberGroup
 * @date 2021/3/29
 */
@Data
public class MemberGroupDTO {

    /**
     * 主键id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long id;

    /**
     * 商户ID
     */
    @JsonSerialize(using =ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long merchantId;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 是否可用
     */
    private Boolean active;

    /**
     * 开通方式,0 自动;1 手动
     */
    
    private Short openMethod;

    /**
     * 升降策略(0 无策略,1 升级策略,2 降级策略,3 有升有降策略)
     *
     */
    private Short liftingStrategy;

    /**
     *   规则策略
     *      0、叠加  1、覆盖
     */
    private Short ruleStrategy;

    /**
     * 有效期为多少天，若为-1,则为永久有效，若为-2则按固定时间生效
     */
    private Integer memberEffectiveTime;

    /**
     * 会员有效开始时间,member_effective_time为-2时生效
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date memberStartTime;

    /**
     * 会员有效结束时间,member_effective_time为-2时生效
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date memberEndTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Long updateBy;

    /**
     * 是否已删除，true:是，false：否
     */
    private Boolean del;
}
