package com.lyy.user.account.infrastructure.account.dto.smallvenue.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 充值汇总
 *
 * <AUTHOR>
 * @since 2022/3/28 - 16:59
 */
@Data
public class BenefitRechargeSummaryDTO {

    private Long merchantBenefitClassifyId;

    private String merchantBenefitClassifyName;

    /**
     * 初始额度
     */
    private BigDecimal originBalance;

    /**
     * 充值额度
     */
    private BigDecimal rechargeBalance;


    /**
     * 频次卡初始张数
     */
    private BigDecimal originNum;

    /**
     * 频次卡充值张数
     */
    private BigDecimal rechargeNum;

    private BigDecimal balance;

    private BigDecimal num;

}
