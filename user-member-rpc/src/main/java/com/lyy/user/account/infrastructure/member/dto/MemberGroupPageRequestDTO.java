package com.lyy.user.account.infrastructure.member.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 会员组分页数据
 * <AUTHOR>
 * @className: MemberGroupPageRequestDTO
 * @date 2021/3/31
 */
@Setter
@Getter
@ToString
public class MemberGroupPageRequestDTO extends MemberGroupDTO {
    /**
     * 每页显示条数，默认 10
     */
    private long size = 10;

    /**
     * 当前页
     */
    private long current = 1;

    /**
     * 会员组的状态
     * -1:全部状态
     * 1:生效中
     * 2:已停用
     * 3:已过期
     * 4:已删除
     * 5:待生效
     */
    private Integer memberGroupStatus;

}
