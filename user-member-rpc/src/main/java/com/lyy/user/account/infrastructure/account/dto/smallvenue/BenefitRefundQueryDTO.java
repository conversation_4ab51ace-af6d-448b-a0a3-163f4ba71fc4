package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @ClassName: BenefitRefundQueryDTO
 * @description: 权益退款查询DTO
 * @author: pengkun
 * @date: 2022/01/11
 **/
@Setter
@Getter
@ToString
public class BenefitRefundQueryDTO {
    /**
     * 用户id
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    /**
     * 商户id
     */
    @NotNull(message = "商户ID不能为空")
    private Long merchantId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 子单号
     */
    private List<String> subOrderNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderTime;
}
