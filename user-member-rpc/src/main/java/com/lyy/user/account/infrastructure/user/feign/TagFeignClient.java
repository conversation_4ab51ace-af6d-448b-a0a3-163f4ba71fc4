package com.lyy.user.account.infrastructure.user.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.user.dto.tag.TagBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserNumberDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserNumberParam;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagSimpleInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagSimpleInfoParam;
import com.lyy.user.account.infrastructure.user.dto.tag.TagStatusDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUnBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserDetailDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserSaveDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingMerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingMerchantUserLinkDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingUserLinkDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.UpdateSpecificBusinessTagsParam;
import com.lyy.user.account.infrastructure.user.dto.tag.ValidList;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @ClassName: TagFeignClient
 * @description: 标签
 * @author: qgw
 * @date: 2021/04/19
 **/
@FeignClient(name = "user-member-center", fallbackFactory = TagFeignClientFallback.class)
public interface TagFeignClient {

    /**
     * 查询用户标签
     *
     * @param param 商户用户Id
     * @return
     */
    @PostMapping("/tag/user/list")
    RespBody<Page<TagUserListDTO>> list(@RequestBody TagUserQueryDTO param);

    @PostMapping("/tag/user/list-all")
    RespBody<List<TagUserListDTO>> listAllTag(@RequestBody TagUserQueryDTO param);

    /**
     * 根据用户查所属标签
     * @param
     * @return
     */
    @PostMapping("/tag/user/listTagByUser")
    RespBody<Page<TagUserListDTO>> listTagByUser(@RequestBody TagUserQueryDTO param);

    /**
     * 根据条件查标签的用户
     *
     * @param
     * @return
     */
    @PostMapping("/tag/user/findByTagId")
    RespBody<TagUserDetailDTO> findByTagId(@RequestBody TagUserQueryDTO dto);


    /**
     * 根据标签ID及商家ID获取对应用户数
     * @param dto
     * @return
     */
    @PostMapping("/tag/user/findMemberCountByTagIds")
    RespBody<List<TagCountUserNumberDTO>> findMemberCountByTagIds(@RequestBody TagCountUserNumberParam dto);

    /**
     * 根据条件查标签的用户数量
     * @param
     * @return
     */
    @PostMapping("/tag/user/countFindByTagId")
    RespBody<Long> countFindByTagId(@RequestBody TagCountUserQueryDTO dto);

    /**
     * 新增或修改用户标签
     *
     * @param dto
     * @return
     */
    @PostMapping("/tag/user/saveOrUpdateTagUser")
    RespBody<Long> saveOrUpdateTagUser(@RequestBody TagUserSaveDTO dto);

    /**
     * 变更标签状态
     *
     * @param dto
     * @return
     */
    @PostMapping("/tag/user/changeTagStatus")
    RespBody<Boolean> changeTagStatus(@RequestBody TagStatusDTO dto);

    /**
     * 标签绑定用户
     *
     * @param dto
     * @return
     */
    @PostMapping("/tag/user/bindUser")
    RespBody<Boolean> bindUser(@RequestBody TagBindUserDTO dto);


    /**
     * 标签解绑用户
     *
     * @param dto
     * @return
     */
    @PostMapping("/tag/user/unBindUser")
    RespBody<Boolean> unBindUser(@RequestBody TagUnBindUserDTO dto);


    /**
     * 修改标签名称
     *
     * @param dto
     * @return
     */
    @PostMapping("/tag/user/updateTagName")
    RespBody<Boolean> updateTagName(@RequestBody TagDTO dto);

    @PostMapping("/tag/user/name/modify")
    RespBody<Long> modifyTagName(@RequestBody TagDTO dto);


    /**
     * 打标签
     *
     * @param dto
     * @return
     */
    @PostMapping("/tag/user/taggingUser")
    RespBody<Boolean> taggingUser(@RequestBody TaggingMerchantUserDTO dto);

    /**
     * 自动打平台标签
     *
     * @param list
     * @return
     */
    @PostMapping("/tag/user/tagging/platformTag")
    RespBody<Boolean> taggingPlatformUser(@RequestBody ValidList<TaggingUserLinkDTO> list);


    /**
     * 自动打商家标签
     *
     * @param list
     * @return
     */
    @PostMapping("/tag/user/tagging/merchantTag")
    RespBody<Boolean> taggingMerchant(@RequestBody ValidList<TaggingMerchantUserLinkDTO> list);

    /**
     * 批量更新会员所属特定类型标签接口(清空旧的，只保留新的标签)
     *
     * @param dto
     * @return
     */
    @PostMapping("/tag/user/batchUpdateSpecificBusinessTagsByUserId")
    RespBody<Boolean> batchUpdateSpecificBusinessTagsByUserId(@RequestBody UpdateSpecificBusinessTagsParam dto);


    /**
     * 查询用户标签
     *
     * @param param 商户用户Id
     * @return
     */
    @PostMapping("/tag/user/tagSimpleInfoList")
    RespBody<List<TagSimpleInfoDTO>> tagSimpleInfoList(@RequestBody TagSimpleInfoParam param);

}
