package com.lyy.user.account.infrastructure.benefit.dto;

import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @description:
 * @author: qgw
 * @date on 2022/2/11.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class BenefitConsumeDetailInfoDTO {

    /**
     * 权益分类
     */
    private BenefitClassifyEnum benefitClassify;


    /**
     * 扣除权益数量或金额
     */
    private BigDecimal consume;

}
