package com.lyy.user.account.infrastructure.user.dto.tag;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @description: 用户标签明细
 * @author: qgw
 * @date on 2021/4/2.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class TagUserDetailDTO {
    /**
     * 标签ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签编码
     */
    private String code;

    /**
     * 商户ID或平台ID
     */
    private Long merchantId;

    /**
     * 标签类型，1:自动, 2:手动
     */
    private Integer category;

    /**
     * 标签的业务类型  TagBusinessTypeEnum
     */
    private Integer businessType;

    /**
     * 备注
     */
    private String description;

    /**
     * 是否可用，true:是，false：否
     */
    private Boolean active;
    /**
     * 	时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 	时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Long createdby;

    private Long updatedby;

    private List<TagUserInfoDTO>  userInfos;

    /**
     * 标签的用户数
     */
    private Long userNumber;
}
