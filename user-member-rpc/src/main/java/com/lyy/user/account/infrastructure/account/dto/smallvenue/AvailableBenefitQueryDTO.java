package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @ClassName: AvailableBenefitQueryDTO
 * @description: 用户可用权益查询DTO
 * @author: pengkun
 * @date: 2022/01/11
 **/
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class AvailableBenefitQueryDTO {

    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @NotNull(message = "商户ID不能为空")
    private Long merchantId;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 账户id
     */
    private Long accountId;

    /**
     * 商户自定义权益类型ids
     */
    private List<Long> merchantBenefitIds;

    private List<Long> accountBenefitIds;

    /**
     * 权益类型
     */
    private Integer classify;

    private Boolean ignoreEmptyBenefit;
}
