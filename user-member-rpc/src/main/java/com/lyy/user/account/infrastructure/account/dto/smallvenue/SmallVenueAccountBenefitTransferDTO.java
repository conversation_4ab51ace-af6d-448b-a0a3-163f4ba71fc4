package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 娱乐会员储值转移到多金宝会员下DTO
 *
 * <AUTHOR> =￣ω￣=
 * @date 2023/9/5
 */
@Data
public class SmallVenueAccountBenefitTransferDTO {

    /**
     * account_benefit_id
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String name;

    /**
     * 储值数量
     */
    private BigDecimal balance;

    /**
     * 储值类型
     */
    private Integer classify;
}
