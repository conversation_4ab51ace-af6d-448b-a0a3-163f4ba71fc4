package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import java.math.BigDecimal;

import com.lyy.user.account.infrastructure.constant.AccountConsumerTypeEnum;
import com.lyy.user.account.infrastructure.constant.AccountOperationChannelEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/1/13
 */
@Data
@NoArgsConstructor
public class AccountRecordInfoDTO {
    /**
     * recordType
     * @see com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum  group 14
     */
    private Integer recordType;

    /**
     * 门店名称
     */
//    @NotBlank(message = "门店名称不能为空")
    private String storeName;

    /**
     * 操作渠道 {@link AccountOperationChannelEnum}
     */
    private Integer operationChannel;

    /**
     * 操作渠道 {@link AccountConsumerTypeEnum}
     */
    private Integer consumerType;
    /**
     * 操作设备名称
     */
    private String operationEquipmentName;

    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 终端编号
     */
    private String equipmentValue;
    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 消费详情
     */
    private String description;

    private Long goodsId;

    private String commodityName;

    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 商品所属分类
     */
    private Long goodsClassify;

    /**
     * 商品所属分离名称
     */
    private String goodsClassifyName;

    /**
     * 商品数量
     */
    private BigDecimal goodsNum;
}
