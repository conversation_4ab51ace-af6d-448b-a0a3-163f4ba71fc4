package com.lyy.user.account.infrastructure.account.dto;

import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class BenefitQueryDTO {

    @NotNull(message = "商户ID不能为空")
    private Long merchantId;

    private Long userId;

    @NotEmpty(message = "账户权益ID不能为空")
    private List<Long> accountBenefitIds;
}
