package com.lyy.user.account.infrastructure.account.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 类描述：账户流水对象
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/1 15:52
 */
@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccountRecordDTO {

    /**
     * 流水号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long id;

    /**
     * 账户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long accountId;

    /**
     * 	平台用户ID
     */
    private Long userId;

    /**
     * 	商户用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long merchantUserId;

    /**
     * 	商户
     */
    private Long merchantId;

    /**
     * 	店铺
     */
    private Long storeId;

    /**
     * 	设备
     */
    private Long equipmentId;

    /**
     * 	权益类型
     */
    private Integer benefitClassify;

    /**
     * 	权益ID
     */
    private Long benefitId;

    /**
     * 	期初权益
     */
    private BigDecimal initialBenefit;

    /**
     * 	实际权益
     */
    private BigDecimal actualBenefit;

    /**
     * 	交易单号
     */
    private String outTradeNo;

    /**
     * 	业务单号
     */
    private String orderNo;

    /**
     * 	方式1=加，2=减
     */
    private Integer mode;

    /**
     * 	时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 	来源
     */
    private String resource;

    /**
     * 	备注
     */
    private String description;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;

    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 设备类型名称
     */
    private String equipmentTypeName;

    /**
     * 设备编号
     */
    private String equipmentValue;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 记录类型
     * @see com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum
     */
    @NotNull(message = "记录类型不能为空")
    private Integer recordType;

}
