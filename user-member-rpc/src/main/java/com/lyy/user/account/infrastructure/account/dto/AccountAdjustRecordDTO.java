package com.lyy.user.account.infrastructure.account.dto;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: qgw
 * @date on 2021-06-03.
 * @Version: 1.0
 */
@Slf4j
@Setter
@Getter
@ToString
public class AccountAdjustRecordDTO {

    /**
     * 操作类型
     */
    private Integer benefitClassify;

    private Integer mode;
    /**
     * 会员昵称
     */
    private String userNick;
    /**
     * 会员id
     */
    private Long userId;
    /**
     * 会员手机号
     */
    private String userPhone;

    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 创建人
     */
    private Date createdby;

    private Date createName;
    /**
     * 创建电话
     */
    private Date createPhone;

    /**
     * 调整余额数
     */
    private BigDecimal ammout;




}
