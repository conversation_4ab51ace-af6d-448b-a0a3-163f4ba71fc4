package com.lyy.user.account.infrastructure.account.dto;

import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 类描述：账户查询条件
 * <p>
 *
 * <AUTHOR>
 * @since 2021/3/31 11:44
 */
@Getter
@Setter
@ToString
public class AccountConditionDTO {

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    private Long userId;

    /**
     * 商户id
     */
    @NotNull(message = "商户id不能为空")
    private Long merchantId;

    /**
     * 权益类型
     *    建议使用批量查询
     */
    @Deprecated
    private Integer classify;
    /**
     * 权益类型
     */
    private List<Integer> classifies;


    /**
     * 不查的权益类型
     */
    private List<Integer> excludeClassify;

    /**
     * 商户用户id
     */
    private Long merchantUserId;

    private boolean excludeExpired;

}
