package com.lyy.user.account.infrastructure.base;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.cglib.proxy.Proxy;

import java.lang.reflect.ParameterizedType;

/**
 * <AUTHOR>
 * @className: BaseFeignFallback
 * @date 2021/4/19
 */

@Slf4j
public abstract class BaseFeignFallback<T> implements FallbackFactory<T> {
    /**
     * 获取对应的对象类型，用于生成动态代理对象
     * @param <T>
     * @return
     */
    public <T> Class<T> getClazz(){
        Class<T> tClass = (Class<T>)((ParameterizedType)getClass().getGenericSuperclass()).getActualTypeArguments()[0];
        return tClass;
    }
    protected volatile T proxy;

    public T getProxy(){
        if(proxy == null){
            synchronized (this){
                if(proxy == null){
                    Class<T> clazz = getClazz();
                    Class[] interfaces;
                    if(clazz.isInterface()){
                        interfaces = new Class[]{clazz};
                    }else{
                        interfaces = clazz.getInterfaces();
                    }
                    try{
                        proxy = (T)Proxy.newProxyInstance(clazz.getClassLoader(),interfaces,new BaseFeignFallbackProxy());
                    }catch (Exception e){
                        log.error(e.getMessage(),e);
                        log.warn("{} 类采用spring的cglib生成动态代理失败，重新采用java 原生的动态代理处理",clazz.getName());
                        proxy = (T) java.lang.reflect.Proxy.newProxyInstance(clazz.getClassLoader(),interfaces,new BaseFeignFallbackProxy());
                    }
                    log.info("{} 类使用动态代理生成具体实现",clazz.getName());
                }
            }
        }
        return proxy;
    }


    @Override
    public T create(Throwable throwable) {
        if (throwable != null && StringUtils.isNotEmpty(throwable.getMessage())) {
            log.error(throwable.getMessage(), throwable);
        }
        return getProxy();
    }

}
