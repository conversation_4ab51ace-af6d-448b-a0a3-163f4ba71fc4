package com.lyy.user.account.infrastructure.user.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.user.dto.tag.TagBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserNumberDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserNumberParam;
import com.lyy.user.account.infrastructure.user.dto.tag.TagCountUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagSimpleInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagSimpleInfoParam;
import com.lyy.user.account.infrastructure.user.dto.tag.TagStatusDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUnBindUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserDetailDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserListDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserQueryDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserSaveDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingMerchantUserDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingMerchantUserLinkDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingUserLinkDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.UpdateSpecificBusinessTagsParam;
import com.lyy.user.account.infrastructure.user.dto.tag.ValidList;
import feign.hystrix.FallbackFactory;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @ClassName: TagFeignClientFallback
 * @description: 标签
 * @author: qgw
 * @date: 2021/04/19
 **/
@Slf4j
@Component
public class TagFeignClientFallback implements FallbackFactory<TagFeignClient> {
    @Override
    public TagFeignClient create(Throwable throwable) {

        if (throwable != null && StringUtils.isNotEmpty(throwable.getMessage())) {
            log.error(throwable.getMessage(), throwable);
        }

        return new TagFeignClient() {

            @Override
            public RespBody<Page<TagUserListDTO>> list(TagUserQueryDTO param) {
                log.error("[新会员] 查询用户标签, list熔断,param:{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<TagUserListDTO>> listAllTag(TagUserQueryDTO param) {
                log.error("[新会员] 查询用户的所有标签, listAllTag熔断,param:{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Page<TagUserListDTO>> listTagByUser(TagUserQueryDTO dto) {
                log.error("[新会员] 根据用户查所属标签, listTagByUser 熔断,dto:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }


            @Override
            public RespBody<TagUserDetailDTO> findByTagId(TagUserQueryDTO dto) {
                log.error("[新会员] 根据条件查标签的用户, findByTagId 熔断,dto:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<TagCountUserNumberDTO>> findMemberCountByTagIds(TagCountUserNumberParam dto) {
                log.error("[新会员] 根据标签ID及商家ID获取对应用户数, findMemberCountByTagIds 熔断,dto:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Long> countFindByTagId(TagCountUserQueryDTO dto) {
                log.error("[新会员] 根据条件查标签的用户数量, countFindByTagId 熔断,dto:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Long> saveOrUpdateTagUser(TagUserSaveDTO dto) {
                log.error("[新会员] 新增或修改用户标签, saveOrUpdateTagUser 熔断,dto:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> changeTagStatus(TagStatusDTO dto) {
                log.error("[新会员] 变更标签状态, changeTagStatus 熔断,dto:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> bindUser(TagBindUserDTO dto) {
                log.error("[新会员] 标签绑定用户, bindUser 熔断,dto:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> unBindUser(TagUnBindUserDTO dto) {
                log.error("[新会员] 标签解绑用户, unBindUser 熔断,dto:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> updateTagName(TagDTO dto) {
                log.error("[新会员] 修改标签名称, updateTagName 熔断,dto:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Long> modifyTagName(TagDTO dto) {
                log.error("[新会员] 修改标签名称, modifyTagName 熔断,dto:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> taggingUser(TaggingMerchantUserDTO dto) {
                log.error("[新会员] 打标签, taggingUser 熔断,dto:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> taggingPlatformUser(ValidList<TaggingUserLinkDTO> list) {
                log.error("[新会员] 自动打平台标签, taggingPlatformUser 熔断,list:{}", list);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> taggingMerchant(ValidList<TaggingMerchantUserLinkDTO> list) {
                log.error("[新会员] 自动打商家标签, taggingMerchant 熔断,list:{}", list);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 批量更新会员所属特定类型标签接口
             *
             * @param dto
             * @return
             */
            @Override
            public RespBody<Boolean> batchUpdateSpecificBusinessTagsByUserId(UpdateSpecificBusinessTagsParam dto) {
                log.error("[新会员] 批量更新会员所属特定类型标签接口, batchUpdateSpecificBusinessTagsByUserId 熔断,dto:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<TagSimpleInfoDTO>> tagSimpleInfoList(TagSimpleInfoParam param) {
                log.error("[新会员] tagSimpleInfoList接口熔断,param:{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }
        };
    }
}
