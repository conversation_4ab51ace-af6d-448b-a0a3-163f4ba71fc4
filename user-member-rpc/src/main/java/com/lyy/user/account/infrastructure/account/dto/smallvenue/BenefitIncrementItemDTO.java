package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lyy.user.account.infrastructure.constant.AccountBenefitNumTypeEnum;
import com.lyy.user.account.infrastructure.constant.CardExpiredTypeEnum;
import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/17
 */
@Data
public class BenefitIncrementItemDTO {

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 押金
     */
    private BigDecimal deposit;

    /**
     * 是否作为默认会员卡true是false不是
     */
    private Boolean defaultFlag;

    /**
     * @see CardExpiredTypeEnum
     */
    private Integer expiredType;

    @JsonFormat( pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date downTime;

    /**
     * 类型：Array必有字段备注：权益明细
     */
    @Valid
    private List<BenefitDetailsDTO> benefitDetails;

    /**
     * 商家权益类型通用门店map key:商家权益类型 value:此权益类型与本门店通用门店list
     */
    private Map<Long, List<Long>> merchantBenefitClassifyScopeMap;


    /**
     * 类型：Array必有字段备注：权益明细Item
     */
    @NoArgsConstructor
    @Data
    public static class BenefitDetailsDTO {

        /**
         * 商户自定义权益类型Id
         */
        @NotNull(message = "商户权益类型ID不能为空")
        private Long merchantBenefitClassifyId;
        /**
         * 商户自定义权益类型名称
         */
        private String merchantBenefitClassifyName;
        /**
         * 数量
         */
        @NotNull(message = "权益数值不能为空")
        private BigDecimal num;

        /**
         * 权益值类型
         *
         * @see AccountBenefitNumTypeEnum
         */
        @NotNull(message = "权益值类型不能为空")
        private Integer numType;

        /**
         * 权益实际价值
         */
        private BigDecimal actualValue;

        /**
         * 生效时间
         */
        private String upTime;
        /**
         * 过期时间
         */
        private String downTime;
        /**
         * 有效期类型:0、无限期,1、日期区间可用,2、单日时间区间可用
         */
        private Integer expiryDateCategory;

        /**
         * 权益使用规则
         */
        private Long useRuleId;

        /**
         * 子单号
         */
        private String subOrderNo;

        /**
         * 账号权益明细ID（用于权益回退）
         */
        private Long accountBenefitId;

        /**
         * 操作详情
         */
        private String description;
    }
}
