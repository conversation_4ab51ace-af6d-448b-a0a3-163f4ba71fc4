package com.lyy.user.account.infrastructure.account.dto;

import com.lyy.user.account.infrastructure.constant.AccountBenefitResourceEnum;
import com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 类描述：账户创建对象
 * <p>
 *
 * <AUTHOR>
 * @since 2021/3/31 09:53
 */
@Getter
@Setter
@ToString
public class AccountCreateDTO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 商户用户id
     */
    private Long merchantUserId;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 设备id
     */
    private Long equipmentId;

    /**
     * 支付单号
     */
    private String outTradeNo;

    /**
     * 业务单号
     */
    private String orderNo;

    /**
     * 权益类型
     */
    private Integer classify;

    /**
     * 描述
     */
    private String description;

    /**
     * 变更来源
     */
    private String resource;

    /**
     * 账户记录类型
     */
    private AccountRecordTypeEnum accountRecordTypeEnum;

    /**
     * 权益来源类型
     * @see AccountBenefitResourceEnum
     */
    private Integer benefitResourceType;

    /**
     * 权益来源id
     */
    private Long benefitResourceId;


    /**
     * 账户权益列表
     */
    private List<AccountBenefitCreateDTO> accountBenefit;

    /**
     * 操作者
     */
    private Long operator;
}
