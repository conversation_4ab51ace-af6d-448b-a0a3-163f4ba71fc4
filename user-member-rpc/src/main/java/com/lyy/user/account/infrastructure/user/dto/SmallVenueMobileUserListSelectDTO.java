package com.lyy.user.account.infrastructure.user.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lyy.user.account.infrastructure.base.StringArrayToLongDeserializer;
import com.lyy.user.account.infrastructure.constant.AccountBenefitNumTypeEnum;
import com.lyy.user.account.infrastructure.constant.UserMemberSysConstants;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SmallVenueMobileUserListSelectDTO {


    /**
     * 搜索关键字  支持微信昵称，会员ID,手机号码
     * 会员卡
     */
    private String keyword;

    /**
     * 搜索关键字类型
     * @see UserMemberSysConstants
     */
    private Integer keywordType;


    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    private Long userId;

    /**
     * 场地标签集合
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> groupTagIdList;

    /**
     * 普通标签
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> otherTagIdList;
    /**
     * 设备类型标签
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> equipmentTypeTagIdList;

    /**
     * 性别标签ID
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> sexTagIdList;

    /**
     * 特定过滤条件(  累计消费 、会员等级、会员来源、会员卡数量 todo )
     */
    private SpecificFilterInfoDTO specificFilterInfoDTO;

    /**
     * 商家自定义权益
     *
     */
    private List<MerchantBenefitClassifyDTO> merchantBenefitClassifyDTOList;

    /**
     * 排序字段信息
     * 1.按最近消费时间排序
     * 2.按消费金额排序
     * 3.按注册时间排序
     * 4.最近活跃时间  todo
     */
    private SortInfoDTO sortInfoDTO;


    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageIndex;

    /**
     * 页长
     */
    @Range(min = 1, max = 100, message = "页长不能超过100")
    @NotNull(message = "页长不能为空")
    private Integer pageSize;


    /**
     * 特定过滤条件(  累计消费 、会员等级、会员来源、会员卡 )
     */
    @NoArgsConstructor
    @Data
    public static class SpecificFilterInfoDTO {
        /**
         * 累计消费最小值
         */
        private Integer totalConsumeMoneyMin;

        /**
         * 累计消费最大值
         */
        private Integer totalConsumeMoneyMax;


        /**
         * 会员等级id
         */
        private List<Long> memberLevelIdList;

        /**
         * 会员来源
         */
        private List<String> userTypeList;

    }

    /**
     * 类型：商家自定义类型
     */
    @NoArgsConstructor
    @Data
    public static class MerchantBenefitClassifyDTO {

        /**
         * 商户自定义权益类型Id
         */
        @NotNull(message = "商户权益类型ID不能为空")
        private Long merchantBenefitClassifyId;

        private BigDecimal amountMin;

        private BigDecimal amountMax;

    }

    /**
     * 排序字段信息
     * 1.按最近消费时间排序
     * 2.按消费金额排序
     * 3.按注册时间排序
     * 4.最近活跃时间  todo
     */
    @NoArgsConstructor
    @Data
    public static class SortInfoDTO {
        /**
         * 按最近消费时间排序
         */
        private String lastConsumeTimeSort;


        /**
         * 按消费金额排序（此处指实付消费金额，包括充值金额和支付启动的金额）
         */
        private String totalConsumeMoneySort;

        /**
         * 按注册时间排序
         */
        private String registerTimeSort;


    }


}
