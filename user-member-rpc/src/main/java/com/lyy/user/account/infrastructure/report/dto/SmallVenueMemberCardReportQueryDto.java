package com.lyy.user.account.infrastructure.report.dto;

import java.util.List;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * <AUTHOR>
 * @Date 2022 12 19 15 25
 **/
@Data
public class SmallVenueMemberCardReportQueryDto {

    /**
     * 页码
     */
    @Min(value = 1,message = "页码必须大于0")
    private Integer pageIndex;

    /**
     * 页长
     */
    @Range(min = 1,max = 1000,message = "页长不能超过1000")
    private Integer pageSize;

    @NotNull(message = "商户id不能为空")
    private Long merchantId;

    private Long accountId;

    private String cardNo;

    private String name;

    private Long merchantUserId;

    private Long userId;

    private String phone;

    /**
     * 标签名称数组
     */
    private List<Long> userTagIdList;

    /**
     * 门店标签名称
     */
    @NotEmpty(message = "门店标签名称数组不能为空")
    private List<String> storeName;

    @NotEmpty(message = "储值通用门店id数组不能为空" )
    private List<Long> storeValueCurrencyStoreIds;


}
