package com.lyy.user.account.infrastructure.benefit.feign;

import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.benefit.dto.BenefitInfoDTO;
import com.lyy.user.account.infrastructure.benefit.dto.GeneralGroupBenefitSaveDTO;
import com.lyy.user.account.infrastructure.benefit.dto.GroupBenefitMergeDTO;
import com.lyy.user.account.infrastructure.benefit.vo.GeneralGroupBenefitSaveVO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import feign.hystrix.FallbackFactory;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2021/4/27 11:47
 */
@Slf4j
@Component
public class BenefitFeignFallback  implements FallbackFactory<BenefitFeignClient> {

    @Override
    public BenefitFeignClient create(Throwable throwable) {
        if (throwable != null && StringUtils.isNotEmpty(throwable.getMessage())) {
            log.error(throwable.getMessage(), throwable);
        }

        return new BenefitFeignClient() {

            @Override
            public RespBody<BenefitInfoDTO> getMerchantBenefit(Long merchantId, Integer benefitClassifyCode, Integer applicable, Long associatedId) {
                log.error("[新会员] getMerchantBenefit 熔断,merchantId:{},benefitClassifyCode:{},applicable:{},associatedId:{}", merchantId, benefitClassifyCode, applicable, associatedId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<BenefitInfoDTO> getBenefitById(Long id, Long merchantId) {
                log.error("[新会员] getBenefitById 熔断,id:{},merchantId:{}", id, merchantId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<GeneralGroupBenefitSaveVO>> saveGeneralGroupBenefit(GeneralGroupBenefitSaveDTO generalGroupBenefitSaveDTO) {
                log.error("[新会员] saveGeneralGroupBenefit 熔断,{}", generalGroupBenefitSaveDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody saveGroupBenefit(GeneralGroupBenefitSaveDTO generalGroupBenefitSaveDTO) {
                log.error("[新会员] saveGroupBenefit 熔断,{}", generalGroupBenefitSaveDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 2.0开启场地通用更新权益使用范围
             *
             * @param generalGroupBenefitSaveDTO
             * @return
             */
            @Override
            public RespBody<Boolean> updateSaaSBenefit(GeneralGroupBenefitSaveDTO generalGroupBenefitSaveDTO) {
                log.error("[新会员] 2.0开启场地通用更新权益使用范围, updateSaaSBenefit 熔断,{}", generalGroupBenefitSaveDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> groupBenefitMerge(GroupBenefitMergeDTO groupBenefitMergeDTO) {
                log.error("[新会员] 场地通用权益合并, groupBenefitMerge 熔断,{}", groupBenefitMergeDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> groupBenefitMergeWithNewGroupList(GroupBenefitMergeDTO groupBenefitMergeDTO) {
                log.error("[新会员] 新场地通用权益合并, groupBenefitMergeWithNewGroupList 熔断,{}", groupBenefitMergeDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }
        };
    }
}
