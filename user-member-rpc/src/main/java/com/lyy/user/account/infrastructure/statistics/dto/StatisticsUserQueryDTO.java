package com.lyy.user.account.infrastructure.statistics.dto;

import com.lyy.user.account.infrastructure.constant.TimeScopeEnum;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserConditionDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2021/10/23 15:25
 */
@Data
public class StatisticsUserQueryDTO {


    /**
     * 时间范围
     */
    @NotNull
    private TimeScopeEnum timeScope;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 手机号码
     */
    private String telephone;

    /**
     * 累计充值排序
     */
    private String totalRechargeMoneySort;

    /**
     * 累计启动支付 排序
     */
    private String totalPayMoneySort;

    /**
     * 累计余额支付 排序
     */
    private String totalConsumeBalanceSort;

    /**
     * 累计消费余币 排序
     */
    private String totalConsumeCoinSort;

    /**
     * 页码
     */
    @Min(value = 1,message = "页码必须大于0")
    private Integer pageIndex;

    /**
     * 页长
     */
    @Range(min = 1,max = 50,message = "页长不能超过50")
    private Integer pageSize;

    private Boolean countSql;

}
