package com.lyy.user.account.infrastructure.account.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class BenefitWithScopeDTO {

    /**
     * 账户权益id
     */
    private Long accountBenefitId;

    /**
     * 权益id
     */
    private Long benefitId;

    /**
     * 总数
     */
    private BigDecimal total;

    /**
     * 权益数量
     */
    private BigDecimal amount;

    /**
     * 权益类型
     */
    private Integer classify;

    /**
     * 有效期类型:0、无限期,1、日期区间可用,2、单日时间区间可用
     */
    private Integer expiryDateCategory;

    /**
     * 生效时间
     */
    private String upTime;

    /**
     * 失效时间
     */
    private String downTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 消耗范围
     */
    private List<BenefitScopeDTO> scope;

    /**
     * 记录状态
     */
    private Integer status;
}
