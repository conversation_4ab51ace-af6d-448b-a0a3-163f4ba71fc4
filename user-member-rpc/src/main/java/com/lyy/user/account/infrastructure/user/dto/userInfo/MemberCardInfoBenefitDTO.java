package com.lyy.user.account.infrastructure.user.dto.userInfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * 会员卡权益信息
 */
@Data
@Accessors(chain = true)
public class MemberCardInfoBenefitDTO {

    /**
     * 自定义权益id
     */
    private Long merchantBenefitClassifyId;

    private BigDecimal balance;

    private Integer num;

    private Long useRuleId;

    private Long storeId;

    /**
     * 生效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date upTime;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date downTime;
}
