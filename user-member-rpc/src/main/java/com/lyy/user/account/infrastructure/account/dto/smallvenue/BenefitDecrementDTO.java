package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import com.lyy.user.account.infrastructure.constant.AccountBenefitNumTypeEnum;
import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.Negative;
import javax.validation.constraints.NegativeOrZero;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/8
 */
@NoArgsConstructor
@Data
public class BenefitDecrementDTO {

    /**
     * 用户Id
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    /**
     * 商户Id
     */
    @NotNull(message = "商户ID不能为空")
    private Long merchantId;
    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 是否允许扣除至负数（true: 允许，false: 不允许, 默认 false）
     */
    private boolean negative;

    /**
     * 权益消耗明细
     */
    @Valid
    @NotEmpty(message = "权益明细不能为空")
    private List<BenefitDetailsDTO> benefitDetails;
    /**
     * 操作人Id
     */
    private Long operatorId;

    /**
     * 单号
     */
    private String orderNo;

    /**
     * 退单单号
     */
    private String refundNo;

    /**
     * 门店Id
     */
    @NotNull(message = "门店ID不能为空")
    private Long storeId;

    /**
     * 流水信息
     */
    @Valid
    private AccountRecordInfoDTO record;

    /**
     * 商家权益类型通用门店map key:商家权益类型 value:此权益类型与本门店通用门店list
     */
    private Map<Long, List<Long>> merchantBenefitClassifyScopeMap;

    /**
     * 权益消耗明细Item
     */
    @NoArgsConstructor
    @Data
    public static class BenefitDetailsDTO {

        /**
         * 账号权益明细ID
         */
        @NotNull(message = "账号权益明细ID不能为空")
        private Long accountBenefitId;

        /**
         * 商家权益类型Id
         */
        private Long merchantBenefitClassifyId;

        private String merchantBenefitClassifyName;

        /**
         * 消耗数量
         */
        @NegativeOrZero(message = "权益数值不能为正数")
        @NotNull(message = "权益数值不能为空")
        private BigDecimal num;

        /**
         * 权益值类型
         *
         * @see AccountBenefitNumTypeEnum
         */
        @NotNull(message = "权益值类型不能为空")
        private Integer numType;

        /**
         * 操作详情
         */
        private String description;

        /**
         * 权益实际价值
         */
        private BigDecimal actualValue;

    }

    /**
     * 校验相关：不同的属性对应不同的校验逻辑
     */
    private BenefitPredicate predicate;
}
