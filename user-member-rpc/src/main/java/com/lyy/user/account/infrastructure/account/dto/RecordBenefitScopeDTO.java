package com.lyy.user.account.infrastructure.account.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @ClassName: BenefitScopeDTO
 * @description: 权益对应使用范围限制
 * @author: pengkun
 * @date: 2022/04/29
 **/
@Setter
@Getter
@ToString
public class RecordBenefitScopeDTO {

    /**
     * 权益类型
     */
    private Integer classify;

    /**
     * 关联的id
     */
    private List<Long>  associatedIds;

}
