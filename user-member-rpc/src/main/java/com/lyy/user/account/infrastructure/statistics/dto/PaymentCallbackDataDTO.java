package com.lyy.user.account.infrastructure.statistics.dto;

import com.lyy.user.account.infrastructure.member.dto.MemberTouchRuleDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TaggingMerchantUserDTO;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/7/11 - 16:43
 */
@Data
public class PaymentCallbackDataDTO {

    private String outTradeNo;
    private TaggingMerchantUserDTO tagUser;
    private List<UserStatisticsUpdateDTO> statistics;
    private MemberTouchRuleDTO memberTouchRule;
}
