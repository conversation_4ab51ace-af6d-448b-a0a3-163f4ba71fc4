package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 移动端账户流水记录列表查询
 */
@Data
@Accessors(chain = true)
public class MobileTerminalRecordSelectDTO {

    /**
     * 商户id
     */
    @NotNull(message = "merchantId不能为空")
    private Long merchantId;


    /**
     * 会员id
     */
    private Long merchantUserId;

    /**
     * 门店id
     */
    private List<Long> storeIds;

    /**
     * 商品类型id
     */
    private List<Long> goodsTypeIds;


    /**
     * 商品分类
     */
    private Long goodsClassify;


    /**
     * 操作渠道
     */
    private List<Integer> operationChannels;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 关键字搜索，支持订单号和商品名称
     */
    private String keyword;

    /**
     * 操作记录类型
     */
    @NotNull(message = "operationType不能为空")
    private Integer operationType;

    /**
     * 记录类型
     */
    private List<Integer> recordType;


    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 权益分类id集合
     */
    private List<Long> merchantBenefitClassifyIds;


    @NotNull(message = "pageIndex不能为空")
    private Integer pageIndex;

    @NotNull(message = "pageSize不能为空")
    private Integer pageSize;


    /**
     * 需要过滤掉的recordType
     */
    private List<Integer> excludeRecordTypes;

    private Boolean countSql;

}
