package com.lyy.user.account.infrastructure.member.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 会员组分页信息的响应内容
 * <AUTHOR>
 * @className: MemberGroupResponseDTO
 * @date 2021/4/9
 */
@Data
public class MemberGroupResponseDTO extends MemberGroupDTO {
    /**
     * 字符串格式的id
     */
    private String idStr;

    public String getIdStr() {
        return Optional.ofNullable(getId()).map(id->id.toString()).orElse("");
    }

    /**
     * 会员数量
     */
    private Integer memberCount;

    /**
     * 会员等级列表信息
     */
    private List<MemberGroupLevelDTO> memberLevelList;

    /**
     * 会员组范围
     */
    private List<MemberRangeAssociatedDTO> memberRangeAssociatedList;

    /**
     * 会员组范围map信息，key表示适用类型(1 品类,2 场地,3 设备,4 商品)，value 表示具体的内容
     */
    private Map<Short,List<MemberGroupRangeResponseDTO>> memberGroupRangeMap;

    /**
     * 会员组保存信息，包括使用范围
    MemberGroupSaveDTO memberGroupSaveDTO;

    /**
     * 会员等级列表信息,包括用户规则信息
    List<MemberLevelSaveDTO> memberLevelList;

    /**
     * 会员升级策略信息，包括策略规则
    List<MemberLiftingSaveDTO> memberLiftingList;
     */



}
