package com.lyy.user.account.infrastructure.user.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName: MerchantUserInfoQueryDTO
 * @description: 商户用户
 * @author: qgw
 * @date: 2021/03/31
 **/
@Setter
@Getter
@ToString(callSuper = true)
public class MerchantUserInfoQueryDTO extends PageQueryDTO {
    /**
     * 标签Id
     */
    @NotNull(message = "标签Id不能为空")
    private List<Long> tagIds;

    /**
     * 商户Id
     */
    @NotNull(message = "商户ID必须指定")
    private Long merchantId;

}
