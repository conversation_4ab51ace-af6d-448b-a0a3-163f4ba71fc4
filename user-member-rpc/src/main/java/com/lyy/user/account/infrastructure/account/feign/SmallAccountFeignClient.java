package com.lyy.user.account.infrastructure.account.feign;

import com.lyy.user.account.infrastructure.account.dto.SmallVenueMergeAccountInfoDTO;
import com.lyy.user.account.infrastructure.account.dto.request.AccountBenefitUpdateDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.*;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.request.AccountDefaultStatusUpdateDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.request.BenefitConsumptionSummaryQueryDTO;
import com.lyy.user.account.infrastructure.account.dto.smallvenue.response.AccountRecordBenefitSummaryDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/11
 */
@FeignClient(name = "user-member-center", fallbackFactory = SmallAccountFeignFallback.class)
public interface SmallAccountFeignClient {

    /**
     * 增加权益
     *
     * @param dto 增加权益DTO
     * @return boolean
     */
    @PostMapping("/account/smallVenue/benefit/increment")
    RespBody<Boolean> increaseAccountBenefit(@RequestBody @Validated BenefitIncrementDTO dto);

    /**
     * 批量用户增加权益
     *
     * @param dto 增加权益DTO
     * @return boolean
     */
    @PostMapping("/account/smallVenue/benefit/increment/batch")
    RespBody<Boolean> increaseAccountBenefitBatch(@RequestBody @Validated BenefitIncrementBatchDTO dto);

    /**
     * 减少权益
     *
     * @param dto 减少权益DTO
     * @return boolean
     */
    @PostMapping("/account/smallVenue/benefit/decrement")
    RespBody<Boolean> decreaseAccountBenefit(@RequestBody @Validated BenefitDecrementDTO dto);


    /**
     * 转账
     *
     * @param dto 会员卡转账DTO
     * @return boolean
     */
    @PostMapping("/account/smallVenue/transfer")
    RespBody<Boolean> transfer(@RequestBody @Validated CardTransferDTO dto);

    /**
     * 注销账户
     *
     * @param dto 注销账户DTO
     * @return boolean
     */
    @PostMapping("/account/smallVenue/cancellation")
    RespBody<Boolean> cancellation(@RequestBody @Validated AccountCancellationDTO dto);

    /**
     * 获取会员卡账户下的权益明细
     */
    @GetMapping("/account/smallVenue/benefits/list")
    RespBody<List<UserAccountBenefit>> listBenefit(@RequestParam("merchantId") Long merchantId,
                                                   @RequestParam("cardNo") String cardNo,
                                                   @RequestParam(value = "status", required = false) Integer status);

    /**
     * 查询用户可用的权益数据(支付前 可用权益接口 )
     *
     * @param queryDTO 可以权益查询DTO
     * @return
     */
    @PostMapping("/account/smallVenue/findUserAvailableBenefit")
    RespBody<List<SmallVenueUserAvailableBenefitDTO>> findUserAvailableBenefit(@RequestBody AvailableBenefitQueryDTO queryDTO);

    /**
     * 根据单号获取消费记录和消费记录中对应的权益明细
     *
     * @param benefitRefundDTO 退权益参数
     * @return
     */
    @PostMapping("/account/smallVenue/findAccountRecordsAndAccountBenefits")
    RespBody<BenefitRefundResultDTO> findAccountRecordsAndAccountBenefits(@RequestBody BenefitRefundQueryDTO benefitRefundDTO);

    /**
     * 当前订单的权益消费汇总信息
     *
     * @param query
     * @return
     */
    @PostMapping("/account/smallVenue/record/benefits/consumption/summary")
    RespBody<AccountRecordBenefitSummaryDTO> benefitsConsumptionSummary(@RequestBody BenefitConsumptionSummaryQueryDTO query);

    @PostMapping("/account/smallVenue/default/adjustment")
    RespBody<Boolean> adjustDefaultAccount(@RequestBody @Validated AccountDefaultStatusUpdateDTO dto);

    /**
     * 商家批量增加权益
     * @param dto
     * @return
     */
    @PostMapping("/account/smallVenue/benefit/merchant/increment")
    RespBody<Boolean> merchantIncrementAccountBenefit(@RequestBody @Validated MerchantBenefitIncrementDTO dto);


    /**
     * 初始化多金宝账户
     *
     * @param merchantId     商户id
     * @param storeId        场地id
     * @param userId         用户id
     * @param merchantUserId 商户用户id
     * @return
     */
    @GetMapping("/account/smallVenue/initDefaultAccount")
    RespBody<Long> initDefaultAccount(@RequestParam("merchantId") Long merchantId,
                                      @RequestParam("storeId") Long storeId,
                                      @RequestParam(value = "userId", required = false) Long userId,
                                      @RequestParam(value = "merchantUserId", required = false) Long merchantUserId);

    /**
     * 多金宝会员账户合并
     *
     * @param smallVenueMergeAccountInfoDTO
     * @return
     */
    @PostMapping("/account/smallVenue/merge/userMember")
    RespBody<Boolean> mergeUserMemberAccount(@RequestBody SmallVenueMergeAccountInfoDTO smallVenueMergeAccountInfoDTO);

    /**
     * 查询娱乐会员在场地下的储值
     *
     * @param smallVenueAccountBenefitTransferQueryDTO
     * @return
     */
    @PostMapping("/account/smallVenue/selectEnbleTransferAccountBenefit")
    RespBody<List<SmallVenueAccountBenefitTransferDTO>> selectTransferAccountBenefit(@RequestBody SmallVenueAccountBenefitTransferQueryDTO smallVenueAccountBenefitTransferQueryDTO);

    /**
     * 多金宝清除娱乐余额余币
     *
     * @param smallVenueAccountBenefitTransferQueryDTO
     * @return
     */
    @PostMapping("/account/smallVenue/smallVenueClearBalanceAndCoin")
    RespBody<Void> smallVenueClearBalanceAndCoin(@RequestBody SmallVenueAccountBenefitTransferQueryDTO smallVenueAccountBenefitTransferQueryDTO);

    /**
     * 初始化多金宝会员账户
     *
     * @param accountInitDTO
     * @return
     */
    @PostMapping("/account/smallVenue/initAccountIfAbsent")
    RespBody<SmallVenueAccount> initAccountIfAbsent(@RequestBody SmallVenueAccountInitDTO accountInitDTO);

    /**
     * 更新多金宝会员账户
     *
     * @param account
     * @return
     */
    @PostMapping("/account/smallVenue/updateVenueAccount")
    RespBody<Void> updateVenueAccount(@RequestBody SmallVenueAccount account);

    /**
     * 更新批量权益过期时间
     *
     * @param accountBenefitUpdateDTOList 待更新的权益列表dtolist
     * @return {@link RespBody}<{@link Boolean}>
     */
    @PostMapping("/account/smallVenue/benefit/expired/setup")
    public RespBody<Boolean> updateBatchBenefitExpire(@RequestBody List<AccountBenefitUpdateDTO> accountBenefitUpdateDTOList);
}
