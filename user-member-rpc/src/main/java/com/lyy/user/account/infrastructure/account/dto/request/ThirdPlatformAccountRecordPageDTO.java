package com.lyy.user.account.infrastructure.account.dto.request;

import com.lyy.user.account.infrastructure.account.dto.PageQueryDTO;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/4/23
 */
@Data
public class ThirdPlatformAccountRecordPageDTO extends PageQueryDTO {

    /**
     * 商户ID
     */
    @NotNull(message = "merchantId不允许为空")
    private Long merchantId;

    /**
     * 用户id
     */
    @NotNull(message = "userId不允许为空")
    private String userId;

    /**
     * 设备id
     */
    private Long equipmentId;

    /**
     * 设备value
     */
    private String equipmentValue;

    /**
     * 外部系统标识： 1.东晙
     */
    @NotNull(message = "externalSystem不允许为空")
    private Integer externalSystem;

    /**
     * 事件类型 1：刷卡，2.扫码
     */
    private Short eventType;

    /**
     * 操作类型 1:增加；2：减少
     */
    private Short mode;


}
