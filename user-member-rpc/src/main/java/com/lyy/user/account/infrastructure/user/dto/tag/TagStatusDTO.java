package com.lyy.user.account.infrastructure.user.dto.tag;

import lombok.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @description: 变更标签DTO
 * @author: qgw
 * @date on 2021/4/6.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class TagStatusDTO {

    @NotEmpty(message = "请选择标签")
    private List<TagStatusInfoDTO> tagInfos;
    /**
     * 是否可用，true:是，false：否
     */
    private Boolean  active;

    /**
     * 记录是否已删除，1:是，0：否
     *
     */
    private Integer  state;


    /**
     * 当前操作人
     */
    private Long operatorId;



}
