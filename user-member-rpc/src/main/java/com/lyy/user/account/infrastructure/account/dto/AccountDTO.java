package com.lyy.user.account.infrastructure.account.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 类描述：账户展示数据对象
 * <p>
 *
 * <AUTHOR>
 * @since 2021/3/31 11:47
 */
@Getter
@Setter
@ToString
public class AccountDTO {

    private Long id;

    /**
     * 总额
     */
    private BigDecimal total;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 权益类型
     */
    private Integer classify;

    /**
     * 描述
     */
    private String description;
}
