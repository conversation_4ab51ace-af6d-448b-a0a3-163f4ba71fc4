package com.lyy.user.account.infrastructure.user.dto.phone;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description:
 * @author: qgw
 * @date on 2021/4/6.
 * @Version: 1.0
 */
@Setter
@Getter
@ToString
public class PlatformUserPhoneQueryParam {
    /**
     * ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long id;

    /**
     * 平台用户ID
     */
    private Long userId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 电话
     */
    private String telephone;

    /**
     */
    private Boolean active;



}
