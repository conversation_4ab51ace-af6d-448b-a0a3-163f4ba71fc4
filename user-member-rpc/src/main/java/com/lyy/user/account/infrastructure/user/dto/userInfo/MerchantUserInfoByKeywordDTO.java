package com.lyy.user.account.infrastructure.user.dto.userInfo;

import com.lyy.user.account.infrastructure.member.dto.SmallVenueMemberLevelInfoDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * 会员信息关键字搜索结果
 */
@Data
@Accessors(chain = true)
public class MerchantUserInfoByKeywordDTO {

    /**
     * 会员信息
     */
    private MerchantUserAndLevelInfoDTO userInfo;

    /**
     * 会员等级相关信息
     */
    private SmallVenueMemberLevelInfoDTO memberLevelInfo;

    /**
     * 会员卡信息
     */
    private List<MemberCardInfoDTO> memberCardInfos;

    /**
     * 是否实名
     */
    private Boolean realNameStatus;

    /**
     * 用户统计
     */
    private MerchantUserStatisticsDTO userStatistics;
}
