package com.lyy.user.account.infrastructure.common;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 数据列表
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class DataList<T> implements Serializable {

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 数据结果
     */
    private List<T> list = new ArrayList<>();

    /**
     * 总页数
     */
    private Long pages;

}
