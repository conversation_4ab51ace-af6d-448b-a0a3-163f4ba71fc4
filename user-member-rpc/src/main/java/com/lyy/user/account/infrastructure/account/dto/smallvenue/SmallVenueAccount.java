package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class SmallVenueAccount {
    /**
     * 	账户ID
     */
    private Long id;

    /**
     * 	用户ID
     */
    private Long userId;

    /**
     * 	商户ID
     */
    private Long merchantId;

    /**
     * 	商户用户ID
     */
    private Long merchantUserId;

    /**
     * 	总权益
     */
    private BigDecimal total;

    /**
     * 	剩余权益
     */
    private BigDecimal balance;

    /**
     * 	权益类型
     */
    private Integer classify;

    /**
     * 	状态：1=正常，2=禁用
     */
    private Integer status;

    /**
     * 	备注
     */
    private String description;

    /**
     * 	创建时间
     */
    private Date createTime;

    private Long createBy;

    /**
     * 	更新时间
     */
    private Date updateTime;

    /**
     * 	更新人
     */
    private Long updateBy;

    /**
     *  卡号
     */
    private String cardNo;

    /**
     * 父账号id
     */
    private Long parentAccountId;

    /**
     * 所属门店id
     */
    private Long storeId;

    /**
     * 卡有效期
     */
    private Date downTime;

    /**
     * 押金
     */
    private BigDecimal deposit;

    /**
     *
     */
    private Boolean defaultFlag;
}