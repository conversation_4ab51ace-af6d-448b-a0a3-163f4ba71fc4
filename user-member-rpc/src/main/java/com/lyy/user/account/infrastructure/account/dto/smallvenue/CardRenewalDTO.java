package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CardRenewalDTO {

    /**
     * 卡号
     */
    @NotNull(message = "cardNo不能为空")
    private String cardNo;

    /**
     * 延长天数
     */
    @NotNull(message = "extendedDays不能为空")
    private Integer extendedDays;

    /**
     * 商户id
     */
    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    /**
     * 操作人id
     */
    @NotNull(message = "operatorId不能为空")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @NotNull(message = "operatorName不能为空")
    private String operatorName;
}
