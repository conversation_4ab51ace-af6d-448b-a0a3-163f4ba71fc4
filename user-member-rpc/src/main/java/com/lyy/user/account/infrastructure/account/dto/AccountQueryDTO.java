package com.lyy.user.account.infrastructure.account.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lyy.user.account.infrastructure.base.StringArrayToLongDeserializer;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 类描述：余额查询对象
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/14 14:59
 */
@Getter
@Setter
@ToString
public class AccountQueryDTO {

    /**
     * 商户id
     */
    @NotNull(message = "商户必须传值")
    private Long merchantId;

    /**
     * 商家用户id
     */
    private Long merchantUserId;

    /**
     * 平台用户id
     */
    private Long userId;

    /**
     * 权益类型集合
     */
    @NotEmpty(message = "权益类型至少传一个值")
    @NotNull(message = "权益类型必须传值")
    private List<Integer> benefitClassify;

    /**
     * 场地id
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> storeIds;

    /**
     * 设备类型id
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> equipmentTypeIds;


    /**
     * 不查的权益类型
     */
    private List<Integer> excludeClassify;
}
