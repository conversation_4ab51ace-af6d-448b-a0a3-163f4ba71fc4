package com.lyy.user.account.infrastructure.user.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import com.lyy.user.account.infrastructure.base.StringArrayToLongDeserializer;
import com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: qgw
 * @date on 2022/4/6.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class UpdateSpecificBusinessTagsParam {

    /**
     * 只保留的标签
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> tagIds;


    /**
     * 商户用户id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    @NotNull(message = "商户用户ID不能为空")
    private Long merchantUserId;

    /**
     * 商户ID
     */
    @NotNull(message = "商户ID不能为空")
    private Long merchantId;

    /**
     * 标签的业务类型
     * 目前仅调用方使用 business_type=0
     */
    @NotNull(message = "请指定标签业务类型")
    private TagBusinessTypeEnum businessType;


    /**
     * 当前操作人
     */
    @NotNull(message = "当前操作人不能为空")
    private Long operatorId;
}
