package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * 移动端会员卡详情附属卡信息
 */
@Data
@Accessors(chain = true)
public class MobileTerminalSupplementaryCardDTO {

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 过期时间
     */
    private Date downTime;
}
