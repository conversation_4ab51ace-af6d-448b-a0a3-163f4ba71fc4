package com.lyy.user.account.infrastructure.statistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 类描述：用户统计更新参数
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/19 14:48
 */

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserStatisticsUpdateDTO {

    /**
     * 商户id
     */
    @NotNull(message = "商户id必填")
    private Long merchantId;

    /**
     * 用户id
     */
    //@NotNull(message = "用户id必填")
    private Long userId;

    /**
     * 商户用户id
     */
    private Long merchantUserId;

    /**
     * 启动次数
     */
    private Integer startTimes;

    /**
     * 充值次数
     */
    private Integer payTimes;

    /**
     * 充值金额
     */
    private BigDecimal payAmount;

    /**
     * 支付购买次数
     */
    private Integer payForServiceTimes;

    /**
     * 支付购买金额
     */
    private BigDecimal payForServiceAmount;

    /**
     * 消耗币数（累计投币）
     */
    private BigDecimal coinsConsumption;

    /**
     * 消耗金额（累计消耗金额）
     */
    private BigDecimal amountConsumption;

    /**
     * 储值币数
     */
    private BigDecimal totalCoins;

    /**
     * 剩余币数
     */
    private BigDecimal balanceCoins;

    /**
     * 剩余金额
     */
    private BigDecimal balanceAmount;

    /**
     * 最近消费时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recentConsumptionTime;

    /**
     * 储值余额
     */
    private BigDecimal totalAmount;

    /**
     * 更新时间（用于规避消息或者调用延迟）
     */
    @NotNull(message = "更新时间参数必填")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 订单号/退款单号
     */
    private String bizNo;

    /**
     * 统计类型：advert,pay,benefit,startTime
     */
    private String type;

    /**
     * 变更类型 1增加 2减少
     */
    private Integer mode;
}
