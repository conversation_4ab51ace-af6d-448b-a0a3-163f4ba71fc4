package com.lyy.user.account.infrastructure.account.dto.request;

import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 保存第三方资金变动DTO
 *
 * <AUTHOR>
 * @since 2023/4/23
 */
@Data
public class ThirdPlatformAccountRecordSaveDTO {

    /**
     * 商户ID
     */
    @NotNull(message = "merchantId不允许为空")
    private Long merchantId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 设备id
     */
    private Long equipmentId;

    /**
     * 设备value
     */
    private String equipmentValue;

    /**
     * 订单号
     */
    private String outTradeNo;

    /**
     * IC卡卡号
     */
    private String cardNo;

    /**
     * 外部用户id
     */
    @NotNull(message = "externalUserId不允许为空")
    private String externalUserId;

    /**
     * 外部系统标识： 1.东晙
     */
    @NotNull(message = "externalSystem不允许为空")
    private Integer externalSystem;

    /**
     * 事件类型 1：刷卡，2.扫码
     */
    @NotNull(message = "eventType不允许为空")
    private Short eventType;

    /**
     * 操作类型 1:增加；2：减少
     */
    private Short mode;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 创建者
     */
    private Long createBy;

}
