package com.lyy.user.account.infrastructure.account.dto.response;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2022/12/15
 */
@Setter
@Getter
@ToString
public class OrderBenefitConsume {

    /**
     * 消耗的权益
     */
    private BigDecimal benefitConsume;
    /**
     * 使用的权益
     */
    private Integer benefitClassify;

}
