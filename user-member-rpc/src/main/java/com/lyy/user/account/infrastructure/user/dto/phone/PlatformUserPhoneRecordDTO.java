package com.lyy.user.account.infrastructure.user.dto.phone;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description:
 * @author: qgw
 * @date on 2021/4/6.
 * @Version: 1.0
 */
@Setter
@Getter
@ToString(callSuper = true)
public class PlatformUserPhoneRecordDTO extends  PlatformUserPhoneDTO{
    /**
     * ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long id;

    /**
     * 商户ID
     */
    private Boolean active;


}
