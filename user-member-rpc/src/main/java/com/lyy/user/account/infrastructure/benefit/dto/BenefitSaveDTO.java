package com.lyy.user.account.infrastructure.benefit.dto;

import com.lyy.user.account.infrastructure.constant.ApplicableEnum;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.BenefitGenerateTypeEnum;
import com.lyy.user.account.infrastructure.constant.ShowDateCategoryEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @create 2021/3/31 16:25
 */
@Data
public class BenefitSaveDTO {


    /**
     * 权益ID,值存在则表示数据更新
     */
    private Long id;
    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 标题
     */
    private String title;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 权益分类
     */
    @NotNull(message = "权益类型不能为空")
    private BenefitClassifyEnum benefitClassify;

    /**
     * 权益数量
     */
    private Long benefitCount;

    /**
     * 可用有效期类型
     */
    private Integer expiryDateCategory;

    /**
     * 上线时间
     */
    private LocalDateTime upTime;

    /**
     * 下线时间
     */
    private LocalDateTime downTime;

    /**
     * 可见有效期类型
     */
    @NotNull(message = "可见有效期类型不能为空")
    private ShowDateCategoryEnum showDateCategory;

    /**
     * 适用范围
     */
    private List<BenefitScopeSaveDTO> benefitScopeList;

    /**
     * 权益规则
     */
    private List<BenefitRuleSaveDTO> benefitRuleList;


}
