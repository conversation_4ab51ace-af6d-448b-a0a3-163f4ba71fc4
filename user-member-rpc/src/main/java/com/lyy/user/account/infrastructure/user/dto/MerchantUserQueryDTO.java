package com.lyy.user.account.infrastructure.user.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 * @create 2021/4/16 15:25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
public class MerchantUserQueryDTO extends MerchantUserConditionDTO {



    /**
     * 页码
     */
    @Min(value = 1,message = "页码必须大于0")
    private Integer pageIndex;

    /**
     * 页长
     */
    @Range(min = 1,max = 50,message = "页长不能超过50")
    private Integer pageSize;




}
