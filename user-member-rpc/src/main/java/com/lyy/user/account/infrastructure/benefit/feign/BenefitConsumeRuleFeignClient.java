package com.lyy.user.account.infrastructure.benefit.feign;

import com.lyy.user.account.infrastructure.benefit.dto.ConsumeRuleSaveDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @ClassName: BenefitConsumeRuleFeignClient
 * @description: 商户权益消耗规则
 * @author: pengkun
 * @date: 2021/08/14
 **/
@FeignClient(name = "user-member-center", fallbackFactory = BenefitConsumeRuleFeignFallback.class)
public interface BenefitConsumeRuleFeignClient {

    /**
     * 初始化商户权益消耗规则
     *
     * @param merchantId  商户id
     * @param operationId 操作人id
     * @return
     */
    @GetMapping("/benefit/consume/initMerchantBenefitConsume")
    RespBody<Void> initMerchantBenefitConsume(@RequestParam("merchantId") Long merchantId,
                                              @RequestParam(value = "operationId", required = false) Long operationId);


    /**
     * 批量商户权益消耗规则处理
     *
     * @param consumeRuleSaveDTO 保存参数
     * @return
     */
    @PostMapping("/benefit/consume/batchMerchantBenefitConsume")
    RespBody<Void> batchMerchantBenefitConsume(@RequestBody ConsumeRuleSaveDTO consumeRuleSaveDTO);

}
