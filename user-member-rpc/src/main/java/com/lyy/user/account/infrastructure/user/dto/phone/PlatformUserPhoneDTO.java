package com.lyy.user.account.infrastructure.user.dto.phone;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * @description:
 * @author: qgw
 * @date on 2021/4/6.
 * @Version: 1.0
 */
@Setter
@Getter
@ToString
public class PlatformUserPhoneDTO {
    /**
     * 平台用户ID
     */
    @NotNull(message = "平台用户ID不能为空")
    private Long userId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 电话
     */
    @NotEmpty(message = "电话不能为空")
    @Pattern(regexp = "^(1)\\d{10}$")
    private String telephone;


    /**
     * 当前操作人
     */
    @NotNull(message = "当前操作人不能为空")
    private Long operatorId;

    /**
     * 商户用户id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long merchantUserId;

    /**
     * 系统标识
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer systemFlag;
}
