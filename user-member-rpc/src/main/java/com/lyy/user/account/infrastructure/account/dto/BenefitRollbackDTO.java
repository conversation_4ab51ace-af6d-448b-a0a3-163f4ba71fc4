package com.lyy.user.account.infrastructure.account.dto;

import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 类描述：权益回滚参数
 * <p>
 *
 * <AUTHOR>
 * @since 2021/5/6 17:17
 */
@Getter
@Setter
@ToString
public class BenefitRollbackDTO {

    /**
     * 支付单号(不使用)
     */
    @Deprecated
    private String outTradeNo;

    /**
     * 业务单号(对应消耗接口的orderNo)
     */
    private String orderNo;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 退回原因
     */
    private String resource;

    /**
     * 退款来源
     */
    private Integer recordType;

    /**
     * 需要退款的余额
     */
    private BigDecimal refundAmount;

    /**
     * 退款类型
     */
    private List<Integer> classify;
    /**
     * 操作人
     */
    private Long operator;

    /**
     * 权益调整类型
     * @see AdjustTypeEnum
     */
    private Integer adjustType;
}
