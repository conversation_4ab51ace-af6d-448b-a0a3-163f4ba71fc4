package com.lyy.user.account.infrastructure.member.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @description:
 * @author: qgw
 * @date on 2021/4/7.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class MemberGrowConsumptionDTO {
    /**
     * 消费 达到赠送成长值目标金额
     */
    private BigDecimal targetAmount;

    private Long growValue;
}
