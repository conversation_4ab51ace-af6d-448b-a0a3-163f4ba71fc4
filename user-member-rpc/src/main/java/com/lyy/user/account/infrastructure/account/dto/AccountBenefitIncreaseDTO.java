package com.lyy.user.account.infrastructure.account.dto;

import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 账户权益增加DTO
 *
 * <AUTHOR>
 * @since 2021/7/1 16:43
 */
@Getter
@Setter
@ToString
public class AccountBenefitIncreaseDTO {

    /**
     * 调整记录id（权益账户表ID）
     */
    private Long id;

    /**
     * 调整类型
     * @see AdjustTypeEnum
     */
    private AdjustTypeEnum adjustType;

    /**
     * 调整数量
     */
    private BigDecimal amount;

    /**
     * 是否清空余额
     */
    private Boolean isEmpty;

    /**
     * 权益类型
     */
    private Integer classify;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 商户用户id
     */
    private Long merchantUserId;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 设备id
     */
    private Long equipmentId;

    /**
     * 支付单号
     */
    private String outTradeNo;

    /**
     * 业务单号
     */
    private String orderNo;

    /**
     * 变更来源
     */
    private String resource;

    /**
     * 操作者
     */
    private Long operator;

    /**
     * 开始时间
     */
    private String upTime;

    /**
     * 结束时间
     */
    private String downTime;

    /**
     * 失效时间类型
     */
    private ExpiryDateCategoryEnum expiryDateCategory;

    /**
     * 账户表ID --用于商家券
     */
    private Long accountId;
    /**
     * 备注
     */
    private String description;
    /**
     * 交易类型
     */
    private Integer tradeType;

    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;
    /**
     * 商品id
     */
    private Long commodityId;
    /**
     * 商品名称
     */
    private String commodityName;
    /**
     * 场地/店铺名称
     */
    private String storeName;
    /**
     * 设备类型id
     */
    private Long equipmentTypeId;
    /**
     * 设备类型名称
     */
    private String equipmentTypeName;
    /**
     * 设备编号
     */
    private String equipmentValue;

    /**
     * 记录类型
     * @see com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum
     */
    @NotNull(message = "记录类型不能为空")
    private Integer recordType;
}
