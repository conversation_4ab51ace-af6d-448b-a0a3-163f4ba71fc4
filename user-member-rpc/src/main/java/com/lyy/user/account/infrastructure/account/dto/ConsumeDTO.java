package com.lyy.user.account.infrastructure.account.dto;

import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 类描述：账户权益消耗传参
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/6 10:42
 */
@Getter
@Setter
@ToString
public class ConsumeDTO {

    /**
     * 用户id
     */
    @NotNull
    private Long userId;
    /**
     * 商户id
     */
    @NotNull
    private Long merchantId;
    /**
     * 场地id(查询权益使用场地范围标识)
     */
    private Long storeId;

    /**
     * 多级场地权益使用范围校验条件
     */
    private List<Long> storeIds;
    /**
     * 设备id
     */
    private Long equipmentId;
    /**
     * 设备类型id
     */
    private Long equipmentTypeId;
    /**
     * 商品id
     */
    private Long commodityId;
    /**
     * 支付单号
     */
    private String outTradeNo;

    /**
     * 业务单号
     */
    private String orderNo;

    /**
     * 变更来源
     */
    private String resource;

    /**
     * 操作者
     */
    private Long operator;
    /**
     * 消耗详情列表
     */
    private List<ConsumeDetailDTO> consume;

    /**
     *  是否检查余额,默认true,调用方可以设置null
     */
    private Boolean  checkBalance;

    /**
     *  allowNegative 是否允许扣为负数,默认false,调用方可以设置null
     */
    private Boolean  allowNegative;

    /**
     * 备注
     */
    private String description;
    /**
     * 交易类型
     */
    private Integer tradeType;

    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;

    /**
     * 商品名称
     */
    private String commodityName;
    /**
     * 场地/店铺名称
     */
    private String storeName;
    /**
     * 设备类型名称
     */
    private String equipmentTypeName;
    /**
     * 设备编号
     */
    private String equipmentValue;

    /**
     * 机台号
     */
    private Integer groupNumber;

    /**
     * 记录类型
     * @see com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum
     */
    @NotNull(message = "记录类型不能为空")
    private Integer recordType;


    /**
     * 不查的权益类型
     */
    private List<Integer> excludeClassify;

    /**
     * 是否预扣 , true:预扣
     */
    private Boolean preDeduction;

    /**
     * 服务类型 （识别账号余额和积分充值余额）
     */
    private Integer serviceType;

    /**
     * 是否排除校验场地限制,默认false,调用方可以设置null
     */
    private Boolean excludeGroup;


    /**
     * 排除打标签
     */
    private Boolean excludeUserTag;

    /**
     * true: 真实扣减，入库
     * @return
     */
    public boolean hasDeduction() {
        if (preDeduction == null) {
            return true;
        }
        return !preDeduction;
    }
}
