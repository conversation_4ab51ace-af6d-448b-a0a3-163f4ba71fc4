package com.lyy.user.account.infrastructure.account.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import com.lyy.user.account.infrastructure.base.StringArrayToLongDeserializer;
import java.util.Date;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/1 17:12
 */
@Getter
@Setter
@ToString(callSuper = true)
public class AccountRecordQueryDTO extends PageQueryDTO {

    /**
     * 商户id
     * 调用方自己控制必传
     */
    //@NotNull(message = "商户ID不能为空")
    private Long merchantId;

    /**
     * 商家用户id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long merchantUserId;

    /**
     * 用户ID(商家用户id不存在时，使用此属性)
     */
    private Long userId;

    /**
     * 场地id
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> storeIds;

    /**
     * 设备类型id
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> equipmentTypeIds;

    /**
     * 交易类型
     //* @see AccountRecordTypeGroupEnum
     */
    private List<Integer> tradeType;

    /**
     * 资金组类型
     * @see com.lyy.user.account.infrastructure.constant.BenefitClassifyGroupEnum
     */
    private Integer balanceType;

    /**
     * 资金类型组
     */
    private List<Integer> balanceTypes;

    /**
     * 	权益类型
     */
    private Integer benefitClassify;

    /**
     * 支付订单号
     */
    private String outTradeNo;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;


    /**
     * 记录类型
     * @see com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum
     */
    private Integer recordType;
    /**
     * 调整类型
     * @see com.lyy.user.account.infrastructure.constant.AdjustTypeEnum
     */
    private Integer mode;

    /**
     * 记录类型列表
     */
    private List<Integer> recordTypeList;

    private List<Integer> excludedRecordTypeList;

    /**
     * 支付订单号列表
     */
    private List<String> outTradeNoList;


    /**
     * 是否包括冻结和解冻记录
     * 默认null与false，不包括
     * @see
     */
    private Boolean hasFreezeAndUnFreezeRecord;
    /**
     * 查询来源
     * C端不显示过期数据
     * 1：C端
     * 2: B端（暂时未用到）
     */
    private Integer queryServerSource;
    private boolean queryHistoryBalance;
    private Boolean countSql;
}
