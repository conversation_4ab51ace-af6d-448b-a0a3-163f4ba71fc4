package com.lyy.user.account.infrastructure.statistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商户统计更新参数
 *
 *
 * <AUTHOR>
 * @since 2021/9/18 14:48
 */

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MerchantStatisticsUpdateDTO {

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 新增用户总数
     */
    private Long  userAmount;

    /**
     * 剩余币数
     */
    private BigDecimal balanceCoins;

    /**
     * 剩余金额
     */
    private BigDecimal balanceAmount;

    /**
     * 支付消费总额
     */
    private BigDecimal totalPayConsume;

    /**
     * 更新时间（用于规避消息或者调用延迟）
     */
    @NotNull(message = "更新时间参数必填")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
