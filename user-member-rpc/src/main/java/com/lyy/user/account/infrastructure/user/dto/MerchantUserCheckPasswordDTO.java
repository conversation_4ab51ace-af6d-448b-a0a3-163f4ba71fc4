package com.lyy.user.account.infrastructure.user.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MerchantUserCheckPasswordDTO {

    @NotNull(message = "userId不能为空")
    private Long userId;

    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    @NotNull(message = "password不能为空")
    private String passWord;
}
