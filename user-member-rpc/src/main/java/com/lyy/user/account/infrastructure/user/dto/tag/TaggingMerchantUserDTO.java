package com.lyy.user.account.infrastructure.user.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @description: 打标签DTO
 * @author: qgw
 * @date on 2021/4/19.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class TaggingMerchantUserDTO {


    /**
     * 商户ID或平台ID
     */
    @NotNull(message = "商户ID或平台ID不能为空")
    private Long merchantId;

    /**
     * 标签名称
     */
    @NotEmpty(message = "标签名称不能为空")
    private String name;


    /**
     * 标签的业务类型
     */
    @NotNull(message = "请指定标签业务类型")
    private Integer businessType;

    /**
     * 商户用户Id或平台用户ID
     */
    @NotNull(message = "请指定商户用户Id或平台用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long userId;
    /**
     * 0 平台用户标签
     * 1 商户用户标签
     *
     */
    @Range(min = 0, message = "标签归属需要指定",max = 1)
    @NotNull
    private Integer tagType;


    /**
     * 当前操作人
     */
    private Long operatorId;



}
