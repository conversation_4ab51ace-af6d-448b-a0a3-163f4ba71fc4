package com.lyy.user.account.infrastructure.account.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @since 2022/10/9 - 16:02
 */
@Data
public class OrderBenefitRecordQueryDTO {

    /**
     * 用户id
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    /**
     * 商户id
     */
    @NotNull(message = "商户ID不能为空")
    private Long merchantId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 子单号
     */
    private List<String> subOrderNos;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

}
