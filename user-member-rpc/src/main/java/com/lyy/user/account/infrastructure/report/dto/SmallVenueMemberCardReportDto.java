package com.lyy.user.account.infrastructure.report.dto;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date : 2022-01-31 11:39
 **/
@Data
public class SmallVenueMemberCardReportDto {

    private Long merchantUserId;

    private Long userId;

    private Long accountId;

    private Long parentAccountId;

    private String cardNo;

    private String initTime;

    private String downTime;

    private BigDecimal deposit;

    private Integer status;

    private String phone;

    private String name;

    private List<SmallVenueStoreValueSimpleDto> storeValueListDto;

}
