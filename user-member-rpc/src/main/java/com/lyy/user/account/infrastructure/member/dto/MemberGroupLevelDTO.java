package com.lyy.user.account.infrastructure.member.dto;

import lombok.Data;

import java.util.Optional;

/**
 * 会员组的等级信息
 * <AUTHOR>
 * @className: MemberGroupLevelDTO
 * @date 2021/4/9
 */
@Data
public class MemberGroupLevelDTO extends MemberLevelDTO {
    /**
     * 该等级的会员数量
     */
    private Integer levelMemberCount;

    /**
     * 字符串格式的id
     */
    private String idStr;

    public String getIdStr() {
        return Optional.ofNullable(getId()).map(id->id.toString()).orElse("");
    }
}
