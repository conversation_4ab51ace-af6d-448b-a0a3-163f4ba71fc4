package com.lyy.user.account.infrastructure.user.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/1 16:18
 */
@Getter
@Setter
@ToString
public class PageQueryDTO {

    /**
     * 页码
     */
    @Range(min = 1, message = "页码参数错误")
    @NotNull
    private Integer pageIndex;

    /**
     * 数据量
     */
    @Range(min = 10, message = "页码参数错误",max = 50)
    @NotNull
    private Integer pageSize;
}
