package com.lyy.user.account.infrastructure.user.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 商户用户查询实体
 */
@Setter
@Getter
@ToString
public class MerchantIntegralUserQueryDTO {
    /**
     * 商户ID
     */
    @NotNull(message = "商户必须传值")
    private Long merchantId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 名称
     */
    private String name;
    /**
     * 手机号码
     */
    private String telephone;
}
