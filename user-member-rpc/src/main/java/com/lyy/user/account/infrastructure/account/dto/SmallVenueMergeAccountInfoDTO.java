package com.lyy.user.account.infrastructure.account.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class SmallVenueMergeAccountInfoDTO {

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 被合并用户id
     */
    private Long mergedUserId;

    /**
     * 被合并会员id
     */
    private Long mergedMerchantUserId;

    /**
     * 合并用户id
     */
    private Long newUserId;

    /**
     * 合并会员id
     */
    private Long newMerchantUserId;

    /**
     * 被合并会员卡号（可能为空）
     */
    private String mergedDefaultCardNo;

    /**
     * 合并会员卡号（可能为空）
     */
    private String newDefaultCardNo;

    /**
     * 被合并卡号对应的accountId
     */
    private Long mergedDefaultAccountId;

    /**
     * 合并卡号对应的accountId
     */
    private Long newDefaultAccountId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 消费方式
     */
    private Integer consumeType;

    /**
     * 设备编号
     */
    private String equipmentValue;

    /**
     * 设备名称
     */
    private String equipmentName;

    /**
     * 操作人id
     */
    private Long createdBy;

    /**
     * 操作人名称
     */
    private String createName;

    /**
     * 被合并账户卡号
     */
    private List<MergeCardBenefit> mergeCardNoList;

    @Data
    public static class MergeCardBenefit {

        private Long accountId;

        private String cardNo;

        private Long merchantBenefitClassifyId;

        private String merchantBenefitClassifyName;

        private BigDecimal balance;
    }
}
