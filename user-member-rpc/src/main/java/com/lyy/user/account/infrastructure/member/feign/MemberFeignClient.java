package com.lyy.user.account.infrastructure.member.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.member.dto.*;
import com.lyy.user.account.infrastructure.resp.RespBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * 会员接口
 * <AUTHOR>
 * @className: MemberFeignClient
 * @date 2021/4/19
 */
@FeignClient(name = "user-member-center", fallbackFactory = MemberFeignFallback.class)
public interface MemberFeignClient {

    /**
     * 会员触发规则
     * @param memberTouchRuleDTO
     * @return 返回触发的规则数量
     */
    @PostMapping("rest/member/member/updateMemberOfRule")
    RespBody<Integer> updateMemberOfRule(@RequestBody MemberTouchRuleDTO memberTouchRuleDTO);

    /**
     * 手动触发更新用户的 会员等级
     * @param updateUserMemberLevelDTO
     * @return
     */
    @PostMapping("rest/member/member/updateUserMemberLevel")
    RespBody<Boolean> updateUserMemberLevel(@Valid @RequestBody UpdateUserMemberLevelDTO updateUserMemberLevelDTO);

    /**
     * 会员触发规则回退，主要用于回调执行了退单等操作的
     * @param memberTouchRuleDTO
     * @return
     */
    @PostMapping("rest/member/member/removeMemberOfRule")
    RespBody<Boolean> removeMemberOfRule(@RequestBody MemberTouchRuleDTO memberTouchRuleDTO);

    /**
     * 分页获取会员信息
     * @param current
     * @param size
     * @param merchantId
     * @param userId
     * @return
     */
    @GetMapping("/rest/member/member/findPageMemberUser")
    RespBody<Page<MemberUserPageDTO>> findPageMemberUser(@RequestParam("current") Integer current, @RequestParam("size") Integer size,
                                                         @RequestParam("merchantId") Long merchantId, @RequestParam("userId") Long userId);


    /**
     * 获取会员的详细信息
     * @param merchantId 商户id
     * @param memberId  会员id
     * @return
     */
    @GetMapping("rest/member/member/getMemberUserInfo")
    RespBody<MemberUserInfoDTO> getMemberUserInfo(@RequestParam("merchantId") Long merchantId, @RequestParam("memberId") Long memberId);

    /**
     * 获取会员等级详情
     * @param merchantId
     * @param memberId
     * @return
     */
    @GetMapping("rest/member/member/getMemberUserLevel")
    RespBody<MemberUserLevelDTO> getMemberUserLevel(@RequestParam("merchantId") Long merchantId, @RequestParam("memberId") Long memberId);


    /**
     * 初始化小场地用户会员等级
     * @return
     */
    @GetMapping("rest/member/member/initSmallVenueUserMemberLevel")
    RespBody initSmallVenueUserMemberLevel(@RequestParam("merchantId") Long merchantId,@RequestParam("userId") Long userId);

    /**
     * 获取小场地用户会员信息
     * @param merchantId
     * @param userId
     * @return
     */
    @GetMapping("rest/member/member/getSmallVenueUserMemberInfo")
    RespBody<MemberUserInfoDTO> getSmallVenueUserMemberInfo(@RequestParam("merchantId") Long merchantId,@RequestParam("userId") Long userId);

}
