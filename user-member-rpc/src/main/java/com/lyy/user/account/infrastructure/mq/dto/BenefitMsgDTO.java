package com.lyy.user.account.infrastructure.mq.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 权益消息实体
 * <AUTHOR>
 * @className: BenefitMsgDTO
 * @date 2021/7/2
 */
@Data
public class BenefitMsgDTO implements Serializable {

    /**
     * 调整数量
     */
    private BigDecimal amount;

    /**
     * 是否清空余额
     */
    private Boolean isEmpty;
    /**
     * 权益类型
     */
    private Integer classify;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 场地id
     */
    private Long equipmentGroupId;

    public BenefitMsgDTO() {
    }
}
