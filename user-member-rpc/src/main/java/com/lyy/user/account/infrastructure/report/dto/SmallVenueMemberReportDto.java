package com.lyy.user.account.infrastructure.report.dto;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date : 2022-01-31 11:39
 **/
@Data
public class SmallVenueMemberReportDto {

    private Long merchantUserId;

    private Long userId;

    private String phone;

    private String name;

    private String userType;

    private Long memberLevelId;

    private String memberLevelName;

    private String gender;

    private String birthday;

    private Long provinceId;

    private Long cityId;

    private Long regionId;

    private String address;

    private List<String>  userTagNames;

    private Long[]  userTagIds;
    private String initTime;

    private List<SmallVenueStoreValueSimpleDto> storeValueListDto;

}
