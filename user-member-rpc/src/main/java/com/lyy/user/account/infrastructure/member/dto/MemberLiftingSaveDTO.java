package com.lyy.user.account.infrastructure.member.dto;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Optional;

/**
 * 升级策略保存信息
 * <AUTHOR>
 * @className: MemberLiftingSaveDTO
 * @date 2021/3/30
 */
@Setter
@Getter
@ToString
@EqualsAndHashCode
public class MemberLiftingSaveDTO extends MemberLiftingDTO {
    /**
     * 规则列表信息
     */
    private List<MemberLiftingRuleDTO> ruleList;
    /**
     * 字符串格式的id
     */
    private String idStr;

    public String getIdStr() {
        return Optional.ofNullable(getId()).map(id->id.toString()).orElse("");
    }
}
