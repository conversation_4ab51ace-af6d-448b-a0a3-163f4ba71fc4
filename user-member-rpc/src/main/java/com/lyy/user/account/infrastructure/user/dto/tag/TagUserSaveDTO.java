package com.lyy.user.account.infrastructure.user.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lyy.user.account.infrastructure.base.StringArrayToLongDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description: 用户标签 创建或修改
 * @author: qgw
 * @date on 2021/4/2.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class TagUserSaveDTO {
    private Long id;

    /**
     * 标签名称
     */
    @NotNull(message = "标签名称不能为空")
    private String name;

    /**
     * 标签编码
     */
    private String code;

    /**
     * 商户ID或平台ID
     */
    private Long merchantId;

    /**
     * 标签类型，1:自动, 2:手动
     */
    private Integer category;

    /**
     * 备注
     */
    private String description;

    /**
     * 是否可用，true:是，false：否
     */
    //@NotNull(message = "请指定标签状态")
    private Boolean active;

    /**
     * 0 平台用户标签
     * 1 商户用户标签
     *
     */
    @Range(min = 0, message = "标签归属需要指定",max = 1)
    @NotNull
    private Integer tagType;


    /**
     * 标签的业务类型
     */
    @NotNull
    private Integer businessType;

    /**
     * 当前操作人
     */
    private Long operatorId;

    /**
     * 商户用户Id或平台用户ID
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> userIds;


}
