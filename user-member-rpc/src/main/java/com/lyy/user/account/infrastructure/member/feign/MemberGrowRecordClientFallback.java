package com.lyy.user.account.infrastructure.member.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.member.dto.MemberGrowRecordListDTO;
import com.lyy.user.account.infrastructure.member.dto.MemberGrowRecordQueryDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @ClassName: MemberGrowRecordClientFallback
 * @description: 成长值
 * @author: qgw
 * @date: 2021/04/19
 **/
@Slf4j
@Component
public class MemberGrowRecordClientFallback implements FallbackFactory<MemberGrowRecordFeignClient> {
    @Override
    public MemberGrowRecordFeignClient create(Throwable throwable) {

        if (throwable != null && StringUtils.isNotEmpty(throwable.getMessage())) {
            log.error(throwable.getMessage(), throwable);
        }

        return new MemberGrowRecordFeignClient() {

            @Override
            public RespBody<Page<MemberGrowRecordListDTO>> list(MemberGrowRecordQueryDTO param) {
                log.error("[新会员] 查询用户成长值记录, list 熔断,param:{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

        };
    }
}
