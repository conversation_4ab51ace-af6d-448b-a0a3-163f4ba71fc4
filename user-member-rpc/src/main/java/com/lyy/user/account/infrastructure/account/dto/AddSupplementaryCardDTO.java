package com.lyy.user.account.infrastructure.account.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AddSupplementaryCardDTO {

    /**
     * 账户id
     */
    private Long accountId;

    /**
     * 账户id
     */
//    @NotNull(message = "cardNo不能为空")
    private String cardNo;

    /**
     * 用户id
     */
    @NotNull(message = "userId不能为空")
    private Long userId;

    /**
     * 商户id
     */
    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    /**
     * 商户用户id
     */
    @NotNull(message = "merchantUserId不能为空")
    private Long merchantUserId;
    /**
     * 附属卡号
     */
    @NotNull(message = "supplementaryCardNo不能为空")
    private String supplementaryCardNo;

    @NotNull(message = "cardValidType不能为空")
    private Integer cardValidType;

    /**
     * 过期时间
     */
    private Date downTime;

    /**
     * 操作人id
     */
    @NotNull(message = "operatorId不能为空")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @NotNull(message = "operatorName不能为空")
    private String operatorName;

    /**
     * 押金
     */
    private BigDecimal deposit;

    /**
     * 卡有效期天数
     */
    private Integer validDays;
}
