package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 移动端会员卡列表
 */
@Data
@Accessors(chain = true)
public class MobileTerminalAccountListDTO {

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 卡过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date downTime;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 会员昵称
     */
    private String name;

    /**
     * 手机号码
     */
    private String telephone;

    /**
     * 头像
     */
    private String headImg;

    /**
     * 账户id
     */
    private Long accountId;

    /**
     * 平台用户id
     */
    private Long userId;

    /**
     * 储值信息
     */
    private List<MobileTerminalAccountBenefitInfo> benefitInfos;

    /**
     * 附属卡张数
     */
    private Integer supplementaryCardNum;

    /**
     * 会员id
     */
    private Long merchantUserId;
}
