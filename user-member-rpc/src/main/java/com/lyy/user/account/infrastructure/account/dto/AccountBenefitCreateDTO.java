package com.lyy.user.account.infrastructure.account.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 类描述：账户权益创建对象
 * <p>
 *
 * <AUTHOR>
 * @since 2021/3/31 09:58
 */
@Getter
@Setter
@ToString
public class AccountBenefitCreateDTO {

    /**
     * 权益id
     */
    private Long benefitId;

    /**
     * 总额
     */
    private BigDecimal total;

    /**
     * 权益类型
     */
    private Integer classify;

    /**
     * 过期时间类型，null为永久有效
     */
    private Integer expiryDateCategory;

    /**
     * 权益有效开始时间
     */
    private String upTime;

    /**
     * 权益有效结束时间
     */
    private String downTime;

}
