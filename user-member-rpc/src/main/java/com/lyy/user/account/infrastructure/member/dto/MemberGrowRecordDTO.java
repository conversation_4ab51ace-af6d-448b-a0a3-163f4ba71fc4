package com.lyy.user.account.infrastructure.member.dto;

import com.lyy.user.account.infrastructure.constant.MemberGrowRecordModeEnum;
import com.lyy.user.account.infrastructure.constant.MemberGrowRuleEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @description:
 * @author: qgw
 * @date on 2021/4/7.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class MemberGrowRecordDTO {

    /**
     * 平台用户ID
     */
    @NotNull
    private Long userId;

    /**
     * 商户用户ID
     */
    @NotNull
    private Long merchantUserId;


    /**
     * 商户id
     */
    @NotNull(message = "商户ID不能为空")
    private Long merchantId;

    /**
     * 成长值规则ID
     */
    private Long ruleId;

    /**
     * 成长值
     */
    private Long growValue;

    /**
     * 消费金额
     */
    private BigDecimal money;

    /**
     * 交易单号
     */
    private String outTradeNo;

    @NotNull
    private MemberGrowRuleEnum growRuleEnum;

    /**
     * 方式: 1加 2减
     */
    @NotNull
    private MemberGrowRecordModeEnum recordModeEnum;

    /**
     * 来源
     */
    private String resources;

    /**
     * 描述
     */
    private String description;

    /**
     * 会员ID
     */
    @NotNull
    private Long memberId;

    /**
     * 当前操作人
     */
    private Long operatorId;
}
