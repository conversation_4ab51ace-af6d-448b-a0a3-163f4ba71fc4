package com.lyy.user.account.infrastructure.account.dto;

import lombok.Data;

import java.util.List;

@Data
public class AccountClassifyBenefitDTO {

    /**
     * 权益类型
     */
    private Integer benefitClassify;

    /**
     * 权益类型权重
     */

    private Integer weight;

    /**
     * 优先级类型
     */
    private Integer expirePriority;

    /**
     * 权益详细信息
     */
    private List<BenefitWithScopeDTO> benefitWithScope;
}
