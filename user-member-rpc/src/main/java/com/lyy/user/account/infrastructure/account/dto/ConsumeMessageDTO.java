package com.lyy.user.account.infrastructure.account.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 类描述：账户权益消耗传参
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/6 10:42
 */
@Getter
@Setter
@ToString
public class ConsumeMessageDTO extends ConsumeDTO {

    /**
     * 是否计算统计
     */
    private Boolean staticsFlag;

}
