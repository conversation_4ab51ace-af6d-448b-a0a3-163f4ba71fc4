package com.lyy.user.account.infrastructure.benefit.feign;

import com.lyy.user.account.infrastructure.benefit.dto.BenefitInfoDTO;
import com.lyy.user.account.infrastructure.benefit.dto.GeneralGroupBenefitSaveDTO;
import com.lyy.user.account.infrastructure.benefit.dto.GroupBenefitMergeDTO;
import com.lyy.user.account.infrastructure.benefit.vo.GeneralGroupBenefitSaveVO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import java.util.List;
import javax.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 权益对外接口
 * <AUTHOR>
 * @create 2021/4/27 11:45
 */
@FeignClient(name = "user-member-center", fallbackFactory = BenefitFeignFallback.class)
public interface BenefitFeignClient {

    /**
     * 获取商户权益，如不存在则创建
     * @param merchantId
     * @param benefitClassifyCode
     * @param applicable
     * @param associatedId
     * @return
     */
    @GetMapping(value = "/benefit/getMerchantBenefit")
    RespBody<BenefitInfoDTO> getMerchantBenefit(@RequestParam("merchantId") Long merchantId, @RequestParam("benefitClassifyCode") Integer benefitClassifyCode,
                                                @RequestParam(value = "applicable",required = false) Integer applicable,
                                                @RequestParam(value = "associatedId",required = false) Long associatedId);

    /**
     * 获取权益详情
     * @param id
     * @param merchantId
     * @return
     */
    @GetMapping(value = "/benefit/getBenefitById")
    RespBody<BenefitInfoDTO> getBenefitById(@RequestParam("id") Long id,@RequestParam(value = "merchantId",required = false)  Long merchantId);

    /**
     * 保存通用场地的权益信息
     * @param generalGroupBenefitSaveDTO
     * @return
     */
    @PostMapping(value = "/benefit/saveGeneralGroupBenefit")
    RespBody<List<GeneralGroupBenefitSaveVO>> saveGeneralGroupBenefit(@RequestBody GeneralGroupBenefitSaveDTO generalGroupBenefitSaveDTO);

    /**
     * 保存普通场地的权益权益信息
     * @param generalGroupBenefitSaveDTO
     * @return
     */
    @PostMapping(value = "/benefit/saveGroupBenefit")
    RespBody saveGroupBenefit(@RequestBody GeneralGroupBenefitSaveDTO generalGroupBenefitSaveDTO);

    /**
     * 2.0开启场地通用更新权益使用范围
     *
     * @param generalGroupBenefitSaveDTO
     * @return
     */
    @PostMapping(value = "/benefit/updateSaaSBenefit")
    RespBody<Boolean> updateSaaSBenefit(GeneralGroupBenefitSaveDTO generalGroupBenefitSaveDTO);

    /**
     * 场地通用权益合并
     *
     * @param groupBenefitMergeDTO
     * @return
     */
    @PostMapping(value = "/benefit/groupBenefitMerge")
    RespBody<Boolean> groupBenefitMerge(@RequestBody @Valid GroupBenefitMergeDTO groupBenefitMergeDTO);

    /**
     * 新场地合并
     * @param groupBenefitMergeDTO
     * @return
     */
    @PostMapping(value = "/benefit/groupBenefitMergeWithNewGroupList")
    RespBody<Boolean> groupBenefitMergeWithNewGroupList(@RequestBody GroupBenefitMergeDTO groupBenefitMergeDTO);

}
