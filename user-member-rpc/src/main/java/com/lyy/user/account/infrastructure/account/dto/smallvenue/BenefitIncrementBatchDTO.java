package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/3/16 - 20:06
 */
@NoArgsConstructor
@Data
public class BenefitIncrementBatchDTO {

    @Valid
    @NotEmpty(message = "权益项不能为空")
    private List<BenefitIncrementDTO> list;

}
