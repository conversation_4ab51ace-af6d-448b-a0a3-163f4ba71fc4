package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @ClassName: UserAccountRecord
 * @description: 用户账户消费流水
 * @author: pengkun
 * @date: 2022/01/12
 **/
@Setter
@Getter
@ToString
public class UserAccountRecord {
    /**
     * 账户明细id
     */
    private Long accountBenefitId;
    /**
     * 账户id
     */
    private Long accountId;
    /**
     * 方式 1=加，2=减
     */
    private Integer mode;
    /**
     * 商家自定义权益id
     */
    private Long merchantBenefitClassifyId;
    /**
     * 主单号
     */
    private String orderNo;
    /**
     * 子单号
     */
    private String subOrderNo;
    /**
     * 实际权益
     */
    private BigDecimal actualBenefit;

    /**
     * 权益实际价值
     */
    private BigDecimal actualValue;
    /**
     * 描述
     */
    private String description;
    /**
     * 退款单号
     */
    private String refundNo;

}
