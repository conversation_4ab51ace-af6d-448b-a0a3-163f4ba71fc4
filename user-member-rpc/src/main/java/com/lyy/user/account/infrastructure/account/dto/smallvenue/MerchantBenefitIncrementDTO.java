package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lyy.user.account.infrastructure.base.StringArrayToLongDeserializer;
import com.lyy.user.account.infrastructure.user.dto.SmallVenueMobileUserListSelectDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName: MerchantBenefitIncrementDTO
 * @description: 商家权益处理
 * @author: pengkun
 * @date: 2022/06/14
 **/
@Setter
@Getter
@ToString
public class MerchantBenefitIncrementDTO {
    /**
     * 平台用户
     */
    private List<Long> userIds;
    /**
     * 商户id
     */
    @NotNull(message = "商户ID不能为空")
    private Long merchantId;
    /**
     * 操作人Id
     */
    private Long operatorId;
    /**
     * 操作人名称
     */
    private String operatorName;
    /**
     * 门店id
     */
    @NotNull(message = "门店ID不能为空")
    private Long storeId;

    /**
     * 全选标识
     */
    private Boolean allFlag;

    private String keyword;

    /**
     * 场地标签集合
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> groupTagIdList;

    /**
     * 普通标签
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> otherTagIdList;
    /**
     * 设备类型标签
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> equipmentTypeTagIdList;

    /**
     * 性别标签ID
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> sexTagIdList;

    /**
     * 特定过滤条件(  累计消费 、会员等级、会员来源、会员卡 )
     */
    private SmallVenueMobileUserListSelectDTO.SpecificFilterInfoDTO specificFilterInfoDTO;

    /**
     * 商家自定义权益
     *
     */
    private List<SmallVenueMobileUserListSelectDTO.MerchantBenefitClassifyDTO> merchantBenefitClassifyDTOList;

    /**
     * 子项
     */
    @Valid
    @NotNull(message = "权益子项不能为空")
    private BenefitIncrementItemDTO incrementItemDTO;

    /**
     * 流水信息
     */
    @Valid
    private AccountRecordInfoDTO record;
}
