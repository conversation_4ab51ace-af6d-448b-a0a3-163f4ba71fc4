package com.lyy.user.account.infrastructure.user.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * 移动端会员注销
 */
@Data
@Accessors(chain = true)
public class MobileTerminalUserCancellationDTO {

    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    @NotNull(message = "merchantUserId不能为空")
    private Long merchantUserId;

    @NotNull(message ="operatorId不能为空")
    private Long operatorId;
}
