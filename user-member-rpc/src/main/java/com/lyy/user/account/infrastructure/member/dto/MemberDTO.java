package com.lyy.user.account.infrastructure.member.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 会员信息
 * <AUTHOR>
 * @className: MemberDTO
 * @date 2021/3/29
 */
@Data
public class MemberDTO {
    /**
     * 主键id
     */
    @JsonSerialize(using =ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long id;

    /**
     * 平台用户,对应lyy_user表的id
     */
    @JsonSerialize(using =ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long userId;

    /**
     * 平台用户
     */
    @JsonSerialize(using =ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long merchantUserId;

    /**
     * 商户ID
     */
    @JsonSerialize(using =ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long merchantId;

    /**
     * 会员组ID
     */
    @JsonSerialize(using =ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long memberGroupId;

    /**
     * 会员等级ID
     */
    @JsonSerialize(using =ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long memberLevelId;

    /**
     * 成长值
     */
    private Long growValue;

    /**
     * 会员有效开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date memberStartTime;

    /**
     * 会员有效结束时间,若为空，则表示没有结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date memberEndTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 创建者
     */
    private Long createBy;
    /**
     * 更新者
     */
    private Long updateBy;
    /**
     * 是否已删除，true:是，false：否
     */
    private Boolean del;



}
