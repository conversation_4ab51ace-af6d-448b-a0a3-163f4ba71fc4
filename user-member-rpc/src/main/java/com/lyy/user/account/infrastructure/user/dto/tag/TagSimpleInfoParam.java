package com.lyy.user.account.infrastructure.user.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.StringArrayToLongDeserializer;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class TagSimpleInfoParam {

    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> tagIds;

    private List<String> names;

    @NotNull(message = "merchantId不能为空")
    private Long merchantId;
    /**
     * 标签类型，1:自动, 2:手动
     *
     * @see com.lyy.user.account.infrastructure.constant.TagCategoryEnum
     */
    private Integer category;


    /**
     * 启用状态
     */
    private Boolean active;


    /**
     * 记录是否已删除，1:是，0：否
     *
     */
    private Integer  state;

    /**
     * 标签的业务类型
     *
     * @see com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum
     */
    private List<Integer> businessTypes;


    /**
     * 模糊搜索标签名
     */
    private String searchTagName;


}
