package com.lyy.user.account.infrastructure.user.dto.userInfo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MerchantUserConsumeStatisticsDTO {

    /**
     * 会员用户 id
     */
    private Long merchantUserId;
    /**
     * 累计消费
     */
    private BigDecimal amountConsumption;

    /**
     * 最近消费时间
     */
    private Date recentConsumptionTime;
}
