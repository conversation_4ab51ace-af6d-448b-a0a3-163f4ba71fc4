package com.lyy.user.account.infrastructure.user.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lyy.user.account.infrastructure.base.StringArrayToLongDeserializer;
import com.lyy.user.account.infrastructure.user.dto.MerchantUserConditionDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @description: 标签绑定用户
 * @author: qgw
 * @date on 2021/4/6.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TagBindUserDTO extends MerchantUserConditionDTO {


    @NotEmpty(message = "请选择标签")
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> tagIds;
    /**
     * 商家用户或平台用户
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long>  userIds;

    /**
     * 当前操作人
     */
    private Long operatorId;

    /**
     * true 选择全部
     */
    private Boolean chooseAll;


    /**
     * 不处理的用户IDs
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> notHandleUserIds;




    private Integer pageIndex;

    /**
     * 页长
     */
    private Integer pageSize;



}
