package com.lyy.user.account.infrastructure.member.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 会员组汇总信息保存，包括等级之类的各个信息
 * <AUTHOR>
 * @date 2021/4/21
 */
@Data
public class MemberGroupInfoSaveDTO {

    /**
     * 会员组保存信息，包括使用范围
     */
    @NotNull
    MemberGroupSaveDTO memberGroupSaveDTO;

    /**
     * 会员等级列表信息,包括用户规则信息
     */
    List<MemberLevelSaveDTO> memberLevelList;

    /**
     * 会员升级策略信息，包括策略规则
     */
    List<MemberLiftingSaveDTO> memberLiftingList;

}
