package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 注销账户
 *
 * <AUTHOR>
 * @since 2022/1/13
 */
@NoArgsConstructor
@Data
public class AccountCancellationDTO {

    /**
     * 卡号
     */
    @NotNull(message = "会员卡号不能为空")
    @NotEmpty(message = "会员卡号不能为空")
    private List<String> cardNos;
    /**
     * 描述信息
     */
    private String description;

    private String outTradeNo;
    /**
     * 操作人Id
     */
    @NotNull(message = "操作人ID不能为空")
    private Long operatorId;
    /**
     * 商户id
     */
    @NotNull(message = "商户ID不能为空")
    private Long merchantId;
    /**
     * 用户id
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    /**
     * 门店Id
     */
    @NotNull(message = "门店ID不能为空")
    private Long storeId;

    @Valid
    private AccountRecordInfoDTO record;

}
