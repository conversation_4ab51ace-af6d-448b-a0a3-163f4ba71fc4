package com.lyy.user.account.infrastructure.member.dto;

import lombok.Data;

import java.util.List;

/**
 * 用户的会员信息，主要用于展示给c端用户
 * <AUTHOR>
 * @className: MemberUserInfoDTO
 * @date 2021/5/22
 */
@Data
public class MemberUserInfoDTO extends MemberUserPageDTO{
    /**
     * 会员当前已经获得的会员规则（权益）
     */
    private List<MemberRuleDTO> memberRuleList;

    /**
     * 会员的任务
     */
    private List<MemberLiftingSaveDTO> memberLiftingList;

//    /**
//     * 当前的会员组范围
//     */
//    private List<MemberRangeAssociatedDTO> memberRangeAssociatedList;
}
