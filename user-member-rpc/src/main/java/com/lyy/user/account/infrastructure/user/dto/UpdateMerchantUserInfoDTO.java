package com.lyy.user.account.infrastructure.user.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class UpdateMerchantUserInfoDTO {

    /**
     * 商户用户Id
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 商户Id
     */
    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    /**
     * 用户Id
     */
    @NotNull(message = "userId不能为空")
    private Long userId;

    /**
     * 会员名称
     */
    private String name;
    /**
     * 性别
     */
    private String gender;
    /**
     * 头像链接
     */
    private String headImg;
    /**
     * 生日
     */
    private String birthday;
    /**
     * 城市Id
     */
    private Long cityId;
    /**
     * 省份城市
     */
    private String provinceCity;

    /**
     * 地址
     */
    private String address;
    /**
     * 省份Id
     */
    private Long provinceId;
    /**
     * 区域Id
     */
    private Long regionId;

    /**
     * 操作人id
     */
    @NotNull(message = "operatorId不能为空")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @NotNull(message = "operatorName不能为空")
    private String operatorName;

}
