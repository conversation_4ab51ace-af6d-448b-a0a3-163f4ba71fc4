package com.lyy.user.account.infrastructure.account.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 类描述： 支付充值退款退权益明细
 * <p>
 *
 * <AUTHOR>
 * @since  2021年11月25日 15:16:04
 */
@Getter
@Setter
@ToString
public class PayRefundBenefitDetailDTO {

    /**
     * 兼容处理旧退款方法
     * 权益id
     */
    private Long benefitId;

    /**
     * 权益类型
     */
    private Integer classify;


    /**
     * 权益数值
     */
    private BigDecimal amount;

}
