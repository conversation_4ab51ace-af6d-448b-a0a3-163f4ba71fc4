package com.lyy.user.account.infrastructure.member.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.member.dto.*;
import com.lyy.user.account.infrastructure.resp.RespBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 会员组接口
 * <AUTHOR>
 * @className: MemberGroupFeignClient
 * @date 2021/4/19
 */
@FeignClient(name = "user-member-center", fallbackFactory = MemberGroupFeignFallback.class)
public interface MemberGroupFeignClient {

    /**
     * 保存会员组
     * @param memberGroupSaveDTO
     * @return
     */
    @PostMapping("rest/member/group/save")
    RespBody<Long> save(@RequestBody MemberGroupSaveDTO memberGroupSaveDTO);

    /**
     * 保存会员组全部信息，包括等级，升级策略等信息
     * @param memberGroupInfoSaveDTO
     * @return 会员id
     */
    @PostMapping("rest/member/group/saveMemberGroupInfo")
    RespBody<Long> saveMemberGroupInfo(@RequestBody MemberGroupInfoSaveDTO memberGroupInfoSaveDTO);

    /**
     * 获取会员组详情
     * @param memberGroupId
     * @return
     */
    @GetMapping("rest/member/group/getInfoById")
    RespBody<MemberGroupResponseDTO> getInfoById(@RequestParam("merchantId") Long merchantId, @RequestParam("memberGroupId") Long memberGroupId);

    /**
     * 更新会员组状态
     * @param memberGroupDTO
     * @return
     */
    @PostMapping("rest/member/group/updateStatus")
    RespBody<Boolean> updateStatus(@RequestBody MemberGroupDTO memberGroupDTO);

    /**
     * 获取默认会员组信息
     * @return
     */
    @GetMapping("rest/member/group/getDefaultMemberGroup")
    RespBody<MemberGroupInfoSaveDTO> getDefaultMemberGroup();

    /**
     * 分页获取会员数据
     * @param memberGroupPageRequestDTO
     * @return
     */
    @PostMapping("rest/member/group/findByPage")
    RespBody<Page<MemberGroupPageResponseDTO>> findByPage(@RequestBody MemberGroupPageRequestDTO memberGroupPageRequestDTO);

    /**
     * 获取会员组详情(包括统计会员信息)
     * @param  merchantId 商户ID
     * @param memberGroupId 会员组ID
     * @return
     */
    @GetMapping("rest/member/group/getInfoCountById")
    RespBody<MemberGroupResponseDTO> getInfoCountById(@RequestParam("merchantId") Long merchantId, @RequestParam("memberGroupId") Long memberGroupId);

    /**
     * 获取会员组详情(用于保存信息)
     * @param  merchantId 商户ID
     * @param memberGroupId 会员组ID
     * @return
     */
    @GetMapping("rest/member/group/getInfoSaveById")
    RespBody<MemberGroupInfoSaveDTO> getInfoSaveById(@RequestParam("merchantId") Long merchantId, @RequestParam("memberGroupId") Long memberGroupId);

    /**
     * 根据用户组信息分页获取会员信息
     * @param current 当前页
     * @param size
     * @param merchantId 商户ID
     * @param memberGroupId
     * @return
     */
    @GetMapping("rest/member/group/findMemberByMemberGroup")
    RespBody<Page<MemberInfoDTO>> findMemberByMemberGroup(@RequestParam("current") Integer current,@RequestParam("size") Integer size,
                                                          @RequestParam("merchantId") Long merchantId,@RequestParam("memberGroupId") Long memberGroupId,
                                                          @RequestParam(value = "memberLevelId",required = false) Long memberLevelId);
    /**
     * 删除会员组信息
     * @param memberGroupId
     * @return
     */
    @GetMapping("rest/member/group/removeByMemberGroup")
    RespBody<Boolean> removeByMemberGroup(@RequestParam("merchantId") Long merchantId, @RequestParam("memberGroupId") Long memberGroupId);

}
