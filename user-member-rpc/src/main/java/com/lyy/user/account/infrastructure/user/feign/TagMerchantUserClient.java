package com.lyy.user.account.infrastructure.user.feign;

import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.user.dto.tag.TagExternalSystemQuery;
import com.lyy.user.account.infrastructure.user.dto.tag.TagOfExternalSystemDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagOfExternalSystemVO;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2023/4/18
 */
@FeignClient(name = "user-member-center", fallbackFactory = TagMerchantUserClientFallback.class)
public interface TagMerchantUserClient {

    /**
     * 查询用户标签-其他平台相关
     */
    @PostMapping("/tag/merchant/user/external-system/get")
    RespBody<TagOfExternalSystemVO> getTagOfExternalSystem(@RequestBody TagOfExternalSystemDTO dto);    /**
     * 查询用户标签-其他平台相关
     */
    @GetMapping("/tag/merchant/user/external-system")
    RespBody<List<TagOfExternalSystemVO>> listTagOfExternalSystem(@SpringQueryMap TagExternalSystemQuery query);

}
