package com.lyy.user.account.infrastructure.statistics.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.statistics.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 统计接口feign
 *
 *
 * <AUTHOR>
 * @since 2021/4/15 11:07
 */
@FeignClient(name = "user-member-center", fallbackFactory = StatisticsFeignFallback.class)
public interface StatisticsFeignClient {

    @PostMapping("/statistics/update")
    RespBody<Void> updateStatistics(@RequestBody UserStatisticsUpdateDTO param);

    /**
     * 获取用户总统计信息
     * @param param
     * @return
     */
    @PostMapping("/statistics/find")
    RespBody<UserStatisticsRecordDTO> find(@RequestBody UserStatisticsConditionDTO param);

    /**
     * 获取商户统计数据
     * @param merchantId
     * @return
     */
    @GetMapping("/statistics/getMerchantStatistics")
    RespBody<MerchantStatisticsRecordDTO> getMerchantStatistics(@RequestParam("merchantId") Long merchantId);

    /**
     * 查询统计用户数据列表
     * @param statisticsUserQueryDTO
     * @return
     */
    @PostMapping("/statistics/queryStatisticsUserList")
    RespBody<Page<MerchantUserStatisticsListDTO>> queryStatisticsUserList(@RequestBody StatisticsUserQueryDTO statisticsUserQueryDTO);

    /**
     * 获取用户当日统计信息
     * @param param
     * @return
     */
    @PostMapping("/statistics/daily/current-date")
    RespBody<MerchantUserStatisticsDailyDTO> getUserCurrentDateStatistics(@RequestBody UserStatisticsConditionDTO param);

    /**
     * 获取用户当日统计信息
     * @param condition
     * @return
     */
    @PostMapping("/statistics/recharge-rank")
    RespBody<List<MerchantUserRankRecordDTO>> getRechargeRank(@RequestBody MerchantUserRankConditionDTO condition);
}
