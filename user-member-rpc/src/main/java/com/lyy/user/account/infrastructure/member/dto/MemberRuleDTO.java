package com.lyy.user.account.infrastructure.member.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @className: MemberRuleDTO
 * @date 2021/3/30
 */
@Data
public class MemberRuleDTO {
    /**
     * 会员等级
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long memberLevelId;

    /**
     * 规则类型--对应账户权益类型
     */
    private Short benefitClassify;

    /**
     * 权益值
     */
    private BigDecimal benefitValue;
    /**
     * 权益单位
     */
    private String benefitUnit;

    /**
     * 有效期类型（0、无限期，1、日期区间可用，2、单日时间区间可用）
     * @see com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum
     */
    private Short expiryDateCategory;
    /**
     * 上线时间
     */

    private String upTime;

    /**
     * 下线时间
     */

    private String downTime;

    /**
     * 规则类型--对应账户权益类型的枚举数据
     */
    private BenefitClassifyEnum benefitClassifyEnum;


}
