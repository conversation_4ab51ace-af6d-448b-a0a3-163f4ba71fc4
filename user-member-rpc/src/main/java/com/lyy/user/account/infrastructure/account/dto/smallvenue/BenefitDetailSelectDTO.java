package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 权益详情查询
 */
@Data
@Accessors(chain = true)
public class BenefitDetailSelectDTO {

    /**
     * 用户id
     */
    @NotNull(message = "userId不能为空")
    private Long userId;

    /**
     * 商户id
     */
    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    /**
     * 账户id
     */
    @NotNull(message = "accountId不能为空")
    private Long accountId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 商家自定义权益id
     */
//    @NotNull(message = "merchantBenefitClassifyId不能为空")
    private Long merchantBenefitClassifyId;

    /**
     * 商家自定义权益ids
     */
    private List<Long> merchantBenefitClassifyIds;

    private Date startTime;

    private Date endTime;


    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageIndex;

    /**
     * 页长
     */
    @Range(min = 1, max = 100, message = "页长不能超过100")
    private Integer pageSize;

    private List<Long> accountBenefitIds;

    private Boolean countSql;

}
