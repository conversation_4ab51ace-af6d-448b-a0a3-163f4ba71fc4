package com.lyy.user.account.infrastructure.user.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description: 修改场地标签或者设备类型标签DTO
 * @author: qgw
 * @date on 2021/4/19.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class TagDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long tagId;

    /**
     * 商户ID或平台ID
     */
    @NotNull(message = "商户ID或平台ID不能为空")
    private Long merchantId;

    /**
     * 标签名称
     */
    private String oldName;


    /**
     * 标签名称
     */
    @NotEmpty(message = "标签新名称不能为空")
    private String newName;

    /**
     * 标签业务类型
     * @see TagBusinessTypeEnum
     */
    private Integer businessType;

    /**
     * 当前操作人
     */
    private Long operatorId;
}
