package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @ClassName: BenefitRefundResultDTO
 * @description: 权益退款返回实体
 * @author: pengkun
 * @date: 2022/01/12
 **/
@Setter
@Getter
@ToString
public class BenefitRefundResultDTO {
    /**
     * 用户权益消耗明细
     */
    private List<UserAccountRecord> accountRecords;
    /**
     * 用户账户权益明细
     */
    private List<UserAccountBenefit> accountBenefits;
}
