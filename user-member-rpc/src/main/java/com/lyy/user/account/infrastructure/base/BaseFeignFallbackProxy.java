package com.lyy.user.account.infrastructure.base;

import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.resp.RespBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cglib.proxy.InvocationHandler;

import java.lang.reflect.Method;

/**
 * 失败回调的handler
 * <AUTHOR>
 * @className: FallbackProxy
 * @date 2021/4/19
 */
@Slf4j
public class BaseFeignFallbackProxy implements InvocationHandler, java.lang.reflect.InvocationHandler {

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        log.error("调用用户中心 [{}] 接口的 [{}] 方法熔断,param:{}", method.getDeclaringClass().getName(), method.getName(), args);
        return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
    }
}
