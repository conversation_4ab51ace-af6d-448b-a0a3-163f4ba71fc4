package com.lyy.user.account.infrastructure.account.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/6 10:45
 */
@Getter
@Setter
@ToString
public class ConsumeDetailDTO {

    /**
     * 权益类型
     */
    private List<Integer> classify;

    /**
     * 权益id
     */
    private List<Long> benefitId;
    
    /**
     * 权益数值
     */
    private BigDecimal amount;

}
