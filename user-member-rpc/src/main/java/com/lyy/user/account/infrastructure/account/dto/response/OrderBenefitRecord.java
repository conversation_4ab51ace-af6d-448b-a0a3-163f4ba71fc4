package com.lyy.user.account.infrastructure.account.dto.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @ClassName: UserAccountRecord
 * @description: 用户账户消费流水
 * @author: pengkun
 * @date: 2022/01/12
 **/
@Setter
@Getter
@ToString
public class OrderBenefitRecord {
    /**
     * 账户明细id
     */
    private Long accountBenefitId;

    private Long benefitId;
    /**
     * 账户id
     */
    private Long accountId;
    /**
     * 方式 1=加，2=减
     */
    private Integer mode;
    /**
     * 主单号
     */
    private String outTradeNo;

    private String refundNo;
    /**
     * 子单号
     */
    private String subOrderNo;
    /**
     * 实际权益
     */
    private BigDecimal actualBenefit;

    /**
     * 权益实际价值
     */
    private BigDecimal actualValue;

    private Integer classify;

    private Integer classifyWeight;

    private Integer recordType;

    private Long commodityId;

    private String commodityCategoryCode;

    private String source;

    private String description;

}
