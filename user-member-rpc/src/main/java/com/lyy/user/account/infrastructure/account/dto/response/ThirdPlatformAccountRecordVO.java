package com.lyy.user.account.infrastructure.account.dto.response;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/4/23
 */
@Data
public class ThirdPlatformAccountRecordVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 设备id
     */
    private Long equipmentId;

    /**
     * 设备value
     */
    private String equipmentValue;

    /**
     * 外部系统标识： 1.东晙
     */
    private Integer externalSystem;

    /**
     * 事件类型 1：刷卡，2.扫码
     */
    private Short eventType;

    /**
     * 操作类型 1:增加；2：减少
     */
    private Short mode;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private Long updateBy;
}
