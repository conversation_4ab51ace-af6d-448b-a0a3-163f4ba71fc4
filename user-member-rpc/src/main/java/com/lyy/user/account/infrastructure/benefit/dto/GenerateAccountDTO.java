package com.lyy.user.account.infrastructure.benefit.dto;

import com.lyy.user.account.infrastructure.constant.BenefitGenerateTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021/4/6 14:27
 */
@Data
public class GenerateAccountDTO {

    /**
     * 权益生成方式
     */
    @NotNull(message = "权益生成方式")
    private BenefitGenerateTypeEnum generateType;

    /**
     * 商户ID
     */
    @NotNull(message = "商户ID不能为空")
    private Long merchantId;

    /**
     * 商户用户ID
     */
    private Long merchantUserId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 场地ID
     */
    private Long storeId;

    /**
     * 商品类型code
     */
    private String commodityCategoryCode;

    /**
     * 商品所属分类
     */
    private String commodityClassifyCode;

    /**
     * 权益ID集合
     */
    List<Long> benefitIdList;
}
