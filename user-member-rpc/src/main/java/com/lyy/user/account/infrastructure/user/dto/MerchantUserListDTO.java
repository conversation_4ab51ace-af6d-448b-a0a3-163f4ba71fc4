package com.lyy.user.account.infrastructure.user.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.user.dto.tag.TagUserDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商户查询用户信息
 *
 * @author: 蒋成
 * @create 2021/4/16 15:26
 **/
@Setter
@Getter
@ToString
public class MerchantUserListDTO {
    /**
     * 商户用户Id
     */
    // FromStringDeserializer 导致feign报错
    //@JsonDeserialize(using = FromStringDeserializer.class)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 商户Id
     */
    private Long merchantId;


    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 头像
     */
    private String headImg;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 性别
     */
    private String gender;

    /**
     * 手机号码
     */
    private String telephone;

    /**
     *  其他标签
     */
    private List<TagUserDTO> otherTagList;

    /**
     * 场地标签ID
     */
    private List<TagUserDTO> groupTagList;

    /**
     * 设备类型
     */
    private List<TagUserDTO> equipmentTypeTagList;

    /**
     * 上次消费时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date lastConsumeTime;

    /**
     * 累计总消费（此处指实付消费金额，包括充值金额和支付启动的金额）
     */
    private BigDecimal totalConsume;

    /**
     * 总余额
     */
    private BigDecimal totalBalance;

    /**
     * 总币数
     */
    private BigDecimal totalCoin;

    /**
     * 卡数
     */
    private Integer cardNum;

    /**
     * 优惠券数
     */
    private Integer couponNum;

    /**
     * 累计消费金额
     */
    private BigDecimal totalConsumeMoney;

    /**
     * 累计消费余币
     */
    private BigDecimal totalConsumeCoin;

    /**
     * 累计充值金额
     */
    private BigDecimal totalRechargeMoney;

    /**
     * 支付笔数
     */
    private Integer payNum;


    /**
     * 启动次数
     */
    private Integer startupNum;

    /**
     * 是否可用，true:是，false：否
     */
    private Boolean active;
}
