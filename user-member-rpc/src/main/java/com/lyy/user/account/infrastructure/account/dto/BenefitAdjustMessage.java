package com.lyy.user.account.infrastructure.account.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName: BenefitAdjustMessage
 * @description: 权益调整消息实体
 * @author: pengkun
 * @date: 2021/07/01
 **/
@Setter
@Getter
@ToString
public class BenefitAdjustMessage extends AccountBenefitAdjustDTO {

    private BigDecimal totalCoins;

    private BigDecimal balanceCoins;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
