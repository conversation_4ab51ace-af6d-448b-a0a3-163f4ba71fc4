package com.lyy.user.account.infrastructure.account.feign;

import com.lyy.user.account.infrastructure.account.dto.request.ThirdPlatformAccountRecordSaveDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2023/4/23
 */
@FeignClient(name = "user-member-center", fallbackFactory = ThirdPlatformAccountFallback.class)
public interface ThirdPlatformAccountClient {

    /**
     * 增加第三方资金变动记录
     *
     * @param dto DTO
     * @return boolean
     */
    @PostMapping("/third-platform-account/record/add")
    RespBody<Boolean> addThirdPlatformAccountRecord(@RequestBody @Validated ThirdPlatformAccountRecordSaveDTO dto);

}
