package com.lyy.user.account.infrastructure.benefit.dto;

import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import lombok.Data;

import java.util.List;

/**
 * 权益消耗规则保存
 * <AUTHOR>
 * @create 2021/4/6 16:33
 */
@Data
public class ConsumeRuleSaveDTO {

    /**
     * 商户ids
     */
    private List<Long> merchantIds;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 权益类型
     */
    private BenefitClassifyEnum benefitClassify;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 优先级
     */
    private Integer expirePriority;

    /**
     * 是否默认
     */
    private Boolean isDefault;

    /**
     * 操作人
     */
    private Long operator;
}
