package com.lyy.user.account.infrastructure.account.dto.smallvenue;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 会员卡详情
 */
@Data
@Accessors(chain = true)
public class MobileTerminalAccountDetailsDTO extends MobileTerminalAccountListDTO {

    /**
     * 附属卡列表
     */
    private List<MobileTerminalSupplementaryCardDTO> supplementaryCardList;

    /**
     * 储值统计，根据门店分组
     */
    private BenefitStatisticsDetailDTO benefitStatisticsDetail;
}
