package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SmallVenueRecordSelectDTO {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商户id
     */
    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 设备名称
     */
    private String equipmentName;

    /**
     * 商品分类
     */
    private Long goodsClassify;

    /**
     * 商品类型
     */
    private List<Long> goodsTypes;

    /**
     * 门店id
     */
    private List<Long> storeIds;
    /**
     * 操作渠道
     */
    private List<Integer> operationChannels;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 操作记录类型
     */
    @NotNull(message = "operationType不能为空")
    private Integer operationType;

    /**
     * 记录类型
     */
    private List<Integer> recordType;

    /**
     * 备注
     */
    private String description;

    /**
     * 消费方式 （1.刷卡消费 2.微信小程序；3.桌面收银台；4.移动收银台）
     */
    private Integer consumeType;

    /**
     * 商家自定义权益id集合
     */
    private Long merchantBenefitClassifyIds;

    /**
     * 商家自定义权益id集合(merchantBenefitClassifyIds == null 时生效)
     */
    private List<Long> merchantBenefitClassifyIdList;

    /**
     * 会员id
     */
    private Long merchantUserId;


    @NotNull(message = "pageIndex不能为空")
    private Integer pageIndex;

    @NotNull(message = "pageSize不能为空")
    private Integer pageSize;

    /**
     * 需要过滤掉的recordType
     */
    private List<Integer> excludeRecordTypes;

    private Boolean countSql;

}
