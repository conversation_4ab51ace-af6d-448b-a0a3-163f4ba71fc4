package com.lyy.user.account.infrastructure.member.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2022/3/14 10:39
 */
@Data
public class UpdateUserMemberLevelDTO {

    @NotNull
    private Long merchantUserId;

    /**
     * 商户ID
     */
    @NotNull
    private Long merchantId;

    /**
     * 需要变更的会员等级id
     */
    @NotNull
    private Long memberLevelId;

}
