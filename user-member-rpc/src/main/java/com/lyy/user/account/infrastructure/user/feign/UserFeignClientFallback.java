package com.lyy.user.account.infrastructure.user.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.common.DataList;
import com.lyy.user.account.infrastructure.member.dto.MemberBatchQueryRequestDTO;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.user.dto.*;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneQueryParam;
import com.lyy.user.account.infrastructure.user.dto.phone.PlatformUserPhoneRecordDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserAndLevelInfoDTO;
import com.lyy.user.account.infrastructure.user.dto.userInfo.MerchantUserInfoByKeywordDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName: UserFeignClientFallback
 * @description: 用户降级
 * @author: pengkun
 * @date: 2021/04/15
 **/
@Slf4j
@Component
public class UserFeignClientFallback implements FallbackFactory<UserFeignClient> {
    @Override
    public UserFeignClient create(Throwable throwable) {

        if (throwable != null && StringUtils.isNotEmpty(throwable.getMessage())) {
            log.error(throwable.getMessage(), throwable);
        }

        return new UserFeignClient() {

            /**
             * 获取平台用户电话
             * @param param
             * @return
             */
            @Override
            public RespBody<List<PlatformUserPhoneRecordDTO>> list(PlatformUserPhoneQueryParam param) {
                log.error("[新会员] 用户中心调用【list】熔断,param:{}", param);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 根据平台用户id和商户id获取用户信息
             *
             * @param userId     平台用户Id
             * @param merchantId 商户Id
             * @return
             */
            @Override
            public RespBody<UserInfoDTO> getUserInfoByUserIdAndMerchantId(Long userId, Long merchantId) {
                log.error("[新会员] 用户中心调用【getUserInfoByUserIdAndMerchantId】熔断,userId:{},merchantId:{}", userId, merchantId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 根据平台用户id和商户id获取用户信息[内含异步初始化指定商户下的 商户用户统计数据]
             * @param userId    平台用户Id
             * @param merchantId    商户Id
             * @return
             */
            @Override
            public RespBody<UserInfoDTO> getUserInfoByUserIdAndMerchantIdAsyn(Long userId, Long merchantId) {
                log.error("[新会员] 用户中心调用【getUserInfoByUserIdAndMerchantIdAsyn】熔断,userId:{},merchantId:{}", userId, merchantId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 根据平台用户id,商户用户id,商户id获取用户信息
             *
             * @param userId         平台用户Id
             * @param merchantUserId 商户用户Id
             * @param merchantId     商户Id
             * @return
             */
            @Override
            public RespBody<UserInfoDTO> getUserInfoByUserIdAndMerchantUserId(Long userId, Long merchantUserId, Long merchantId) {
                log.error("[新会员] 用户中心调用【getUserInfoByUserIdAndMerchantUserId】熔断,userId:{},merchantUserId:{},merchantId:{}", userId, merchantUserId, merchantId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 根据openId或unionId获取用户信息
             *
             * @param openId     openId
             * @param unionId    unionId
             * @param merchantId 商户Id
             * @return
             */
            @Override
            public RespBody<UserInfoDTO> getUserInfoByOpenIdOrUnionId(String openId, String unionId, Long merchantId) {
                log.error("[新会员] 用户中心调用【getUserInfoByOpenIdOrUnionId】熔断,openId:{},unionId:{},merchantId:{}", openId, unionId, merchantId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 根据手机号和商户id查询可切换的用户列表信息
             *
             * @param telephone  手机号
             * @param merchantId 商户id
             * @return
             */
            @Override
            public RespBody<List<UserInfoDTO>> listByTelephoneAndMerchantId(String telephone, Long merchantId) {
                log.error("[新会员] 用户中心调用【listByTelephoneAndMerchantId】熔断,telephone:{},merchantId:{}", telephone, merchantId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 更新用户绑定的手机号码
             *
             * @param dto
             * @return
             */
            @Override
            public RespBody<Boolean> updateTelephone(MerchantUserDTO dto) {
                log.error("[新会员] 用户中心调用【updateTelephone】熔断,dto:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 用户初始化
             *
             * @param userCreateDTO
             * @return
             */
            @Override
            public RespBody<UserInfoDTO> initUserInfo(UserCreateDTO userCreateDTO) {
                log.error("[新会员] 用户中心调用【initUserInfo】熔断,userCreateDTO:{}", userCreateDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> updateUserInfo(MerchantUserDTO dto) {
                log.error("[新会员] 用户中心调用【updateUserInfo】熔断,MerchantUserDTO:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 更新平台用户信息
             *
             * @param userCreateDTO
             * @return
             */
            @Override
            public RespBody<Boolean> updatePlatformUserInfo(UserCreateDTO userCreateDTO) {
                log.error("[新会员] 用户中心调用【updatePlatformUserInfo】熔断,userCreateDTO:{}", userCreateDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 注销用户
             *
             * @param userId          平台用户id
             * @param merchantUserId    商户用户id
             * @param merchantId      商户id
             * @param operationUserId 操作人id
             * @return
             */
            @Override
            public RespBody<Boolean> deleteUserInfo(Long userId, Long merchantUserId, Long merchantId, Long operationUserId) {
                log.error("[新会员] 用户中心调用【deleteUserInfo】熔断,userId:{},merchantUserId:{},merchantId:{},operationUserId:{}", userId, merchantUserId, merchantId, operationUserId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 创建用户APP信息
             *
             * @param userAppDTO 用户APP信息
             * @return
             */
            @Override
            public RespBody<Boolean> createUserApp(UserAppDTO userAppDTO) {
                log.error("[新会员] 用户中心调用【createUserApp】熔断,userAppDTO:{}", userAppDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 根据openId和appId获取平台用户信息
             * @param openId
             * @param appId
             * @return
             */
            @Override
            public RespBody<UserInfoDTO> getPlatformUserInfoByOpenIdAndAppId(String openId, String appId) {
                log.error("[新会员] 用户中心调用【getPlatformUserInfoByOpenIdAndAppId】熔断,openId:{},appId:{}", openId, appId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 根据id获取商户用户信息
             *
             * @param merchantUserId 商户用户id
             * @param merchantId     商户id
             * @return
             */
            @Override
            public RespBody<MerchantUserDTO> findById(Long merchantUserId, Long merchantId) {
                log.error("[新会员] 用户中心调用【findById】熔断,merchantUserId:{},merchantId:{}", merchantUserId, merchantId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<MerchantUserDTO>> findByUserIds(MemberBatchQueryRequestDTO request) {
                log.error("[新会员] 用户中心调用【findByUserIds】熔断,request:{}", request);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<MerchantUserDTO> findByUserIdAndMerchantId(Long userId, Long merchantId) {
                log.error("[新会员] 用户中心调用【findByUserIdAndMerchantId】熔断,userId:{},merchantId:{}", userId, merchantId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 根据手机号和商户Id获取商户用户信息
             *
             * @param telephone  手机号码
             * @param merchantId 商户Id
             * @return
             */
            @Override
            public RespBody<List<MerchantUserDTO>> findByTelephoneAndMerchantId(String telephone, Long merchantId) {
                log.error("[新会员] 用户中心调用【findByTelephoneAndMerchantId】熔断,telephone:{},merchantId:{}", telephone, merchantId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Page<MerchantUserListDTO>> queryUserListByMerchant(MerchantUserQueryDTO merchantUserQueryDTO) {
                log.error("[新会员] 用户中心调用【queryUserListByMerchant】熔断,param:{}", merchantUserQueryDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Long> countUserListByMerchant(MerchantUserQueryDTO merchantUserQueryDTO) {
                log.error("[新会员] 用户中心调用【countUserListByMerchant】熔断,param:{}", merchantUserQueryDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Void> payoutBenefit(PayoutBenefitDTO payoutWelfareDTO) {
                log.error("[新会员] 用户中心调用【PayoutBenefitDTO】熔断,param:{}", payoutWelfareDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<IndexUserAccountDTO> getIndexAccountInfo(Long merchantUserId, Long merchantId) {
                log.error("[新会员] 用户中心调用【getIndexAccountInfo】熔断,param:merchantUserId:{},merchantId:{}", merchantUserId, merchantId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 根据userId获取平台用户信息
             *
             * @param userId
             * @return
             */
            @Override
            public RespBody<UserInfoDTO> getPlatformUserInfoByUserId(Long userId) {
                log.error("[新会员] 用户中心调用【getPlatformUserInfoByUserId】熔断,param:userId:{}", userId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<MerchantUserInfoByKeywordDTO> searchByKeywords(Long merchantId, String keyWords) {
                log.error("[新会员] 用户中心调用【searchByKeywords】熔断,param:merchantId:{},keyWords:{}", merchantId, keyWords);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<MerchantUserInfoByKeywordDTO> searchByKeywordsAndType(Long merchantId, String keyWords, Integer keyWordsType) {
                log.error("[新会员] 用户中心调用【searchByKeywordsAndType】熔断,param:merchantId:{},keyWords:{},keyWordsType:{}", merchantId, keyWords, keyWordsType);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> updateUserPassword(MerchantUserUpdatePasswordDTO merchantUserUpdatePasswordDTO) {
                log.error("[新会员] 用户中心调用【updateUserPassword】熔断,param:MerchantUserUpdatePasswordDTO:{}", merchantUserUpdatePasswordDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> checkUserPassword(MerchantUserCheckPasswordDTO merchantUserCheckPasswordDTO) {
                log.error("[新会员] 用户中心调用【checkUserPassword】熔断,param:merchantUserCheckPasswordDTO:{}", merchantUserCheckPasswordDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> updateMerchantUserInfo(UpdateMerchantUserInfoDTO updateMerchantUserInfoDTO) {
                log.error("[新会员] 用户中心调用【updateMerchantUserInfo】熔断,param:updateMerchantUserInfoDTO:{}", updateMerchantUserInfoDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<DataList<SmallVenueUserDTO>> selectSmallVenueUserList(SmallVenueUserListSelectDTO smallVenueUserListSelectDTO) {
                log.error("[新会员] 用户中心调用【selectSmallVenueUserList】熔断,param:smallVenueUserListSelectDTO:{}", smallVenueUserListSelectDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<DataList<SmallVenueUserMobileVO>> smallVenueMobileUserList(SmallVenueMobileUserListSelectDTO smallVenueMobileUserListSelectDTO) {
                log.error("[新会员] 用户中心调用【smallVenueMobileUserList】熔断,param:smallVenueMobileUserListSelectDTO:{}", smallVenueMobileUserListSelectDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> updateUserTelephone(MerchantUserUpdateTelephoneDTO merchantUserUpdateTelephoneDTO) {
                log.error("[新会员] 用户中心调用【updateUserTelephone】熔断,param:merchantUserUpdateTelephoneDTO:{}", merchantUserUpdateTelephoneDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<MerchantUserDTO> getMerchantUserByCondition(MerchantIntegralUserQueryDTO queryDTO) {
                log.error("[新会员] 用户中心调用【getMerchantUserByCondition】熔断,param:merchantUserUpdateTelephoneDTO:{}", queryDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<Boolean> mobileTerminalMerchantUserCancellation(MobileTerminalUserCancellationDTO cancellationDTO) {
                log.error("[新会员] 用户中心调用【mobileTerminalMerchantUserCancellation】熔断,param:cancellationDTO:{}", cancellationDTO);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            /**
             * 根据openId和appId获取用户信息
             *
             * @param openId     openId
             * @param appId      appId
             * @param merchantId 商户id
             * @return
             */
            @Override
            public RespBody<UserInfoDTO> getUserInfoByOpenIdAndAppId(String openId, String appId, Long merchantId) {
                log.error("[新会员] 用户中心调用【getUserInfoByOpenIdAndAppId】熔断,openId:{},appId:{},merchantId:{}", openId, appId, merchantId);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<MerchantUserAndLevelInfoDTO> searchBaseInfoKeywordsAndType(Long merchantId, String keyWords,
                    Integer keyWordsType) {
                log.error("[新会员] 用户中心调用【searchBaseInfoKeywordsAndType】熔断,param:merchantId:{},keyWords:{},keyWordsType:{}", merchantId, keyWords, keyWordsType);
                return RespBody.fail(GlobalErrorCode.FALLBACK_EXCEPTION);
            }

        };
    }
}
