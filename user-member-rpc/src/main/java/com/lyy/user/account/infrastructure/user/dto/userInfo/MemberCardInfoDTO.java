package com.lyy.user.account.infrastructure.user.dto.userInfo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 会员卡信息
 */
@Data
@Accessors(chain = true)
public class MemberCardInfoDTO {

    private Long accountId;

    private String cardNo;

    private Date downTime;

    private Long storeId;

    private Integer status;

    private Long parentAccountId;

    private Boolean defaultFlag;

    private List<MemberCardInfoBenefitDTO> benefit;

    private List<SupplementCardsDTO> supplementCards;
}
