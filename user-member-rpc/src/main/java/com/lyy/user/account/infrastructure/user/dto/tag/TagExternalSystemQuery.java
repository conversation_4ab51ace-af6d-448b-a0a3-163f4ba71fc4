package com.lyy.user.account.infrastructure.user.dto.tag;

import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/7/19 - 14:52
 */
@Data
public class TagExternalSystemQuery {

    /**
     * userId与merchantUserId必填一个，merchantUserId非空时只使用merchantUserId不使用userId
     */
    private Long merchantUserId;

    /**
     * userId与merchantUserId必填一个，merchantUserId非空时只使用merchantUserId不使用userId
     */
    private Long userId;

    @NotNull(message = "merchantId不允许为空")
    private Long merchantId;

    private String externalSystem;

}
