package com.lyy.user.account.infrastructure.user.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * @ClassName: PlatformUserDTO
 * @description: 平台用户信息
 * @author: pengkun
 * @date: 2021/04/25
 **/
@Setter
@Getter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformUserDTO {
    /**
     * 平台用户Id
     */
    private Long lyyUserId;
    /**
     * 手机号码
     */
    private String telephone;
    /**
     * 头像
     */
    private String headImg;
    /**
     * 昵称
     */
    private String name;
    /**
     * 性别
     */
    private String gender;
    /**
     * openId
     */
    private String openid;
    /**
     * 生日
     */
    private String birthday;
    /**
     * unionid
     */
    private String unionid;
    /**
     * 用户类型
     * W 微信
     * A 支付宝
     * J 京东
     * U 云闪付
     * O 其他渠道
     */
    private String userType;
    /**
     * aapId
     */
    private String appId;
    /**
     * 更新操作人
     */
    private Long updateby;
    /**
     * 是否有效
     */
    private String isactive;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date created;
    /**
     * 更新日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updated;
    /**
     * 密码
     */
    private String password;
    /**
     *  城市Id
     */
    private Long lyyCityId;
    /**
     * 城市信息
     */
    private String provinceCity;

    private Integer adOrgId;

    /**
     * 省份Id
     */
    private Long provinceId;
    /**
     * 区域Id
     */
    private Long regionId;
}
