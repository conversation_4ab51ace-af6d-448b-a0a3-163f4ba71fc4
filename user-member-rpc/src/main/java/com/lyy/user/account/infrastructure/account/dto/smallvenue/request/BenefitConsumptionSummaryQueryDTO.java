package com.lyy.user.account.infrastructure.account.dto.smallvenue.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 订单权益消费充值汇总查询
 *
 * <AUTHOR>
 * @since 2022/3/28 - 16:39
 */
@Data
public class BenefitConsumptionSummaryQueryDTO {

    @NotNull(message = "商户ID不能为空")
    private Long merchantId;

    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @NotBlank(message = "订单编号不能为空")
    private String orderNo;

    /**
     * true 订单退款，false 则为订单充值
     */
    private boolean refund;

    private Date date;

    /**
     * 订单抵扣的权益中存在赠送逻辑的权益
     */
    private List<Long> presentationBenefitClassifyIds;

}
