package com.lyy.user.account.infrastructure.member.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @className: MemberRangeDTO
 * @date 2021/3/30
 */
@Data
public class MemberRangeDTO {
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long id;
    /**
     * 会员组ID
     */
    @JsonSerialize(using =ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long memberGroupId;

    /**
     * 适用类型(1 品类,2 场地,3 设备,4 商品)
     */
    private Short applicable;

    /**
     * 关联关系ID
     */

    private Long associatedId;
}
