package com.lyy.user.account.infrastructure.report.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SmallVenueMemberStoreValueRecordDto {
    private Long accountId;
    private String name;
    private String phone;
    private Long userId;
    private Long merchantUserId;
    private String cardNo;
    private Integer recordType;
    private String storeValueName;
    private Long storeValueId;
    private BigDecimal initialBenefit;
    private BigDecimal actualBenefit;
    private Integer mode;
    private Integer operationChannel;
    private String assetEquipmentName;
    private String terminalName;
    private String createName;
    private String createTime;
    private String orderNo;
    private String description;
    private Long merchantId;
    private Long storeId;
    private String storeName;
    private String subOrderNo;
    private String equipmentValue;
    private String equipmentName;
    private Long accountBenefitId;
    private Long goodsId;
    private BigDecimal accountInitialBalance;
    private BigDecimal accountInitialNum;
    private Long merchantBenefitClassifyId;
}
