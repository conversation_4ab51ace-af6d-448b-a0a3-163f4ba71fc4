package com.lyy.user.account.infrastructure.user.dto.tag;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/4/17
 */
@Data
public class TagOfExternalSystemVO {

    /**
     * 商户ID
     */
    private Long merchantId;
    private Long userId;

    /**
     * 商户用户ID
     */
    private Long merchantUserId;

    /**
     * 对应的-平台类型
     */
    private Integer externalSystem;

    /**
     * 对应的-用户来源标签ID
     */
    private Long externalUserTagId;

    /**
     * 对应的-内部标签（不对外展示）
     */
    private Long externalBalanceNullTagId;

    /**
     * 是否有对应的-用户来源标签
     */
    private Boolean externalUser;

    /**
     * 是否有对应的-内部标签（不对外展示）
     */
    private Boolean externalBalanceNull;
}
