package com.lyy.user.account.infrastructure.account.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName: BenefitScopeQueryDTO
 * @description: 消费记录使用权益的使用范围
 * @author: pengkun
 * @date: 2022/04/29
 **/
@Setter
@Getter
@ToString
public class RecordBenefitScopeQueryDTO {
    @NotNull(message = "商户ID不能为空")
    private Long merchantId;
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    /**
     * 支付订单号
     */
    private String outTradeNo;
    /**
     * 调整类型
     * @see com.lyy.user.account.infrastructure.constant.AdjustTypeEnum
     */
    private Integer mode;
    /**
     * 权益类型
     */
    private List<Integer> classifies;
    /**
     *
     */
    private Integer applicable;
}
