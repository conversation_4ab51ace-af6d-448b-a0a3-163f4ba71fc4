package com.lyy.user.account.infrastructure.user.dto;

import com.lyy.user.account.infrastructure.user.dto.tag.TagUserDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @ClassName: MerchantUserInfoDTO
 * @description: 商户用户
 * @author: qgw
 * @date: 2021/03/31
 **/
@Setter
@Getter
@ToString
public class MerchantUserInfoDTO {
    /**
     * 商户用户Id
     */
    private Long id;

    /**
     * 商户Id
     */
    private Long merchantId;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 是否可用，true:是，false：否
     */
    private Boolean active;
    /**
     * 创建人
     */
    private Long createdby;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 手机号码
     */
    private String telephone;
    /**
     * 描述
     */
    private String description;

    /**
     * 对应的标签List
     */
    private List<TagUserDTO> tagUserDTOS;
}
