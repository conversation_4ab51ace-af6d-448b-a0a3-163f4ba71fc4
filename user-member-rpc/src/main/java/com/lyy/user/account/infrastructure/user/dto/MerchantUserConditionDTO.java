package com.lyy.user.account.infrastructure.user.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import com.lyy.user.account.infrastructure.base.StringArrayToLongDeserializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: qgw
 * @date on 2021-06-23.
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MerchantUserConditionDTO {

    /**
     * 搜索关键字  支持微信昵称，会员ID,手机号码
     */
    private String keyword;

    /**
     * 搜索条件:1:昵称，2：id，3手机号
     */
    private Integer keywordType;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 场地标签集合
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> groupTagIdList;

    /**
     * 普通标签
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> otherTagIdList;
    /**
     * 设备类型标签
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> equipmentTypeTagIdList;

    /**
     * 性别标签ID
     */
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> sexTagIdList;

    /**
     * 累计消费最小值
     */
    private Integer totalConsumeMoneyMin;

    /**
     * 累计消费最大值
     */
    private Integer totalConsumeMoneyMax;

    /**
     * 余额最小值
     */
    private Integer totalBalanceMin;

    /**
     * 余额最大值
     */
    private Integer totalBalanceMax;

    /**
     * 余币最小值
     */
    private Integer totalCoinMin;

    /**
     * 余币最大值
     */
    private Integer totalCoinMax;

    /**
     * 按最近消费时间排序
     */
    private String lastConsumeTimeSort;

    /**
     * 按消费金额排序（此处指实付消费金额，包括充值金额和支付启动的金额）
     */
    private String totalConsumeMoneySort;

    /**
     * 余额排序
     */
    private String totalBalanceSort;

    /**
     * 余币排序
     */
    private String totalCoinSort;
}
