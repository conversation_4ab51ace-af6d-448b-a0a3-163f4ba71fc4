package com.lyy.user.account.infrastructure.user.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Data;

@Data
public class TagSimpleInfoDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long tagId;
    private String name;
    private Long merchantId;
    /**
     * 标签类型，1:自动, 2:手动
     *
     * @see com.lyy.user.account.infrastructure.constant.TagCategoryEnum
     */
    private Integer category;

    /**
     * 标签的业务类型
     *
     * @see com.lyy.user.account.infrastructure.constant.TagBusinessTypeEnum
     */
    private Integer businessType;


    private String businessTypeName;
}
