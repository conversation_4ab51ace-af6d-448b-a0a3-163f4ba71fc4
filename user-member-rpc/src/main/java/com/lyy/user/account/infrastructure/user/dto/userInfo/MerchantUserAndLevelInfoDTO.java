package com.lyy.user.account.infrastructure.user.dto.userInfo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MerchantUserAndLevelInfoDTO {

    private String name;

    private Long userId;

    private String headImg;

    private Date createTime;

    private String userType;

    private String telephone;

    private String birthday;

    private String gender;

    private String provinceCity;

    private Long merchantUserId;

    private String address;

    private Long provinceId;

    private Long regionId;

    private Long cityId;

    private List<TagInfoDTO> tags;
}
