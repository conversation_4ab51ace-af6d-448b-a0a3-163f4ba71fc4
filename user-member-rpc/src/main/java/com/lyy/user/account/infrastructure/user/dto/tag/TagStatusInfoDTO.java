package com.lyy.user.account.infrastructure.user.dto.tag;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.lyy.user.account.infrastructure.base.StringArrayToLongDeserializer;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: qgw
 * @date on 2021-05-17.
 * @Version: 1.0
 */
@ToString
@Getter
@Setter
@NoArgsConstructor
public class TagStatusInfoDTO {

    @NotEmpty(message = "请选择标签")
    @JsonDeserialize(using = StringArrayToLongDeserializer.class)
    private List<Long> tagIds;


    @NotNull(message = "请指定商家")
    private Long merchantId;
}
