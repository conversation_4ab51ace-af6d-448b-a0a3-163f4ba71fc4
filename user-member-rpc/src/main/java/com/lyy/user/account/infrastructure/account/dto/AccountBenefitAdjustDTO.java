package com.lyy.user.account.infrastructure.account.dto;

import com.lyy.user.account.infrastructure.benefit.dto.BenefitScopeSaveDTO;
import com.lyy.user.account.infrastructure.constant.AdjustTypeEnum;
import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 权益调整保存DTO
 *
 *
 * <AUTHOR>
 * @since 2021/3/31 16:43
 */
@Getter
@Setter
@ToString
public class AccountBenefitAdjustDTO {

    /**
     * 调整记录id（权益账户表ID）
     */
    private Long id;

    /**
     * 调整类型
     * @see com.lyy.user.account.infrastructure.constant.AdjustTypeEnum
     */
    private AdjustTypeEnum adjustType;

    /**
     * 调整数量
     */
    private BigDecimal amount;

    /**
     * 是否清空余额
     */
    private Boolean isEmpty;

    /**
     * 权益类型
     */
    private Integer classify;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 商户用户id
     */
    private Long merchantUserId;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 设备id
     */
    private Long equipmentId;

    /**
     * 支付单号
     */
    private String outTradeNo;

    /**
     * 业务单号
     */
    private String orderNo;

    private String subOrderNo;

    private String refundNo;

    /**
     * 变更来源
     */
    private String resource;

    /**
     * 操作者
     */
    private Long operator;

    /**
     * 权益ID,此属性维护不准，停止使用
     * @since 2021-06-17
     */
    @Deprecated
    private Long benefitId;

    /**
     * 开始时间
     */
    private String upTime;

    /**
     * 结束时间
     */
    private String downTime;

    /**
     * 失效时间类型
     */
    private ExpiryDateCategoryEnum expiryDateCategory;

    /**
     * 账户表ID --用于商家券
     */
    private Long accountId;
    /**
     * 备注
     */
    private String description;
    /**
     * 交易类型
     */
    private Integer tradeType;

    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;
    /**
     * 商品id
     */
    private Long commodityId;
    /**
     * 商品名称
     */
    private String commodityName;
    /**
     * 场地/店铺名称
     */
    private String storeName;
    /**
     * 设备类型id
     */
    private Long equipmentTypeId;
    /**
     * 设备类型名称
     */
    private String equipmentTypeName;
    /**
     * 设备编号
     */
    private String equipmentValue;

    /**
     * 机台号
     */
    private Integer groupNumber;

    /**
     * 记录类型
     * @see com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum
     */
    @NotNull(message = "记录类型不能为空")
    private Integer recordType;


    /**
     * 权益ID 与上面 benefitId一样,但为了区别之前的
     */
    private Long benfitId;

    /**
     * 扣减顺序
     */
    private Integer sort;

    /**
     * 适用范围
     *  有值则是固定范围；
     *  没值则是通用场地，不限制
     */
    private List<BenefitScopeSaveDTO> benefitScopeList;

    /**
     * 是否增加总数
     */
    private Boolean addTotal;

    /**
     * 是否合并到旧批次权益
     */
    private Boolean mergeOld;

    /**
     * 排除打标签
     */
    private Boolean excludeUserTag;
}
