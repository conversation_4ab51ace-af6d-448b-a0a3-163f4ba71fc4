package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SmallVenueAccountRecordSaveDTO {

    /**
     * 平台用户id
     */
    @NotNull(message = "userId不能为空")
    private Long userId;
    /**
     * 商户
     */
    @NotNull(message = "merchantId不能为空")
    private Long merchantId;

    /**
     * 店铺
     */
    private Long storeId;

    /**
     * 业务单号
     */
    private String orderNo;


    /**
     * 方式1=加，2=减
     */
    @NotNull(message = "mode不能为空")
    private Integer mode;

    /**
     * 备注
     */
    private String description;

    /**
     * 实付金额/实兑换储值/回收储值数量
     */
    @NotNull(message = "actualBenefit不能为空")
    private BigDecimal actualBenefit;


    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 操作人id
     */
    private Long operatorId;
    /**
     * 操作人名称
     */
    private String operatorName;
    /**
     * 卡号
     */
    private String cardNo;
    /**
     * 退单号
     */
    private String refundNo;
    /**
     * 商品id
     */
    private Long goodsId;
    /**
     * 商品所属分类
     */
    private Long goodsClassify;
    /**
     * 商品数量
     */
    private BigDecimal goodsNum;
    /**
     * 优惠金额
     */
    private BigDecimal discountsAmount;
    /**
     * 操作设备名称
     */
    private String operationEquipmentName;
    /**
     * 记录类型（1.商品购买记录 2.商品兑换记录；3.商品回收记录；4.储值变更记录（储值变更记录包括设备消费记录））
     */
    private Integer operationType;
    /**
     * 操作渠道（1.微信小程序；2.桌面收银台；3.移动收银台）
     */
    private Integer operationChannel;

    /**
     * 权益类型
     */
    @NotNull(message = "benefitClassify不能为空")
    private Integer benefitClassify;

    /**
     * 期初权益
     */
    @NotNull(message = "initialBenefit不能为空")
    private BigDecimal initialBenefit;

    /**
     * 权益
     */
    @NotNull(message = "originalBenefit不能为空")
    private BigDecimal originalBenefit;

    /**
     * 账户流水类型
     *
     * @see com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum
     */
    @NotNull(message = "recordType不能为空")
    private Integer recordType;

    /**
     * 商品自定义权益id
     */
    private Long merchantBenefitClassifyId;

    /**
     * 商品自定义权益名称
     */
    private String merchantBenefitClassifyName;

    /**
     * 设备名称
     */
    private String equipmentName;

    /**
     * 设备编号
     */
    private String equipmentValue;

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 商品单价
     */
    private BigDecimal goodsPrice;

    /**
     * 权益id
     */
    @NotNull(message = "权益id不能为空")
    private Long benefitId;

    /**
     * 账户id
     */
    @NotNull(message = "accountId不能为空")
    private Long accountId;

    /**
     * 商品类型名称
     */
    private String goodsTypeName;

    /**
     * 商品所属分类名称
     */
    private String goodsClassifyName;


    /**
     * 商品类型id
     */
    private Long goodsTypeId;

    /**
     * 交易类型
     */
    private Integer tradeType;

    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;


    /**
     * 消费方式
     */
    private Integer consumeType;

}
