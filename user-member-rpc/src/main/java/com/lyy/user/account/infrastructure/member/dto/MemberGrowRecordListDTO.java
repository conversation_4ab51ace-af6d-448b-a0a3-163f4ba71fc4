package com.lyy.user.account.infrastructure.member.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 用户成长值
 * @author: qgw
 * @date on 2021/4/7.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class MemberGrowRecordListDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long id;
    /**
     * 平台用户ID
     */
    @JsonSerialize(using =ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long userId;

    /**
     * 商户用户ID
     */
    @JsonSerialize(using =ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long merchantUserId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 成长值
     */
    private Long growValue;

    /**
     * 消费金额
     */
    private BigDecimal money;

    /**
     * 交易单号
     */
    private String outTradeNo;

    /**
     * 来源
     */
    private String resources;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 会员ID
     */
    @JsonSerialize(using =ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long memberId;

    private Long initGrowValue;
    /**
     * @see com.lyy.user.account.infrastructure.constant.MemberGrowRecordModeEnum
     */
    private Integer mode;
}
