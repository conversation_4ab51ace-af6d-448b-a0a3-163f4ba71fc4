package com.lyy.user.account.infrastructure.member.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @className: MemberLiftingRuleDTO
 * @date 2021/3/30
 */
@Data
public class MemberLiftingRuleDTO {
    /**
     * 升降级策略
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long memberLiftingId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 超过日期范围
     */
    private Short rangeDate;

    /**
     * 策略(1 登录次数,2 支付笔数,3 消费金额
     */
    private Short category;
    /**
     * 范围值,若为消费金额时，单位为元
     */
    private BigDecimal rangeValue;

    /**
     * 判断条件，true：为符合该条件，主要用于升级判断，false：为不符合该条件，主要用于降级判断
     */
    private Boolean judgeCondition;

}
