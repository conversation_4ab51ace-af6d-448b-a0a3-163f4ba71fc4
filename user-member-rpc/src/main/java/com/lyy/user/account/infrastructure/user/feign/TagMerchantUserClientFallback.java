package com.lyy.user.account.infrastructure.user.feign;

import com.lyy.error.constant.GlobalErrorCode;
import com.lyy.user.account.infrastructure.resp.RespBody;
import com.lyy.user.account.infrastructure.user.dto.tag.TagExternalSystemQuery;
import com.lyy.user.account.infrastructure.user.dto.tag.TagOfExternalSystemDTO;
import com.lyy.user.account.infrastructure.user.dto.tag.TagOfExternalSystemVO;
import feign.hystrix.FallbackFactory;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/4/18
 */
@Slf4j
@Component
public class TagMerchantUserClientFallback implements FallbackFactory<TagMerchantUserClient> {

    @Override
    public TagMerchantUserClient create(Throwable throwable) {
        if (throwable != null && StringUtils.isNotEmpty(throwable.getMessage())) {
            log.error(throwable.getMessage(), throwable);
        }
        return new TagMerchantUserClient() {

            @Override
            public RespBody<TagOfExternalSystemVO> getTagOfExternalSystem(TagOfExternalSystemDTO dto) {
                log.error("[新会员] getTagOfExternalSystem接口熔断,param:{}", dto);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }

            @Override
            public RespBody<List<TagOfExternalSystemVO>> listTagOfExternalSystem(TagExternalSystemQuery query) {
                log.error("[新会员] listTagOfExternalSystem,param:{}", query);
                return RespBody.fail(GlobalErrorCode.INTERNAL_SERVER_ERROR);
            }
        };
    }
}
