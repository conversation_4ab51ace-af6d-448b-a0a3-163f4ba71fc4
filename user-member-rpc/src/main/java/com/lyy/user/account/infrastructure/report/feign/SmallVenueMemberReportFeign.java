package com.lyy.user.account.infrastructure.report.feign;


import com.lyy.user.account.infrastructure.common.DataList;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberCardReportDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberCardReportQueryDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberCardStoreValueReportDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberReportDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberReportQueryDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberStoreValueRecordDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberStoreValueRecordQueryDto;
import com.lyy.user.account.infrastructure.report.dto.SmallVenueMemberStoreValueReportDto;
import com.lyy.user.account.infrastructure.resp.RespBody;
import java.math.BigDecimal;
import javax.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
  <AUTHOR>
 */
@FeignClient(name = "user-member-center",fallbackFactory = SmallVenueMemberReportFeignFallback.class)
public interface SmallVenueMemberReportFeign {

    @PostMapping("/rest/report/member/page")
    public RespBody<DataList<SmallVenueMemberReportDto>> pageQueryMemberReport(@RequestBody @Valid SmallVenueMemberReportQueryDto smallVenueMemberReportQueryDto) ;

    @PostMapping("/rest/report/member/total")
    public RespBody<SmallVenueMemberStoreValueReportDto> totalQueryMemberReport(@RequestBody @Valid SmallVenueMemberReportQueryDto smallVenueMemberReportQueryDto) ;

    @PostMapping("/rest/report/card/page")
    public RespBody<DataList<SmallVenueMemberCardReportDto>> pageQueryMemberCardReport(@RequestBody @Valid SmallVenueMemberCardReportQueryDto smallVenueMemberCardReportQueryDto) ;

    @PostMapping("/rest/report/card/total")
    public RespBody<SmallVenueMemberCardStoreValueReportDto> totalQueryMemberCardReport(@RequestBody @Valid SmallVenueMemberCardReportQueryDto smallVenueMemberCardReportQueryDto) ;
    @PostMapping("/rest/report/storeValue/page")
    public RespBody<DataList<SmallVenueMemberStoreValueRecordDto>> pageQueryMemberStoreValueReport(@RequestBody @Valid SmallVenueMemberStoreValueRecordQueryDto smallVenueMemberStoreValueRecordQueryDto) ;
    @PostMapping("/rest/report/storeValue/total")
    public RespBody<BigDecimal> totalQueryMemberStoreValueReport(@RequestBody @Valid SmallVenueMemberStoreValueRecordQueryDto smallVenueMemberStoreValueRecordQueryDto);

    @PostMapping("/rest/report/member/page/v2")
    public RespBody<DataList<SmallVenueMemberReportDto>> pageQueryMemberReportV2(@RequestBody @Valid SmallVenueMemberReportQueryDto smallVenueMemberReportQueryDto) ;

}
