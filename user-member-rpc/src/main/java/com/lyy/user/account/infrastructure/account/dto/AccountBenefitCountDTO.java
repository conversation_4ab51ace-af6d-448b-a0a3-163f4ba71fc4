package com.lyy.user.account.infrastructure.account.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 账号权益统计信息
 * <AUTHOR>
 * @className: AccountBenefitCountDTO
 * @date 2021/6/11
 */
@Data
public class AccountBenefitCountDTO {

    /**
     * 生效中权益数量
     */
    private BigDecimal activeBalance;
    /**
     * 已消耗权益数量
     */
    private BigDecimal consumeBalance;
    /**
     * 已过期权益数量
     */
    private BigDecimal overdueBalance;

    public AccountBenefitCountDTO() {
        activeBalance = BigDecimal.ZERO;
        consumeBalance = BigDecimal.ZERO;
        overdueBalance = BigDecimal.ZERO;
    }
}
