package com.lyy.user.account.infrastructure.benefit.dto;

import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import com.lyy.user.account.infrastructure.constant.BenefitGenerateTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021/4/6 10:32
 */
@Data
public class BenefitRuleSaveDTO {

    private Long id;

    /**
     * 权益ID
     */
    private Long benefitId;

    /**
     * 生成方式
     */
    private BenefitGenerateTypeEnum generateType;


    /**
     * 权益分类
     */
    private BenefitClassifyEnum benefitClassify;

    /**
     * 权益值
     */
    private Integer ruleValue;

    /**
     * 权益单位
     */
    private String ruleUnit;
}
