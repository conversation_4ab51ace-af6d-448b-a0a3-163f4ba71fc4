package com.lyy.user.account.infrastructure.member.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SmallVenueMemberLevelInfoDTO {

    /**
     * 用户等级id
     */
    private Long memberLevelId;

    /**
     * 用户等级名称
     */
    private String memberLevelName;

    /**
     * 成长值
     */
    private Long growValue;

    /**
     * 距离升级差的成长值
     */
    private Long lackValue;
}
