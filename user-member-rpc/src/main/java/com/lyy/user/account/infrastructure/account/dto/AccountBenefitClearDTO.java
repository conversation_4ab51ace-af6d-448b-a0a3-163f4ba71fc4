package com.lyy.user.account.infrastructure.account.dto;

import com.lyy.user.account.infrastructure.constant.ExpiryDateCategoryEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 权益清除保存DTO
 *
 * <AUTHOR>
 * @since 2021/3/31 16:43
 */
@Getter
@Setter
@ToString
public class AccountBenefitClearDTO {

    /**
     * 调整数量
     */
    private BigDecimal amount;

    /**
     * 是否清空余额
     */
    private Boolean isEmpty;

    /**
     * 权益类型
     */
    private Integer classify;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 商户用户id
     */
    private Long merchantUserId;

    /**
     * 店铺id
     */
    private Long storeId;


    /**
     * 支付单号
     */
    private String outTradeNo;

    /**
     * 业务单号
     */
    private String orderNo;

    /**
     * 变更来源
     */
    private String resource;

    /**
     * 操作者
     */
    private Long operator;


    /**
     * 开始时间
     */
    private String upTime;

    /**
     * 结束时间
     */
    private String downTime;

    /**
     * 失效时间类型
     */
    private ExpiryDateCategoryEnum expiryDateCategory;

    /**
     * 备注
     */
    private String description;

    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;


    /**
     * 记录类型
     * @see com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum
     */
    @NotNull(message = "记录类型不能为空")
    private Integer recordType;



}
