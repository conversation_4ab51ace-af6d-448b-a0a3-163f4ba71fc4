package com.lyy.user.account.infrastructure.statistics.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 类描述：查询条件
 * <p>
 *
 * <AUTHOR>
 * @since 2021/06/26 11:44
 */
@Getter
@Setter
@ToString
public class UserStatisticsConditionDTO {

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    private Long userId;

    /**
     * 商户id
     */
    @NotNull(message = "商户id不能为空")
    private Long merchantId;


    private Long merchantUserId;
}
