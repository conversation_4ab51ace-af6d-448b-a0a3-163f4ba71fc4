package com.lyy.user.account.infrastructure.account.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 类描述：
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/1 15:39
 */
@Getter
@Setter
@ToString
public class AccountBenefitResultDTO {



    /**
     * 账户权益余额
     */
    private BigDecimal balance;


    private Integer classify;


    private String classifyName;

}
