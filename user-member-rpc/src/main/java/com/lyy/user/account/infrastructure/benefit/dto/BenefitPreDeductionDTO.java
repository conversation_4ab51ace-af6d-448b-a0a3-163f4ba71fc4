package com.lyy.user.account.infrastructure.benefit.dto;

import com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @author: qgw
 * @date on 2021/10/15.
 * @Version: 1.0
 */
@Slf4j
@Getter
@Setter
@ToString
public class BenefitPreDeductionDTO {


    /**
     * 权益分类
     */
    private BenefitClassifyEnum benefitClassify;


    private Long benefitId;

    /**
     * 待扣除权益数量
     */
    private BigDecimal consume;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 账号权益明细id
     */
    private Long accountBenefitId;

    /**
     * 适用范围
     */
    private List<BenefitScopeSaveDTO> benefitScopeList;

}
