package com.lyy.user.account.infrastructure.statistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/6/13
 */
@Data
public class MerchantUserRankConditionDTO {

    private Integer distributorId;
    private Long lyyUserId;
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date startDate;
    private Integer limit;
}
