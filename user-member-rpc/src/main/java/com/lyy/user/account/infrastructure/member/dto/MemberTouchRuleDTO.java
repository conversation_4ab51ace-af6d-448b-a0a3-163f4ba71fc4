package com.lyy.user.account.infrastructure.member.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 会员触发规则
 * <AUTHOR>
 * @className: MemberTouchRuleDTO
 * @date 2021/4/6
 */
@Data
public class MemberTouchRuleDTO {

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    private Long userId;
    @NotNull(message = "商户id不能为空")
    private Long merchantId;
    /**
     * 策略(1 登录次数,2 支付笔数,3 消费金额
     */
    @NotNull(message = "策略信息不能为空")
    private Short category;
    /**
     * 范围值,若为消费金额时，单位为元
     */
    private BigDecimal rangeValue;

    /**
     * 其他记录信息，每次触发记录都要唯一（如加订单号信息）,要用于退单时的回退操作
     */
    private String otherInfo;

    /**
     * 描述信息，用于描述此处触发规则的内容
     */
    private String description;

    @NotNull
    private MemberGroupRangCheckDTO memberGroupRangCheckDTO;

    /**
     * 订单号
     */
    private String outTradeNo;
}
