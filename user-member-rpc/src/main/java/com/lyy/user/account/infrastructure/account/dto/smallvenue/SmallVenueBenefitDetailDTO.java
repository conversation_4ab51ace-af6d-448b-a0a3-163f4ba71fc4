package com.lyy.user.account.infrastructure.account.dto.smallvenue;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * 小场地权益详情
 */
@Data
@Accessors(chain = true)
public class SmallVenueBenefitDetailDTO {

    private Long merchantUserId;

    private String downTime;

    private String upTime;

    private Date createTime;

    private Long storeId;

    private BigDecimal total;

    private BigDecimal balance;

    private Long useRuleId;

    private BigDecimal todayUseNum;

    private String cardNo;

    private Long accountBenefitId;

    private Long merchantBenefitClassifyId;

    private Integer expiryDateCategory;
}
