package com.lyy.user.account.infrastructure.account.dto.request;


import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;



@Data
public class AccountAdjustRecordQueryDTO {


    private Integer pageIndex;

    private Integer pageSize;

    private Long merchantId;

    private Integer adUserId;

    /**
     * 搜索关键字
     */
    private String keyword;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createDate;

    /**
     * 数据1-增加,2-减少,3-清空
     */
    private Integer mode;
    /**
     * 1-余额，2-余币
     */
    private Integer benefitClassify;




}
