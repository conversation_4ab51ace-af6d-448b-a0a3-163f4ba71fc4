package com.lyy.user.account.infrastructure.user.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * @ClassName: UserAppDTO
 * @description:
 * @author: pengkun
 * @date: 2021/04/19
 **/
@Setter
@Getter
@ToString
public class UserAppDTO {

    private Long lyyUserAppId;

    private Long lyyUserId;

    private String openid;

    private String appId;

    private String unfollow;

    private String description;

    private Long adOrgId;

    private Long adClientId;

    private String isactive;

    private Date created;

    private Long createdby;

    private Date updated;

    private Long updatedby;
}
