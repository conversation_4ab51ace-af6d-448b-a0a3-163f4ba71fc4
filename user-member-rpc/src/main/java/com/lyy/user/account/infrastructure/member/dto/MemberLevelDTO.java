package com.lyy.user.account.infrastructure.member.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 会员等级DTO
 * <AUTHOR>
 * @date 2021/3/30
 */
@Data
public class MemberLevelDTO {
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long id;
    /**
     * 商户ID
     */
    @NotNull
    @JsonSerialize(using =ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long merchantId;
    /**
     * 等级名称
     */
    private String name;

    /**
     * 成长值
     */

    private Long growValue;

    /**
     * 会员组
     */
    @JsonSerialize(using =ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long memberGroupId;

    /**
     * 会员级别涉及的会员总数，用于小场地
     */
    private Integer memberCount;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 创建者
     */

    private Long createBy;

    /**
     * 更新者
     */

    private Long updateBy;
}
