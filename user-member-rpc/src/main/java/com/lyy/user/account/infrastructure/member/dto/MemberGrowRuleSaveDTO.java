package com.lyy.user.account.infrastructure.member.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @description: 会员成长值规则
 * @author: qgw
 * @date on 2021/4/7.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class MemberGrowRuleSaveDTO {

    private Long id;
    /**
     * 商户ID
     */
    @NotNull(message = "商户ID不能为空")
    private Long merchantId;

    /**
     * 登录成长值
     */
    private Long login;

    /**
     * 会员成长值
     */
    private Long member;

    /**
     * 绑定手机号成长值
     */
    private Long bindPhone;

    /**
     * 完善信息成长值
     */
    private Long completeInfo;

    /**
     * 关注公众号成长值
     */
    private Long subscribe;

    /**
     * 消费
     */
    private String consumption;

    /**
     * 下单
     */
    private String planceOrder;

    /**
     * 当前操作人
     */
    @NotNull(message = "当前操作人不能为空")
    private Long operatorId;

}
