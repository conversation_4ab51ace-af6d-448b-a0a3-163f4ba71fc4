package com.lyy.user.account.infrastructure.account.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/4/26 - 13:52
 */
@Data
public class AccountBenefitScopeQueryDTO {

    /**
     * 商户id
     */
    private List<Long> merchantIds;

    private Long merchantId;

    /**
     * 用户id
     */
    @NotNull(message = "用户 ID 不能为空")
    private Long userId;

    /**
     * 权益类型集合
     */
    private List<Integer> classify;

    @NotNull(message = "适用范围类型不能为空")
    private Integer applicable;

}
