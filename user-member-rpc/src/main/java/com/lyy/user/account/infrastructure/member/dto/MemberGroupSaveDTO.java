package com.lyy.user.account.infrastructure.member.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

/**
 * 会员组保存信息
 * <AUTHOR>
 * @className: MemberGroupSaveDTO
 * @date 2021/3/30
 */
@Data
public class MemberGroupSaveDTO extends MemberGroupDTO {
    /**
     * 字符串格式的id
     */
    private String idStr;

    public String getIdStr() {
        return Optional.ofNullable(getId()).map(id->id.toString()).orElse("");
    }

    //会员组范围相关关联关系id列表
    @NotEmpty(message ="会员组范围关系不能为空")
    List<MemberRangeAssociatedDTO> memberRangeAssociatedList;

}
