package com.lyy.user.account.infrastructure.benefit.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 返回实扣金额信息
 * @author: qgw
 * @date on 2022/2/10.
 * @Version: 1.0
 */
@Getter
@Setter
@ToString
public class BenefitConsumeInfoDTO {

    private BigDecimal balanceAmount;


    private List<BenefitConsumeDetailInfoDTO> detailInfos;



}
