package com.lyy.user.account.infrastructure.user.dto.tag;

import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/4/17
 */
@Data
public class TagOfExternalSystemDTO {

    /**
     * userId与merchantUserId必填一个，merchantUserId非空时只使用merchantUserId不使用userId
     */
    private Long merchantUserId;

    /**
     * userId与merchantUserId必填一个，merchantUserId非空时只使用merchantUserId不使用userId
     */
    private Long userId;

    @NotNull(message = "merchantId不允许为空")
    private Long merchantId;

    @NotNull(message = "externalSystem不允许为空")
    private Integer externalSystem;

}
