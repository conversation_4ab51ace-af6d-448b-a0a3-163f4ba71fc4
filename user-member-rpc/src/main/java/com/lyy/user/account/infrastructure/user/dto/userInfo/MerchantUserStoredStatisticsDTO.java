package com.lyy.user.account.infrastructure.user.dto.userInfo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MerchantUserStoredStatisticsDTO {

    private Long merchantBenefitClassifyId;

    private BigDecimal total;

    private BigDecimal balance;

    private BigDecimal totalConsume;

    private BigDecimal totalInvalid;

    private Integer remainNum;

    private Integer totalNum;

    private Integer totalNumConsume;

    private Integer totalNumInvalid;
}
