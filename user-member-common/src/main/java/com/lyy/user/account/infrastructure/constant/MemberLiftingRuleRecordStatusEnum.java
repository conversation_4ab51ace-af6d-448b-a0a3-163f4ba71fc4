package com.lyy.user.account.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @className: MemberLiftingRuleRecordStatusEnum
 * @date 2021/4/7
 */
@Getter
@AllArgsConstructor
public enum MemberLiftingRuleRecordStatusEnum {
    INIT((short) 1,"已经触发，未处理"),
    FINISH((short) 2,"已经处理完成"),
    FAILURE((short) 3,"已经失效(过期)"),
    ;
    private short value;
    private String description;
}
