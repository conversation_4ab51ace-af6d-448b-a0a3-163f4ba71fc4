package com.lyy.user.account.infrastructure.constant;

import lombok.Getter;

/**
 * 操作渠道
 *
 * <AUTHOR>
 * @since 2022/6/8 - 20:04
 */
@Getter
public enum AccountOperationChannelEnum {

    WECHAT(2, "微信小程序"),
    DESK_CHECKOUT(3, "桌面收银台"),
    MOBILE_CHECKOUT(3, "移动收银台"),
    DBJ(5, "兑币机"),
    BACK_STAGE(6, "后台");

    private final Integer code;
    private final String description;

    AccountOperationChannelEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

}
