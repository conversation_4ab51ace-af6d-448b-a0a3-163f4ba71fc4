package com.lyy.user.account.infrastructure.constant;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 权益类型枚举类
 *
 * <AUTHOR>
 * @create 2021/3/30 18:13
 */
@AllArgsConstructor
@Getter
public enum BenefitClassifyEnum {

    /**
     * 用户充值余额
     */
    USER_RECHARGE_BALANCE(1,"user_recharge_balance","用户充值余额"),

    /**
     * 用户充值余币
     */
    USER_RECHARGE_COIN(2,"user_recharge_coin","用户充值余币"),

    /**
     * 商户派发余额
     */
    MERCHANT_PAYOUT_BALANCE(3,"merchant_payout_balance","商户派发余额"),

    /**
     * 商户派发余币
     */
    MERCHANT_PAYOUT_COIN(4,"merchant_payout_coin","商户派发余币"),

    /**
     * 平台派发余额
     */
    PLATFORM_PAYOUT_BALANCE(5,"platform_payout_balance","平台派发余额"),

    /**
     * 平台派发余币
     */
    PLATFORM_PAYOUT_COIN(6,"platform_payout_coin","平台派发余币"),

    /**
     * 设备使用次数
     */
    DEVICE_USE_COUNT(7,"device_use_count","设备使用次数"),
    /**
     * 项目启动次数(带时间)
     */
    PROJECT_START_COUNT(8,"project_start_count","项目启动次数(带时间)"),
    /**
     * 设备使用时长
     */
    DEVICE_USE_TIME(9,"device_use_time","设备使用时长"),
//--insert into benfit_classify values("10","intergal","积分")
    /**
     * 成长值
     */
    GROW_VALUE(11,"grow_value","成长值"),
    /**
     * 电量
     */
    ELECTRICITY(12,"electricity","电量"),
    /**
     * 水量
     */
    WATER_VOLUME(13,"waterVolume","水量"),

    /**
     * 商户派发优惠券
     */
    MERCHANT_PAYOUT_COUPON(14,"merchantPayoutCoupon","商户派发优惠券"),

    /**
     * 商户会员卡
     */
    MEMBER_CARD(15,"memberCard","商户会员卡"),

    /**
     * 商家派的红包币
     */
    RED_COIN(16, "redCoin", "红包币"),

    /**
     * 商家派的红包金额
     */
    RED_BALANCE(17, "redAmount", "红包余额"),

    /**
     * 用户广告币
     */
    ADVERT_COIN(18, "advertCoin", "广告币"),

    /**
     * 用户广告红包
     */
    ADVERT_RED_PACKET(19, "advertRedPacket", "广告红包"),

    /**
     * 平台券
     */
    PLATFORM_COUPON(20, "platformCoupon", "平台券"),

    /**
     * 闲时币
     */
    IDLE_TIME_COIN(21, "idleTimeCoin", "闲时币"),
    /**
     * 设备支付启动失败，暂时只用于支付启动失败进行退款的
     */
    @Deprecated
    DEVICE_USE_COUNT_FAIL(22, "device_use_count_fail", "设备启动失败"),

    /**
     * 0元启动
     */
    FREE_TO_USE(23, "free_to_use", "免费使用"),

    /**
     * 第三方平台导入币
     */
    THIRD_PLATFORM_COINS(24,"thirdPlatformCoins","第三方平台导入币"),
    /**
     * 第三方平台导入金额
     */
    THIRD_PLATFORM_AMOUNT(25,"thirdPlatformAmount","第三方平台导入金额"),

    /**
     * IC卡充值
     */
    IC_RECHARGE_BALANCE(26,"ic_recharge_balance","IC卡充值余额"),
    /**
     * 识别账号余额
     */
    MERCHANT_RECOGNITION_BALANCE(27,"merchantRecognitionBalance","识别账号余额"),
    /**
     * 积分充值余额
     */
    MERCHANT_INTEGRAL_RECHARGE_BALANCE(28,"merchantIntegralRechargeBalance","积分充值余额"),


    /**
     * 延迟结算余额
     */
    DELAYED_SETTELEMENT_BALANCE(29,"user_recharge_balance","延迟结算余额"),

    /**
     * 延迟结算余币
     */
    DELAYED_SETTELEMENT_COIN(30,"user_recharge_coin","延迟结算余币"),

    /**
     * 用户充值余额
     */
    USER_RECHARGE_GIVE_BALANCE(31, "user_recharge_give_balance", "用户充值赠送余额"),

    /**
     * 用户充值余币
     */
    USER_RECHARGE_GIVE_COIN(32, "user_recharge_give_coin", "用户充值赠送余币"),
    MERCHANT_PAYOUT_GROUP_BALANCE(33,"merchant_payout_group_balance","商户派发场地余额"),
    MERCHANT_PAYOUT_GROUP_COIN(34,"merchant_payout_group_coin","商户派发场地余币"),


    SMALL_VENUE_DEFAULT(99,"small_venue_default","小场地默认"),

    /**
     * 商家立减金
     */
    DEDUCTION_AMOUNT(100, "商家立减金","商家立减金"),

    /**
     * 第三方余额
     */
    THIRD_PLATFORM_BALANCE(101, "third_platform_balance", "第三方余额"),
    HISTORY_BALANCE(102, "history_balance", "历史余额"),


    /**
     * 超净洗月卡
     */
    SUPER_CLEAN_MONTH_CARD(103,"super_clean_month_card","洗衣VIP月卡服务"),
    ;



    private Integer code;
    private String name;
    private String desc;


    public static String getDesc(Integer code){
        for(BenefitClassifyEnum benefitClassifyEnum:BenefitClassifyEnum.values()){
            if(benefitClassifyEnum.getCode().equals(code)){
                return benefitClassifyEnum.getDesc();
            }
        }
        return null;
    }
    public static BenefitClassifyEnum of(Integer code){
        for(BenefitClassifyEnum benefitClassifyEnum:BenefitClassifyEnum.values()){
            if(benefitClassifyEnum.getCode().equals(code)){
                return benefitClassifyEnum;
            }
        }
        return null;
    }

    /**
     * 余额类权益类型枚举
     */
    private final static List<BenefitClassifyEnum> BENEFIT_CLASSIFY_BALANCE_ENUM;
    /**
     * 余币类权益类型枚举
     */
    private final static List<BenefitClassifyEnum>  BENEFIT_CLASSIFY_COIN_ENUM;
    static {
        //使用Arrays.asList，防止外部对该数据进行增加或减少
        //余额类权益类型枚举
        BENEFIT_CLASSIFY_BALANCE_ENUM = Arrays.asList(
                USER_RECHARGE_BALANCE,
                MERCHANT_PAYOUT_BALANCE,
                PLATFORM_PAYOUT_BALANCE,
                RED_BALANCE,
                ADVERT_RED_PACKET,
                THIRD_PLATFORM_AMOUNT,
                DEDUCTION_AMOUNT,
                DELAYED_SETTELEMENT_BALANCE,
                USER_RECHARGE_GIVE_BALANCE,
                MERCHANT_PAYOUT_GROUP_BALANCE
                );

        //余币类权益类型枚举
        BENEFIT_CLASSIFY_COIN_ENUM = Arrays.asList(
                USER_RECHARGE_COIN,
                MERCHANT_PAYOUT_COIN,
                PLATFORM_PAYOUT_COIN,
                RED_COIN,
                ADVERT_COIN,
                //闲时币也算余币
                IDLE_TIME_COIN,
                THIRD_PLATFORM_COINS,
                DELAYED_SETTELEMENT_COIN,
                USER_RECHARGE_GIVE_COIN,
                MERCHANT_PAYOUT_GROUP_COIN
                );
    }

    /**
     * 获取余额类权益类型枚举列表
     * @return
     */
    public static List<BenefitClassifyEnum> getBenefitClassifyBalanceEnum() {
        return BENEFIT_CLASSIFY_BALANCE_ENUM;
    }

    /**
     * 获取余币类权益类型枚举列表
     * @return
     */
    public static List<BenefitClassifyEnum> getBenefitClassifyCoinEnum() {
        return BENEFIT_CLASSIFY_COIN_ENUM;
    }

    /**
     * 获取所有的枚举
     * @return
     */
    public static List<Integer> getAllBenefitClassifyCode(){
        List<Integer> list = new ArrayList<>();
        for(BenefitClassifyEnum benefitClassifyEnum:BenefitClassifyEnum.values()){
            list.add(benefitClassifyEnum.getCode());
        }
        return list;
    }
}
