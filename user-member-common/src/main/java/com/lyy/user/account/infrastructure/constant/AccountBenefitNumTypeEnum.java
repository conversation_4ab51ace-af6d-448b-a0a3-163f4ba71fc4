package com.lyy.user.account.infrastructure.constant;

import lombok.Getter;

/**
 * 权益值类型
 *
 * <AUTHOR>
 * @since 2022/1/10
 */
@Getter
public enum AccountBenefitNumTypeEnum {

    AMOUNT(1, "额度类型"),
    FREQUENCY(2, "频次卡类型");


    private final Integer code;
    private final String description;

    AccountBenefitNumTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

}
