package com.lyy.user.account.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: qgw
 * @date on 2021/3/31.
 * @Version: 1.0
 */
@Getter
@AllArgsConstructor
public enum TagCategoryEnum {
    /**
     * 标签类型
     */
    AUTO(1, "自动标签"),
    MANUAL(2, "手动标签"),

    ;

    private Integer status;

    private String desc;
    public static TagCategoryEnum of(Integer status) {
        for (TagCategoryEnum typeEnum : TagCategoryEnum.values()) {
            if (typeEnum.getStatus().equals(status)) {
                return typeEnum;
            }
        }
        return null;
    }
}
