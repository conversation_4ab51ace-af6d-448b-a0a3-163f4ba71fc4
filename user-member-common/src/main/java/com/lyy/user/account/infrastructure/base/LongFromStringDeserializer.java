package com.lyy.user.account.infrastructure.base;

import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.FromStringDeserializer;
import java.io.IOException;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @className: LongFromStringDeserializer
 * @date 2021/6/4
 */
public class LongFromStringDeserializer extends FromStringDeserializer {
    protected LongFromStringDeserializer(Class vc) {
        super(vc);
    }

    public LongFromStringDeserializer() {
        this(Long.class);
    }

    @Override
    protected Object _deserialize(String value, DeserializationContext ctxt) throws IOException {
        if(StringUtils.isNumeric(value)){
            return Long.parseLong(value);
        }
        return null;
    }
}
