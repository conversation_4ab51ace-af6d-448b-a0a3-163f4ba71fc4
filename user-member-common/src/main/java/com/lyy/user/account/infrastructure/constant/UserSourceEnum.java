package com.lyy.user.account.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description 用户来源枚举
 * @Date 2021/4/6
 **/
@Getter
@AllArgsConstructor
public enum UserSourceEnum {
    /**
     * 微信网页
     */
    WE_CHAT_WEB("W", "weChatWebUserInit", "微信网页"),
    /**
     * 微信小程序
     */
    WE_CHAT_MINI_PROGRAM("W", "weChatMiniProgramUserInit", "微信小程序"),
    /**
     * 微信其他小程序
     */
    WE_CHAT_OTHER_MINI_PROGRAM("W", "weChatOtherMiniProgramUserInit", "微信其他小程序"),
    /**
     * 支付宝网页
     */
    ALI_WEB("A", "aliWebUserInit", "支付宝网页"),
    /**
     * 支付宝小程序
     */
    ALI_MINI_PROGRAM("A", "aliMiniProgramUserInit", "支付宝小程序"),
    /**
     * 京东
     */
    JD("J", "jdUserInit", "京东"),
    /**
     * 云闪付
     */
    UNION("U", "unionUserInit", "云闪付"),
    /**
     * 其他来源
     */
    OTHER("O", "otherUserInit", "其他来源"),
    /**
     * 微信网页B端商户授权
     */
    WE_WHAT_WEB_OF_MERCHANT("W", "weChatWebUserOfMerchantInit", "微信网页B端商户授权"),
    /**
     * 小场地桌面收银台
     */
    SMALL_VENUE_DESK_COUNTER("Z", "smallVenueUserInit", "小场地桌面收银台"),
    /**
     * 小场地移动收银台
     */
    SMALL_VENUE_MOBILE_COUNTER("Y", "smallVenueUserInit", "小场地移动收银台"),
    /**
     * 小场地微信小程序
     */
    SMALL_VENUE_WE_CHAT_MINI_PROGRAM("W", "smallVenueWeChatMiniProgramUserInit", "小场地微信小程序"),
    /**
     * 小场地后台导入
     */
    SMALL_VENUE_IMPORT_COUNTER("I", "smallVenueUserInit", "小场地后台导入"),
    /**
     * 小场地移动B端
     */
    SMALL_VENUE_MERCHANT_H5("B", "smallVenueUserInit", "小场地移动B端"),

    /**
     * 微信授权第三方APP
     */
    XIAOLE_WECHAT_THIRD_APP("W", "xiaoleWechatThirdAppInit", "乐仔生活-微信授权第三方APP"),
    ;

    /**
     * 用户类别
     * W：微信
     * A：支付宝
     * J：京东
     * U：云闪付
     * O：其他
     * Y 移动收银台
     * Z 桌面收银台
     */
    private String userType;
    /**
     * 处理类
     */
    private String className;
    /**
     * 描述
     */
    private String desc;

}

