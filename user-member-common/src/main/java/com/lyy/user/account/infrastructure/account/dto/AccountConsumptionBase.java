package com.lyy.user.account.infrastructure.account.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lyy.user.account.infrastructure.base.LongFromStringDeserializer;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/6/12 - 14:59
 */
@Data
public class AccountConsumptionBase {

    /**
     * 流水号
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongFromStringDeserializer.class)
    private Long id;
    /**
     * 商品名称或其他操作
     */
    private String leftTopTitle;
    /**
     * 设备
     */
    private Long equipmentId;
    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 交易类型
     */
    private Integer tradeType;
    /**
     * 交易类型
     */
    private String tradeTypeName;
    /**
     * 设备类型名称
     */
    private String equipmentTypeName;
    /**
     * 设备value
     */
    private String equipmentValue;
    /**
     * 机台号
     */
    private Integer groupNumber;
    /**
     * 商品名称
     */
    private String commodityName;
    /**
     * 店铺名称
     */
    private String storeName;
    /**
     * 消费实际涉及权益
     */
    private BigDecimal actualBenefit;
    /**
     * 备注
     */
    private String description;
    /**
     * 权益类型
     */
    private String classifyName;
    private String rightTopTitle;
    private String rightMiddleValue;
    private String leftTitleText;
    /**
     * 右侧标题-中间
     */
    private String rightMiddleTitle;
    /**
     * 右侧标题-下
     */
    private String rightBottomTitle;
    /**
     * 业务单号
     */
    private String orderNo;
    /**
     * 方式1=加，2=减
     */
    private Integer mode;

    /**
     * 以下3个字段的数据统计不正确，待产品新出方案
     */
    /**
     * 线上支付金额
     */
    private BigDecimal payAmount;
    /**
     * 余币
     */
    private BigDecimal coins;
    /**
     * 变动余额
     */
    private BigDecimal amount;
    /**
     * 记录单位（元、币、“”）
     */
    private String unit;
    /**
     * 记录类型
     *
     * @see com.lyy.user.account.infrastructure.constant.AccountRecordTypeEnum
     */
    private Integer recordType;
    /**
     * 初始权益
     */
    private BigDecimal initialBenefit;
}
