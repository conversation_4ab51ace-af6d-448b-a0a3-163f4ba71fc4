package com.lyy.user.account.infrastructure.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;


/**
 * <AUTHOR>
 */
@Getter
@ToString
@AllArgsConstructor
public enum  CardValidTypeEnum {


    SAME_MAIN_CARD(0,"同主卡有效期"),
    APPOINT_DAYS(1,"指定有效期");


    /**
     * 卡有效期类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String description;
}