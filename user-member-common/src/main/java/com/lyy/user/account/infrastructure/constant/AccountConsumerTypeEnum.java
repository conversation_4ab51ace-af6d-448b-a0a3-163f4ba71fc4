package com.lyy.user.account.infrastructure.constant;

import lombok.Getter;

/**
 * 消费方式
 *
 * <AUTHOR>
 * @since 2022/1/10
 */
@Getter
public enum AccountConsumerTypeEnum {

    POS(1, "刷卡消费"),
    WECHAT(2, "微信小程序"),
    DESK_CHECKOUT(3, "桌面收银台"),
    MOBILE_CHECKOUT(4, "移动收银台"),
    DBJ(5, "兑币机"),
    BACK_STAGE(6, "管理后台"),
    ZHIFUBAO(8, "支付宝小程序")
    ;

    private final Integer code;
    private final String description;

    AccountConsumerTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

}
