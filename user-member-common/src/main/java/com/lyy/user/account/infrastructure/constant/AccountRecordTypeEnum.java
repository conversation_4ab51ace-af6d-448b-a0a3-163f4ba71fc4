package com.lyy.user.account.infrastructure.constant;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 类描述：账户流水类型
 * <p>
 *
 * <AUTHOR>
 * @since 2021/6/15 10:44
 */
@Getter
@AllArgsConstructor
public enum AccountRecordTypeEnum {
    EQUIP_START_ALL(0,"设备启动","设备启动",1),
    EQUIP_START_BLUETOOTH_BALANCE(1,"蓝牙启动余额启动扣除","设备启动",1),
    EQUIP_START_COIN(2,"余币启动扣减权益","设备启动",1),
    EQUIP_START_BALANCE_START(3,"余额启动扣减权益","设备启动",1),
    EQUIP_START_WATER_BALANCE_START(4,"售水机余额启动扣减权益","设备启动",1),
    EQUIP_START_WATER_PAY_AFTER_USE_START(5,"售水机先出水后计费模式扣除","设备启动",1),
    EQUIP_START_PAY_SUBTRACT_AD(6,"支付扣广告红包权益","设备启动",1),
    EQUIP_START_COIN_START_FROM_START_SERVER(7,"startServer余币启动扣除","设备启动",1),
    EQUIP_START_PAY(8,"支付启动","设备启动",1),
    EQUIP_START_BALANCE_DEDUCTION(9,"余额抵扣","设备启动",1),
    FREE_TO_USE(10,"免费使用","免费使用",1),

    EQUIP_START_BARRAGE_MESSAGE(11, "氛围类型(发弹幕)","氛围类型(发弹幕)",1),
    EQUIP_START_ABSOLUTE_HIT(12, "氛围类型(必中符)","氛围类型(必中符)",1),
    EQUIP_START_LIKE(13, "氛围类型(点赞)","氛围类型(点赞)",1),
    EQUIP_START_HEART(14, "氛围类型(比心)","氛围类型(比心)",1),
    EQUIP_START_FIGHTING(15, "氛围类型(加油)","氛围类型(加油)",1),
    EQUIP_START_SIXTY_SIX(16, "氛围类型(666)","氛围类型(666)",1),
    EQUIP_PAY_BUT_START_FAIL(17, "已支付，设备启动失败","已支付，设备启动失败",1),
    /**
     * 商家抵扣金
     */
    DEDUCTION_AMOUNT(18, "商家抵扣金扣除","支付抵扣",1),

    THIRD_PLATFORM_BALANCE(19, "第三方余额支付启动", "设备启动", 1),
    THIRD_PLATFORM_BALANCE_REFUND(1019, "第三方余额支付启动退款", "启动退款", 1),
    MEMBER_CARD_START(1020, "会员卡启动", "会员卡启动", 1),
    MEMBER_CARD_REFUND(1021, "会员卡启动失败退回", "会员卡启动失败退回", 1),

    IC_CARD_TRANSFER_OUT(1023, "余额转出到ic卡", "余额转出到ic卡", 1),

    //商品购买 COMMODITY_PURCHASE
    //未实现
    COMMODITY_PURCHASE_ALL(20,"商品购买","商品购买",2),
    COMMODITY_PURCHASE(21,"商品购买","商品购买",2),
    COMMODITY_PURCHASE_CARD(23,"会员卡购买","购买会员卡",2),
    INTEGRAL_EXCHANGE_GOODS_BALANCE(24, "扣除积分商品兑换费用", "扣除积分商品费用", 2),

    SUPER_CLEAN_MONTH_CARD(25, "洗衣VIP月卡服务", "洗衣VIP月卡服务", 2),

    //充值
    RECHARGE_ALL(30,"充值","充值",3),
    RECHARGE(31,"支付派权益","充值",3),
    RECHARGE_ADD_BALANCE_OR_COIN(32,"支付派权益(余额/余币)","充值",3),
    RECHARGE_IC(33,"IC卡充值","IC卡充值",3),
    INTEGRAL_SYSTEM_RECHARGE_BALANCE(34, "后台充值积分商城余额", "乐摇摇后台充值", 3),
    INTEGRAL_MERCHANT_RECHARGE_BALANCE(35, "商户充值积分商城余额", "商户主动充值", 3),
    RECHARGE_DEDUCTION_AMOUNT(36, "充值商家抵扣金","充值赠送",3),
    USER_STORE_COINS(37, "用户存币", "用户存币", 3),
    PAYAFTER(38, "先玩后付", "先玩后付", 3),

    RECHARGE_FWJ_BARRAGE_MESSAGE(340, "支付-氛围类型(发弹幕)","氛围类型(发弹幕)",3),
    RECHARGE_ABSOLUTE_HIT(341, "支付-氛围类型(必中符)","氛围类型(必中符)",3),
    RECHARGE_LIKE(342, "支付-氛围类型(点赞)","氛围类型(点赞)",3),
    RECHARGE_HEART(343, "支付-氛围类型(比心)","氛围类型(比心)",3),
    RECHARGE_FIGHTING(344, "支付-氛围类型(加油)","氛围类型(加油)",3),
    RECHARGE_SIXTY_SIX(345, "支付-氛围类型(666)","氛围类型(666)",3),
    RECHARGE_ADDITIONAL_PURCHASES(346, "加购余额充值", "加购余额", 3),

    //赠送
    GIFT_MEMBER_GIVE_ALL(40, "赠送", "赠送", 4),
    GIFT_MEMBER_GIVE_BALANCE(41, "会员派余额", "会员升级赠送", 4),
    GIFT_MEMBER_GIVE_COIN(42, "会员派余币", "会员升级赠送", 4),
    GIFT_RECHARGE_GIVE_BALANCE(43, "充值赠送(余额)", "赠送", 4),
    GIFT__RECHARGE_GIVE_COIN(44, "充值赠送(余币)", "赠送", 4),
    GIFT_MERCHANT_GIVE_COIN(45, "商户派发余币增加权益", "商家赠送", 4),
    GIFT_MERCHANT_GIVE_BALANCE(46, "商户派发余额增加权益", "商家赠送", 4),
    GIFT_MERCHANT_GIVE_THIRD_IMPORT(47, "第三方导入增加权益", "第三方导入", 4),
    GIFT_MERCHANT_INTEGRATION_COIN(48, "积分兑换", "积分兑换", 4),
    GIFT_MEMBER_DEGRADE_BALANCE_COIN(49, "会员降级扣减余额余币", "会员降级扣减", 4),

    GIFT_MEMBER_CARD_PURCHASE(410, "会员购买赠送", "会员购买赠送", 4),
    GIFT_MEMBER_UPGRADE(411, "会员升级级赠送权益", "会员降级扣减", 4),
    GIFT_MEMBER_DEGRADE(412, "会员降级扣减权益", "会员降级扣减", 4),
    GIFT_MEMBER_TIKTOK_ACTIVITY(413, "商家赠送（抖音活动）", "商家赠送（抖音活动）", 4),
    GIFT_LOTTERY_SUPPER_DBJ(414, "大转盘抽奖", "大转盘抽奖", 4),
    TIKTOK_GROUP_BUYING_VERIFICATION(415, "抖音团购核销", "抖音团购核销", 4),

    //退款
    REFUND_START_FAIL_ALL(50, "退款", "退款", 5),
    REFUND_START_FAIL_COIN(51, "余币启动失败退款", "启动失败退款", 5),
    REFUND_START_FAIL_BALANCE_REFUND(52, "余额启动失败退款", "启动失败退款", 5),
    REFUND_START_FAIL_AFTER_PAY_REFUND_BALANCE(53, "支付启动失败退余额权益", "启动失败退款", 5),
    REFUND_BENEFIT(54, "退款退权益", "商家退款", 5),
    REFUND_START_FAIL_AFTER_PAY_REFUND_MONEY(55, "支付启动失败退款", "启动失败退款", 5),
    REFUND_RECEIVE_CODE_PAY(56, "收款码退款", "收款码退款", 5),
    REFUND_RECEIVE_PROJECT_PAY(57, "项目码退款", "项目码退款", 5),
    REFUND_START_FAIL_AFTER_PAY_REFUND_COIN(58,"支付启动失败退余币权益","启动失败退款",5),
    REFUND_MEMBER_CARD(59, "会员卡退款", "会员卡退款", 5),

    //剩余金额退回
    REMAINING_MONEY_ALL(60,"剩余金额退回","剩余金额退回",6),
    REMAINING_MONEY_FROM_CHARGING(61,"充电桩结束退款","剩余金额退回",6),
    REMAINING_MONEY_FROM_WATER(62,"售水机结束退款","剩余金额退回",6),
    REMAINING_MONEY_FROM_XCJ(63,"洗车机上报结束退款","剩余金额退回",6),
    INTEGRAL_CANCEL_ORDER_BALANCE(64, "取消积分订单退还余额", "取消订单退还余额", 6),

    IC_CARD_TRANSFER_IN(65, "ic卡余额转入", "ic卡余额转入", 6),

    //活动
    ACTIVITY_ALL(70,"活动","活动",7),
    ACTIVITY_RED_COIN(71,"红包币增加权益","红包币",7),
    ACTIVITY_MARKETING_ADD_BENEFIT(72,"其他营销活动增加权益","活动赠送",7),
    ACTIVITY_MEMBER_CARD_ADD_BALANCE(73,"购买会员卡派余额","会员卡赠送",7),
    ACTIVITY_JD_ONE_MIN_ADVERT_RED_PACKET(74,"京东一分购广告红包","一分购红包",7),
    ACTIVITY_RED_BALANCE(75,"红包余额增加权益","红包余额",7),
    ACTIVITY_ADVERT_COIN(76, "广告币增加权益", "活动赠币", 7),
    ACTIVITY_ADVERT_RED_PACKET(77, "广告红包增加权益", "活动红包", 7),
    ACTIVITY_FREE_CARD_COMMISSION_COINS(78, "逍遥卡活动商家派币", "逍遥卡活动", 7),
    ACTIVITY_MARKETING_TIKTOK_BENEFIT(79, "抖音派送奖励", "抖音派送奖励", 7),
    ACTIVITY_FANS_FREE_PLAY_COINS(134, "免费玩（激励涨粉）", "关注活动派币", 7),
    ACTIVITY_FANS_FREE_PLAY_RED_PACKET(135, "免费玩（激励涨粉）", "关注活动派红包", 7),
    ACTIVITY_CPA_JD_ONE_POINT(136, "京东一分购", "京东一分购活动", 7),
    ACTIVITY_CPA_AOGE_COLOR_RING(137, "奥格彩铃", "咪咕会员活动", 7),
    ACTIVITY_CPA_EASY_CHANGE(138, "任我换", "任我换活动", 7),
    ACTIVITY_CPA_CMCC(139, "移动积分", "移动积分活动", 7),
    ACTIVITY_CPA_CLOUD_CODE(140, "云码", "支付宝活动", 7),
    ACTIVITY_CPA_CLOUD_CODE_ISV(141, "聚合拉新", "支付宝活动", 7),
    ACTIVITY_UPGRADE_ORDER(142, "升单", "升单活动派币", 7),
    ACTIVITY_PROMOTION_GUEST(143, "升单宝", "升单宝活动派币", 7),
    ACTIVITY_LAKEBAO(144, "拉客宝", "拉客宝活动派币", 7),
    ACTIVITY_VIDEO_SHOW_COINS(145, "视频秀派币奖励", "视频秀派币奖励", 7),
    ACTIVITY_ALI_COUPON_COINS(146, "阿里券码核销派币奖励", "阿里券码核销", 7),
    ACTIVITY_MEITUAN_COINS(147, "美团活动", "美团活动赠币", 7),
    ACTIVITY_MEITUAN_COUPON(148, "美团活动", "美团活动红包", 7),
    ACTIVITY_FUGOUYI(149, "复购易", "复购易活动派币", 7),
    ACTIVITY_MEITUAN_BALANCE(150, "美团活动", "美团活动余额", 7),
    EXT_MINI_PROGRAM_XYB_EXCHANGE(151, "洗衣币兑换广告红包", "洗衣币兑换广告红包", 7),
    EXT_MINI_PROGRAM_CDB_EXCHANGE(152, "充电币兑换广告红包", "充电币兑换广告红包", 7),
    ACTIVITY_PRODUCT_MATRIX(153, "智能导购", "智能导购赠币", 7),
    SUPER_CLEAN_WASH_REFUND(154, "超净洗退款广告红包", "洗衣vip订单退费", 7),
    SUPER_CLEAN_WASH_ALIPAY_LOTTERY(155, "超净洗支付宝抽奖广告红包", "洗衣活动红包", 7),
    ACTIVITY_CPA_JD_PAY_AMOUNT(156, "京东支付派发余额", "京东支付余额", 7),
    ACTIVITY_CPA_ZHONGAN_DBJ(157, "众安CPA保险兑币机派币", "限时福利", 7),
    ACTIVITY_FAULT_COMPENSATION_RED_PACKET(158, "故障宝赔付红包", "故障宝赔付红包", 7),
    ACTIVITY_ALIPAY_CODE_MONEY(159, "支付宝码上有钱", "支付宝活动", 7),
    ACTIVITY_ALIPAY_INCENTIVE_RED_PACKET(160, "支付宝激励广告派发红包", "支付宝激励广告派发红包", 7),
    ACTIVITY_V_MEMBER_RED_PACKET(161, "V会员红包派发", "V会员红包派发", 7),
    ACTIVITY_BLIND_DRAWER_LOTTERY(162, "盲抽柜抽奖", "盲抽柜抽奖", 7),
    PINGAN_INSURANCE_FAMILY(163, "平安家财险活动", "平安家财险活动", 7),
    ACTIVITY_CPA_YOUKU(164, "优酷活动派币", "优酷活动派币", 7),
    ACTIVITY_CPA_WECHAT_AUTO_DEBIT(165, "微信畅听会员活动", "微信畅听会员活动", 7),
    ACTIVITY_V_MEMBER_BENEFIT_DISCOUNT(166, "V会员权益优惠", "V会员权益优惠", 7),
    DOUYIN_ONE_CENT_ACTIVITY(167, "抖音活动", "抖音一分购活动", 7),
    ACTIVITY_CPA_JD_AUTO_DEBIT(168, "微信视听会员活动", "微信视听会员活动", 7),
    ABC_ONE_CENT_ACTIVITY(169, "农行一分购广告红包", "农行一分购", 7),


    //过期
    EXPIRED_ALL(80,"过期","过期",8),
    EXPIRED_FROM_BALANCE(81,"余额过期","余额过期",8),
    EXPIRED_FROM_COIN(82,"余币过期","余币过期",8),
    EXPIRED_ACTIVITY_V_MEMBER_RED_PACKET(83, "V会员红包派发", "V会员红包过期", 8),


    //智能引流
    INTELLIGENT_ALL(90,"智能引流","智能引流",9),
    INTELLIGENT_OTHER_EQUIP_GET(91,"智能引流-其他设备导流小程序领取","智能引流",9),
    INTELLIGENT_OTHER_EQUIP_AUTO(92,"智能引流-其他设备导流自动派发","智能引流",9),
    INTELLIGENT_GET(93,"智能引流小程序领取","智能引流",9),
    INTELLIGENT_AUTO(94,"智能引流自动派发","智能引流",9),

    //其他
    OTHER_All(100,"其他","其他",10),
    OTHER_ONLINE_GIFT(101,"线上派发","其他",10),
    OTHER_STORE_GIFT(102,"门店派发","其他",10),


    // 调整
    MERCHANT_ADJUST_All(110,"商家调整","商家调整",11),
    MERCHANT_ADJUST_BALANCE(111,"商家调整充值余额","商家调整",11),
    MERCHANT_ADJUST_COIN(112,"商家调整充值余币","商家调整",11),
    MERCHANT_ADJUST_PAYOUT_BALANCE(113,"商家调整派送余额","商家调整",11),
    MERCHANT_ADJUST_PAYOUT_COIN(114,"商家调整派币","商家调整",11),
    MERCHANT_ADJUST_RED_BALANCE(115,"商家调整红包余额","商家调整",11),
    MERCHANT_ADJUST_RED_COIN(116,"商家调整红包余币","商家调整",11),
    MERCHANT_ADJUST_IDLE_COIN(117,"商家调整闲时币","商家调整",11),
    MERCHANT_ADJUST_ADVERT_COIN(118,"商家调整广告币","商家调整",11),
    MERCHANT_CANCEL_COUPLE(119,"商家核销优惠券","商家调整",11),
    /**
     * 系统调整的也显示为商家调整 from 产品
     */
    SYSTEM_ADJUST_BALANCE(119,"系统调整充值余额","商家调整",11),
    SYSTEM_ADJUST_COIN(1110,"系统调整充值余币","商家调整",11),
    SYSTEM_ADJUST_PAYOUT_BALANCE(1111,"系统调整派送余额","商家调整",11),
    SYSTEM_ADJUST_PAYOUT_COIN(1112,"系统调整派币","商家调整",11),
    SYSTEM_ADJUST_RED_BALANCE(1113,"系统调整红包余额","商家调整",11),
    SYSTEM_ADJUST_RED_COIN(1114,"系统调整红包余币","商家调整",11),
    SYSTEM_ADJUST_IDLE_COIN(1115,"系统调整闲时币","商家调整",11),
    SYSTEM_ADJUST_ADVERT_COIN(1116,"系统调整广告币","商家调整",11),


    /**
     * 收款码业务需要单独处理，包括项目码与收款码
     */
    RECEIVE_CODE_PAY(130, "收款码支付", "收款码支付",13),
    RECEIVE_CODE_RECHARGE(131, "收款码充值", "收款码充值",13),


    /**
     * 新会员流水记录添加冻结、解冻类型
     */
    FREEZE_RECORD(132,"冻结","冻结",10),
    UN_FREEZE_RECORD(133,"解冻","解冻",10),

    /**
     * 小场地 SV => small verne
     */
    SV_RECHARGE(1401, "订单购买充值", "充值", 14),
    SV_REFUND(1402, "订单退款扣减", "退款", 14),
    SV_PRESENTATION(1403, "赠送储值", "赠送", 14),
    SV_TRANSFER_IN(1404, "转账转入", "转账转入", 14),
    SV_TRANSFER_OUT(1405, "转账转出", "转账转出", 14),
    SV_EQUIPMENT_CONSUMPTION(1406, "设备刷卡扣费", "设备消费", 14),
    SV_EQUIPMENT_CONSUMPTION_REFUND(1407, "设备消费退费", "设备消费退回", 14),
    SV_STORED_VALUE_DEDUCTION(1408, "订单储值抵扣", "储值抵扣", 14),
    SV_STORED_VALUE_DEDUCTION_REFUND(1409, "订单退款退抵扣", "退储值抵扣", 14),
    SV_EXCHANGE(1410, "储值兑换", "兑换", 14),
    SV_EXCHANGE_REFUND(1411, "退兑换单据", "退兑换", 14),
    SV_GIFT_RECYCLING(1412, "礼品回收", "礼品回收", 14),
    SV_GIFT_REFUND(1413, "礼品退回", "礼品退回", 14),
    SV_WITHDRAW_COINS(1414, "兑币机取币", "取币", 14),
    SV_DEPOSIT_MONEY(1415, "存币", "存币", 14),
    SV_STORED_VALUE_EXPIRED(1416, "储值过期", "储值过期", 14),
    SV_ACCOUNT_CANCELLATION(1417, "会员卡注销", "注销", 14),
    SV_PRESENTATION_REFUND(1418, "退赠送积分", "退赠送", 14),
    SV_DEPOSIT(1419, "押金", "押金", 14),
    SV_COMMODITY_PURCHASE(1420, "商品购买消费", "商品购买", 14),
    SV_DEDUCTION(1421, "人工扣减", "扣减", 14),
    SV_EQUIPMENT_GIFT(1422, "设备出礼", "出礼", 14),
    SV_DEPOSIT_SCORE(1423, "存分", "存分", 14),
    SV_CONVERSION_IN(1424,"转换转入","转换转入",14),
    SV_CONVERSION_OUT(1425,"转换转出","转换转出",14),
    /**
     *
     */
    SYSTEM_NOT_QUERY_RECORD(9999,"为解决分库分表查询问题，此数据不归属业务数据","",null),
    PRE_DEDUCTION(10000,"预扣权益数据，不入库","",null),
    EXT_MINI_PROGRAM_H5_CDZ_EXCHANGE(170, "H5积分墙充电红包", "充电红包", 7),

    ;


    /**
     * 这个按顺序来,
     */
    private Integer code;
    private String desc;

    private String showText;
    /**
     * 这个是分组
     * @see  com.lyy.user.account.infrastructure.constant.AccountRecordTypeGroupEnum
     */
    private Integer groupId;


    public static AccountRecordTypeEnum findByCode(Integer code) {
        for (AccountRecordTypeEnum value : AccountRecordTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }


    public static List<Integer> getAllByGroupId(List<Integer>  groupIds) {
       return Arrays.stream(AccountRecordTypeEnum.values())
               .filter(e->groupIds.contains(e.getGroupId()))
               .map(AccountRecordTypeEnum::getCode)
                .collect(Collectors.toList());
    }


}
