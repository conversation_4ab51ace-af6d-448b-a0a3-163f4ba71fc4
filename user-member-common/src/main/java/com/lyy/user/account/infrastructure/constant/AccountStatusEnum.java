package com.lyy.user.account.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 类描述：账户状态枚举
 * <p>
 *
 * <AUTHOR>
 * @since 2021/3/31 10:44
 */
@Getter
@AllArgsConstructor
public enum AccountStatusEnum {

    /**
     * 账户状态枚举
     */
    NORMAL(1, "正常使用"),
    DISABLED(2, "禁用"),
    LOSS(3, "挂失"),
    OVERDUE(4, "过期"),
    CANCELLATION(5, "已注销"),
    ;

    private Integer status;

    private String desc;


}
