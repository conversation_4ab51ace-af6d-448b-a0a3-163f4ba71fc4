package com.lyy.user.account.infrastructure.constant;

import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 类描述：调整类型
 * <p>
 *
 * <AUTHOR>
 * @since 2021/3/31 16:45
 */
@Getter
@AllArgsConstructor
public enum AdjustTypeEnum {

    /**
     * 调整类型：增加
     */
    INCREMENT(1),

    /**
     * 调整类型：减少
     */
    DECREMENT(2),

    /**
     * 重置为零
     */
    EMPTY(3);

    private Integer type;

    public static boolean checkAdjustType(Integer type) {
        return Arrays.stream(values()).anyMatch(m -> m.type.equals(type));
    }

}
