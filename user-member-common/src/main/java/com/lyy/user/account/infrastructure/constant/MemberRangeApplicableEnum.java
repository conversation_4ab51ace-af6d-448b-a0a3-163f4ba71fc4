package com.lyy.user.account.infrastructure.constant;

import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员范围的适应类型枚举
 * <AUTHOR>
 * @className: MemberRangeApplicableEnum
 * @date 2021/3/29
 */
@AllArgsConstructor
@Getter
public enum MemberRangeApplicableEnum {

    APPLICABLE_EQUIPMENT_TYPE((short)1,"品类","适用于品类"),
    APPLICABLE_EQUIPMENT_GROUP((short)2,"场地","适用于场地"),
    APPLICABLE_EQUIPMENT((short)3,"设备","适用于设备"),
    APPLICABLE_COMMODITY((short)4,"商品","适用于商品"),
    APPLICABLE_NOT_ALL_SELECT_EQUIPMENT_GROUP((short)5,"非全选场地","非全选场地,不作实际使用，用于前端展示"),
    ;
    private Short value;
    private String name;
    private String description;

    public static MemberRangeApplicableEnum getByValue(short value) {
       return Arrays.stream(MemberRangeApplicableEnum.values())
                .filter(e->e.getValue() == value)
                .findFirst()
                .orElse(null);
    }
}
