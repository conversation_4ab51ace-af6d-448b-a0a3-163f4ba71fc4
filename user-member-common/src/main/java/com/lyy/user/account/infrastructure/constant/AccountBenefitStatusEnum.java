package com.lyy.user.account.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 类描述：账户权益状态枚举
 * <p>
 *
 * <AUTHOR>
 * @since 2021/3/31 10:44
 */
@Getter
@AllArgsConstructor
public enum AccountBenefitStatusEnum {

    /**
     * 账户权益状态枚举
     */
    NORMAL(1, "正常使用"),
    EXPIRED(2, "已过期"),
    CONSUMED(3, "已消耗"),
    ;

    private Integer status;

    private String desc;


}
