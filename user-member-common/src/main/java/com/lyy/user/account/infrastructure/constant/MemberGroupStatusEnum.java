package com.lyy.user.account.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员组状态
 * <AUTHOR>  liz<PERSON><PERSON><PERSON>n
 * @createDate   :  2024/12/23 16:34
 * @since        :  1.0.0
 */
@Getter
@AllArgsConstructor
public enum MemberGroupStatusEnum {

    /**
     *
     */
    PUTTING(1, "投放中"),
    STOPPED(2, "已停用"),
    EXPIRED(3, "已过期"),
    REMOVED(4, "已删除"),
    PENDING(5, "待生效"),
    ;

    private final Integer status;

    private final String desc;


}
