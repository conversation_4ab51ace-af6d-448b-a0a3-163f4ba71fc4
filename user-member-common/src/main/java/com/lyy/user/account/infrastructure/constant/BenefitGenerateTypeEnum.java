package com.lyy.user.account.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2021/4/1 17:33
 */
@AllArgsConstructor
@Getter
public enum BenefitGenerateTypeEnum {


    /**
     * 售卖
     */
    SELL(1, "sell","售卖"),

    /**
     * 赠送
     */
    GIVE(2,"give","赠送"),

    /**
     * 活动
     */
    ACTIVITY(3,"activity","活动"),

    /**
     * 标签
     */
    //LABEL(4,"label","标签"),

    /**
     * 设置商品
     */
    SETTING_COMMODITY(4,"settingCommodity","设置商品"),

    /**
     * 任务
     */
    TASK(5,"task","任务"),

    /**
     * 会员等级变动
     */
    MEMBERSHIP_LEVEL_CHANGE(6,"memberLevelChange","会员等级变动");
    ;



    private Integer type;

    private String name;

    private String desc;
}
