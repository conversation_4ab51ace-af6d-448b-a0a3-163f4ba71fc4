package com.lyy.user.account.infrastructure.constant;

import java.util.Arrays;
import java.util.Objects;

/**
 * <p>用于 3.0 会员流水 支付回调 PayMode 字段 与 TradeType 映射<p/>
 * <p>3.0 支付 tradeType 不一致 </p>
 *
 * <p>
 * payMode: {@link cn.lyy.open.payment.constant.PaymentModeEnum.PaymentClient }<br/>
 * tradeType: {@link cn.lyy.base.communal.constant.TradeTypeEnum}
 * <p/>
 *
 * <AUTHOR>
 * @since 2021/11/20
 */
@SuppressWarnings("JavadocReference")
public enum PayMode2TradeTypeMappingEnum {

    WECHAT_MAPPING(1, 2),
    ALIPAY_MAPPING(2, 1),
    UNION_MAPPING(3, 3),
    JD_MAPPING(4, 10),
    PURSE_MAPPING(5, 11),
    IC_CARD_MAPPING(14, 5),
    OTHER_MAPPING(100, null),
    ;


    /**
     * 支付渠道
     */
    private Integer payMode;

    /**
     * 交易类型
     */
    private Integer tradeType;

    PayMode2TradeTypeMappingEnum(Integer payMode, Integer tradeType) {
        this.payMode = payMode;
        this.tradeType = tradeType;
    }

    public Integer getPayMode() {
        return payMode;
    }

    public Integer getTradeType() {
        return tradeType;
    }

    public static Integer findTradeType(Integer payMode) {
        return Arrays.stream(PayMode2TradeTypeMappingEnum.values())
            .filter(e -> Objects.equals(e.getPayMode(), payMode))
            .findFirst()
            .map(PayMode2TradeTypeMappingEnum::getTradeType)
            .orElse(null);
    }
}
