package com.lyy.user.account.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 类描述：交易类型枚举
 * <p>
 *     用在账户流水的trade_type中
 *   产品说交易类型不是指支付类型
 * <AUTHOR>
 * @since 2021/6/15 14:34
 */
@Getter
@AllArgsConstructor
@Deprecated
public enum TradeTypeGroupEnum {

   //
   // /**
   //  * 交易类型
   //  */
   // WECHAT(1, "微信支付", Arrays.asList(
   //         TradeTypeEnum.WECHAT_TRADE.getId(),
   //         TradeTypeEnum.WECHAT_MINI_PROGRAM_TRADE.getId(),
   //         TradeTypeEnum.WECHAT_APP.getId(),
   //         TradeTypeEnum.WECHAT_FACE_TRADE.getId()
   // )),
   // ALIPAY(2, "支付宝支付",Arrays.asList(
   //         TradeTypeEnum.ALIPAY_TRADE.getId(),
   //         TradeTypeEnum.ALIPAY_FACE_TRADE.getId()
   // )),
   // UNION(3, "云闪付支付",Arrays.asList(
   //         TradeTypeEnum.UNION_TRADE.getId())),
   // JD(4, "京东支付",Arrays.asList(
   //         TradeTypeEnum.JD_TRADE.getId())),
   // REVERSE_SCAN(5, "反扫付款码",Arrays.asList(
   //         TradeTypeEnum.REVERSE_SCAN.getId())),
   //
   //WALLET(6, "其他支付",Arrays.asList(
   //         TradeTypeEnum.PURSE.getId(),
   //         TradeTypeEnum.BOOST.getId(),
   //         TradeTypeEnum.GRABPAY.getId(),
   //         TradeTypeEnum.Maybank.getId(),
   //        TradeTypeEnum.DIRECT_TRADE.getId()
   //)
   // ),
   //
   //
   // ;
   //
   //
   // private final Integer groupPayType;
   //
   // private final String description;
   // /**
   //  * 分组内包含的支付类型
   //  */
   // private final List<Integer> tradeTypes;
   //
   //
   // /**
   //  * 根据分组类型获取包含的支付类型
   //  * @param groupPayType
   //  * @return
   //  */
   // public static List<Integer> getTradeTypesByGroupPayType(Integer groupPayType) {
   //     return Arrays.stream(TradeTypeGroupEnum.values())
   //             .filter(group -> Objects.equals(group.getGroupPayType(), groupPayType))
   //             .map(TradeTypeGroupEnum::getTradeTypes)
   //             .findFirst()
   //             .orElseThrow(() -> new RuntimeException("分组支付类型错误"));
   // }
   //
   //
   // public static String findGroupPayType(Integer tradeType) {
   //     for (TradeTypeGroupEnum value : TradeTypeGroupEnum.values()) {
   //         if (value.getTradeTypes().contains(tradeType)) {
   //             return value.getDescription();
   //         }
   //     }
   //     throw new RuntimeException("分组支付类型错误");
   // }
   //
   // public static List<ConsumptionTradeType> list() {
   //     List<ConsumptionTradeType> list = new ArrayList<>();
   //     for (TradeTypeGroupEnum value : TradeTypeGroupEnum.values()) {
   //         ConsumptionTradeType consumption = new ConsumptionTradeType();
   //         consumption.setCode(value.getGroupPayType());
   //         consumption.setName(value.getDescription());
   //         list.add(consumption);
   //     }
   //     return list;
   // }
}

