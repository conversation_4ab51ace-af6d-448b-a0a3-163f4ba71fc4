package com.lyy.user.account.infrastructure.constant;

import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2021/3/31 16:39
 */
@AllArgsConstructor
@Getter
public enum ExpiryDateCategoryEnum {

    /**
     * 无限期
     */
    NO_LIMIT(0,"无限期"),

    /**
     * 时间区间
     */
    TIME_INTERVAL(1,"时间区间"),

    /**
     * 单日时间区间
     */
    ONE_DAY_TIME_INTERVAL(2,"单日时间区间"),
    ;

    private int value;

    private String description;

    public static Optional<ExpiryDateCategoryEnum> getCategory(Integer value) {
        for (ExpiryDateCategoryEnum item : values()) {
            if (Objects.equals(item.getValue(), value)) {
                return Optional.of(item);
            }
        }
        return Optional.empty();
    }
}
