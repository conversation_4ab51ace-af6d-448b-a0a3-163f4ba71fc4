package com.lyy.user.account.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: qgw
 * @date on 2021/4/7.
 * @Version: 1.0
 */
@AllArgsConstructor
@Getter
public enum MemberGrowRuleEnum {

    /**
     * 登录成长值
     */
    LOGIN("login","登录获取的成长值"),
    MEMBER("member","成为会员获取的成长值"),
    BIND_PHONE("bindPhone","绑定手机号获取的成长值"),
    COMPLETE_INFO("completeInfo","完善信息获取的成长值"),
    SUBSCRIBE("subscribe","关注公众号获取的成长值"),
    CONSUMPTION("consumption","消费获取的成长值"),
    PLANCE_ORDER("planceOrder","下单获取的成长值"),

    /**
     * 调整类型：增加或减少
     */
    MANUAL_ADJUST("manualAdjust","手工调整成长值"),



    ;

    private String growRuleType;

    private String desc;

}
