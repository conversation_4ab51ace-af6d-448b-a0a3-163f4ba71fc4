package com.lyy.user.account.infrastructure.constant;

import java.util.Arrays;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员升级规则策略枚举
 * <AUTHOR>
 * @className: MemberLiftingRuleCategoryEnum
 * @date 2021/3/29
 */
@AllArgsConstructor
@Getter
public enum MemberLiftingRuleCategoryEnum {
    CATEGORY_DEFAULT((short)-1,"默认行为，主要用于默认扣权益"),
    CATEGORY_LOGIN_NUM((short)1,"登录次数"),
    CATEGORY_PAY_NUM((short)2,"支付笔数"),
    CATEGORY_CONSUMPTION_MONEY((short)3,"消费金额"),
    CATEGORY_PERFECT_INFORMATION((short)4,"完善信息"),
    CATEGORY_BIND_PHONE((short)5,"绑定手机"),
    CATEGORY_FOLLOW_PUBLIC_ACCOUNT((short)6,"关注公众号"),
    ;
    private Short value;
    private String description;

    /**
     * 统计次数的策略类型
     */
    private final static List<MemberLiftingRuleCategoryEnum> COUNT_CATEGORY_LIST = Arrays.asList(
            MemberLiftingRuleCategoryEnum.CATEGORY_LOGIN_NUM,
            MemberLiftingRuleCategoryEnum.CATEGORY_PAY_NUM,
            MemberLiftingRuleCategoryEnum.CATEGORY_PERFECT_INFORMATION,
            MemberLiftingRuleCategoryEnum.CATEGORY_BIND_PHONE,
            MemberLiftingRuleCategoryEnum.CATEGORY_FOLLOW_PUBLIC_ACCOUNT
    );
    /**
     * 统计累计总数的策略类型
     */
    private final static List<MemberLiftingRuleCategoryEnum>  SUM_CATEGORY_LIST = Arrays.asList(
            MemberLiftingRuleCategoryEnum.CATEGORY_CONSUMPTION_MONEY
    );

    public static List<MemberLiftingRuleCategoryEnum> getCountCategoryList() {
        return COUNT_CATEGORY_LIST;
    }

    public static List<MemberLiftingRuleCategoryEnum> getSumCategoryList() {
        return SUM_CATEGORY_LIST;
    }

    /**
     * 根据value 获取对应的枚举
     * @param value
     * @return
     */
    public static MemberLiftingRuleCategoryEnum getByValue(Short value){
        if (value == null){
            return null;
        }
       return Arrays.stream(MemberLiftingRuleCategoryEnum.values())
                .filter(memberLiftingRuleCategoryEnum -> memberLiftingRuleCategoryEnum.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }

}
