package com.lyy.user.account.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 类描述：权益来源枚举
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/12 14:14
 */
@Getter
@ToString
@AllArgsConstructor
public enum AccountBenefitResourceEnum {

    /**
     * 会员升级
     */
    MEMBER_UPGRADE(1, "会员升级"),
    BENEFIT_RULE(2, "权益规则"),
    MEMBER_DEGRADE(3, "会员降级"),
    ;

    /**
     * 会员权益变更类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String description;


}
