package com.lyy.user.account.infrastructure.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/1/10
 */
@Getter
public enum AccountRecordOperationTypeEnum {
    COMMODITY_PURCHASE(1, "商品购买记录"),
    COMMODITY_EXCHANGE(2, "商品兑换记录"),
    COMMODITY_RECYCLE(3, "商品回收记录"),
    BENEFIT_MODIFY(4, "储值变更记录");

    private Integer code;
    private String description;

    AccountRecordOperationTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}
