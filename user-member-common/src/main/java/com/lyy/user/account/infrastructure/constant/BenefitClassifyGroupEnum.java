package com.lyy.user.account.infrastructure.constant;

import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.ADVERT_COIN;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.ADVERT_RED_PACKET;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.DEDUCTION_AMOUNT;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.DELAYED_SETTELEMENT_BALANCE;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.DELAYED_SETTELEMENT_COIN;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.IDLE_TIME_COIN;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.MERCHANT_PAYOUT_BALANCE;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.MERCHANT_PAYOUT_COIN;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.MERCHANT_PAYOUT_COUPON;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.MERCHANT_PAYOUT_GROUP_BALANCE;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.MERCHANT_PAYOUT_GROUP_COIN;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.PLATFORM_COUPON;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.PLATFORM_PAYOUT_BALANCE;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.PLATFORM_PAYOUT_COIN;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.RED_BALANCE;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.RED_COIN;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.THIRD_PLATFORM_AMOUNT;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.THIRD_PLATFORM_COINS;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.USER_RECHARGE_BALANCE;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.USER_RECHARGE_COIN;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.USER_RECHARGE_GIVE_BALANCE;
import static com.lyy.user.account.infrastructure.constant.BenefitClassifyEnum.USER_RECHARGE_GIVE_COIN;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 类描述：权益分组
 * <p>
 *
 * <AUTHOR>
 * @since 2021/4/20 09:45
 */
@AllArgsConstructor
@Getter
public enum BenefitClassifyGroupEnum {

    /**
     * 属于币的权益
     */
    COINS(1, Arrays.asList(USER_RECHARGE_COIN.getCode(),
            MERCHANT_PAYOUT_COIN.getCode(),
            PLATFORM_PAYOUT_COIN.getCode(),
            ADVERT_COIN.getCode(),
            IDLE_TIME_COIN.getCode(),
            RED_COIN.getCode(),
            THIRD_PLATFORM_COINS.getCode(),
            DELAYED_SETTELEMENT_COIN.getCode(),
            USER_RECHARGE_GIVE_COIN.getCode(),
            MERCHANT_PAYOUT_GROUP_COIN.getCode())),


    /**
     * 属于余额的权益
     */
    MONEY(2, Arrays.asList(USER_RECHARGE_BALANCE.getCode(),
            MERCHANT_PAYOUT_BALANCE.getCode(),
            PLATFORM_PAYOUT_BALANCE.getCode(),
            RED_BALANCE.getCode(),
            ADVERT_RED_PACKET.getCode(),
            THIRD_PLATFORM_AMOUNT.getCode(),
            // 新增抵扣金类型
            DEDUCTION_AMOUNT.getCode(),
            DELAYED_SETTELEMENT_BALANCE.getCode(),
            USER_RECHARGE_GIVE_BALANCE.getCode(),
            MERCHANT_PAYOUT_GROUP_BALANCE.getCode())),

    /**
     * 属于券的权益
     */
    COUPON(3, Arrays.asList(MERCHANT_PAYOUT_COUPON.getCode(),
        PLATFORM_COUPON.getCode()));

    /**
     * 分组类型
     */
    private final Integer type;

    /**
     * 分组内包含的权益类型
     */
    private final List<Integer> classify;


    /**
     * 根据分组类型获取包含的权益类型
     */
    public static List<Integer> getClassifyByType(Integer targetType) {
        return Arrays.stream(BenefitClassifyGroupEnum.values())
            .filter(group -> Objects.equals(group.getType(), targetType))
            .map(BenefitClassifyGroupEnum::getClassify)
            .findFirst()
            .orElseThrow(() -> new RuntimeException("分组类型类型错误"));
    }
}
