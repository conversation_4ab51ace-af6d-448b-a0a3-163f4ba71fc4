package com.lyy.user.account.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 标签业务类型，用于区分设备场地、设备类型
 * @author: qgw
 * @date on 2021/3/31.
 * @Version: 1.0
 */
@Getter
@AllArgsConstructor
public enum TagBusinessTypeEnum {
    /**
     * 标签类型
     */
    NORMAL(0, "普通标签"),
    GROUP_NAME(1, "场地名称"),
    EQUIPMENT(2, "设备类型"),
    PAY_TYPE(3, "支付方式标签"),
    SEX(4, "性别标签"),
    MEMBER(5, "会员标签"),
    USER_SOURCE(6, "用户来源标签"),
    INNER(7, "内部标签（不对外展示）"),
    ;

    private Integer status;

    private String desc;

    public static TagBusinessTypeEnum of(Integer status) {
        for (TagBusinessTypeEnum typeEnum : TagBusinessTypeEnum.values()) {
            if (typeEnum.getStatus().equals(status)) {
                return typeEnum;
            }
        }
        return null;
    }

}
