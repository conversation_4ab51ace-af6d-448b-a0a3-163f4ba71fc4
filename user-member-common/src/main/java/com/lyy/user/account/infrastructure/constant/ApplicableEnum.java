package com.lyy.user.account.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 权益范围类型枚举类
 *
 * <AUTHOR>
 * @create 2021/4/1 10:51
 */
@Getter
@AllArgsConstructor
public enum ApplicableEnum {

    /**
     * 品类
     */
    CATEGORY(1,"品类"),

    /**
     * 场地
     */
    GROUP(2,"场地"),

    /**
     * 设备
     */
    DEVICE(3,"设备"),

    /**
     * 商品
     */
    COMMODITY(4,"商品"),
    ;

    private Integer value;

    private String description;

    public static ApplicableEnum findByValue(Integer value) {
        for (ApplicableEnum applicableEnum : ApplicableEnum.values()) {
            if (applicableEnum.getValue().equals(value)) {
                return applicableEnum;
            }
        }
        return null;
    }
}
