package com.lyy.user.account.infrastructure.constant;

import com.lyy.user.account.infrastructure.account.dto.ConsumptionTradeType;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.CollectionUtils;

/**
 * 类描述：交易类型分组-非支付类型
 * <p>
 *
 * <AUTHOR>
 * @since 2021/6/18 09:45
 */
@AllArgsConstructor
@Getter
public enum AccountRecordTypeGroupEnum {

    /**
     * 设备启动
     */
    EQUIP_START(1,"设备启动"),


    /**
     * 商品购买
     */
    COMMODITY_PURCHASE(2,"商品购买"),

    /**
     * 充值
     */
    RECHARGE(3,"充值"),
  /**
     * 赠送
     */
  GIFT(4,"赠送"),


  /**
     * 退款
     */
  REFUND(5,"退款"),



  /**
     * 剩余金额退回
     */
  REMAINING_BALANCE(6,"剩余金额退回"),


  /**
     * 活动
     */
  ACTIVITY(7,"活动"),


  /**
     * 过期
     */
  EXPIRED(8,"过期"),


  /**
     * 智能引流
     */
  INTELLIGENT(9,"智能引流"),


  /**
     * 其他
     */
  OTHER(10,"其他" ),

    /**
     * 商家调整
     */
    ADJUST(11,"调整"),

    /**
     * 收款码
     */
    RECEIVE_CODE(13,"收款码"),

    /**
     * 小场地
     */
    SMALL_VENUE(14,"小场地"),

    ;

    /**
     * 分组类型
     */
    private final Integer type;

    /**
     * 分组交易类型名称
     */
    private final String typeName;



    /**
     * 根据分组类型获取包含的交易类型
     * @param groupIds
     * @return
     */
    public static List<Integer> getTradeTypesByGroupPayType(List<Integer> groupIds) {
        List<Integer> list = AccountRecordTypeEnum.getAllByGroupId(groupIds);
        if (CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("分组交易类型错误");
        }
        return list;
    }



    public static String findGroupPayType(Integer tradeType) {
        AccountRecordTypeEnum recordTypeEnum = AccountRecordTypeEnum.findByCode(tradeType);
        if (recordTypeEnum == null) {
            throw new RuntimeException("分组交易类型错误");
        }
        return recordTypeEnum.getShowText();
    }

    public static List<ConsumptionTradeType> list() {
        List<ConsumptionTradeType> list = new ArrayList<>();
        for (AccountRecordTypeGroupEnum value : AccountRecordTypeGroupEnum.values()) {
            ConsumptionTradeType consumption = new ConsumptionTradeType();
            consumption.setCode(value.getType());
            consumption.setName(value.getTypeName());
            list.add(consumption);
        }
        return list;
    }
}
